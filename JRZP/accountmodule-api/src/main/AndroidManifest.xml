<manifest package="com.bxkj.accountmodule.api" tools:ignore="LockedOrientationActivity" xmlns:tools="http://schemas.android.com/tools">
  <application android:label="@string/common_app_name" xmlns:android="http://schemas.android.com/apk/res/android" android:supportsRtl="true" android:theme="@style/JrzpAppTheme" tools:replace="android:label">
    <activity android:name="com.bxkj.account.ui.updatepassword.UpdatePasswordActivityV2" android:launchMode="singleTask" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.account.ui.register.RegisterActivityV2" android:launchMode="singleTask" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.account.ui.login.LoginV2Activity" android:launchMode="singleTask" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.account.ui.jobselection.JobSelectionActivity" android:configChanges="keyboardHidden" android:screenOrientation="portrait" android:windowSoftInputMode="stateHidden"/>
    <activity android:name="com.bxkj.account.ui.recoverpassword.RecoverPasswordActivity" android:configChanges="keyboardHidden" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
  </application>
</manifest>
