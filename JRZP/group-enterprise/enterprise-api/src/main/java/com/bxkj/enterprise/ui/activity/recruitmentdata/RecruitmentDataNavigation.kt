package com.bxkj.enterprise.ui.activity.recruitmentdata

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * Description:
 * Author:Sanjin
 * Date:2024/4/9
 **/
class RecruitmentDataNavigation {

  companion object {
    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/recruitment_data"

    fun create(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}