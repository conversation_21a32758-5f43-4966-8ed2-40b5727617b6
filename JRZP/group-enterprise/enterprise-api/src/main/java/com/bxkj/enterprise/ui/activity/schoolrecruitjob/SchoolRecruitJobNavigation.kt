package com.bxkj.enterprise.ui.activity.schoolrecruitjob

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants
import com.bxkj.enterprise.data.JobItem

/**
 *
 * @author: sanjin
 * @date: 2022/6/1
 */
class SchoolRecruitJobNavigation {

    companion object {
        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/schoolrecruitjob"

        const val EXTRA_JOB_LIST = "JOB_LIST"

        fun create(list: List<JobItem>?): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withParcelableArrayList(EXTRA_JOB_LIST, list?.let { ArrayList(it) })
        }
    }
}