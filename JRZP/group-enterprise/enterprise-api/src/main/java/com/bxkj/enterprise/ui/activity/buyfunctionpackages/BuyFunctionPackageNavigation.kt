package com.bxkj.enterprise.ui.activity.buyfunctionpackages

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * Description:
 * Author:Sanjin
 * Date:2024/2/23
 **/
class BuyFunctionPackageNavigation {

    companion object {

        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/buyfunctionpackage"

        const val EXTRA_TYPE = "TYPE"

        fun create(type: Int): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withInt(EXTRA_TYPE, type)
        }
    }
}