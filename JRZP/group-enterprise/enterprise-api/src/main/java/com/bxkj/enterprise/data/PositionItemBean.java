package com.bxkj.enterprise.data;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;
import androidx.recyclerview.widget.DiffUtil;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.widget.popup.IWheelOptions;
import com.bxkj.common.widget.popup.menupopup.PopupMenuItem;
import com.bxkj.enterprise.api.BR;
import com.contrarywind.interfaces.IPickerViewData;
import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * @version V1.0
 * @Description:
 */
public class PositionItemBean extends BaseObservable
  implements Parcelable, IPickerViewData, PopupMenuItem, IWheelOptions {

  /**
   * id : 1256023 name : asp.net开发工程师 edate1 : 2018/7/17 10:15:59 money : 5000-8000元 wtName : 1年以上
   * quaName : 本科 shiName : 杭州 quName : 滨江区 applyCount : 0 refresh : 0
   */

  private int id;

  private int index;

  private String name;

  private String edate1;

  private String money;

  private String wtName;

  private String quaName;

  private String shiName;

  private String quName;

  private int applyCount;

  private int refresh;

  private int istop;

  private String jieName;

  private int count;

  private int type2;

  private int shi;

  private int toudiCount;

  private int uid;

  private int type;

  private String moneyUnitName;

  private String moneyJiesuanName;

  @SerializedName("shenfenName")
  private String identityRequire;

  @SerializedName("jnName2")
  private String partnerNature;

  @SerializedName("jnName")
  private String tag;

  private int IsVip;

  private String address;

  private String hrLxr;

  private String hrMobile;

  private int qu;

  private int hrid;

  private int IsZidinyi;

  private int Istoudi;

  private int Isfufei;

  public int getIsfufei() {
    return Isfufei;
  }

  public void setIsfufei(final int isfufei) {
    Isfufei = isfufei;
  }

  public int getIstoudi() {
    return Istoudi;
  }

  public void setIstoudi(final int istoudi) {
    Istoudi = istoudi;
    notifyPropertyChanged(BR.delivered);
  }

  public void markDelivered() {
    Istoudi = 1;
    notifyPropertyChanged(BR.delivered);
  }

  @Bindable
  public boolean isDelivered() {
    return Istoudi > 0;
  }

  protected PositionItemBean(Parcel in) {
    id = in.readInt();
    index = in.readInt();
    name = in.readString();
    edate1 = in.readString();
    money = in.readString();
    wtName = in.readString();
    quaName = in.readString();
    shiName = in.readString();
    quName = in.readString();
    applyCount = in.readInt();
    refresh = in.readInt();
    istop = in.readInt();
    jieName = in.readString();
    count = in.readInt();
    type2 = in.readInt();
    shi = in.readInt();
    toudiCount = in.readInt();
    uid = in.readInt();
    type = in.readInt();
    moneyUnitName = in.readString();
    moneyJiesuanName = in.readString();
    identityRequire = in.readString();
    partnerNature = in.readString();
    tag = in.readString();
    IsVip = in.readInt();
    address = in.readString();
    hrLxr = in.readString();
    hrMobile = in.readString();
    qu = in.readInt();
    hrid = in.readInt();
    Istoudi = in.readInt();
  }

  public static final Creator<PositionItemBean> CREATOR = new Creator<PositionItemBean>() {
    @Override
    public PositionItemBean createFromParcel(Parcel in) {
      return new PositionItemBean(in);
    }

    @Override
    public PositionItemBean[] newArray(int size) {
      return new PositionItemBean[size];
    }
  };

  @Override
  public int describeContents() {
    return 0;
  }

  @Override
  public void writeToParcel(final Parcel dest, final int flags) {
    dest.writeInt(id);
    dest.writeInt(index);
    dest.writeString(name);
    dest.writeString(edate1);
    dest.writeString(money);
    dest.writeString(wtName);
    dest.writeString(quaName);
    dest.writeString(shiName);
    dest.writeString(quName);
    dest.writeInt(applyCount);
    dest.writeInt(refresh);
    dest.writeInt(istop);
    dest.writeString(jieName);
    dest.writeInt(count);
    dest.writeInt(type2);
    dest.writeInt(shi);
    dest.writeInt(toudiCount);
    dest.writeInt(uid);
    dest.writeInt(type);
    dest.writeString(moneyUnitName);
    dest.writeString(moneyJiesuanName);
    dest.writeString(identityRequire);
    dest.writeString(partnerNature);
    dest.writeString(tag);
    dest.writeInt(IsVip);
    dest.writeString(address);
    dest.writeString(hrLxr);
    dest.writeString(hrMobile);
    dest.writeInt(qu);
    dest.writeInt(hrid);
    dest.writeInt(Istoudi);
  }

  public int getIsZidinyi() {
    return IsZidinyi;
  }

  public void setIsZidinyi(final int isZidinyi) {
    IsZidinyi = isZidinyi;
  }

  public int getHrid() {
    return hrid;
  }

  public void setHrid(final int hrid) {
    this.hrid = hrid;
  }

  public int getQu() {
    return qu;
  }

  public void setQu(final int qu) {
    this.qu = qu;
  }

  public String getHrLxr() {
    return CheckUtils.isNullOrEmpty(hrLxr) ? "" : hrLxr;
  }

  public void setHrLxr(String hrLxr) {
    this.hrLxr = hrLxr;
  }

  public String getHrMobile() {
    return CheckUtils.isNullOrEmpty(hrMobile) ? "" : hrMobile;
  }

  public void setHrMobile(String hrMobile) {
    this.hrMobile = hrMobile;
  }

  public PositionItemBean() {
  }

  public PositionItemBean(int id, String name) {
    this.id = id;
    this.name = name;
  }

  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  @Override
  public String getPickerViewText() {
    return name;
  }

  @Override
  public String getTitle() {
    return name;
  }

  @Override
  public String getItemOption() {
    return name + (getTag() == null ? "" : "-" + getTag());
  }

  public int getIsVip() {
    return IsVip;
  }

  public void setIsVip(int isVip) {
    IsVip = isVip;
  }

  public String getTag() {
    return tag;
  }

  public void setTag(String tag) {
    this.tag = tag;
  }

  public String getTypeText() {
    if (type == 0) {
      return "社招";
    } else {
      return "校招";
    }
  }

  public Boolean isNormalRecruitment() {
    return type == 0;
  }

  public int getType() {
    return type;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getIndex() {
    return index;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  public int getToudiCount() {
    return toudiCount;
  }

  public void setToudiCount(int toudiCount) {
    this.toudiCount = toudiCount;
  }

  public int getCount() {
    return count;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public String getJieName() {
    return jieName;
  }

  public void setJieName(String jieName) {
    this.jieName = jieName;
  }

  public int getIstop() {
    return istop;
  }

  public void setIstop(int istop) {
    this.istop = istop;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getEdate1() {
    return edate1;
  }

  public String getFormatDate() {
    return CheckUtils.isNullOrEmpty(edate1) ? "" : edate1.split("\\s")[0];
  }

  public void setEdate1(String edate1) {
    this.edate1 = edate1;
  }

  public String getMoney() {
    return money;
  }

  public void setMoney(String money) {
    this.money = money;
  }

  public String getWtName() {
    return wtName;
  }

  public void setWtName(String wtName) {
    this.wtName = wtName;
  }

  public String getQuaName() {
    return quaName;
  }

  public void setQuaName(String quaName) {
    this.quaName = quaName;
  }

  public String getShiName() {
    return shiName;
  }

  public void setShiName(String shiName) {
    this.shiName = shiName;
  }

  public String getQuName() {
    return quName;
  }

  public void setQuName(String quName) {
    this.quName = quName;
  }

  public int getApplyCount() {
    return applyCount;
  }

  public void setApplyCount(int applyCount) {
    this.applyCount = applyCount;
  }

  public int getRefresh() {
    return refresh;
  }

  public void setRefresh(int refresh) {
    this.refresh = refresh;
  }

  public int getType2() {
    return type2;
  }

  public void setType2(int type2) {
    this.type2 = type2;
  }

  public int getShi() {
    return shi;
  }

  public void setShi(int shi) {
    this.shi = shi;
  }

  public int getUid() {
    return uid;
  }

  public void setUid(int uid) {
    this.uid = uid;
  }

  public boolean showAbout() {
    return id > 0;
  }

  public String getPositionAbout() {
    StringBuilder stringBuilder = new StringBuilder();
    if (!CheckUtils.isNullOrEmpty(quName)) {
      stringBuilder.append(quName).append(" | ");
    }
    if (!CheckUtils.isNullOrEmpty(quaName)) {
      stringBuilder.append(quaName);
    }
    if (!CheckUtils.isNullOrEmpty(wtName)) {
      stringBuilder.append(" | ").append(wtName);
    }
    if (!CheckUtils.isNullOrEmpty(money)) {
      stringBuilder.append(" | ").append(money);
    }
    if (!CheckUtils.isNullOrEmpty(moneyUnitName)) {
      stringBuilder.append("/").append(moneyUnitName);
    }
    return stringBuilder.toString();
  }

  public String getPositionAboutNoSalary() {
    StringBuilder stringBuilder = new StringBuilder();
    if (!CheckUtils.isNullOrEmpty(quName)) {
      stringBuilder.append(quName);
    }
    if (!CheckUtils.isNullOrEmpty(jieName)) {
      stringBuilder.append(jieName).append(" | ");
    }
    if (!CheckUtils.isNullOrEmpty(quaName)) {
      stringBuilder.append(quaName).append(" | ");
    }
    if (!CheckUtils.isNullOrEmpty(wtName)) {
      stringBuilder.append(wtName).append(" | ");
    }
    return CheckUtils.isNullOrEmpty(stringBuilder) ? stringBuilder.toString()
      : stringBuilder.substring(0, stringBuilder.lastIndexOf("|") - 1);
  }

  public String getMoneyUnitName() {
    return moneyUnitName;
  }

  public void setMoneyUnitName(String moneyUnitName) {
    this.moneyUnitName = moneyUnitName;
  }

  public String getMoneyJiesuanName() {
    return moneyJiesuanName;
  }

  public void setMoneyJiesuanName(String moneyJiesuanName) {
    this.moneyJiesuanName = moneyJiesuanName;
  }

  public String getIdentityRequire() {
    return identityRequire;
  }

  public void setIdentityRequire(String identityRequire) {
    this.identityRequire = identityRequire;
  }

  public String getPartnerNature() {
    return partnerNature;
  }

  public void setPartnerNature(String partnerNature) {
    this.partnerNature = partnerNature;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PositionItemBean that = (PositionItemBean) o;
    return id == that.id &&
      Objects.equals(name, that.name);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, name);
  }

  public String getConvertSalary() {
    if (!CheckUtils.isNullOrEmpty(getMoney()) && getMoney().equals("面议")) {
      return getMoney();
    } else {
      final StringBuilder salaryBuilder = new StringBuilder();
      salaryBuilder.append(getMoney());
      if (!CheckUtils.isNullOrEmpty(moneyUnitName)) {
        salaryBuilder.append("/").append(moneyUnitName);
      }
      if (!CheckUtils.isNullOrEmpty(moneyJiesuanName)) {
        salaryBuilder.append("/").append(moneyJiesuanName);
      }
      return salaryBuilder.toString();
    }
  }

  public static class DiffCallback extends DiffUtil.ItemCallback<PositionItemBean> {

    @Override
    public boolean areItemsTheSame(@NonNull final PositionItemBean oldItem,
      @NonNull final PositionItemBean newItem) {
      return oldItem == newItem;
    }

    @Override
    public boolean areContentsTheSame(@NonNull final PositionItemBean oldItem,
      @NonNull final PositionItemBean newItem) {
      return oldItem.id == newItem.id;
    }
  }
}