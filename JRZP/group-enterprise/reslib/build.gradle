plugins{
    id 'com.android.library'
}

android {

    defaultConfig {
        compileSdk libs.versions.compileSdkVersion.get().toInteger()
        minSdkVersion libs.versions.minSdkVersion.get()
        targetSdkVersion libs.versions.targetSdkVersion.get()

      testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
  namespace 'com.bxkj.reslib'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation libs.appcompat
}
