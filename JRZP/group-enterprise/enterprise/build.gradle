plugins {
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
  id 'kotlin-parcelize'
  id 'pins-module'
  id 'kotlin-kapt'
  id 'com.google.devtools.ksp'
}

android {

  namespace "com.bxkj.enterprise"

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()
    versionCode libs.versions.versionCode.get().toInteger()
    versionName libs.versions.versionName.get()
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    multiDexEnabled true
  }

  compileOptions {

    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }

  resourcePrefix "b_"

  buildFeatures {
    dataBinding = true
  }
}

dependencies {
  implementation fileTree(dir: 'libs', include: ['*.jar'])
  coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'

  ksp libs.therouter.apt
  ksp libs.dagger.complier
  ksp libs.dagger.android.processor
  ksp libs.room.compiler

  implementation project(':group-enterprise:commonlib')

  implementation project(':group-business:video')
  implementation project(':group-support:share')
  implementation project(':group-support:upload')
  implementation project(':group-support:feature')
  implementation project(":group-support:chat")

  implementation "com.github.ssseasonnn:DownloadX:1.0.5"

  implementation 'com.yx95:calenderview:1.0.0'
}
