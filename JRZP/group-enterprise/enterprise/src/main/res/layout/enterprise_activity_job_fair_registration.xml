<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.jobfairregistration.JobFairRegistrationViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:right_text="@string/sign_up"
      app:title="@string/job_fair_registration" />

    <androidx.core.widget.NestedScrollView
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <include layout="@layout/enterprise_include_asterisk" />

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/company_name" />

          <com.bxkj.ecommon.widget.ClearEditText
            android:id="@+id/et_name"
            style="@style/common_EditText.Basic.RightAlignment"
            android:text="@={viewModel.companyInfo.name}" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <include layout="@layout/enterprise_include_asterisk" />

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/contract_contracts" />

          <com.bxkj.ecommon.widget.ClearEditText
            android:id="@+id/et_contracts"
            style="@style/common_EditText.Basic.RightAlignment"
            android:text="@={viewModel.companyInfo.lxr}" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <include layout="@layout/enterprise_include_asterisk" />

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/contract_phone" />

          <com.bxkj.ecommon.widget.ClearEditText
            android:id="@+id/et_contract_phone"
            style="@style/common_EditText.Basic.RightAlignment"
            android:inputType="number"
            android:text="@={viewModel.companyInfo.phone}" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <include layout="@layout/enterprise_include_asterisk" />

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/company_details_address" />

          <TextView
            android:id="@+id/tv_details_address"
            style="@style/common_Text.InfoItem.Select"
            android:onClick="@{onClickListener}"
            android:text="@{viewModel.companyInfo.addressData+viewModel.companyInfo.address}" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <include layout="@layout/enterprise_include_asterisk" />

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/company_about" />

          <TextView
            android:id="@+id/tv_about"
            style="@style/common_Text.InfoItem.Select"
            android:onClick="@{onClickListener}"
            android:text="@{viewModel.companyInfo.info}" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <include layout="@layout/enterprise_include_asterisk" />

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/company_industry" />

          <TextView
            android:id="@+id/tv_industry"
            style="@style/common_Text.InfoItem.Select"
            android:onClick="@{onClickListener}"
            android:text="@{viewModel.companyInfo.tradeName}" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <include layout="@layout/enterprise_include_asterisk" />

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/company_nature" />

          <TextView
            android:id="@+id/tv_nature"
            style="@style/common_Text.InfoItem.Select"
            android:onClick="@{onClickListener}"
            android:text="@{viewModel.companyInfo.proName}" />
        </LinearLayout>

        <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

          <include layout="@layout/enterprise_include_asterisk" />

          <TextView
            style="@style/common_Text.15sp.333333"
            android:text="@string/company_size" />

          <TextView
            android:id="@+id/tv_size"
            style="@style/common_Text.InfoItem.Select"
            android:onClick="@{onClickListener}"
            android:text="@{viewModel.companyInfo.sizeName}" />
        </LinearLayout>

        <TextView
          style="@style/common_Text.14sp.767676"
          android:layout_width="match_parent"
          android:layout_height="@dimen/dp_48"
          android:background="@drawable/bg_f4f4f4"
          android:gravity="center_vertical"
          android:paddingStart="@dimen/common_dp_12"
          android:paddingEnd="@dimen/common_dp_12"
          android:text="@string/job_fair_recruit_info" />

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/recycler_recruit_info"
          style="@style/match_wrap"
          android:adapter="@{viewModel.recruitInfoListAdapter}"
          android:overScrollMode="never" />

      </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <FrameLayout
      android:id="@+id/fl_add_recruit_info"
      android:layout_width="match_parent"
      android:layout_height="57dp"
      android:onClick="@{onClickListener}">

      <TextView
        style="@style/common_Text.14sp.888888"
        android:layout_gravity="center"
        android:drawableStart="@drawable/ic_add_recruit_info"
        android:drawablePadding="@dimen/common_dp_2"
        android:gravity="center"
        android:text="@string/job_fair_recruit_info_add" />
    </FrameLayout>
  </LinearLayout>
</layout>
