<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.enterprise.data.SchoolRecruitCityData" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:background="@{data.checked?@drawable/bg_f4f4f4_round:@drawable/bg_ffffff}"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/dp_12"
    android:paddingEnd="@dimen/dp_12">

    <TextView
      style="@style/Text.14sp.333333"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_8"
      android:text="@{data.name}" />

    <androidx.legacy.widget.Space
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <ImageView
      style="@style/wrap_wrap"
      android:src="@drawable/ic_address_selected"
      android:visibility="@{data.checked?View.VISIBLE:View.GONE}" />
  </LinearLayout>
</layout>