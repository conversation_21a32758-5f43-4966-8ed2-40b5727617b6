<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.bxkj.enterprise.data.PositionItemBean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:paddingTop="@dimen/common_dp_12"
        android:paddingBottom="@dimen/common_dp_12">

        <TextView
            android:id="@+id/tv_job_name"
            style="@style/common_Text.18sp.333333"
            android:layout_marginEnd="@dimen/common_dp_16"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.name}"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tv_salary"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_salary"
            style="@style/common_Text.14sp.49C280"
            android:text="@{data.money}"
            app:layout_constraintBottom_toBottomOf="@id/tv_job_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_job_name" />

        <TextView
            android:id="@+id/tv_job_about"
            style="@style/common_Text.12sp.888888"
            android:layout_marginTop="@dimen/common_dp_4"
            android:layout_marginEnd="@dimen/common_dp_16"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.positionAboutNoSalary}"
            app:layout_constraintEnd_toStartOf="@id/tv_date"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_job_name" />

        <TextView
            android:id="@+id/tv_date"
            style="@style/common_Text.12sp.888888"
            android:text="@{data.formatDate}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_job_about"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>