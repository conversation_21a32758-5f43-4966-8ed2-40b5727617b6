<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.CheckUtils" />

    <variable
      name="viewed"
      type="boolean" />

    <variable
      name="data"
      type="com.bxkj.enterprise.data.ApplicantResumeData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:layout_marginStart="@dimen/dp_8"
    android:layout_marginEnd="@dimen/dp_8"
    android:background="@drawable/bg_ffffff_radius_10"
    android:paddingStart="@dimen/dp_20"
    android:paddingEnd="@dimen/dp_20">

    <com.bxkj.common.widget.ZPTextView
      android:id="@+id/tv_name"
      style="@style/Text.16sp"
      android:layout_width="@dimen/common_dp_0"
      android:layout_marginEnd="@dimen/common_dp_14"
      android:text="@{data.ubInfo.name}"
      android:textColor="@{viewed?@color/cl_999999:@color/cl_333333}"
      app:layout_constraintBottom_toTopOf="@id/tv_desc"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="@id/iv_avatar"
      app:layout_constraintVertical_chainStyle="spread_inside" />

    <TextView
      android:id="@+id/tv_desc"
      style="@style/Text.12sp"
      android:layout_width="@dimen/common_dp_0"
      android:layout_marginEnd="@dimen/common_dp_14"
      android:text="@{data.desc}"
      android:textColor="@{viewed?@color/cl_999999:@color/cl_888888}"
      app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
      app:layout_constraintEnd_toStartOf="@id/iv_avatar"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <de.hdodenhof.circleimageview.CircleImageView
      android:id="@+id/iv_avatar"
      android:layout_width="@dimen/dp_48"
      android:layout_height="@dimen/dp_48"
      android:layout_marginTop="@dimen/dp_14"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      bind:imgUrl="@{data.ubInfo.tx}" />

<!--    <ImageView-->
<!--      style="@style/common_wrap_wrap"-->
<!--      android:src="@{data.ubInfo.man?@drawable/ic_male:@drawable/ic_female}"-->
<!--      app:layout_constraintBottom_toBottomOf="@id/iv_avatar"-->
<!--      app:layout_constraintEnd_toEndOf="@id/iv_avatar" />-->

    <com.bxkj.common.widget.ZPTextView
      android:id="@+id/tv_expect_job"
      style="@style/Text.14sp"
      android:layout_marginTop="@dimen/common_dp_10"
      android:drawableStart="@drawable/enterprise_ic_seen_me_geek_expect"
      android:drawablePadding="@dimen/dp_4"
      android:gravity="center_vertical"
      android:text="@{@string/enterprise_seen_me_geek_expect_format(data.desiredJob)}"
      android:textColor="@{viewed?@color/cl_999999:@color/cl_333333}"
      app:layout_constraintBottom_toTopOf="@id/tv_self_assessment"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_desc"
      app:layout_goneMarginBottom="@dimen/dp_12" />

    <TextView
      android:id="@+id/tv_self_assessment"
      style="@style/Text.14sp"
      android:layout_width="@dimen/common_dp_0"
      android:layout_marginTop="@dimen/common_dp_12"
      android:ellipsize="end"
      android:maxLines="2"
      android:text="@{data.selfIntro}"
      android:textColor="@{viewed?@color/cl_999999:@color/cl_888888}"
      android:visibility="@{CheckUtils.isNullOrEmpty(data.selfIntro)?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toTopOf="@id/v_split"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_expect_job"
      app:layout_goneMarginBottom="@dimen/dp_12" />

    <View
      android:id="@+id/v_split"
      style="@style/Line.Horizontal.Light"
      android:layout_marginTop="@dimen/common_dp_12"
      android:visibility="@{data.hasTimeDiff()?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toTopOf="@id/tv_date"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_self_assessment"
      app:layout_goneMarginTop="@dimen/dp_0" />

    <TextView
      android:id="@+id/tv_date"
      style="@style/Text.12sp"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_8"
      android:text="@{data.timeDiff}"
      android:textColor="@{viewed?@color/cl_999999:@color/cl_888888}"
      android:visibility="@{data.hasTimeDiff()?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/v_split" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>