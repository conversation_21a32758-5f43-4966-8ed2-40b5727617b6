<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.enterprise.ui.activity.udpatephonenumber.UpdatePhoneNumberViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44" />

        <TextView
            style="@style/Text.28sp.333333"
            android:layout_marginEnd="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_24"
            android:text="@string/enterprise_update_phone_number_title" />

        <TextView
            style="@style/Text.14sp.666666"
            android:layout_marginEnd="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_18"
            android:text="@{@string/enterprise_update_phone_number_tips(viewModel.originPhoneNumber)}" />

        <EditText
            style="@style/EditText.Basic"
            android:layout_marginEnd="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_24"
            android:layout_weight="0"
            android:background="@drawable/common_bg_edittext"
            android:hint="@string/enterprise_update_phone_number_hint"
            android:imeOptions="actionNext"
            android:inputType="number"
            android:text="@={viewModel.phoneNumber}" />

        <LinearLayout
            style="@style/match_wrap"
            android:layout_marginEnd="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_24"
            android:orientation="horizontal">

            <EditText
                style="@style/EditText.Basic"
                android:layout_marginEnd="@dimen/dp_12"
                android:background="@drawable/common_bg_edittext"
                android:hint="@string/enterprise_update_phone_sms_code_hint"
                android:imeOptions="actionDone"
                android:inputType="number"
                android:text="@={viewModel.smsCode}" />

            <TextView
                style="@style/Text.14sp.FE6600"
                android:background="@drawable/frame_fe6600"
                android:enabled="@{viewModel.smdCodeCountdown==0}"
                android:onClick="@{()->viewModel.requestSmsCode()}"
                android:padding="@dimen/dp_8"
                android:text="@{viewModel.smdCodeCountdown==0?@string/enterprise_update_phone_send_sms_code:@string/enterprise_update_phone_sms_code_countdown_format(viewModel.smdCodeCountdown)}"
                android:typeface="monospace" />

        </LinearLayout>

        <TextView
            style="@style/Button.Basic"
            android:layout_margin="@dimen/dp_24"
            android:onClick="@{()->viewModel.save()}"
            android:enabled="@{viewModel.loginBtnEnabled}"
            android:text="@string/save" />
    </LinearLayout>

</layout>