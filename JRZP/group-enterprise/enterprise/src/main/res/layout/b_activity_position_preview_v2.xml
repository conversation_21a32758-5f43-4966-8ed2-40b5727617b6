<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.CheckUtils" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.positionpreview.JobPreviewViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44"
      app:title="@string/position_preview" />

    <androidx.core.widget.NestedScrollView
      android:id="@+id/scroll_content"
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_0"
      android:layout_weight="1">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <TextView
          android:id="@+id/tv_name_and_salary"
          style="@style/common_Text.18sp.333333.Bold"
          android:layout_marginStart="@dimen/common_dp_12"
          android:layout_marginTop="@dimen/common_dp_16"
          android:layout_marginEnd="@dimen/common_dp_12"
          android:text="@{HtmlUtils.fromHtml(@string/position_preview_name_and_salary_format(viewModel.jobDetails.name,viewModel.jobDetails.money))}" />

        <LinearLayout
          style="@style/match_wrap"
          android:layout_marginTop="@dimen/common_dp_16"
          android:orientation="horizontal"
          android:paddingStart="@dimen/common_dp_12"
          android:paddingEnd="@dimen/common_dp_12">

          <TextView
            android:id="@+id/tv_job_address"
            style="@style/common_Text.12sp.888888"
            android:drawableStart="@drawable/ic_position_preview_address"
            android:drawablePadding="@dimen/common_dp_4"
            android:ellipsize="end"
            android:lines="1"
            android:maxWidth="90dp"
            android:text="@{viewModel.jobDetails.countyAndDistrictAddress}"
            android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.jobDetails.countyAndDistrictAddress) ? View.GONE : View.VISIBLE}" />

          <TextView
            android:id="@+id/tv_job_exp"
            style="@style/common_Text.12sp.888888"
            android:layout_marginStart="@dimen/common_dp_12"
            android:drawableStart="@drawable/ic_position_preview_exp"
            android:drawablePadding="@dimen/common_dp_4"
            android:text="@{viewModel.jobDetails.wtName}"
            android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.jobDetails.wtName) ? View.GONE : View.VISIBLE}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_job_address"
            app:layout_constraintStart_toEndOf="@id/tv_job_address" />

          <TextView
            android:id="@+id/tv_job_education"
            style="@style/common_Text.12sp.888888"
            android:layout_marginStart="@dimen/common_dp_12"
            android:drawableStart="@drawable/ic_position_preview_education"
            android:drawablePadding="@dimen/common_dp_4"
            android:text="@{viewModel.jobDetails.quaName}"
            android:visibility="@{CheckUtils.isNullOrEmpty(viewModel.jobDetails.quaName) ? View.GONE : View.VISIBLE}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_job_exp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_job_exp" />

          <Space
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:layout_weight="1" />

          <TextView
            android:id="@+id/tv_publish_date"
            style="@style/Text.12sp.FF7647"
            android:layout_marginStart="@dimen/common_dp_12"
            android:drawableStart="@drawable/common_ic_job_publish_date"
            android:drawablePadding="@dimen/common_dp_4"
            android:text="@{viewModel.jobDetails.publishDateDiff}" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/recycler_welfare"
          style="@style/match_wrap"
          android:layout_marginStart="@dimen/dp_12"
          android:layout_marginTop="@dimen/dp_8"
          android:layout_marginEnd="@dimen/dp_12" />

        <View
          style="@style/common_Line.Horizontal"
          android:layout_height="@dimen/common_dp_8"
          android:layout_marginTop="@dimen/dp_16" />

        <TextView
          android:id="@+id/tv_desc_of_content"
          style="@style/Text.PositionPreviewItemTitle"
          android:text="@string/position_preview_job_content" />

        <TextView
          android:id="@+id/tv_desc_of_text"
          style="@style/common_Text.15sp.888888"
          android:layout_width="match_parent"
          android:layout_marginStart="@dimen/common_dp_12"
          android:layout_marginTop="@dimen/dp_18"
          android:layout_marginEnd="@dimen/common_dp_12"
          android:layout_marginBottom="@dimen/common_dp_16"
          android:lineSpacingExtra="@dimen/common_dp_4"
          android:text="@{viewModel.jobDetails.des}"
          app:layout_constraintTop_toBottomOf="@id/tv_desc_of_content" />

        <View
          style="@style/common_Line.Horizontal"
          android:layout_height="@dimen/common_dp_8" />

        <androidx.constraintlayout.widget.ConstraintLayout
          style="@style/match_wrap"
          android:paddingBottom="@dimen/common_dp_16">

          <TextView
            android:id="@+id/tv_contract_way"
            style="@style/Text.PositionPreviewItemTitle"
            android:text="@string/position_preview_contract_way"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

          <TextView
            android:id="@+id/tv_contracts"
            style="@style/common_Text.15sp.333333"
            android:layout_marginStart="@dimen/common_dp_12"
            android:layout_marginTop="@dimen/common_dp_16"
            android:drawableStart="@drawable/ic_position_preview_contracts"
            android:drawablePadding="@dimen/common_dp_5"
            android:text="@{@string/position_preview_contracts_format(viewModel.jobDetails.lxr)}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_contract_way" />

          <TextView
            android:id="@+id/tv_contract_phone"
            style="@style/common_Text.15sp.333333"
            android:layout_width="@dimen/common_dp_0"
            android:layout_marginStart="@dimen/dp_44"
            android:layout_marginEnd="@dimen/common_dp_25"
            android:drawableStart="@drawable/ic_position_preview_contract_phone"
            android:drawablePadding="@dimen/common_dp_5"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{@string/position_preview_contract_phone_format(viewModel.jobDetails.phone)}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_contracts"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_contracts" />

          <!--          <TextView-->
          <!--            android:id="@+id/tv_contract_QQ"-->
          <!--            style="@style/common_Text.15sp.333333"-->
          <!--            android:layout_marginTop="@dimen/common_dp_16"-->
          <!--            android:drawableStart="@drawable/ic_position_preview_contract_qq"-->
          <!--            android:drawablePadding="@dimen/common_dp_5"-->
          <!--            app:layout_constraintBottom_toBottomOf="parent"-->
          <!--            app:layout_constraintStart_toStartOf="@id/tv_contracts"-->
          <!--            app:layout_constraintTop_toBottomOf="@id/tv_contracts" />-->
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
          style="@style/common_Line.Horizontal"
          android:layout_height="@dimen/common_dp_8" />

        <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_wrap">

          <TextView
            android:id="@+id/tv_working_place"
            style="@style/Text.PositionPreviewItemTitle"
            android:text="@string/position_preview_work_place"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

          <FrameLayout
            android:id="@+id/fl_company_location"
            android:layout_width="@dimen/common_dp_0"
            android:layout_height="@dimen/common_dp_0"
            android:layout_marginTop="12dp"
            android:background="@drawable/img_map_placeholder"
            app:layout_constraintDimensionRatio="25:9"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_working_place">

            <com.baidu.mapapi.map.MapView
              android:id="@+id/map_address"
              style="@style/match_match" />

            <TextView
              android:id="@+id/tv_map_address"
              style="@style/common_wrap_wrap"
              android:layout_gravity="center"
              android:background="@drawable/bg_ffffff"
              android:ellipsize="end"
              android:lineSpacingExtra="@dimen/dp_3"
              android:maxWidth="255dp"
              android:maxLines="2"
              android:paddingStart="@dimen/common_dp_12"
              android:paddingTop="@dimen/common_dp_6"
              android:paddingEnd="@dimen/common_dp_12"
              android:paddingBottom="@dimen/common_dp_6"
              android:text="@{viewModel.jobDetails.com.address}"
              android:textColor="@color/cl_333333"
              android:textSize="@dimen/sp_14" />

          </FrameLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
          style="@style/common_Text.15sp.888888"
          android:drawableStart="@drawable/ic_post_job_tips"
          android:drawablePadding="@dimen/common_dp_16"
          android:gravity="center_vertical"
          android:lineSpacingExtra="@dimen/dp_3"
          android:paddingStart="@dimen/common_dp_12"
          android:paddingTop="@dimen/common_dp_16"
          android:paddingEnd="@dimen/common_dp_12"
          android:paddingBottom="@dimen/common_dp_16"
          android:text="@string/position_preview_tips" />

        <View
          style="@style/Line.Horizontal.Light"
          android:layout_height="@dimen/dp_8" />

        <FrameLayout
          android:layout_width="match_parent"
          android:layout_height="245dp">

          <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_add_video_placeholder"
            style="@style/match_match">

            <TextView
              android:id="@+id/tv_my_video"
              style="@style/Text.16sp.333333.Bold"
              android:layout_marginStart="@dimen/dp_20"
              android:layout_marginTop="@dimen/dp_16"
              android:text="@string/position_preview_video"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toTopOf="parent" />

            <View
              android:id="@+id/v_split"
              style="@style/Line.Horizontal.Margin12OfStartAndEnd"
              android:layout_marginTop="@dimen/dp_16"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/tv_my_video" />

            <TextView
              android:id="@+id/tv_add_video"
              style="@style/common_Button.Basic"
              android:layout_marginStart="@dimen/dp_77"
              android:layout_marginEnd="@dimen/dp_77"
              android:text="@string/position_preview_add_video"
              app:layout_constraintBottom_toTopOf="@id/tv_add_video_tips"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/v_split"
              app:layout_constraintVertical_chainStyle="packed" />

            <TextView
              android:id="@+id/tv_add_video_tips"
              style="@style/Text.14sp.888888"
              android:layout_marginTop="@dimen/dp_12"
              android:text="@string/position_preview_add_video_tips"
              app:layout_constraintBottom_toBottomOf="parent"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/tv_add_video" />

          </androidx.constraintlayout.widget.ConstraintLayout>

          <com.youth.banner.Banner
            android:id="@+id/vp_job_link_videos"
            android:layout_width="match_parent"
            android:layout_height="245dp"
            app:banner_auto_loop="false"
            app:banner_indicator_normal_color="@color/common_white"
            app:banner_indicator_selected_color="@color/common_49C280" />
        </FrameLayout>

      </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <View style="@style/common_Line.Horizontal" />

    <LinearLayout
      android:id="@+id/ll_bottom_options_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_52"
      android:background="@color/common_fbfbfb"
      android:gravity="center_vertical"
      android:paddingStart="@dimen/dp_14"
      android:paddingEnd="@dimen/dp_14"
      android:visibility="gone">

      <TextView
        android:id="@+id/tv_online_or_offline"
        style="@style/common_Text.16sp.FF7647"
        android:layout_width="@dimen/common_dp_0"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_4"
        android:layout_weight="1"
        android:background="@drawable/frame_primary_radius_4"
        android:gravity="center"
        android:onClick="@{()->viewModel.showSwitchJobStateTipsDialog()}"
        android:text="@{viewModel.jobOptionText}" />

      <TextView
        android:id="@+id/tv_edit"
        style="@style/common_Text.16sp.FFFFFF"
        android:layout_width="@dimen/common_dp_0"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginBottom="@dimen/dp_4"
        android:layout_weight="1"
        android:background="@drawable/bg_ff7405_radius_4"
        android:gravity="center"
        android:onClick="@{()->viewModel.toEditJob()}"
        android:text="@string/position_preview_update" />
    </LinearLayout>

  </LinearLayout>
</layout>