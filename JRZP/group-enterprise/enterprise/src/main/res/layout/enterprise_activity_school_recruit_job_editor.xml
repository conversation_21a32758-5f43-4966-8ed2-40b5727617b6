<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.schoolrecruitjob.editor.SchoolRecruitJobEditorViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:right_text="@string/school_recruit_job_editor_delete"
      app:showRight="@{viewModel.isEditMode()}"
      app:show_right_text_bg="false"
      app:title="@string/school_recruit_job_editor_title" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/YUI.CommonListItemView"
      app:yui_content="@={viewModel.jobInfo.jobName}"
      app:yui_content_hint="@string/please_enter"
      app:yui_content_type="edit"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/school_recruit_job_editor_name" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/YUI.CommonListItemView"
      android:onClick="@{()->viewModel.showCityPicker()}"
      app:yui_accessory_type="chevron"
      app:yui_content="@{viewModel.jobInfo.cityName}"
      app:yui_content_hint="@string/common_please_select"
      app:yui_content_type="text"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/school_recruit_job_editor_city" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/YUI.CommonListItemView"
      android:inputType="textEmailAddress"
      app:yui_content="@={viewModel.jobInfo.email}"
      app:yui_content_hint="@string/please_enter"
      app:yui_content_type="edit"
      app:yui_icon="@drawable/common_ic_required"
      app:yui_title="@string/school_recruit_job_editor_email" />

    <androidx.legacy.widget.Space
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <TextView
      style="@style/Button.Basic.Round"
      android:layout_margin="@dimen/dp_12"
      android:onClick="@{()->viewModel.save()}"
      android:text="@string/save" />

  </LinearLayout>
</layout>