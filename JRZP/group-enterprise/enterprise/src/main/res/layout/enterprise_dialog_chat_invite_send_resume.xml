<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_ffffff_radius_4"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_close"
        style="@style/common_wrap_wrap"
        android:layout_gravity="end"
        android:src="@drawable/common_ic_close" />

    <TextView
        style="@style/common_Text.18sp.333333.Bold"
        android:text="@string/invite_send_dialog_title" />

    <TextView
        style="@style/Text.14sp.333333"
        android:layout_marginTop="@dimen/common_dp_8"
        android:text="@string/invite_send_dialog_tips" />

    <LinearLayout
        style="@style/match_wrap"
        android:layout_marginStart="@dimen/common_dp_8"
        android:layout_marginTop="@dimen/common_dp_20"
        android:layout_marginEnd="@dimen/common_dp_8"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_job"
            style="@style/common_Text.12sp.888888"
            android:layout_width="@dimen/common_dp_0"
            android:layout_marginStart="@dimen/common_dp_8"
            android:layout_weight="1"
            android:drawableStart="@drawable/ic_job"
            android:drawablePadding="@dimen/common_dp_4"
            android:ellipsize="end"
            android:gravity="start"
            android:lines="1"
            android:paddingTop="@dimen/common_dp_8"
            android:paddingBottom="@dimen/common_dp_8" />

        <TextView
            android:id="@+id/tv_location"
            style="@style/common_Text.12sp.888888"
            android:layout_width="@dimen/common_dp_0"
            android:layout_marginStart="@dimen/common_dp_8"
            android:layout_weight="1"
            android:drawableStart="@drawable/ic_location"
            android:drawablePadding="@dimen/common_dp_4"
            android:ellipsize="end"
            android:gravity="start"
            android:lines="1"
            android:paddingTop="@dimen/common_dp_8"
            android:paddingBottom="@dimen/common_dp_8" />

        <TextView
            android:id="@+id/tv_salary"
            style="@style/common_Text.12sp.888888"
            android:layout_width="@dimen/common_dp_0"
            android:layout_marginStart="@dimen/common_dp_8"
            android:layout_marginEnd="@dimen/common_dp_8"
            android:layout_weight="1"
            android:drawableStart="@drawable/ic_salary"
            android:drawablePadding="@dimen/common_dp_4"
            android:ellipsize="end"
            android:gravity="start"
            android:lines="1"
            android:paddingTop="@dimen/common_dp_8"
            android:paddingBottom="@dimen/common_dp_8" />
    </LinearLayout>

    <View
        style="@style/common_Line.Horizontal"
        android:layout_marginTop="@dimen/common_dp_25" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_select_other"
            style="@style/Text.14sp.333333"
            android:layout_width="@dimen/common_dp_0"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/invite_send_dialog_select_other" />

        <TextView
            android:id="@+id/tv_confirm"
            style="@style/common_Text.14sp.FFFFFF"
            android:layout_width="@dimen/common_dp_0"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/bg_dialog_confirm"
            android:gravity="center"
            android:text="@string/invite_send_dialog_send" />
    </LinearLayout>

</LinearLayout>