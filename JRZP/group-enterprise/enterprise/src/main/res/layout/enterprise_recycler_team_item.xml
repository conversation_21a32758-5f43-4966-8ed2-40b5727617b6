<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:paddingTop="@dimen/common_dp_16"
    android:paddingBottom="@dimen/common_dp_12">

    <ImageView
        android:id="@+id/iv_header"
        android:layout_width="@dimen/dp_42"
        android:layout_height="@dimen/dp_42"
        android:layout_marginStart="@dimen/common_dp_12"
        android:layout_marginLeft="@dimen/common_dp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_name"
        style="@style/common_Text.16sp.333333"
        android:layout_width="@dimen/common_dp_0"
        android:layout_marginStart="@dimen/common_dp_10"
        android:layout_marginLeft="@dimen/common_dp_10"
        android:layout_marginEnd="@dimen/common_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_header"
        app:layout_constraintTop_toTopOf="@id/iv_header" />

    <TextView
        android:id="@+id/tv_introduction"
        style="@style/common_Text.12sp.767676"
        android:layout_width="@dimen/common_dp_0"
        android:layout_marginEnd="@dimen/common_dp_12"
        android:ellipsize="end"
        android:maxLines="2"
        app:layout_constraintBottom_toTopOf="@id/v_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <View
        android:id="@+id/v_line"
        style="@style/common_Line.Horizontal.Margin12OfStartAndEnd"
        android:layout_marginTop="@dimen/common_dp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_header" />

    <TextView
        android:id="@+id/tv_delete"
        android:layout_width="@dimen/common_dp_60"
        android:layout_height="@dimen/common_dp_28"
        android:layout_marginEnd="@dimen/common_dp_10"
        android:background="@drawable/common_frame_e8e8e8"
        android:gravity="center"
        android:text="@string/common_delete"
        app:layout_constraintBottom_toBottomOf="@id/tv_edit"
        app:layout_constraintEnd_toStartOf="@id/tv_edit" />

    <TextView
        android:id="@+id/tv_edit"
        android:layout_width="@dimen/common_dp_60"
        android:layout_height="@dimen/common_dp_28"
        android:layout_marginTop="@dimen/common_dp_12"
        android:layout_marginEnd="@dimen/common_dp_12"
        android:background="@drawable/common_frame_e8e8e8"
        android:gravity="center"
        android:text="@string/common_edit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_line" />
</androidx.constraintlayout.widget.ConstraintLayout>