<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text.18sp.333333.Bold"
            android:layout_marginTop="@dimen/dp_18"
            android:text="@string/enterprise_my_job_list_release_menu_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/tv_full_time"
            android:layout_width="90dp"
            android:layout_height="100dp"
            android:layout_marginBottom="@dimen/dp_30"
            android:layout_marginTop="@dimen/dp_40"
            android:src="@drawable/enterprise_ic_post_job_full_time"
            app:layout_constraintBottom_toTopOf="@id/iv_close"
            app:layout_constraintEnd_toStartOf="@id/tv_depart_time"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <ImageView
            android:id="@+id/tv_depart_time"
            android:layout_width="90dp"
            android:layout_height="100dp"
            android:layout_marginStart="@dimen/dp_14"
            android:src="@drawable/enterprise_ic_post_job_menu_depart_time"
            app:layout_constraintBottom_toBottomOf="@id/tv_full_time"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tv_full_time" />

        <ImageView
            android:id="@+id/iv_close"
            style="@style/wrap_wrap"
            android:layout_marginBottom="@dimen/dp_30"
            android:src="@drawable/common_ic_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>