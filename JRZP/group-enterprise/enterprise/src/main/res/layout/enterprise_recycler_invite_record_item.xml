<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="com.bxkj.ecommon.util.MTextUtils" />

        <variable
            name="inviteRecordItem"
            type="com.bxkj.enterprise.data.InviteRecordItemData" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical"
        style="@style/match_wrap">

        <TextView
            style="@style/common_Text.15sp.333333"
            android:layout_marginStart="@dimen/common_dp_12"
            android:layout_marginTop="@dimen/common_dp_16"
            android:lineSpacingExtra="@dimen/dp_3"
            android:layout_marginEnd="@dimen/common_dp_12"
            android:layout_marginBottom="@dimen/common_dp_16"
            android:text="@{MTextUtils.fromHtml(@string/invite_record_item_text_format(inviteRecordItem.des,inviteRecordItem.integral))}" />

        <View
            style="@style/common_Line.Horizontal"
            android:layout_marginStart="@dimen/common_dp_12"
            android:layout_marginEnd="@dimen/common_dp_12" />

    </LinearLayout>
</layout>