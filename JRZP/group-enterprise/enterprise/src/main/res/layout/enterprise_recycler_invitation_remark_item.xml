<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/common_dp_52">

    <TextView
        android:id="@+id/tv_item"
        style="@style/common_Text.15sp.333333"
        android:layout_marginStart="@dimen/common_dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_checked"
        style="@style/common_wrap_wrap"
        android:layout_marginEnd="@dimen/common_dp_12"
        android:src="@drawable/common_bg_checkbox_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        style="@style/common_Line.Horizontal.Margin12OfStartAndEnd"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>