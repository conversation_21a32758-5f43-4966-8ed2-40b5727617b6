<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="data"
      type="com.bxkj.enterprise.data.SchoolRecruitResumeData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:paddingStart="@dimen/common_dp_12"
    android:paddingTop="@dimen/common_dp_16"
    android:paddingEnd="@dimen/common_dp_12"
    android:paddingBottom="@dimen/common_dp_16">

    <com.google.android.material.imageview.ShapeableImageView
      android:id="@+id/iv_header"
      style="@style/roundedCornerImageStyle"
      android:layout_width="@dimen/dp_42"
      android:layout_height="@dimen/dp_42"
      android:scaleType="centerCrop"
      bind:imgUrl="@{data.avatar}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/common_Text.16sp.333333"
      android:layout_marginStart="@dimen/common_dp_10"
      android:text="@{data.name}"
      app:layout_constraintStart_toEndOf="@id/iv_header"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_about"
      style="@style/common_Text.12sp.888888"
      android:layout_marginTop="@dimen/common_dp_5"
      android:text="@{data.resumeAbout}"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_position"
      style="@style/common_Text.12sp.888888"
      android:layout_marginTop="@dimen/common_dp_5"
      android:text="@{@string/school_recruit_resume_job_format(data.expectJobName)}"
      app:layout_constraintStart_toStartOf="@id/tv_about"
      app:layout_constraintTop_toBottomOf="@id/tv_about" />

    <TextView
      android:id="@+id/tv_time"
      style="@style/common_Text.12sp.888888"
      android:text="@{data.updateTime}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>