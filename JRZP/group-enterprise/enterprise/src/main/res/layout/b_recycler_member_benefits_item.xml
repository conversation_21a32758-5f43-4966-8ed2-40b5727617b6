<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:bind="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.enterprise.data.BenefitsItem" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp_8"
    android:paddingBottom="@dimen/dp_8">

    <ImageView
      android:layout_width="@dimen/dp_50"
      android:layout_height="@dimen/dp_50"
      bind:imgUrl="@{data.img}" />

    <TextView
      style="@style/Text.12sp.333333"
      android:layout_marginTop="@dimen/dp_8"
      android:text="@{data.name}" />

    <TextView
      style="@style/Text.12sp.FF7647"
      android:text="@{data.count}" />
  </LinearLayout>
</layout>