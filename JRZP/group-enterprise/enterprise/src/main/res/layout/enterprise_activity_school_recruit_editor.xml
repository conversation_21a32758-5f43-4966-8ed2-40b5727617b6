<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.enterprise.ui.activity.schoolrecruiteditor.SchoolRecruitEditorViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_match"
        android:clickable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            android:id="@+id/title_bar"
            style="@style/match_wrap"
            app:right_text="@string/save"
            app:show_right_text_bg="false"
            app:title="@{viewModel.isAddMode?@string/school_recruit_editor_add_title:@string/school_recruit_editor_update_title}" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
            style="@style/YUI.CommonListItemView"
            app:yui_content="@={viewModel.schoolRecruitInfo.title}"
            app:yui_content_hint="@string/please_enter"
            app:yui_content_type="edit"
            app:yui_icon="@drawable/common_ic_required"
            app:yui_title="@string/school_recruit_editor_title" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
            android:id="@+id/info_expiration"
            style="@style/YUI.CommonListItemView"
            android:onClick="@{()->viewModel.showExpirationPicker()}"
            app:yui_accessory_type="chevron"
            app:yui_content="@{viewModel.schoolRecruitInfo.jsdate}"
            app:yui_content_hint="@string/common_please_select"
            app:yui_content_type="text"
            app:yui_icon="@drawable/common_ic_required"
            app:yui_title="@string/school_recruit_editor_expiration" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
            style="@style/YUI.CommonListItemView"
            android:onClick="@{()->viewModel.toEditSchoolRecruitJob()}"
            app:yui_accessory_type="chevron"
            app:yui_content="@{viewModel.schoolRecruitInfo.jobsText}"
            app:yui_content_hint="@string/common_please_select"
            app:yui_content_type="text"
            app:yui_icon="@drawable/common_ic_required"
            app:yui_title="@string/school_recruit_editor_job" />

        <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
            style="@style/YUI.CommonListItemView"
            android:onClick="@{()->viewModel.toEditContent()}"
            app:yui_accessory_type="chevron"
            app:yui_content="@{viewModel.schoolRecruitInfo.convertHtmlText}"
            app:yui_content_hint="@string/please_enter"
            app:yui_content_type="text"
            app:yui_icon="@drawable/common_ic_required"
            app:yui_title="@string/school_recruit_editor_details" />

    </LinearLayout>
</layout>