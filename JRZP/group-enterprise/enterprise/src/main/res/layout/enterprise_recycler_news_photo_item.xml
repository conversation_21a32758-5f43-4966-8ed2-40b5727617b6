<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.enterprise.ui.activity.conversation.EMomentFileItem" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="109dp"
        android:layout_height="109dp"
        android:background="@drawable/bg_f4f4f4"
        android:orientation="vertical"
        app:layout_constraintDimensionRatio="w,1:1">

        <ImageView
            android:id="@+id/iv_photo"
            style="@style/match_match"
            android:scaleType="centerCrop"
            android:visibility="@{data.isAddItem?View.GONE:View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_delete"
            style="@style/common_wrap_wrap"
            android:src="@drawable/ic_delete_photo"
            android:visibility="@{data.isAddItem?View.GONE:View.VISIBLE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            style="@style/common_wrap_wrap"
            android:src="@drawable/ic_news_photo_add"
            android:visibility="@{data.isAddItem?View.VISIBLE:View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>