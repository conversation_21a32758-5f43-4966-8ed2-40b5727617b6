<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/common_dp_52">

    <View
        style="@style/common_Line.Horizontal"
        android:layout_marginStart="@dimen/common_dp_20"
        android:layout_marginEnd="@dimen/common_dp_20"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/tv_search_record_item"
        style="@style/common_Text.14sp.888888"
        android:layout_width="0dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/common_dp_20"
        android:layout_weight="1"
        android:drawablePadding="@dimen/common_dp_10"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_delete_record"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_delete_record"
        style="@style/common_wrap_wrap"
        android:layout_marginEnd="@dimen/dp_18"
        android:src="@drawable/ic_delete_resume_receive_mailbox"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>


