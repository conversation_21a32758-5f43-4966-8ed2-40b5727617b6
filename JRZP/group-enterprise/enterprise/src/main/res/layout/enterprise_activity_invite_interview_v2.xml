<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.invitationinterview.InviteInterviewViewModelV2" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_f4f4f4"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_42"
      android:background="@drawable/bg_ff7405"
      app:left_img="@drawable/common_ic_white_back" />

    <androidx.core.widget.NestedScrollView
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <LinearLayout
          style="@style/match_wrap"
          android:background="@drawable/enterprise_bg_invite_interview_top"
          android:orientation="vertical"
          android:paddingStart="@dimen/dp_16"
          android:paddingEnd="@dimen/dp_16">

          <TextView
            style="@style/Text.24sp.FFFFFF"
            android:layout_marginTop="@dimen/dp_18"
            android:text="@{@string/enterprise_invite_interview_user_say_hello_text_format(viewModel.inviteUserName)}" />

          <LinearLayout
            style="@style/match_wrap"
            android:layout_marginTop="@dimen/dp_18"
            android:background="@drawable/bg_ffffff_radius_10"
            android:orientation="vertical">

            <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
              style="@style/JrzpCommonListItemView"
              android:onClick="@{()->viewModel.showJobListDialog()}"
              app:yui_accessory_type="chevron"
              app:yui_content="@{viewModel.interviewInfo.relName}"
              app:yui_content_type="text"
              app:yui_title="@string/enterprise_invite_interview_position" />

            <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
              style="@style/JrzpCommonListItemView"
              android:onClick="@{()->viewModel.toEditInterviewAddress()}"
              app:yui_accessory_type="chevron"
              app:yui_content="@{viewModel.interviewAddress.cityAreaAddressText}"
              app:yui_content_type="text"
              app:yui_title="@string/enterprise_invite_interview_address" />

            <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
              style="@style/JrzpCommonListItemView"
              android:onClick="@{()->viewModel.showHRListDialog()}"
              app:yui_accessory_type="chevron"
              app:yui_border_bg="@null"
              app:yui_content="@{viewModel.interviewInfo.contactInfoText}"
              app:yui_content_type="text"
              app:yui_title="@string/enterprise_invite_interview_contact" />

          </LinearLayout>

        </LinearLayout>

        <LinearLayout
          style="@style/match_wrap"
          android:layout_marginStart="@dimen/dp_16"
          android:layout_marginTop="@dimen/dp_8"
          android:layout_marginEnd="@dimen/dp_16"
          android:background="@drawable/bg_ffffff_radius_10"
          android:orientation="vertical">

          <LinearLayout
            style="@style/match_wrap"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/dp_14"
            android:paddingEnd="@dimen/dp_14">

            <TextView
              style="@style/Text.14sp.333333.Bold"
              android:layout_marginTop="@dimen/dp_18"
              android:layout_marginBottom="@dimen/dp_18"
              android:layout_weight="1"
              android:text="@string/enterprise_invite_interview_date_title" />

            <TextView
              style="@style/Text.14sp.333333"
              android:text="@{viewModel.interviewDate}" />
          </LinearLayout>

          <com.bxkj.common.widget.zpcalenderview.ZPCalenderView
            android:id="@+id/calender_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

          <View
            style="@style/Line.Horizontal.Light"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_14" />

          <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
            android:id="@+id/item_interview_time"
            style="@style/JrzpCommonListItemView"
            android:onClick="@{onClickListener}"
            app:yui_accessory_type="chevron"
            app:yui_border_bg="@null"
            app:yui_content="@{viewModel.interviewTime}"
            app:yui_content_hint="@string/enterprise_invite_interview_date_hint"
            app:yui_content_type="text"
            app:yui_title="@string/enterprise_invite_interview_date" />
        </LinearLayout>

        <LinearLayout
          style="@style/match_wrap"
          android:layout_marginStart="@dimen/dp_16"
          android:layout_marginTop="@dimen/dp_8"
          android:layout_marginEnd="@dimen/dp_16"
          android:layout_marginBottom="@dimen/dp_16"
          android:background="@drawable/bg_ffffff_radius_10"
          android:orientation="vertical">

          <TextView
            style="@style/Text.14sp.333333.Bold"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginTop="@dimen/dp_18"
            android:layout_marginEnd="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_18"
            android:text="@string/enterprise_invite_interview_remark" />

          <LinearLayout
            style="@style/match_wrap"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_14"
            android:background="@drawable/bg_f4f4f4_radius_10"
            android:orientation="vertical"
            android:padding="@dimen/dp_14">

            <EditText
              style="@style/Text.14sp.333333"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:background="@null"
              android:gravity="start"
              android:hint="@string/enterprise_invite_interview_remark_hint"
              android:lines="6"
              android:maxLength="140"
              android:text="@={viewModel.interviewInfo.otherRemark}" />

            <LinearLayout
              style="@style/match_wrap"
              android:gravity="center_vertical"
              android:orientation="horizontal">

              <TextView
                android:onClick="@{()->viewModel.clearRemark()}"
                style="@style/Text.14sp.999999"
                android:drawableStart="@drawable/common_ic_delete"
                android:drawablePadding="@dimen/dp_4"
                android:text="@string/enterprise_invite_interview_remark_clear" />

              <androidx.legacy.widget.Space
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/common_dp_0"
                android:layout_weight="1" />

              <TextView
                style="@style/Text.14sp.999999"
                android:text="@{@string/enterprise_invite_interview_remark_length_format(viewModel.interviewInfo.otherRemark.length(),viewModel.remarkMaxLength)}"
                android:visibility="visible" />

            </LinearLayout>
          </LinearLayout>

        </LinearLayout>

      </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
      android:id="@+id/tv_conversation"
      style="@style/Button.Basic.Round"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_10"
      android:onClick="@{()->viewModel.sendRequest()}" />

  </LinearLayout>
</layout>