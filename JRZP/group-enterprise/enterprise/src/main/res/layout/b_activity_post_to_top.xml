<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.positiontop.PostToTopViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44"
      app:title="@string/position_top_title" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      android:id="@+id/clv_topped_city"
      style="@style/JrzpCommonListItemView"
      android:visibility="gone"
      app:yui_accessory_type="none"
      app:yui_content_hint="@string/common_please_select"
      app:yui_content_type="text"
      app:yui_title="@string/position_topped_area" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      android:id="@+id/clv_top_city"
      style="@style/JrzpCommonListItemView"
      android:onClick="@{onClickListener}"
      app:yui_accessory_type="chevron"
      app:yui_content="@{viewModel.postToTopCity.name}"
      app:yui_content_hint="@string/common_please_select"
      app:yui_content_type="text"
      app:yui_title="@string/enterprise_promotion_area" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      android:id="@+id/clv_top_days"
      style="@style/JrzpCommonListItemView"
      android:onClick="@{onClickListener}"
      app:yui_accessory_type="chevron"
      app:yui_content="@{HtmlUtils.fromHtml(viewModel.selectedPostToTopCost.itemOption)}"
      app:yui_content_hint="@string/common_please_select"
      app:yui_content_type="text"
      app:yui_title="@string/enterprise_position_top_days" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      android:id="@+id/clv_keywords"
      style="@style/JrzpCommonListItemView"
      android:onClick="@{()->viewModel.toAddKeywords()}"
      app:yui_accessory_type="chevron"
      app:yui_content="@{viewModel.postToTopKeywords}"
      app:yui_content_hint="@string/please_enter"
      app:yui_content_type="text"
      app:yui_title="@string/position_top_keyword_text" />

    <TextView
      style="@style/common_Text.FromHeader"
      android:text="@string/enterprise_refresh_pay_method" />

    <LinearLayout
      android:id="@+id/ll_pay_by_days"
      style="@style/common_Layout.SelectItemGroup"
      android:onClick="@{onClickListener}">

      <TextView
        android:id="@+id/tv_top_day"
        style="@style/Text.14sp"
        android:text="@string/position_top_for_days"
        android:textColor="@color/common_333333_to_ff4100_selector" />

      <TextView
        android:id="@+id/tv_top_days_balance"
        style="@style/Text.12sp.888888"
        android:layout_weight="1"
        android:text="@{HtmlUtils.fromHtml(@string/position_top_day_balance_format(viewModel.postToTopDaysBalance))}" />

      <ImageView
        android:id="@+id/iv_pay_by_days"
        style="@style/wrap_wrap"
        android:src="@drawable/enterprise_ic_item_selected" />
    </LinearLayout>

    <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

    <LinearLayout
      android:id="@+id/ll_pay_by_bean"
      style="@style/common_Layout.SelectItemGroup"
      android:onClick="@{onClickListener}">

      <TextView
        android:id="@+id/tv_top_integral"
        style="@style/common_Text.14sp"
        android:text="@string/enterprise_position_top_unit"
        android:textColor="@color/common_333333_to_ff4100_selector" />

      <TextView
        android:id="@+id/tv_top_integral_count"
        style="@style/common_Text.12sp.888888"
        android:layout_weight="1"
        android:text="@{HtmlUtils.fromHtml(@string/position_refresh_count(viewModel.beanBalance))}" />

      <ImageView
        android:id="@+id/iv_pay_by_bean"
        style="@style/common_wrap_wrap"
        android:src="@drawable/enterprise_ic_item_selected"
        android:visibility="gone" />
    </LinearLayout>

    <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

    <Space
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_0"
      android:layout_weight="1" />

    <View style="@style/Line.Horizontal.Light" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_48"
      android:gravity="center_vertical"
      android:paddingStart="@dimen/common_dp_12">

      <TextView
        android:id="@+id/tv_top_consumption"
        style="@style/common_Text.14sp.000000"
        android:layout_weight="1"
        android:text="@{HtmlUtils.fromHtml(viewModel.postToTopCostText)}" />

      <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="@dimen/common_dp_120"
        android:layout_height="match_parent"
        android:background="@drawable/enterprise_shape_ffbb96_to_ff865d"
        android:gravity="center"
        android:onClick="@{()->viewModel.addPostTop()}"
        android:text="@string/common_confirm"
        android:textColor="@color/common_white"
        android:textSize="@dimen/common_sp_16" />
    </LinearLayout>
  </LinearLayout>
</layout>