<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.customservice.CustomServiceViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_f4f4f4"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44" />

    <TextView
      style="@style/Text.18sp.333333"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_8"
      android:background="@drawable/bg_ffffff_radius_10"
      android:drawableStart="@drawable/enterprise_ic_bean_mall_function"
      android:drawablePadding="@dimen/dp_8"
      android:paddingStart="@dimen/dp_12"
      android:paddingTop="@dimen/dp_6"
      android:paddingEnd="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_6"
      android:text="@string/b_custom_service" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_margin="@dimen/dp_8"
      android:background="@drawable/bg_ffffff_radius_10"
      android:padding="@dimen/dp_16">

      <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_52"
        android:layout_height="@dimen/dp_52"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/roundedCornerImageStyle.Avatar"
        bind:imgUrl="@{viewModel.servicesInfo.avatar}" />

      <TextView
        android:id="@+id/tv_name"
        style="@style/Text.16sp.333333"
        android:layout_width="0dp"
        android:layout_marginStart="@dimen/dp_8"
        android:text="@{viewModel.servicesInfo.kefuName}"
        app:layout_constraintBottom_toTopOf="@id/tv_exp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

      <TextView
        android:id="@+id/tv_exp"
        style="@style/Text.14sp.333333"
        android:layout_width="@dimen/dp_0"
        android:text="@{viewModel.servicesInfo.time}"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

      <FrameLayout
        android:id="@+id/fl_phone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_16"
        android:background="@drawable/bg_f4f4f4_radius_10"
        android:onClick="@{()->viewModel.callServicesPhone()}"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_24"
        android:paddingTop="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_24"
        android:paddingBottom="@dimen/dp_8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar">

        <ImageView
          android:id="@+id/iv_phone"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:src="@drawable/b_ic_custom_service_phone" />

        <TextView
          android:id="@+id/tv_phone"
          style="@style/Text.16sp.333333"
          android:layout_gravity="center"
          android:text="@{viewModel.servicesInfo.kefuTel}" />

      </FrameLayout>

      <FrameLayout
        android:id="@+id/fl_wechat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_14"
        android:background="@drawable/bg_f4f4f4_radius_10"
        android:onClick="@{()->viewModel.jumpToWechat()}"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_24"
        android:paddingTop="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_24"
        android:paddingBottom="@dimen/dp_8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fl_phone">

        <ImageView
          android:id="@+id/iv_wechat"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:src="@drawable/b_ic_custom_service_wechat" />

        <TextView
          android:id="@+id/tv_wechat"
          style="@style/Text.16sp.333333"
          android:layout_gravity="center"
          android:text="@string/b_custom_service_wechat" />

      </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
  </LinearLayout>


</layout>
