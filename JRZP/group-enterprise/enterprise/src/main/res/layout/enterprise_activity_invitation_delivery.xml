<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/enterprise_include_theme_title_bar" />

    <androidx.core.widget.NestedScrollView style="@style/match_wrap">

        <LinearLayout
            style="@style/match_match"
            android:orientation="vertical">

            <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/select_position" />

                <TextView
                    android:id="@+id/tv_position"
                    style="@style/common_Text.InfoItem.Select" />
            </LinearLayout>

            <View
                android:id="@+id/v_line"
                style="@style/common_Line.Horizontal" />

            <TextView
                style="@style/common_Text.12sp.888888"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_dp_36"
                android:background="@drawable/bg_f4f4f4"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/common_dp_14"
                android:text="@string/invitation_delivery_setting_greeting" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_greeting"
                style="@style/match_wrap" />

            <TextView
                style="@style/common_Text.12sp.888888"
                android:layout_marginEnd="@dimen/common_dp_12"
                android:layout_marginStart="@dimen/common_dp_12"
                android:layout_marginTop="@dimen/common_dp_10"
                android:text="@string/invitation_delivery_tips" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</LinearLayout>