<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.CheckUtils" />

    <variable
      name="data"
      type="com.bxkj.jrzp.support.chat.data.ChatMsgItemData" />
  </data>

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginBottom="@dimen/dp_8"
    android:orientation="vertical">

    <include layout="@layout/enterprise_layout_conversation_time_tag" />

    <LinearLayout
      style="@style/match_wrap"
      android:background="@drawable/bg_ffffff_radius_4"
      android:orientation="vertical"
      android:padding="@dimen/common_dp_14">

      <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap">

        <de.hdodenhof.circleimageview.CircleImageView
          android:id="@+id/iv_sender_avatar"
          android:layout_width="@dimen/conversation_msg_avatar_size"
          android:layout_height="@dimen/conversation_msg_avatar_size"
          bind:imgUrl="@{data.resInfo.photo}"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent" />

        <TextView
          android:id="@+id/tv_name"
          style="@style/common_Text.16sp.333333.Bold"
          android:layout_marginStart="@dimen/common_dp_8"
          android:layout_marginEnd="@dimen/common_dp_12"
          android:ellipsize="end"
          android:lines="1"
          android:text="@{data.resInfo.name}"
          app:layout_constrainedWidth="true"
          app:layout_constraintEnd_toStartOf="@id/tv_salary"
          app:layout_constraintHorizontal_bias="0"
          app:layout_constraintStart_toEndOf="@id/iv_sender_avatar"
          app:layout_constraintTop_toTopOf="parent" />

        <TextView
          style="@style/Text.14sp.333333"
          android:layout_marginStart="@dimen/common_dp_8"
          android:text="@{data.resInfo.resumeAbout}"
          app:layout_constraintBottom_toBottomOf="@id/iv_sender_avatar"
          app:layout_constraintStart_toEndOf="@id/iv_sender_avatar"
          app:layout_constraintTop_toBottomOf="@id/tv_name" />

        <TextView
          android:id="@+id/tv_salary"
          style="@style/common_Text.14sp.49C280"
          android:textStyle="bold"
          app:layout_constraintBottom_toBottomOf="@id/tv_name"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintTop_toTopOf="@id/tv_name" />

      </androidx.constraintlayout.widget.ConstraintLayout>

      <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/match_wrap"
        android:layout_marginTop="@dimen/common_dp_12"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:paddingStart="@dimen/common_dp_12"
        android:paddingTop="@dimen/common_dp_8"
        android:paddingEnd="@dimen/common_dp_12"
        android:paddingBottom="@dimen/common_dp_8"
        android:visibility="@{CheckUtils.isNullOrEmpty(data.resInfo.expCompanyName)?View.GONE:View.VISIBLE}">

        <TextView
          android:id="@+id/tv_com_name"
          style="@style/common_Text.12sp.333333"
          android:layout_width="@dimen/common_dp_0"
          android:layout_marginEnd="@dimen/common_dp_16"
          android:text="@{data.resInfo.expCompanyName}"
          android:textStyle="bold"
          app:layout_constraintEnd_toStartOf="@id/tv_work_time"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent" />

        <TextView
          android:id="@+id/tv_work_time"
          style="@style/common_Text.10sp.888888"
          android:text="@{data.resInfo.workTime}"
          app:layout_constraintBottom_toBottomOf="@id/tv_com_name"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintTop_toTopOf="@id/tv_com_name" />

        <TextView
          style="@style/common_Text.12sp.333333"
          android:text="@{data.resInfo.expJob}"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintHorizontal_bias="0"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@id/tv_com_name" />
      </androidx.constraintlayout.widget.ConstraintLayout>


      <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/match_wrap"
        android:paddingTop="@dimen/common_dp_8"
        android:paddingBottom="@dimen/common_dp_8"
        android:visibility="@{CheckUtils.isNullOrEmpty(data.resInfo.eduSchool)?View.GONE:View.VISIBLE}">

        <TextView
          android:id="@+id/tv_school_name"
          style="@style/common_Text.12sp.333333"
          android:layout_marginEnd="@dimen/common_dp_16"
          android:text="@{data.resInfo.eduSchool}"
          android:textStyle="bold"
          app:layout_constraintEnd_toStartOf="@id/tv_edu_time"
          app:layout_constraintHorizontal_bias="0"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent" />

        <TextView
          android:id="@+id/tv_edu_time"
          style="@style/common_Text.10sp.888888"
          android:text="@{data.resInfo.eduTime}"
          app:layout_constraintBottom_toBottomOf="@id/tv_school_name"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintTop_toTopOf="@id/tv_school_name" />

        <TextView
          style="@style/common_Text.12sp.333333"
          android:text="@{data.resInfo.eduAbout}"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintHorizontal_bias="0"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@id/tv_school_name" />
      </androidx.constraintlayout.widget.ConstraintLayout>

      <View
        style="@style/common_Line.Horizontal"
        android:layout_marginTop="@dimen/common_dp_4" />

      <TextView
        style="@style/common_Text.14sp.888888"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/common_dp_14"
        android:text="@string/chat_view_resume_details" />
    </LinearLayout>

  </LinearLayout>
</layout>