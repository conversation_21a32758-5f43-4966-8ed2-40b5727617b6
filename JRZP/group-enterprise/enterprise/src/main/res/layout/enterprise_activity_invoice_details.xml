<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <include layout="@layout/enterprise_include_theme_title_bar" />

    <androidx.core.widget.NestedScrollView style="@style/match_wrap">

        <LinearLayout
            style="@style/match_wrap"
            android:orientation="vertical">

            <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/invoice_status" />

                <TextView
                    android:id="@+id/tv_invoice_status"
                    style="@style/common_Text.InfoItem"
                    android:textColor="@color/common_49C280" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_express_status"
                style="@style/match_wrap"
                android:orientation="vertical"
                android:visibility="gone">

                <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

                <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

                    <TextView
                        style="@style/common_Text.15sp.333333"
                        android:text="@string/invoice_express" />

                    <TextView
                        android:id="@+id/tv_invoice_express"
                        style="@style/common_Text.InfoItem"
                        android:textColor="@color/common_49C280" />
                </LinearLayout>

                <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

                <LinearLayout style="@style/common_Layout.InfoItem.Padding12">

                    <TextView
                        style="@style/common_Text.15sp.333333"
                        android:text="@string/invoice_express_number" />

                    <TextView
                        android:id="@+id/tv_invoice_express_number"
                        style="@style/common_Text.InfoItem"
                        android:textColor="@color/common_49C280" />
                </LinearLayout>
            </LinearLayout>

            <View
                style="@style/common_Line.Horizontal"
                android:layout_height="@dimen/common_dp_8" />

            <TextView
                style="@style/common_Text.16sp.333333.GroupTitle"
                android:text="@string/invoice_info" />

            <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

            <LinearLayout
                style="@style/match_wrap"
                android:layout_marginTop="@dimen/common_dp_16"
                android:paddingStart="@dimen/common_dp_12"
                android:paddingEnd="@dimen/common_dp_12">

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/invoice_type_t" />

                <TextView
                    android:id="@+id/tv_invoice_type"
                    style="@style/common_Text.15sp.767676"
                    android:layout_marginStart="@dimen/common_dp_5" />
            </LinearLayout>

            <LinearLayout
                style="@style/match_wrap"
                android:layout_marginTop="@dimen/common_dp_16"
                android:paddingStart="@dimen/common_dp_12"
                android:paddingEnd="@dimen/common_dp_12">

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/invoice_title_t" />

                <TextView
                    android:id="@+id/tv_invoice_title"
                    style="@style/common_Text.15sp.767676"
                    android:layout_marginStart="@dimen/common_dp_5" />
            </LinearLayout>

            <LinearLayout
                style="@style/match_wrap"
                android:layout_marginTop="@dimen/common_dp_16"
                android:layout_marginBottom="@dimen/common_dp_16"
                android:paddingStart="@dimen/common_dp_12"
                android:paddingEnd="@dimen/common_dp_12">

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/invoice_duty_paragraph_t" />

                <TextView
                    android:id="@+id/tv_invoice_duty_paragraph"
                    style="@style/common_Text.15sp.767676"
                    android:layout_marginStart="@dimen/common_dp_5" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_special_info"
                style="@style/match_wrap"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    style="@style/match_wrap"
                    android:paddingStart="@dimen/common_dp_12"
                    android:paddingEnd="@dimen/common_dp_12">

                    <TextView
                        style="@style/common_Text.15sp.333333"
                        android:text="@string/invoice_registered_address_t" />

                    <TextView
                        android:id="@+id/tv_invoice_registered_address"
                        style="@style/common_Text.15sp.767676"
                        android:layout_marginStart="@dimen/common_dp_5" />
                </LinearLayout>

                <LinearLayout
                    style="@style/match_wrap"
                    android:layout_marginTop="@dimen/common_dp_16"
                    android:paddingStart="@dimen/common_dp_12"
                    android:paddingEnd="@dimen/common_dp_12">

                    <TextView
                        style="@style/common_Text.15sp.333333"
                        android:text="@string/invoice_registered_phone_t" />

                    <TextView
                        android:id="@+id/tv_invoice_registered_phone"
                        style="@style/common_Text.15sp.767676"
                        android:layout_marginStart="@dimen/common_dp_5" />
                </LinearLayout>

                <LinearLayout
                    style="@style/match_wrap"
                    android:layout_marginTop="@dimen/common_dp_16"
                    android:paddingStart="@dimen/common_dp_12"
                    android:paddingEnd="@dimen/common_dp_12">

                    <TextView
                        style="@style/common_Text.15sp.333333"
                        android:text="@string/invoice_bank_t" />

                    <TextView
                        android:id="@+id/tv_invoice_bank"
                        style="@style/common_Text.15sp.767676"
                        android:layout_marginStart="@dimen/common_dp_5" />
                </LinearLayout>

                <LinearLayout
                    style="@style/match_wrap"
                    android:layout_marginTop="@dimen/common_dp_16"
                    android:layout_marginBottom="@dimen/common_dp_16"
                    android:paddingStart="@dimen/common_dp_12"
                    android:paddingEnd="@dimen/common_dp_12">

                    <TextView
                        style="@style/common_Text.15sp.333333"
                        android:text="@string/invoice_bank_account_t" />

                    <TextView
                        android:id="@+id/tv_invoice_bank_account"
                        style="@style/common_Text.15sp.767676"
                        android:layout_marginStart="@dimen/common_dp_5" />
                </LinearLayout>

            </LinearLayout>

            <View
                style="@style/common_Line.Horizontal"
                android:layout_height="@dimen/common_dp_8" />

            <TextView
                style="@style/common_Text.16sp.333333.GroupTitle"
                android:text="@string/invoice_mailing_address" />

            <View style="@style/common_Line.Horizontal.Margin12OfStartAndEnd" />

            <LinearLayout
                style="@style/match_wrap"
                android:layout_marginTop="@dimen/common_dp_16"
                android:paddingStart="@dimen/common_dp_12"
                android:paddingEnd="@dimen/common_dp_12">

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/name_t" />

                <TextView
                    android:id="@+id/tv_name"
                    style="@style/common_Text.15sp.767676"
                    android:layout_marginStart="@dimen/common_dp_5" />
            </LinearLayout>

            <LinearLayout
                style="@style/match_wrap"
                android:layout_marginTop="@dimen/common_dp_16"
                android:paddingStart="@dimen/common_dp_12"
                android:paddingEnd="@dimen/common_dp_12">

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/mobile_t" />

                <TextView
                    android:id="@+id/tv_mobile"
                    style="@style/common_Text.15sp.767676"
                    android:layout_marginStart="@dimen/common_dp_5" />
            </LinearLayout>

            <LinearLayout
                style="@style/match_wrap"
                android:layout_marginTop="@dimen/common_dp_16"
                android:paddingStart="@dimen/common_dp_12"
                android:paddingEnd="@dimen/common_dp_12">

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/postcode_t" />

                <TextView
                    android:id="@+id/tv_postcode"
                    style="@style/common_Text.15sp.767676"
                    android:layout_marginStart="@dimen/common_dp_5" />
            </LinearLayout>

            <LinearLayout
                style="@style/match_wrap"
                android:layout_marginTop="@dimen/common_dp_16"
                android:layout_marginBottom="@dimen/common_dp_16"
                android:paddingStart="@dimen/common_dp_12"
                android:paddingEnd="@dimen/common_dp_12">

                <TextView
                    style="@style/common_Text.15sp.333333"
                    android:text="@string/address_t" />

                <TextView
                    android:id="@+id/tv_address"
                    style="@style/common_Text.15sp.767676"
                    android:layout_marginStart="@dimen/common_dp_5" />
            </LinearLayout>
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>