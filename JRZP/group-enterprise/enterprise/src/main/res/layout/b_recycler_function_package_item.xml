<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="data"
            type="com.bxkj.enterprise.data.PackagesDesc" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/dp_14">

        <TextView
            android:id="@+id/tv_validity"
            style="@style/wrap_wrap"
            android:paddingBottom="@dimen/dp_2"
            android:paddingEnd="@dimen/dp_4"
            android:paddingStart="@dimen/dp_4"
            android:paddingTop="@dimen/dp_2"
            android:textColor="@color/common_white"
            android:textSize="8sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_count"
            style="@style/Text.14sp"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@{data.countText}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.bxkj.common.widget.ZPTextView
            android:id="@+id/tv_price"
            style="@style/Text.22sp"
            android:text="@{@string/b_buy_function_packages_price_unit_format(data.count)}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_count" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>