<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/ll_filter_bar"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="horizontal"
      android:paddingTop="@dimen/dp_8"
      android:paddingBottom="@dimen/dp_8">

      <RadioGroup
        android:id="@+id/rg_filter"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_weight="1"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RadioButton
          android:id="@+id/rb_all"
          style="@style/Text.12sp.333333"
          android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
          android:button="@null"
          android:paddingStart="@dimen/dp_8"
          android:paddingTop="@dimen/dp_2"
          android:paddingEnd="@dimen/dp_8"
          android:paddingBottom="@dimen/dp_2"
          android:text="@string/chat_type_all"
          android:textColor="@color/cl_333333_to_fe6600_selector" />

        <RadioButton
          android:id="@+id/rb_me"
          style="@style/Text.12sp.333333"
          android:layout_marginStart="@dimen/dp_8"
          android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
          android:button="@null"
          android:paddingStart="@dimen/dp_8"
          android:paddingTop="@dimen/dp_2"
          android:paddingEnd="@dimen/dp_8"
          android:paddingBottom="@dimen/dp_2"
          android:text="@string/chat_type_me"
          android:textColor="@color/cl_333333_to_fe6600_selector" />

        <RadioButton
          android:id="@+id/rb_you"
          style="@style/Text.12sp.333333"
          android:layout_marginStart="@dimen/dp_8"
          android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
          android:button="@null"
          android:paddingStart="@dimen/dp_8"
          android:paddingTop="@dimen/dp_2"
          android:paddingEnd="@dimen/dp_8"
          android:paddingBottom="@dimen/dp_2"
          android:text="@string/chat_type_you"
          android:textColor="@color/cl_333333_to_fe6600_selector" />
      </RadioGroup>

      <TextView
        android:id="@+id/tv_filter_job"
        style="@style/Text.12sp.333333"
        android:layout_marginStart="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
        android:drawableEnd="@drawable/ic_expand"
        android:drawablePadding="@dimen/dp_4"
        android:ellipsize="end"
        android:lines="1"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_2"
        android:text="职位"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toEndOf="@id/rg_filter"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
      android:id="@+id/fl_container"
      style="@style/match_match" />

  </LinearLayout>
</layout>