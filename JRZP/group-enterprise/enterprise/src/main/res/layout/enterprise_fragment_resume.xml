<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  style="@style/match_match"
  android:orientation="vertical">

  <FrameLayout
    android:id="@+id/ll_title_bar"
    style="@style/Layout.TitleBar"
    android:background="@drawable/common_bg_title_bar">

    <ImageView
      android:id="@+id/iv_back"
      style="@style/wrap_wrap"
      android:layout_gravity="center_vertical"
      android:src="@drawable/common_ic_back" />

    <TextView
      android:id="@+id/tv_position_name"
      style="@style/common_Text.18sp.333333"
      android:layout_height="match_parent"
      android:layout_gravity="center"
      android:drawableEnd="@drawable/ic_black_expand"
      android:drawablePadding="@dimen/common_dp_5"
      android:gravity="center"
      android:text="@string/enterprise_all_position" />

  </FrameLayout>

  <net.lucode.hackware.magicindicator.MagicIndicator
    android:id="@+id/tab_layout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_44" />

  <View
    android:id="@+id/v_line"
    style="@style/common_Line.Horizontal" />

  <androidx.viewpager.widget.ViewPager
    android:id="@+id/vp_resume_content"
    style="@style/match_match" />

</LinearLayout>