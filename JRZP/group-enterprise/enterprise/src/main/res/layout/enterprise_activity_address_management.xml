<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="viewModel"
      type="com.bxkj.enterprise.ui.activity.addressmanagement.AddressManagementViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      style="@style/match_wrap"
      app:title="@string/enterprise_address_management_title" />

    <com.sanjindev.pagestatelayout.PageStateLayout
      android:id="@+id/psl_address_list"
      style="@style/match_match">

      <LinearLayout
        style="@style/match_match"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/recycler_address_list"
          android:layout_width="match_parent"
          android:layout_height="0dp"
          android:layout_weight="1"
          bind:items="@{viewModel.addressList}" />

        <TextView
          android:id="@+id/tv_add"
          style="@style/Button.Basic"
          android:layout_margin="@dimen/common_dp_16"
          android:text="@string/enterprise_address_management_add"
          android:visibility="@{viewModel.addressList.size()&lt;6?View.VISIBLE:View.GONE}" />
      </LinearLayout>

    </com.sanjindev.pagestatelayout.PageStateLayout>
  </LinearLayout>
</layout>