package com.bxkj.enterprise.ui.activity.ordermanagement

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.bxkj.common.base.BaseDaggerActivity
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.enterprise.R
import com.bxkj.enterprise.ui.fragment.ordermanagement.OrderManagementFragment
import com.bxkj.enterprise.ui.fragment.ordermanagement.OrderManagementNavigation

/**
 * <AUTHOR>
 * @date 2018/10/11
 * @version V1.0
 */
class OrderManagementActivity : BaseDaggerActivity() {

  companion object {

    private const val ORDER_IS_PAY = "order_is_pay"

    fun start(context: Context, orderIsPay: Boolean) {
      val intent = Intent(context, OrderManagementActivity::class.java)
      intent.putExtra(ORDER_IS_PAY, orderIsPay)
      context.startActivity(intent)
    }
  }

  private var _contentFragment: Fragment? = null

  private var mOrderIsPay: Boolean = false

  override fun getLayoutId(): Int = R.layout.enterprise_activity_order_management

  override fun initPage() {
  }

  override fun initIntent(intent: Intent?) {
    mOrderIsPay = intent!!.getBooleanExtra(ORDER_IS_PAY, true)
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    checkHasFragmentCache(savedInstanceState)
    setupContent()
  }

  private fun checkHasFragmentCache(savedInstanceState: Bundle?) {
    savedInstanceState?.let {
      supportFragmentManager.findFragmentByTag(OrderManagementFragment::class.java.simpleName)
        ?.let {
          _contentFragment = it
        }
    }
  }

  override fun onSaveInstanceState(outState: Bundle) {
    super.onSaveInstanceState(outState)
    supportFragmentManager.fragments.forEach {
      supportFragmentManager.putFragment(outState, it::class.simpleName.getOrDefault(), it)
    }
  }

  private fun setupContent() {
    if (!ensureContentFragment().isAdded) {
      supportFragmentManager.beginTransaction()
        .replace(R.id.fl_content, ensureContentFragment()).commit()
    }
  }

  private fun ensureContentFragment(): Fragment {
    _contentFragment?.let {
      return it
    } ?: let {
      return OrderManagementFragment.newInstance(OrderManagementNavigation.TYPE_PAID)
    }
  }
}