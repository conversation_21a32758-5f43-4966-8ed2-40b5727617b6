package com.bxkj.enterprise.data.source.datasource;

import com.bxkj.common.base.mvvm.callback.ResultCallBack;
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.enterprise.data.CompanyInfoDataV2;
import com.bxkj.enterprise.data.SchoolRecruitDetailsData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data.source.datasource
 * @Description:
 * @TODO: TODO
 * @date 2019/6/13
 */
public interface SchoolRecruitDetailsSource {

  void getSchoolRecruitDetails(int schoolRecruitId,
      ResultDataCallBack<SchoolRecruitDetailsData> callBack);

  void jobFairRegistration(int userId, int jobFairId, CompanyInfoDataV2 companyInfoData, String jobs,
      ResultDataCallBack<BaseResponse> callBack);

  void checkSchoolRecruitStatus(int userId, int schoolRecruitId, ResultCallBack callBack);
}
