package com.bxkj.enterprise.ui.activity.positiontop;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.Nullable;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.util.rxbus.RxBus;
import com.bxkj.common.util.rxbus.RxMsgCode;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.common.widget.popup.menupopup.MenuPopup;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.ecommon.util.MTextUtils;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.api.EnterpriseApiConstants;
import com.bxkj.enterprise.data.PositionTopCostInfo;
import com.bxkj.enterprise.data.PositionTopData;
import com.bxkj.enterprise.data.PositionTopKeywordData;
import com.bxkj.enterprise.mvp.contract.AccountVipContract;
import com.bxkj.enterprise.mvp.presenter.AccountVipPresenter;
import com.bxkj.enterprise.ui.activity.positiontopkeywords.PositionTopKeywordsActivity;
import com.bxkj.enterprise.ui.fragment.positionlist.PositionListFragment;
import com.bxkj.jrzp.user.data.AccountVipData;
import com.bxkj.jrzp.user.mine.ui.membercenter.MemberCenterWebNavigation;
import com.zaaach.citypicker.CityPicker;
import com.zaaach.citypicker.adapter.OnPickListener;
import com.zaaach.citypicker.model.City;
import com.zaaach.citypicker.model.LocatedCity;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.inject.Inject;

/**
 * @description 职位置顶
 * @date 2018/7/16
 */
public class PositionTopActivity extends BaseDaggerActivity
  implements PositionTopContract.View, AccountVipContract.View {

  private static final String POSITION_ID = "position_id";
  private static final int TO_EDIT_KEYWORDS_CODE = 1;
  private static final int TO_UPGRADE_MEMBER_CODE = 2;

  @Inject
  PositionTopPresenter mPositionTopPresenter;
  @Inject
  AccountVipPresenter mAccountVipPresenter;

  private TextView tvAddress;
  private TextView tvTopDays;
  private TextView tvTopIntegralCount;
  private TextView tvCount;
  private TextView tvKeyword;
  private ImageView ivIntegralSelected;
  private TextView tvTopDaysBalance;
  private ImageView ivDaysSelected;
  private TextView tvTopIntegral;
  private TextView tvTopDay;
  private LinearLayout llToppedArea;
  private TextView tvToppedCity;
  private TextView tvSubmit;

  private int mPositionId;
  private static final int PAYMENT_BY_INTEGRAL = 2;
  private static final int PAYMENT_BY_DAYS = 1;
  private int mPaymentMethod = PAYMENT_BY_DAYS;
  private int mTopCity = ECommonApiConstants.NO_DATA;
  private ArrayList<PositionTopKeywordData> mTempKeywords;

  private MenuPopup mTopCostMenuPopup;
  private CityPicker mCityPicker;
  //职位置顶天数
  private PositionTopCostInfo mPositionTopCostInfo;

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_position_top;
  }

  public static void start(Context context, int positionId) {
    Intent starter = new Intent(context, PositionTopActivity.class);
    starter.putExtra(POSITION_ID, positionId);
    context.startActivity(starter);
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mPositionTopPresenter);
    presenters.add(mAccountVipPresenter);
    return presenters;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.enterprise_position_top));
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());
    mAccountVipPresenter.getAccountVipInfo(getMUserID());
    tvCount.setText(MTextUtils.fromHtml(
      String.format(getString(R.string.position_refresh_consumption_integral_format), 0)));
    mPositionTopPresenter.getPositionTopCost(getMUserID(), 0);
    mPositionTopPresenter.getTopDaysBalance(getMUserID());

    setupCity();
    setupCityPicker();
  }

  @Override
  protected void initIntent(Intent intent) {
    mPositionId = intent.getIntExtra(POSITION_ID, 0);
    mPositionTopPresenter.getToppedCityList(getMUserID(), mPositionId);
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_address) {
      mCityPicker.show();
    } else if (view.getId() == R.id.ll_top_days) {
      if (mTopCity == CommonApiConstants.NO_ID) {
        showToast("请先选择推广地区");
        return;
      }
      if (mTopCostMenuPopup != null) {
        mTopCostMenuPopup.show();
      }
    } else if (view.getId() == R.id.ll_keyword) {
      if (mTopCity == CommonApiConstants.NO_ID) {
        showToast("请先选择推广地区");
        return;
      }
      if (mPositionTopCostInfo == null) {
        showToast(getString(R.string.position_top_no_days_tips));
        return;
      }
      startActivityForResult(PositionTopKeywordsActivity.Companion.newIntent(this, mTopCity,
        mPositionTopCostInfo.getCount(), mTempKeywords), TO_EDIT_KEYWORDS_CODE);
    } else if (view.getId() == R.id.ll_refresh_integral) {
      mPaymentMethod = PAYMENT_BY_INTEGRAL;
      ivIntegralSelected.setVisibility(View.VISIBLE);
      ivDaysSelected.setVisibility(View.GONE);
      tvTopIntegral.setSelected(true);
      tvTopDay.setSelected(false);
      calculationPositionTopCost();
    } else if (view.getId() == R.id.ll_refresh_days) {
      mPaymentMethod = PAYMENT_BY_DAYS;
      ivDaysSelected.setVisibility(View.VISIBLE);
      ivIntegralSelected.setVisibility(View.GONE);
      tvTopDay.setSelected(true);
      tvTopIntegral.setSelected(false);
      calculationPositionTopCost();
    } else {
      if (mPositionTopCostInfo == null) {
        showToast(getString(R.string.position_top_no_days_tips));
        return;
      }
      mPositionTopPresenter.addPositionTop(getMUserID(), EnterpriseApiConstants.POSITION_TOP_FOR_PC,
        mPositionId, mPositionTopCostInfo.getCount(), mPaymentMethod, mTopCity, mTempKeywords);
    }
  }

  private void setupCity() {
    if (!Objects.equals(UserUtils.getUserSelectedCityName(), "全国")) {
      tvAddress.setText(UserUtils.getUserSelectedCityName());
    }
    mTopCity = UserUtils.getUserSelectedCityId();
  }

  private void setupCityPicker() {
    mCityPicker = new CityPicker()
      .setLocatedCity(
        new LocatedCity(UserUtils.getUserLocateCityName(), ECommonApiConstants.NO_TEXT,
          String.valueOf(UserUtils.getUserLocateCityId())))
      .setFragmentManager(getSupportFragmentManager())
      .enableAnimation(true)
      .setAnimationStyle(R.style.common_RightPopupAnim)
      .setOnPickListener(new OnPickListener() {
        @Override
        public void onPick(int position, City data) {
          if (data != null) {
            if (checkCityIsTopped(data.getName())) {
              showToast(getString(R.string.position_top_selected_city_topped_tips));
              return;
            }
            tvSubmit.setEnabled(true);
            mTopCity = Integer.parseInt(data.getCode());
            tvAddress.setText(data.getName());
          }
        }

        @Override
        public void onLocate() {

        }
      });
  }

  /**
   * 检查该城市是否已置顶
   */
  private boolean checkCityIsTopped(String city) {
    return !CheckUtils.isNullOrEmpty(tvToppedCity.getText()) && tvToppedCity.getText()
      .toString()
      .contains(city);
  }

  @Override
  public void getPositionTopInfoSuccess(PositionTopData positionTopData) {
    //        tvAddress.setText(positionTopData.getShiName());
  }

  @Override
  public void getPositionTopCostSuccess(List<PositionTopCostInfo> topCostData) {
    mTopCostMenuPopup = new MenuPopup.Builder(this)
      .setData(topCostData)
      .setOnItemClickListener((view, position) -> {
        tvTopDays.setText(MTextUtils.fromHtml(topCostData.get(position).getItemOption()));
        mPositionTopCostInfo = topCostData.get(position);
        calculationPositionTopCost();
      }).build();
  }

  private void calculationPositionTopCost() {
    if (mPositionTopCostInfo != null) {
      if (mPaymentMethod == PAYMENT_BY_INTEGRAL) {
        if (mPositionTopCostInfo.hasDiscount()) {
          tvCount.setText(MTextUtils.fromHtml(
            getString(R.string.position_top_consumption_integral_format,
              mPositionTopCostInfo.getTotalIntegral(),
              mPositionTopCostInfo.getTotalIntegralZk())));
        } else {
          tvCount.setText(MTextUtils.fromHtml(
            String.format(getString(R.string.position_top_no_discount_format),
              mPositionTopCostInfo.getTotalIntegral())));
        }
      } else {
        tvCount.setText(MTextUtils.fromHtml(
          getString(R.string.position_top_day_cost_format, mPositionTopCostInfo.getCount())));
      }
    } else {
      if (mPaymentMethod == PAYMENT_BY_INTEGRAL) {
        tvCount.setText(MTextUtils.fromHtml(
          String.format(getString(R.string.position_top_no_discount_format), 0)));
      } else {
        tvCount.setText(MTextUtils.fromHtml(
          String.format(getString(R.string.position_top_day_cost_format), 0)));
      }
    }
  }

  @Override
  public void positionTopFailed(String errorMsg) {
    new ActionDialog.Builder()
      .setTitle(getString(R.string.tips))
      .setContent(errorMsg)
      .setConfirmText("去购买")
      .setOnConfirmClickListener((dialog) ->
        MemberCenterWebNavigation.create().start()
      )
      .build()
      .show(getSupportFragmentManager());
  }

  @Override
  public void getTopDayBalanceSuccess(int balance) {
    tvTopDaysBalance.setText(
      MTextUtils.fromHtml(getString(R.string.position_top_day_balance_format, balance)));
  }

  @Override
  public void getToppedAreaSuccess(String toppedCity) {
    llToppedArea.setVisibility(View.VISIBLE);
    tvToppedCity.setText(toppedCity);
    if (!CheckUtils.isNullOrEmpty(tvAddress.getText()) && toppedCity.contains(
      tvAddress.getText().toString())) {
      tvSubmit.setEnabled(false);
    }
  }

  @Override
  public void addPositionTopSuccess() {
    RxBus.get().post(
      new RxBus.Message(RxMsgCode.REFRESH_POSITION_LIST_CODE, PositionListFragment.TAG_RUNNING));
    showToast(getString(R.string.position_top_success));
    setResult(RESULT_OK);
    finish();
  }

  @Override
  public void getAccountVipInfoSuccess(AccountVipData accountVipData) {
    tvTopIntegralCount.setText(
      MTextUtils.fromHtml(String.format(getString(R.string.position_refresh_count)
        , accountVipData.getDouCount() + "招聘豆")));
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == TO_EDIT_KEYWORDS_CODE
      && resultCode == PositionTopKeywordsActivity.RESULT_SUCCESS
      && data != null) {
      mTempKeywords = data.getParcelableArrayListExtra(PositionTopKeywordsActivity.EXTRA_KEYWORDS);
      if (!CheckUtils.isNullOrEmpty(mTempKeywords)) {
        StringBuilder keywordsText = new StringBuilder();
        for (int i = 0; i < mTempKeywords.size(); i++) {
          String itemKeyword = mTempKeywords.get(i).getKeyword();
          if (!CheckUtils.isNullOrEmpty(itemKeyword)) {
            keywordsText.append(itemKeyword).append(",");
          }
        }
        tvKeyword.setText(keywordsText.substring(0, keywordsText.lastIndexOf(",")));
      }
    } else if (requestCode == TO_UPGRADE_MEMBER_CODE) {
      mAccountVipPresenter.getAccountVipInfo(getMUserID());
    }
  }

  private void bindView(View bindSource) {
    tvAddress = bindSource.findViewById(R.id.tv_address);
    tvTopDays = bindSource.findViewById(R.id.tv_top_days);
    tvTopIntegralCount = bindSource.findViewById(R.id.tv_top_integral_count);
    tvCount = bindSource.findViewById(R.id.tv_count);
    tvKeyword = bindSource.findViewById(R.id.tv_keyword);
    ivIntegralSelected = bindSource.findViewById(R.id.iv_refresh_integral_selected);
    tvTopDaysBalance = bindSource.findViewById(R.id.tv_top_days_balance);
    ivDaysSelected = bindSource.findViewById(R.id.iv_refresh_days_selected);
    tvTopIntegral = bindSource.findViewById(R.id.tv_top_integral);
    tvTopDay = bindSource.findViewById(R.id.tv_top_day);
    llToppedArea = bindSource.findViewById(R.id.ll_topped_city);
    tvToppedCity = bindSource.findViewById(R.id.tv_topped_city);
    tvSubmit = bindSource.findViewById(R.id.tv_submit);
    bindSource.findViewById(R.id.tv_address).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.ll_top_days).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.ll_keyword).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.ll_refresh_integral).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.ll_refresh_days).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_submit).setOnClickListener(v -> onViewClicked(v));
  }
}
