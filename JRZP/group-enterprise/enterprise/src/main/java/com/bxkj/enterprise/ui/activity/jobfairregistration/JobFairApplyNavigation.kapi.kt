package com.bxkj.enterprise.ui.activity.jobfairregistration

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * @Description:
 * @author: Yang<PERSON>in
 * @date: 2020/12/4
 * @version: V1.0
 */
class JobFairApplyNavigation {

  companion object {
    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/jobfairapply"

    const val EXTRA_JOB_FAIR_ID = "JOB_FAIR_ID"

    fun create(jobFairID: Int): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_JOB_FAIR_ID, jobFairID)
    }
  }
}