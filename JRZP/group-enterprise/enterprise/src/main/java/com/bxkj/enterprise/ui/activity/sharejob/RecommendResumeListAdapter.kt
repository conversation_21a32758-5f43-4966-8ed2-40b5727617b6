package com.bxkj.enterprise.ui.activity.sharejob

import android.widget.ImageView
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.enterprise.BR
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.RecommendResumeData

/**
 * @Description:
 * @author: YangXin
 * @date: 2021/1/6
 * @version: V1.0
 */
class RecommendResumeListAdapter constructor(private val viewModel: ShareJobViewModel) :
  SimpleDiffListAdapter<RecommendResumeData>(
    R.layout.enterprise_recycler_recommend_resume_item,
    RecommendResumeData.DiffCallback(),
    BR.data
  ) {

  override fun bind(holder: SuperViewHolder, item: RecommendResumeData, position: Int) {
    super.bind(holder, item, position)
    val ivSelect = holder.findViewById<ImageView>(R.id.iv_select)
    ivSelect.isSelected = viewModel.getSelectedResume().contains(item)
    ivSelect.setOnClickListener {
      viewModel.selectItem(item)
    }
  }

}