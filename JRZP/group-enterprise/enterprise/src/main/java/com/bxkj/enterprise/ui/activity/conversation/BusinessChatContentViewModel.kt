package com.bxkj.enterprise.ui.activity.conversation

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.data.ChatInfoBean
import com.bxkj.common.data.ResumeStateData
import com.bxkj.common.data.repository.CommonRepository
import com.bxkj.common.data.repository.ConversationRepository
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.enterprise.data.PositionPreviewData
import com.bxkj.enterprise.data.ShareResumeInfoBean
import com.bxkj.enterprise.data.VideoCallBalanceData
import com.bxkj.enterprise.data.source.AccountInfoRepo
import com.bxkj.enterprise.data.source.ResumeRepo
import com.bxkj.enterprise.data.source.MyJobRepo
import com.bxkj.jrzp.support.chat.ChatMark
import com.bxkj.jrzp.support.chat.ChatMsgType
import com.bxkj.jrzp.support.chat.api.ChatRepo
import com.bxkj.jrzp.support.chat.data.ChatMsgItemData
import com.bxkj.jrzp.support.chat.ui.chatbase.ChatBaseViewModel
import com.bxkj.jrzp.user.api.OpenUserApiConstants.ContactType
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.support.upload.repository.UploadRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/1
 * @version: V1.0
 */
class BusinessChatContentViewModel @Inject constructor(
    private val _accountRepo: AccountInfoRepo,
    private val _resumeRepo: ResumeRepo,
    private val _My_jobRepo: MyJobRepo,
    private val _commonRepo: CommonRepository,
    private val _openUserRepository: OpenUserRepository,
    conversationRepository: ConversationRepository,
    private val _chatRepo: ChatRepo,
    uploadRepo: UploadRepository
) : ChatBaseViewModel(_chatRepo, conversationRepository, _openUserRepository, uploadRepo) {

    val showSwitchChatJobDialogCommand = MutableLiveData<VMEvent<Int>>()
    val resumeState = MutableLiveData<ResumeStateData?>()
    val resumeIsOpen = MutableLiveData<Boolean>().apply { value = false }

    val showNoInviteBalanceTipsCommand = LiveEvent<Void>()

    //显示标记弹窗
    val showChatMarkDialogCommand = MutableLiveData<VMEvent<ChatInfoBean>>()

    val toViewResumeCommand =
        LiveEvent<ChatInfoBean>()
    val showShareCommand =
        LiveEvent<ShareResumeInfoBean>()
    val showInviteInterviewDialogCommand = MutableLiveData<VMEvent<ChatInfoBean>>()

    val confirmExchangePhoneCommand = MutableLiveData<VMEvent<String>>()

    val toDownloadResumeCommand =
        LiveEvent<ChatInfoBean>()

    val inviteSendResumeCommand =
        LiveEvent<PositionPreviewData>()

    val requestExchangeContactWayCommand = MutableLiveData<VMEvent<ChatInfoBean>>()

    val callPhoneCommand = LiveEvent<String>()
    val showNoJobTipsCommand = LiveEvent<Void>()

    //邀请视频通话
    val toInviteVideoCallCommand = LiveEvent<ChatInfoBean>()

    //视频面试余量提醒
    val showVideoCallBalanceTipsCommand = LiveEvent<VideoCallBalanceData>()

    //显示发动短信弹窗
    val showSmsDialogCommand = MutableLiveData<VMEvent<Int>>()

    fun updateMarkState(state: Int) {
        conversationInfo.value?.updateMarkState(state)
    }

    /**
     * 分享简历
     */
    fun shareResume() {
        conversationInfo.value?.let { conversation ->
            viewModelScope.launch {
                showLoading()
                _resumeRepo.getShareResumeParams(conversation.relID, conversation.resID)
                    .handleResult({
                        showShareCommand.value = it?.apply {
                            shareUrl =
                                "http://m.jrzp.com/Resumefenxiang/view.aspx?userID=" + conversation.userID + "&id=" + conversation.resID
                        }
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    fun cancelMark() {
        conversationInfo.value?.let {
            showLoading()
            viewModelScope.launch {
                _chatRepo.markChat(it.ncID, ChatMark.MARK_NORMAL)
                    .handleResult({
                        conversationInfo.value?.cancelUnsuitable()
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    /**
     * 查看简历
     */
    fun toViewResume() {
        conversationInfo.value?.let {
            if (it.resID == 0 || it.userID == 0) {
                showToast("暂无简历信息")
            } else {
                toViewResumeCommand.value = it
            }
        } ?: showToast("暂无简历信息")
    }

    /**
     * 邀请投递
     */
    fun inviteSendResume() {
        invitePreCheck()
    }

    /**
     * 请求交换联系方式
     */
    fun requestExchangeContractWay() {
        conversationInfo.value?.let {
            requestExchangeContactWayCommand.value = VMEvent(it)
        }
    }

    private fun getJobDetails() {
        conversationInfo.value?.let { conversation ->
            viewModelScope.launch {
                showLoading()
                _My_jobRepo.getJobDetails(conversation.relID)
                    .handleResult({
                        inviteSendResumeCommand.value = it
                    }, {
                        showToast("未找到职位信息")
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    private fun invitePreCheck() {
        viewModelScope.launch {
            showLoading()
            _openUserRepository.getUserInviteBalance(getSelfUserID())
                .handleResult({
                    it?.let {
                        if (it.yaoyueCount > 0) {
                            getJobDetails()
                        } else {
                            showNoInviteBalanceTipsCommand.call()
                        }
                    } ?: let {
                        showNoInviteBalanceTipsCommand.call()
                    }
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    /**
     * 邀请投递
     */
    fun reqInviteSendResume(jobId: Int) {
        conversationInfo.value?.let {
            viewModelScope.launch {
                showLoading()
                _resumeRepo.inviteSendResume(
                    getSelfUserID(),
                    jobId,
                    it.resID.toString(),
                    getSayHelloText()
                ).handleResult({
                    refreshChatInfo()
                    refreshMsgList()
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
            }
        }
    }

    /**
     * 邀请视频面试
     */
    fun inviteVideoCall() {
        conversationInfo.value?.let {
            toInviteVideoCallCommand.value = it
        }
    }

    fun getConversationId(): Int {
        return _conversationId
    }

    /**
     * 返回聊天信息
     */
    fun getConversation(): ChatInfoBean? {
        return conversationInfo.value
    }

    /**
     * 获取视频面试余量
     */
    fun checkVideoCallBalance() {
        viewModelScope.launch {
            showLoading()
            _accountRepo.getVideoCallBalance()
                .handleResult({
                    showVideoCallBalanceTipsCommand.value = it
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    /**
     * 面试通过
     */
    fun interviewSuccess(msg: ChatMsgItemData) {
        viewModelScope.launch {
            showLoading()
            _resumeRepo.updateResumeState(getSelfUserID(), msg.relid, msg.resid, 6)
                .handleResult({
                    refreshChatInfo()
                    refreshMsgList()
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    fun requestExchangePhone() {
        conversationInfo.value?.let {
            if (it.phoneExchanged()) {
                addShowContactPhoneItem()
            } else {
                viewModelScope.launch {
                    showLoading()
                    _openUserRepository.getUserContactInfo(ContactType.PHONE)
                        .handleResult({ result ->
                            confirmExchangePhoneCommand.value = VMEvent(result?.contactWay.getOrDefault())
                            refreshChatInfo()
                        }, { err ->
                            showToast(err.errMsg)
                        }, {
                            hideLoading()
                        })
                }
            }
        }
    }

    /**
     * 交换手机号请求
     */
    fun sendExchangePhoneRequest() {
        conversationInfo.value?.let {
            showLoading()
            viewModelScope.launch {
                _chatRepo.requestExchangeContactWay(
                    it.ncID,
                    ChatRole.PERSONAL,
                    getSelfUserID(),
                    it.userID,
                    it.relID,
                    it.resID,
                    ChatMsgType.REQ_EXCHANGE_PHONE
                ).handleResult({
                    refreshChatInfo()
                    refreshMsgList()
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
            }
        }
    }

    fun showInviteInterviewDialog() {
        conversationInfo.value?.let {
            showInviteInterviewDialogCommand.value = VMEvent(it)
        }
    }

    fun showChatMarkDialog() {
        conversationInfo.value?.let {
            showChatMarkDialogCommand.value = VMEvent(it)
        }
    }

    fun switchJob() {
        conversationInfo.value?.let {
            showSwitchChatJobDialogCommand.value = VMEvent(it.relID)
        }
    }

    fun switchChatJob(result: PositionItemBean) {
        conversationInfo.value?.let {
            viewModelScope.launch {
                showLoading()
                _chatRepo.switchChatJob(getSelfUserID(), it.ncID, it.userID, it.relID, result.id, it.resID)
                    .handleResult({
                        refreshChatInfo()
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    fun showSmsDialog() {
        conversationInfo.value?.let {
            showSmsDialogCommand.value = VMEvent(it.userID)
        }
    }

    /**
     * 检查简历状态
     */
    private fun checkResumeState(it: ChatInfoBean) {
        viewModelScope.launch {
            _commonRepo.checkResumeStatus(getSelfUserID(), it.resID)
                .handleResult({
                    resumeState.value = it
                }, {
                    resumeState.value = null
                })
            _resumeRepo.getResumeCareerObjective(it.userID, it.resID)
                .handleResult({
                    resumeIsOpen.value = (it?.state == 0)
                }, {
                    resumeIsOpen.value = false
                })
        }
    }
}
