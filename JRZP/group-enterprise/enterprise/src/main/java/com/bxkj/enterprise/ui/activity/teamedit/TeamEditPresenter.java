package com.bxkj.enterprise.ui.activity.teamedit;

import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.TeamItemData;

import org.jetbrains.annotations.NotNull;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.teamedit
 * @Description: TeamEdit
 * @TODO: TODO
 * @date 2018/3/27
 */

public class TeamEditPresenter extends TeamEditContract.Presenter {

    private static final String TAG = TeamEditPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public TeamEditPresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    void addTeamMember(int userId, TeamItemData teamItemData) {
        if (checkDataHasEmpty(teamItemData)) return;
        mBusinessApi.addTeamMember(userId, teamItemData)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(@NotNull BaseResponse baseResponse) {
                        if (baseResponse.getIntegral()>0){
                            mView.integralReward(baseResponse.getIntegral());
                        }else {
                            mView.addTeamMemberSuccess();
                        }
                    }

                    @Override
                    protected void onError(@NotNull RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    private boolean checkDataHasEmpty(TeamItemData teamItemData) {
        if (CheckUtils.isNullOrEmpty(teamItemData.getPhoto())) {
            mView.onError("头像不可为空");
            return true;
        } else if (CheckUtils.isNullOrEmpty(teamItemData.getName())) {
            mView.onError("姓名不可为空");
            return true;
        } else if (CheckUtils.isNullOrEmpty(teamItemData.getPosition())) {
            mView.onError("职位不可为空");
            return true;
        } else if (CheckUtils.isNullOrEmpty(teamItemData.getDes())) {
            mView.onError("个人介绍不可为空");
            return true;
        }
        return false;
    }

    @Override
    void updateTeamMember(int userId, TeamItemData teamItemData) {
        if (checkDataHasEmpty(teamItemData)) return;
        mBusinessApi.updateTeamMember(userId, teamItemData)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.updateTeamMemberSuccess();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
