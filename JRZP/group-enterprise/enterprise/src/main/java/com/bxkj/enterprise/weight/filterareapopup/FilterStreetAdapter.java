package com.bxkj.enterprise.weight.filterareapopup;

import android.content.Context;
import android.widget.TextView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.enterprise.R;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.weight.FilterAreaPopup
 * @Description: 筛选街道适配器
 * @TODO: TODO
 * @date 2018/4/3
 */

public class FilterStreetAdapter extends SuperAdapter<AreaOptionsData> {
    private int mSelectPosition = -1;

    public FilterStreetAdapter(Context context, List<AreaOptionsData> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, AreaOptionsData areaOptionsData, int position) {
        TextView tvStreetItem = holder.findViewById(R.id.tv_street_item);
        tvStreetItem.setText(areaOptionsData.getName());
        tvStreetItem.setSelected(position == mSelectPosition);
        holder.itemView.setOnClickListener(view -> {
            if (mSelectPosition == position) return;
            if (SuperItemClickListener != null) {
                SuperItemClickListener.onClick(holder.itemView, position);
            }
            mSelectPosition = position;
            notifyDataSetChanged();
        });
    }

    public void resetSelectPosition() {
        mSelectPosition = -1;
    }
}
