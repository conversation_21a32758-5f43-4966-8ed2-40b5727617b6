package com.bxkj.enterprise.ui.fragment.homev2

import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter.TabItemConfig
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.kotlin.attachIndicator
import com.bxkj.common.util.kotlin.dip
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseFragmentHomeResumeContainerBinding
import com.bxkj.enterprise.ui.fragment.recommendresume.LatestResumeFragment
import com.bxkj.enterprise.ui.fragment.recommendresume.RecommendResumeFragmentV2
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.indicators.WrapPagerIndicator

class HomeResumeContainerFragment : BaseDBFragment<EnterpriseFragmentHomeResumeContainerBinding, BaseViewModel>(),
    OnClickListener, ResumeListContainer {

    companion object {

        private const val EXTRA_JOB_ID = "JOB_ID"
        fun newInstance(jobID: Int): Fragment {
            return HomeResumeContainerFragment().apply {
                arguments = bundleOf(
                    EXTRA_JOB_ID to jobID
                )
            }
        }
    }

    private val _extraJobID by lazy { arguments?.getInt(EXTRA_JOB_ID) }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_fragment_home_resume_container

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.onClickListener = this
        setupViewPager()
    }

    override fun onClick(view: View?) {
        view?.let {
            if (it.id == R.id.tv_filter) {
                getCurrentShowChild()?.showFilter()
            }
        }
    }

    fun refreshChild() {
        getCurrentShowChild()?.refresh()
    }

    private fun getCurrentShowChild(): ResumeFilterChild? {
        val currentChildFragment =
            childFragmentManager.findFragmentByTag("f${viewBinding.vpContent.currentItem}")
        if (currentChildFragment is ResumeFilterChild) {
            return currentChildFragment
        }
        return null
    }

    private fun setupViewPager() {

        viewBinding.vpContent.isEnabled = false

        viewBinding.vpContent.adapter = object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int = 2

            override fun createFragment(position: Int): Fragment {
                return if (position == 0) {
                    RecommendResumeFragmentV2.newInstance(_extraJobID)
                } else {
                    LatestResumeFragment.newInstance(_extraJobID)
                }
            }
        }

        viewBinding.indicatorType.navigator = CommonNavigator(requireContext()).apply {
            adapter = MagicIndicatorAdapter(
                arrayOf("推荐", "最新"), TabItemConfig(
                    textSize = 16f,
                    normalColor = R.color.common_999999,
                    selectedColor = R.color.cl_333333,
                    pageIndicator = WrapPagerIndicator(requireContext()).apply {
                        verticalPadding = dip(2)
                        horizontalPadding = dip(4)
                        roundRadius = dip(4).toFloat()
                    }
                )
            ).apply {
                setOnTabClickListener(object : OnTabClickListener {
                    override fun onTabClicked(v: View, index: Int) {
                        viewBinding.vpContent.setCurrentItem(index, true)
                    }
                })
            }
        }

        viewBinding.vpContent.attachIndicator(viewBinding.indicatorType)
    }

    override fun finishRefresh() {
        if (parentFragment is ResumeListContainer) {
            (parentFragment as ResumeListContainer).finishRefresh()
        }
    }
}