package com.bxkj.enterprise.ui.activity.candidate

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.BActivityCandidateListBinding
import com.bxkj.enterprise.ui.activity.resumedetails.ApplicantResumeDetailActivityV2
import com.bxkj.enterprise.data.ResumeItemDataV2

/**
 * Description:职位候选人列表
 * Author:Sanjin
 * Date:2024/2/21
 **/
class CandidateListActivityV2 :
  BaseDBActivity<BActivityCandidateListBinding, CandidateListViewModel>() {

  override fun getViewModelClass(): Class<CandidateListViewModel> =
    CandidateListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.b_activity_candidate_list

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupCandidateListAdapter()

    viewModel.start(intent.getIntExtra(EXTRA_JOB_ID, 0))
  }

  private fun setupCandidateListAdapter() {
    viewBinding.listCandidate.recyclerContent.apply {
      layoutManager = LinearLayoutManager(this@CandidateListActivityV2)
      addItemDecoration(
        LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_f4f4f4_8))
          .drawHeader(true)
          .drawFoot(true)
          .build()
      )
    }

    val adapter =
      SimpleDBListAdapter<ResumeItemDataV2>(
        this,
        R.layout.enterprise_recycler_received_resume_item_v2
      ).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            data[position]?.let { item ->
              if (v.id == R.id.tv_delete) {
                viewModel.removeCandidate(item)
                viewBinding.listCandidate.recyclerContent.smoothCloseMenu()
              } else {
                startActivity(
                  ApplicantResumeDetailActivityV2.newIntent(
                    this@CandidateListActivityV2,
                    item.id,
                    item.ubInfo?.uid.getOrDefault()
                  )
                )
              }
            }
          }
        }, R.id.tv_delete)
      }
    viewModel.candidateListViewModel.setAdapter(adapter)
  }

  companion object {

    const val EXTRA_JOB_ID = "JOB_ID"

    fun newIntent(context: Context, jobId: Int): Intent {
      return Intent(context, CandidateListActivityV2::class.java).apply {
        putExtra(EXTRA_JOB_ID, jobId)
      }
    }
  }
}