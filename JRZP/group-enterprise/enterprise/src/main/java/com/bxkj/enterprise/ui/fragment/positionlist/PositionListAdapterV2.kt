package com.bxkj.enterprise.ui.fragment.positionlist

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.constants.OnceTag
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.ecommon.util.MTextUtils
import com.bxkj.enterprise.R
import com.bxkj.enterprise.R.layout
import com.bxkj.enterprise.data.JobPageTipsData
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.common.once.Once
import java.util.concurrent.TimeUnit

/**
 *
 * @author: sanjin
 * @date: 2022/9/22
 */
open class PositionListAdapterV2(private val _pageType: String) :
  RecyclerView.Adapter<SuperViewHolder>() {

  companion object {

    private const val LAYOUT_TIPS = 1
    private const val LAYOUT_JOB = 2
  }

  private var attachRecyclerView: RecyclerView? = null

  private var _data: MutableList<Any> = mutableListOf()

  private var _superItemClickListener: SuperItemClickListener? = null

  private var _onVipTipsClickListener: OnVipTipsClickListener? = null

  override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
    super.onAttachedToRecyclerView(recyclerView)
    attachRecyclerView = recyclerView
  }

  fun setOnItemClickListener(superItemClickListener: SuperItemClickListener) {
    _superItemClickListener = superItemClickListener
  }

  fun setOnVipTipsClickListener(onVipTipsClickListener: OnVipTipsClickListener) {
    _onVipTipsClickListener = onVipTipsClickListener
  }

  fun addVipTips(jobPageTipsData: JobPageTipsData) {
    _data.add(0, jobPageTipsData)
    notifyItemInserted(0)
  }

  fun getData(): MutableList<Any> {
    return _data
  }

  fun addAll(items: List<Any>) {
    _data.addAll(items)
    notifyItemRangeChanged(_data.size - items.size, items.size)
  }

  open fun removeAt(position: Int): Int {
    if (position < _data.size) {
      _data.removeAt(position)
      notifyItemRemoved(position)
      notifyItemRangeChanged(position, _data.size)
    }
    if (_data.isNotEmpty()) {
      return if (_data.get(0) is JobPageTipsData) (_data.size - 1) else _data.size
    }
    return _data.size
  }

  fun clear() {
    _data.clear()
    notifyDataSetChanged()
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SuperViewHolder {
    return if (viewType == LAYOUT_TIPS) {
      SuperViewHolder(
        LayoutInflater.from(parent.context)
          .inflate(layout.enterprise_recycler_job_tips, parent, false)
      )
    } else {
      SuperViewHolder(
        LayoutInflater.from(parent.context)
          .inflate(layout.enterprise_recycler_position_item, parent, false)
      )
    }
  }

  override fun onBindViewHolder(holder: SuperViewHolder, position: Int) {
    if (getItemViewType(position) == LAYOUT_TIPS) {
      val tips = _data[position] as JobPageTipsData
      val image = holder.findViewById<ImageView>(R.id.iv_vip_tips)
      Glide.with(holder.itemView).load(tips.vipImg.replace("https", "http")).into(image)
      image.setOnClickListener {
        _onVipTipsClickListener?.onVipTipsClicked(tips)
      }
    } else {
      val item = _data[position] as PositionItemBean
      val tvJobTag = holder.findViewById<TextView>(R.id.tv_job_tag)
      val candidateCount = item.toudiCount
      val tvCandidateCount = holder.findViewById<TextView>(R.id.tv_candidate_count)
      val ivVipJobTag = holder.findViewById<ImageView>(R.id.iv_vip_job_tag)
      if (item.isfufei == 1) {
        ivVipJobTag.visibility = View.VISIBLE
      } else {
        ivVipJobTag.visibility = View.GONE
      }
      if (candidateCount > 0) {
        tvCandidateCount.visibility = View.VISIBLE
        if (candidateCount > 99) {
          tvCandidateCount.text = "+99"
        } else {
          tvCandidateCount.text = "+${candidateCount}"
        }
      } else {
        tvCandidateCount.visibility = View.GONE
      }

      if (CheckUtils.isNullOrEmpty(item.tag)) {
        tvJobTag.visibility = View.GONE
      } else {
        tvJobTag.visibility = View.VISIBLE
        tvJobTag.text = item.tag
        var tagBgId = 0
        if (item.tag.equals("实")) {
          tagBgId = R.drawable.enterprise_bg_job_tag_shi
        } else if (item.tag.equals("兼")) {
          tagBgId = R.drawable.enterprise_bg_job_tag_jian
        } else if (item.tag.equals("灵")) {
          tagBgId = R.drawable.enterprise_bg_job_tag_ling
        }
        if (tagBgId != 0) {
          tvJobTag.background =
            ContextCompat.getDrawable(holder.itemView.context, tagBgId)
        }
      }

      val llCandidate = holder.findViewById<LinearLayout>(R.id.ll_candidate)
      val tvPositionTitle = holder.findViewById<TextView>(R.id.tv_position_title)
      val tvPositionAbout = holder.findViewById<TextView>(R.id.tv_position_about)
      val tvPositionCandidate = holder.findViewById<TextView>(R.id.tv_candidate)
      val tvOptionTwo = holder.findViewById<TextView>(R.id.tv_option_two)
      val tvOptionThree = holder.findViewById<TextView>(R.id.tv_option_three)
      val tvOptionFour = holder.findViewById<TextView>(R.id.tv_options_four)
      holder.findViewById<View>(R.id.iv_position_top).visibility =
        if (item.istop == 1) View.VISIBLE else View.GONE
      holder.findViewById<View>(R.id.iv_position_refreshing).visibility =
        if (item.refresh == 1) View.VISIBLE else View.GONE

      tvPositionTitle.text = item.name
      tvPositionAbout.text = item.positionAbout
      tvPositionCandidate.text = item.applyCount.toString()

      val tvJobType = holder.findViewById<TextView>(R.id.tv_type)
      tvJobType.visibility = if (item.isNormalRecruitment) View.GONE else View.VISIBLE
      tvJobType.text = item.typeText

      holder.setText(
        R.id.tv_follow,
        MTextUtils.fromHtml(
          holder.itemView.context.getString(
            R.string.position_follow,
            item.getCount()
          )
        )
      )

      val ivShare = holder.findViewById<ImageView>(R.id.iv_share)
      val llShareTips = holder.findViewById<LinearLayout>(R.id.ll_share_tips)
      llShareTips.setOnClickListener {
        llShareTips.visibility = View.GONE
        Once.markDone(OnceTag.JOB_LIST_SHOW_SHARE_TIPS)
      }

      ivShare.setOnClickListener {
        llShareTips.visibility = View.GONE
        Once.markDone(OnceTag.JOB_LIST_SHOW_SHARE_TIPS)
        _superItemClickListener?.onClick(it, position)
      }

      val tvUpgrade = holder.findViewById<TextView>(R.id.tv_upgrade_member)
      val vOptionsLine = holder.findViewById<View>(R.id.v_options_line)


      when (_pageType) {
        PositionListFragment.TAG_RUNNING -> {
          ivShare.visibility = View.VISIBLE
          tvOptionTwo.visibility = View.VISIBLE
          tvOptionTwo.setText(R.string.position_refresh)
          tvOptionThree.setText(R.string.position_top)
          tvOptionFour.setText(R.string.position_offline)
          if (position == 1) {
            if (item.isVip == 0) {
              tvUpgrade.visibility = View.VISIBLE
              vOptionsLine.visibility = View.VISIBLE
            }
            if (!Once.beenDone(
                TimeUnit.DAYS,
                7,
                OnceTag.JOB_LIST_SHOW_SHARE_TIPS
              )
            ) {
              llShareTips.visibility = View.VISIBLE
            } else {
              llShareTips.visibility = View.GONE
            }

            val scaleAnim = AnimationUtils.loadAnimation(holder.itemView.context, R.anim.anim_scale)
            holder.findViewById<ImageView>(R.id.iv_share).startAnimation(scaleAnim)
          } else {
            tvUpgrade.visibility = View.GONE
            vOptionsLine.visibility = View.GONE
            llShareTips.visibility = View.GONE
          }
        }

        PositionListFragment.TAG_END -> {
          tvOptionTwo.visibility = View.VISIBLE
          tvOptionTwo.setText(R.string.common_delete)
          tvOptionThree.setText(R.string.common_update)
          tvOptionFour.setText(R.string.enterprise_position_launce)
          MTextUtils.setTextColor(
            ContextCompat.getColor(holder.itemView.context, R.color.cl_888888),
            tvPositionTitle
          )
        }

        PositionListFragment.TAG_REVIEW -> {
          llCandidate.visibility = View.GONE
          holder.findViewById<View>(R.id.v_top_line).visibility = View.GONE
          tvPositionCandidate.visibility = View.GONE
          tvOptionThree.setText(R.string.common_update)
          tvOptionFour.setText(R.string.common_delete)
        }

        PositionListFragment.TAG_FAIL -> {
          llCandidate.visibility = View.GONE
          holder.findViewById<View>(R.id.v_top_line).visibility = View.GONE
          tvOptionThree.setText(R.string.common_update)
          tvOptionFour.setText(R.string.common_delete)
          MTextUtils.setTextColor(
            ContextCompat.getColor(holder.itemView.context, R.color.cl_888888),
            tvPositionTitle
          )
        }

        else -> {}
      }

      setOnChildClickListener(
        position, holder.itemView, tvOptionTwo, tvOptionThree, tvOptionFour,
        holder.findViewById(R.id.ll_candidate), tvUpgrade
      )
    }
  }

  private fun setOnChildClickListener(position: Int, vararg views: View) {
    for (itemView in views) {
      itemView.setOnClickListener { view: View ->
        _superItemClickListener?.onClick(view, position)
      }
    }
  }

  interface OnVipTipsClickListener {

    fun onVipTipsClicked(jobPageTipsData: JobPageTipsData)
  }

  override fun getItemViewType(position: Int): Int {
    return if (_data[position] is JobPageTipsData) LAYOUT_TIPS else LAYOUT_JOB
  }

  override fun getItemCount(): Int {
    return _data.size.getOrDefault()
  }
}