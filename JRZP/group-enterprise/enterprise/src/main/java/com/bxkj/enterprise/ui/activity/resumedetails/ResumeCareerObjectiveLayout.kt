package com.bxkj.enterprise.ui.activity.resumedetails

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.bxkj.common.util.HtmlUtils
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.ResumeCareerObjectiveData
import com.bxkj.enterprise.databinding.BLayoutResumeCareerObjectiveBinding

/**
 * author:Sanjin
 * date:2025/4/17
 **/
class ResumeCareerObjectiveLayout @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

  private val viewBinding =
    BLayoutResumeCareerObjectiveBinding.inflate(LayoutInflater.from(context), this, true)

  fun setResumeCareerObjective(resumeCareerObjective: ResumeCareerObjectiveData) {
    if (resumeCareerObjective.expectJobText.isNullOrBlank()) {
      viewBinding.tvExpectedCareer.visibility = GONE
    } else {
      viewBinding.tvExpectedCareer.visibility = VISIBLE
      viewBinding.tvExpectedCareer.setText(
        HtmlUtils.fromHtml(
          context.getString(
            R.string.resume_details_expected_career_format,
            resumeCareerObjective.expectJobText
          )
        )
      )
    }
    if (resumeCareerObjective.jobName2.isNullOrBlank()) {
      viewBinding.tvCareer.visibility = GONE
    } else {
      viewBinding.tvCareer.visibility = VISIBLE

      viewBinding.tvCareer.setText(
        HtmlUtils.fromHtml(
          context.getString(
            R.string.resume_details_career_format,
            resumeCareerObjective.jobName2
          )
        )
      )
    }
    if (resumeCareerObjective.jnName.isNullOrBlank()) {
      viewBinding.tvWorkNature.visibility = GONE
    } else {
      viewBinding.tvWorkNature.visibility = VISIBLE
      viewBinding.tvWorkNature.setText(
        HtmlUtils.fromHtml(
          context.getString(
            R.string.resume_details_work_nature_format,
            resumeCareerObjective.jnName
          )
        )
      )
    }
    if (resumeCareerObjective.addressText.isNullOrBlank()) {
      viewBinding.tvWorkAddress.visibility = GONE
    } else {
      viewBinding.tvWorkAddress.visibility = VISIBLE
      viewBinding.tvWorkAddress.setText(
        HtmlUtils.fromHtml(
          context.getString(
            R.string.resume_details_work_address_format,
            resumeCareerObjective.addressText
          )
        )
      )
    }
    if (resumeCareerObjective.daogangName.isNullOrBlank()) {
      viewBinding.tvAvailableTime.visibility = GONE
    } else {
      viewBinding.tvAvailableTime.visibility = VISIBLE
      viewBinding.tvAvailableTime.setText(
        HtmlUtils.fromHtml(
          context.getString(
            R.string.resume_details_available_time_format,
            resumeCareerObjective.daogangName
          )
        )
      )
    }

    if (resumeCareerObjective.hasAttachedResume()) {
      viewBinding.llAttachedResume.visibility = VISIBLE
    } else {
      viewBinding.llAttachedResume.visibility = GONE
    }
  }

  fun setOnViewAttachmentClickListener(listener: OnClickListener) {
    viewBinding.llAttachedResume.setOnClickListener(listener)
  }
}