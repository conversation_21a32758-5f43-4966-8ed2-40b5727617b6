package com.bxkj.enterprise.ui.activity.reportreason

import androidx.recyclerview.widget.DiffUtil

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/8
 * @version: V1.0
 */
data class EnterprsieReportReasonItem(
  var title: String,
  var desc: String
) {

  class DiffCallback : DiffUtil.ItemCallback<EnterprsieReportReasonItem>() {
    override fun areItemsTheSame(oldItemEnterprsie: EnterprsieReportReasonItem, newItemEnterprsie: EnterprsieReportReasonItem): Bo<PERSON>an {
      return oldItemEnterprsie == newItemEnterprsie
    }

    override fun areContentsTheSame(oldItemEnterprsie: EnterprsieReportReasonItem, newItemEnterprsie: EnterprsieReportReasonItem): Boolean {
      return oldItemEnterprsie.title == newItemEnterprsie.title
    }
  }
}