package com.bxkj.enterprise.data.source.datasource;

import com.bxkj.common.base.mvvm.callback.ResultListCallBack;
import com.bxkj.enterprise.data.SchoolRecruitItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data.source.datasource
 * @Description:
 * @TODO: TODO
 * @date 2019/6/13
 */
public interface SchoolRecruitListSource {

    void getSchoolRecruitList(int cityId, String title, String startDate, int pageIndex, int pageSize, ResultListCallBack<List<SchoolRecruitItemData>> callBack);
}
