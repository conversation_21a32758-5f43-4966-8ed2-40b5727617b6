package com.bxkj.enterprise.data.source

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.AESOperator
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.enterprise.api.BusinessApi
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.data.source.datasource
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/7/16
 * @version V1.0
 */
class LoginToComputerRepo @Inject constructor(businessApi: BusinessApi) : BaseRepo() {

  private val mEnterpriseApi = businessApi

  public fun loginToComputer(
    channel: String,
    userId: Int,
    userNameOrPhone: String,
    userType: Int,
    resultCallBack: ResultCallBack
  ) {
    mEnterpriseApi.scanQrCodeLogin(
      channel,
      AESOperator.safeEncrypt("$userId|$userNameOrPhone|$userType")
    )
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          resultCallBack.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          resultCallBack.onError(respondThrowable)
        }
      })
  }
}