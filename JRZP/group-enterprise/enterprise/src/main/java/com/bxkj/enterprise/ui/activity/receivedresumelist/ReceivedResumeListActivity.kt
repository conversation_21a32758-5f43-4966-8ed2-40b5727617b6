package com.bxkj.enterprise.ui.activity.receivedresumelist

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.therouter.router.Route
import com.bxkj.common.adapter.indicator.HasExpandTabIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.indicator.TAG_EXPAND_TAB
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.attachIndicator
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResArray
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxBus.Message
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.DropDownMenuView
import com.bxkj.common.widget.dropdown.DropDownPopup
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseActivityReceivedResumeListV2Binding
import com.bxkj.enterprise.ui.fragment.receivedresume.ReceivedResumeListFragmentV2
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

@Route(path = ReceivedResumeListNavigation.PATH)
class ReceivedResumeListActivity :
  BaseDBActivity<EnterpriseActivityReceivedResumeListV2Binding, ReceivedResumeViewModel>() {

  private var _jobListDropDownPopup: DropDownPopup? = null
  private var _jobListDropDownMenu: DropDownMenuView? = null

  private var _resumeStateFilterDropDownPopup: DropDownPopup? = null
  private var _indicatorAdapter: HasExpandTabIndicatorAdapter? = null

  override fun getViewModelClass(): Class<ReceivedResumeViewModel> =
    ReceivedResumeViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_activity_received_resume_list_v2

  override fun initPage(savedInstanceState: Bundle?) {

    statusBarManager.titleBar(viewBinding.flTitleBar).statusBarDarkFont(true, 0.4f).init()

    viewBinding.tvPositionName.setOnClickListener {
      _jobListDropDownPopup?.showAsDropDown()
    }

    viewBinding.ivBack.setOnClickListener {
      finish()
    }

    subscribeViewModelEvent()

    setupContentPager()

    setupJobListDropDownMenu()

    setupResumeStateFilterPopup()

    viewModel.start()
  }

  private fun subscribeViewModelEvent() {
    viewModel.jobList.observe(this) {
      _jobListDropDownMenu?.setData(it)
    }

    viewModel.switchJobCommand.observe(this, EventObserver {
      viewBinding.tvPositionName.text = it.name
      RxBus.get()
        .post(
          Message(
            RxMsgCode.REFRESH_RESUME_LISE_CODE,
            it.id
          )
        )
    })
  }

  private fun setupJobListDropDownMenu() {
    _jobListDropDownMenu = DropDownMenuView(this).apply {
      setOnItemClickListener { _, position ->
        _jobListDropDownPopup?.close()
        viewModel.switchJob(position)
      }
    }
    _jobListDropDownPopup = DropDownPopup(this, viewBinding.flTitleBar).apply {
      addContentViews(_jobListDropDownMenu)
    }
  }

  private fun setupResumeStateFilterPopup() {
    val inviteStates = getResArray(R.array.resume_invited_states)
    val inviteStateMenu = DropDownMenuView(this).apply {
      setData(inviteStates)
      setOnItemClickListener { view, position ->
        _indicatorAdapter?.updateTitleByPosition(inviteStates[position].getOrDefault(), 2)
        _resumeStateFilterDropDownPopup?.close()
        getCurrentShowChild()?.changeFilterState(position + 3)
      }
    }

    val offerStates = getResArray(R.array.resume_offer_states)
    val interviewStateMenu = DropDownMenuView(this).apply {
      setData(offerStates)
      setOnItemClickListener { view, position ->
        _indicatorAdapter?.updateTitleByPosition(offerStates[position].getOrDefault(), 3)
        _resumeStateFilterDropDownPopup?.close()
        getCurrentShowChild()?.changeFilterState(position + 7)
      }
    }

    _resumeStateFilterDropDownPopup = DropDownPopup(this, viewBinding.vContentSplit).apply {
      addContentViews(inviteStateMenu, interviewStateMenu)
    }
  }

  private fun getCurrentShowChild(): ReceivedResumeListFragmentV2? {
    val currentChildFragment =
      supportFragmentManager.findFragmentByTag("f${viewBinding.vpResumeContent.currentItem}")
    if (currentChildFragment is ReceivedResumeListFragmentV2) {
      return currentChildFragment
    }
    return null
  }

  private fun setupContentPager() {
    _indicatorAdapter =
      HasExpandTabIndicatorAdapter(getResArray(R.array.resume_status_options), 2, 3).apply {
        setOnTabClickListener(object : OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            if (<EMAIL>() == index && v.tag == TAG_EXPAND_TAB) {
              if (index == 2) {
                _resumeStateFilterDropDownPopup?.showItemAsDropDown(0)
              } else {
                _resumeStateFilterDropDownPopup?.showItemAsDropDown(1)
              }
            } else {
              _resumeStateFilterDropDownPopup?.dismiss()
            }
            viewBinding.vpResumeContent.currentItem = index
          }
        })
      }

    viewBinding.indicatorState.navigator = CommonNavigator(this).apply {
      adapter = _indicatorAdapter
      isAdjustMode = true
    }

    viewBinding.vpResumeContent.adapter = object : FragmentStateAdapter(this) {
      override fun getItemCount(): Int = 5

      override fun createFragment(position: Int): Fragment {
        return when (position) {
          0 -> {
            ReceivedResumeListFragmentV2.newInstance(ReceivedResumeListFragmentV2.TAG_RESUME_ALL)
          }

          1 -> {
            ReceivedResumeListFragmentV2.newInstance(ReceivedResumeListFragmentV2.TAG_RESUME_UNTREATED)
          }

          2 -> {
            ReceivedResumeListFragmentV2.newInstance(ReceivedResumeListFragmentV2.TAG_RESUME_INVITED)
          }

          3 -> {
            ReceivedResumeListFragmentV2.newInstance(ReceivedResumeListFragmentV2.TAG_RESUME_ACCEPTED)
          }

          4 -> {
            ReceivedResumeListFragmentV2.newInstance(ReceivedResumeListFragmentV2.TAG_RESUME_REFUSED)
          }

          else -> {
            ReceivedResumeListFragmentV2.newInstance(ReceivedResumeListFragmentV2.TAG_RESUME_ALL)
          }
        }
      }
    }

    viewBinding.vpResumeContent.attachIndicator(viewBinding.indicatorState)
  }
}