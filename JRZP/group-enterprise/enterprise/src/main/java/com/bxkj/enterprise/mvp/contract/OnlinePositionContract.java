package com.bxkj.enterprise.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

import com.bxkj.enterprise.data.PositionItemBean;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.mvp.contract
 * @Description: OnlinePosition
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface OnlinePositionContract {
    interface View extends BaseView {
        void getOnlinePositionSuccess(List<PositionItemBean> positionItemBeanList);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getOnlinePosition(int userId);
    }
}
