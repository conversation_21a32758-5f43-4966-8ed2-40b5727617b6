package com.bxkj.enterprise.data;

import android.location.Address;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.jrzp.support.feature.data.AddressItemData;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description:
 * @TODO: TODO
 * @date 2018/8/14
 */
public class ResumeCareerObjectiveData {

  /**
   * id : 24825 name : asp.net软件工程师 detailsName : 项目研发 wtName : jnid : 7 jnName : 全职 tradeid : 5
   * tradeName : jobid1 : 19 jobName1 : 计算机、互联网类 jobid2 : 23 jobName2 : 软件工程师 willMoney : 8000.00
   * sheng : 28009 shengName : 浙江 shi : 28590 shiName : 杭州 pingjia : daogang : 1 daogangName : 面谈
   */

  private int id;

  private String name;

  private String detailsName;

  private String wtName;

  private int jnid;

  private String jnName;

  private int tradeid;

  private String tradeName;

  private int jobid1;

  private String jobName1;

  private int jobid2;

  private String jobName2;

  private String willMoney;

  private int sheng;

  private String shengName;

  private int shi;

  private String shiName;

  private String zhenName;

  private String xiangName;

  private String pingjia;

  private int daogang;

  private String daogangName;

  //0:公开，允许下载 1：公开，禁止下载 2：不公开
  private int state;

  private int kzid;

  private String editTime;

  private String resfj;

  private List<AddressItemData> area;

  public List<AddressItemData> getArea() {
    return area;
  }

  public void setArea(List<AddressItemData> area) {
    this.area = area;
  }

  public boolean hasAttachedResume() {
    return resfj != null && !resfj.isEmpty();
  }

  public String getResfj() {
    return resfj;
  }

  public void setResfj(final String resfj) {
    this.resfj = resfj;
  }

  public String getEditTime() {
    return editTime;
  }

  public void setEditTime(String editTime) {
    this.editTime = editTime;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDetailsName() {
    return detailsName;
  }

  public void setDetailsName(String detailsName) {
    this.detailsName = detailsName;
  }

  public String getWtName() {
    return wtName;
  }

  public void setWtName(String wtName) {
    this.wtName = wtName;
  }

  public int getJnid() {
    return jnid;
  }

  public void setJnid(int jnid) {
    this.jnid = jnid;
  }

  public String getJnName() {
    return jnName;
  }

  public void setJnName(String jnName) {
    this.jnName = jnName;
  }

  public int getTradeid() {
    return tradeid;
  }

  public void setTradeid(int tradeid) {
    this.tradeid = tradeid;
  }

  public String getTradeName() {
    return tradeName;
  }

  public void setTradeName(String tradeName) {
    this.tradeName = tradeName;
  }

  public int getJobid1() {
    return jobid1;
  }

  public void setJobid1(int jobid1) {
    this.jobid1 = jobid1;
  }

  public String getJobName1() {
    return jobName1;
  }

  public void setJobName1(String jobName1) {
    this.jobName1 = jobName1;
  }

  public int getJobid2() {
    return jobid2;
  }

  public void setJobid2(int jobid2) {
    this.jobid2 = jobid2;
  }

  public String getJobName2() {
    return jobName2;
  }

  public void setJobName2(String jobName2) {
    this.jobName2 = jobName2;
  }

  public String getWillMoney() {
    return willMoney;
  }

  public void setWillMoney(String willMoney) {
    this.willMoney = willMoney;
  }

  public int getSheng() {
    return sheng;
  }

  public void setSheng(int sheng) {
    this.sheng = sheng;
  }

  public String getShengName() {
    return shengName;
  }

  public void setShengName(String shengName) {
    this.shengName = shengName;
  }

  public int getShi() {
    return shi;
  }

  public void setShi(int shi) {
    this.shi = shi;
  }

  public String getShiName() {
    return shiName;
  }

  public void setShiName(String shiName) {
    this.shiName = shiName;
  }

  public String getPingjia() {
    return pingjia;
  }

  public void setPingjia(String pingjia) {
    this.pingjia = pingjia;
  }

  public int getDaogang() {
    return daogang;
  }

  public void setDaogang(int daogang) {
    this.daogang = daogang;
  }

  public String getDaogangName() {
    return daogangName;
  }

  public void setDaogangName(String daogangName) {
    this.daogangName = daogangName;
  }

  public int getState() {
    return state;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getKzid() {
    return kzid;
  }

  public void setKzid(int kzid) {
    this.kzid = kzid;
  }

  public boolean isOpened() {
    return state == 0;
  }

  public String getZhenName() {
    return zhenName;
  }

  public void setZhenName(String zhenName) {
    this.zhenName = zhenName;
  }

  public String getXiangName() {
    return xiangName;
  }

  public void setXiangName(String xiangName) {
    this.xiangName = xiangName;
  }

  public String getExpectJobText() {
    final StringBuilder stringBuilder = new StringBuilder();
    if (!CheckUtils.isNullOrEmpty(jobName1)) {
      stringBuilder.append(jobName1).append("/");
    }
    if (!CheckUtils.isNullOrEmpty(jobName2)) {
      stringBuilder.append(jobName2);
    }
    return stringBuilder.toString();
  }

  public String getAddressText() {
    final StringBuilder addressBuilder = new StringBuilder();
    if (area == null || area.isEmpty()) {
      if (!CheckUtils.isNullOrEmpty(shengName)) {
        addressBuilder.append(shengName);
      }
      if (!CheckUtils.isNullOrEmpty(shiName) && !shiName.equals(shengName)) {
        addressBuilder.append("/").append(shiName);
      }
      if (!CheckUtils.isNullOrEmpty(zhenName)) {
        addressBuilder.append("/").append(zhenName);
      }
      if (!CheckUtils.isNullOrEmpty(xiangName)) {
        addressBuilder.append("/").append(xiangName);
      }
    } else {
      for (int i = 0; i < area.size(); i++) {
        AddressItemData item = area.get(i);
        addressBuilder.append(item.getProvinceName());

        StringBuilder countyBuilder = new StringBuilder();
        //countyName为空时，表示全city 为全市
        if (CheckUtils.isNullOrEmpty(item.getCountyName())) {
          countyBuilder.append("/").append("全").append(item.getCityName());
        } else {
          if (!item.getProvinceName().equals(item.getCityName())) {
            addressBuilder.append("/").append(item.getCityName());
          }
          countyBuilder.append("/").append(item.getCountyName());
        }
        for (int j = i + 1; j < area.size(); j++) {
          AddressItemData compareItem = area.get(j);
          if (item.getProvinceName().equals(compareItem.getProvinceName()) && item.getCityName()
            .equals(compareItem.getCityName())) {
            if (!item.getCountyName().equals(compareItem.getCountyName())) {
              countyBuilder.append("/").append(compareItem.getCountyName());
            }
            area.remove(j);
            j--;
          }
        }
        addressBuilder.append(countyBuilder).append(" ");
      }
    }
    return addressBuilder.toString();
  }
}
