package com.bxkj.enterprise.ui.activity.invitationinterview

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.enterprise.data.CompanyAddressData
import com.bxkj.enterprise.data.HrItemData
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.enterprise.data.source.MyJobRepo
import com.bxkj.jrzp.support.chat.api.ChatRepo
import com.bxkj.jrzp.support.chat.data.InterviewInfoBean
import kotlinx.coroutines.launch
import java.time.LocalDate
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
class InviteInterviewViewModelV2 @Inject constructor(
  private val _myJobRepo: MyJobRepo,
  private val _chatRepo: ChatRepo
) : BaseViewModel() {

  val inviteUserName = MutableLiveData<String>()

  val remarkMaxLength = MutableLiveData<Int>().apply { value = 140 }

  val showJobListDialogCommand = MutableLiveData<VMEvent<Int>>()
  val toEditInterviewAddressCommand = MutableLiveData<VMEvent<CompanyAddressData>>()
  val showHRListDialogCommand = MutableLiveData<VMEvent<Int>>()

  val inviteSuccessEvent = MutableLiveData<VMEvent<Unit>>()

  private var _interviewAddress: CompanyAddressData? = null
  val interviewAddress = MutableLiveData<CompanyAddressData>()

  val interviewInfo = MutableLiveData<InterviewInfoBean>().apply { value = InterviewInfoBean() }

  private var _interviewTime: String = ""
  private var _interviewDate: String = ""

  val interviewTime = MutableLiveData<String>()
  val interviewDate = MutableLiveData<String>()

  private var _resumeID: Int = 0
  private var _interviewId: Int = 0

  fun setPageParams(jobID: Int, resumeID: Int, inviteUserName: String?, interviewId: Int) {
    this._resumeID = resumeID
    this.inviteUserName.value = inviteUserName
    this._interviewId = interviewId
    if (interviewId != 0) {
      checkHasInterviewInfo(jobID, resumeID)
    } else {
      getJobInfo(jobID)
    }
  }

  fun showJobListDialog() {
    interviewInfo.value?.let {
      showJobListDialogCommand.value = VMEvent(it.relId)
    }
  }

  fun toEditInterviewAddress() {
    toEditInterviewAddressCommand.value = VMEvent(_interviewAddress ?: CompanyAddressData())
  }

  fun showHRListDialog() {
    interviewInfo.value?.let {
      showHRListDialogCommand.value = VMEvent(it.hrId)
    }
  }

  fun updateInterviewJobInfo(jobInfo: PositionItemBean) {
    interviewInfo.value?.apply {
      updateJobInfo(jobInfo)
    }
  }

  fun updateInterviewAddress(companyAddressData: CompanyAddressData) {
    _interviewAddress = companyAddressData
    interviewAddress.value = _interviewAddress ?: CompanyAddressData()
  }

  fun updateInterviewHR(selected: HrItemData) {
    interviewInfo.value?.apply {
      updateContact(selected.id, selected.name, selected.mobile)
    }
  }

  fun updateInterviewTime(time: String) {
    _interviewTime = time
    interviewTime.value = time
  }

  fun updateInterviewDate(interviewDate: String) {
    _interviewDate = interviewDate
    this.interviewDate.value = interviewDate
  }

  fun clearRemark() {
    interviewInfo.value?.otherRemark = ""
  }

  //发送邀请
  fun sendRequest() {
    interviewInfo.value?.let { interviewInfo ->
      if (interviewInfo.relId == 0) {
        showToast("请选择职位");
        return
      }
      if (_interviewAddress == null) {
        showToast("请选择面试地址")
        return
      }
      if (interviewInfo.jiLxr.isEmpty() || interviewInfo.jiPhone.isEmpty()) {
        showToast("请选择联系人")
        return
      }
      if (_interviewTime.isEmpty()) {
        showToast("请选择面试时间")
        return
      }
      if (_interviewDate.isEmpty()) {
        _interviewDate = LocalDate.now().toString()
      }
      viewModelScope.launch {
        showLoading()
        _myJobRepo.inviteInterview(
          getSelfUserID(),
          interviewInfo.relId.getOrDefault(),
          _resumeID,
          "$_interviewDate $_interviewTime",
          _interviewAddress?.getCityAreaAddressText().getOrDefault(),
          interviewInfo.jiLxr.getOrDefault(),
          interviewInfo.jiPhone.getOrDefault(),
          true,
          interviewInfo.otherRemark.getOrDefault(),
          cityId = _interviewAddress?.cityId.getOrDefault(),
          areaId = _interviewAddress?.countyId.getOrDefault(),
          address = _interviewAddress?.address.getOrDefault(),
          interviewId = _interviewId
        ).handleResult({
          if (_interviewId > 0) {
            showToast("修改成功")
          } else {
            showToast("邀请成功")
          }
          inviteSuccessEvent.value = VMEvent(Unit)
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  private fun checkHasInterviewInfo(jobID: Int, resumeID: Int) {
    viewModelScope.launch {
      showLoading()
      _chatRepo.getInterviewInfo(jobID, resumeID, AppConstants.BUSINESS_TYPE)
        .handleResult({
          it?.let {
            interviewInfo.value = it
            updateInterviewAddress(
              CompanyAddressData().apply {
                cityId = it.shi
                cityName = it.shiName
                countyId = it.qu
                countyName = it.quName
                address = it.address
              }
            )
            updateInterviewDate(it.getInterviewDate())
            updateInterviewTime(it.getInterviewTime())
          }
        }, {
          interviewInfo.value = InterviewInfoBean()
          getJobInfo(jobID)
        }, {
          hideLoading()
        })
    }
  }

  private fun getJobInfo(jobID: Int) {
    viewModelScope.launch {
      showLoading()
      _myJobRepo.getOnlineJobInfo(getSelfUserID(), jobID)
        .handleResult({
          it?.let {
            updateInterviewJobInfo(it)
            updateInterviewAddress(
              CompanyAddressData().apply {
                cityId = it.shi
                cityName = it.shiName
                countyId = it.qu
                countyName = it.quName
                address = it.address
              }
            )
            updateInterviewHR(HrItemData().apply {
              id = it.hrid
              name = it.hrLxr
              mobile = it.hrMobile
            })
          }
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }
}