package com.bxkj.enterprise.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description:
 * @TODO: TODO
 * @date 2019/8/8
 */
public class IntegralTaskItemData {

    /**
     * id : 1
     * name : 完善基本资料（包括上传头像）
     * content :
     * integral : 5
     * url : /CompanyCenter/BasicInfo.aspx
     * px : 0
     * record : 10609
     */

    private int id;
    private String name;
    private String content;
    private int integral;
    private String url;
    private int px;
    private int record;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getIntegral() {
        return integral;
    }

    public void setIntegral(int integral) {
        this.integral = integral;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getPx() {
        return px;
    }

    public void setPx(int px) {
        this.px = px;
    }

    public int getRecord() {
        return record;
    }

    public void setRecord(int record) {
        this.record = record;
    }
}
