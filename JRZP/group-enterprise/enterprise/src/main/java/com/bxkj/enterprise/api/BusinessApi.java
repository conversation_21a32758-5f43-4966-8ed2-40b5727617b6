package com.bxkj.enterprise.api;

import com.bxkj.common.network.ZPRequestBody;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.data.EncryptReqParams;
import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.data.ShareInfoData;
import com.bxkj.common.data.UploadFileRequestParams;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.ecommon.data.HasStateOnlinePositionData;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.util.AESOperator;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.enterprise.api.parameters.SearchResumeParameters;
import com.bxkj.enterprise.api.parameters.GetReceivedResumeListParameters;
import com.bxkj.enterprise.data.AboutUsData;
import com.bxkj.enterprise.data.JobPageTipsData;
import com.bxkj.enterprise.data.PositionItemBean;
import com.bxkj.jrzp.user.data.AccountVipData;
import com.bxkj.enterprise.data.AppVersionData;
import com.bxkj.enterprise.data.CertificateData;
import com.bxkj.enterprise.data.CompanyInfoDataV2;
import com.bxkj.enterprise.data.CompanyStyleItemData;
import com.bxkj.enterprise.data.CustomServiceData;
import com.bxkj.enterprise.data.DailyTaskData;
import com.bxkj.enterprise.data.EnterpriseInfoData;
import com.bxkj.enterprise.data.EnterpriseUseCountData;
import com.bxkj.enterprise.data.FacultyRecruitItemData;
import com.bxkj.enterprise.data.FacultyTypeItemData;
import com.bxkj.enterprise.data.GreetBalanceData;
import com.bxkj.enterprise.data.HomeBannerData;
import com.bxkj.enterprise.data.HrItemData;
import com.bxkj.enterprise.data.IntegralRightsItemData;
import com.bxkj.enterprise.data.IntegralTaskItemData;
import com.bxkj.enterprise.data.InviteRecordItemData;
import com.bxkj.enterprise.data.InvoiceData;
import com.bxkj.enterprise.data.JobContentData;
import com.bxkj.enterprise.data.JobTemplateItemData;
import com.bxkj.enterprise.data.MemberServicesData;
import com.bxkj.enterprise.data.MessageItemData;
import com.bxkj.enterprise.data.NewsData;
import com.bxkj.enterprise.data.OrderItemData;
import com.bxkj.enterprise.data.PositionAutoRefreshData;
import com.bxkj.enterprise.data.PositionPreviewData;
import com.bxkj.enterprise.data.PositionTopCityItemData;
import com.bxkj.enterprise.data.PositionTopCostInfo;
import com.bxkj.enterprise.data.PositionTopData;
import com.bxkj.enterprise.data.ProductItemData;
import com.bxkj.enterprise.data.RechargeIntegralItemData;
import com.bxkj.enterprise.data.RecommendResumeTypeData;
import com.bxkj.enterprise.data.ResumeCareerObjectiveData;
import com.bxkj.enterprise.data.ResumeCertificateData;
import com.bxkj.enterprise.data.ResumeEduBackgroundData;
import com.bxkj.enterprise.data.ResumeItemData;
import com.bxkj.enterprise.data.ResumeLanguageData;
import com.bxkj.enterprise.data.ResumePersonalData;
import com.bxkj.enterprise.data.ResumeReceiveMailboxItemData;
import com.bxkj.enterprise.data.ResumeSchoolSituationData;
import com.bxkj.enterprise.data.ResumeSkillData;
import com.bxkj.common.data.ResumeStateData;
import com.bxkj.enterprise.data.ResumeWorkExpData;
import com.bxkj.enterprise.data.SchoolDetailsData;
import com.bxkj.enterprise.data.SchoolJobFairItemData;
import com.bxkj.enterprise.data.SchoolRecruitDetailsData;
import com.bxkj.enterprise.data.SchoolRecruitItemData;
import com.bxkj.enterprise.data.SchoolRecruitmentItemData;
import com.bxkj.enterprise.data.SignUpUserItemData;
import com.bxkj.enterprise.data.TeamItemData;
import com.bxkj.enterprise.data.UserCenterData;
import com.bxkj.enterprise.data.VideoUnreadMsgItemData;
import com.bxkj.enterprise.data.WelfareItemData;
import com.bxkj.enterprise.data.ResumeBaseInfoData;
import com.bxkj.jrzp.user.data.UserVipBalanceData;
import com.bxkj.video.VideoType;
import com.bxkj.video.data.OnlineVideoData;
import com.bxkj.video.data.VideoData;
import com.google.gson.Gson;

import java.io.File;
import java.util.List;

import io.reactivex.Observable;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Retrofit;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.api
 * @Description:
 * @TODO: TODO
 * @date 2018/7/17
 */
public class BusinessApi {

  private EnterpriseServices mEnterpriseServices;

  public BusinessApi(Retrofit retrofit) {
    mEnterpriseServices = retrofit.create(EnterpriseServices.class);
  }

  /**
   * 职位页Vip提示
   * @return
   */
  public Observable<BaseResponse<JobPageTipsData>> getJobPageVipTips() {
    return mEnterpriseServices.getJobPageVipTips();
  }

  /**
   * 上传资讯文件
   */
  public Observable<BaseResponse> uploadFile(String filePath,
      UploadFileRequestParams uploadFileRequestParams) {
    File file = new File(filePath);
    RequestBody fileBody = RequestBody.create(MediaType.parse("multipart/form-data"), file);
    MultipartBody mRequestBody = new MultipartBody.Builder()
        .setType(MultipartBody.FORM)
        .addFormDataPart("imgFile", CheckUtils.encode(file.getName()), fileBody)
        .addFormDataPart("action", "uploadFile2")
        .addFormDataPart("para",
            AESOperator.safeEncrypt(new Gson().toJson(uploadFileRequestParams)))
        .build();
    return mEnterpriseServices.uploadAttachmentResume(
        EnterpriseApiConstants.I_OPERATION_ATTACHMENT_RESUME, mRequestBody);
  }

  /**
   * 根据多条件获取职位列表
   *
   * @param positionStates 0待审核,1未通过,2未上线,3在线中,4已下线
   */
  public Observable<BaseResponse<List<PositionItemBean>>> getPositionListByMultiple(int userId,
      int type, String positionTitle, int positionStates, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("name", positionTitle);
    ZPRequestBody.put("state", positionStates);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getPositionListByMultiple(ZPRequestBody);
  }

  /**
   * 修改职位状态
   *
   * @param positionIds 0待审核,1未通过,2未上线,3在线中,4已下线
   */
  public Observable<BaseResponse> updatePositionState(int userId, String positionIds, int state) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("relids", positionIds);
    ZPRequestBody.put("state", state);
    return mEnterpriseServices.updatePositionState(ZPRequestBody);
  }

  /**
   * 刪除职位
   */
  public Observable<BaseResponse> deletePosition(int userId, int positionId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", positionId);
    return mEnterpriseServices.deletePosition(ZPRequestBody);
  }

  /**
   * 获取职位置顶信息
   */
  public Observable<BaseResponse<PositionTopData>> getPositionTopInfo(int userId, int positionId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", positionId);
    return mEnterpriseServices.getPositionTopInfo(ZPRequestBody);
  }

  /**
   * 获取职位置顶花费
   */
  public Observable<BaseResponse<List<PositionTopCostInfo>>> getPositionTopCost(int userId,
      int location) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("lanmu", 2);
    ZPRequestBody.put("location", location);
    return mEnterpriseServices.getPositionTopCost(EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 职位置顶
   */
  public Observable<BaseResponse> positionTop(int uid, int location, int positionId, int days) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", uid);
    ZPRequestBody.put("location", location);
    ZPRequestBody.put("relid", positionId);
    ZPRequestBody.put("days", days);
    return mEnterpriseServices.positionTop(ZPRequestBody);
  }

  /**
   * 获取行业列表
   */
  public Observable<BaseResponse<List<PickerOptionsData>>> getIndustryList() {
    return mEnterpriseServices.getIndustryList();
  }

  /**
   * 获取公司性质
   */
  public Observable<BaseResponse<List<PickerOptionsData>>> getNatureOfCompany() {
    return mEnterpriseServices.getNatureOfCompany();
  }

  /**
   * 获取企业信息
   */
  public Observable<BaseResponse<EnterpriseInfoData>> getEnterpriseInfo(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getEnterpriseInfo(ZPRequestBody);
  }

  /**
   * 修改企业信息
   */
  public Observable<BaseResponse> updateEnterpriseInfo(EnterpriseInfoData enterpriseInfoData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", enterpriseInfoData.getUid());
    ZPRequestBody.put("lxr", enterpriseInfoData.getLxr());
    ZPRequestBody.put("phone", enterpriseInfoData.getPhone());
    ZPRequestBody.put("name", enterpriseInfoData.getName());
    ZPRequestBody.put("province", enterpriseInfoData.getProvince());
    ZPRequestBody.put("city", enterpriseInfoData.getCity());
    ZPRequestBody.put("county", enterpriseInfoData.getCounty());
    ZPRequestBody.put("town", enterpriseInfoData.getTown());
    ZPRequestBody.put("address", enterpriseInfoData.getAddress());
    ZPRequestBody.put("Info", enterpriseInfoData.getInfo());
    ZPRequestBody.put("name2", enterpriseInfoData.getName2());
    ZPRequestBody.put("tradeid", enterpriseInfoData.getTradeid());
    ZPRequestBody.put("proid", enterpriseInfoData.getProid());
    ZPRequestBody.put("sizeid", enterpriseInfoData.getSizeid());
    ZPRequestBody.put("coordinate", enterpriseInfoData.getCoordinate());
    return mEnterpriseServices.updateEnterpriseInfo(EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 获取企业使用次数
   */
  public Observable<BaseResponse<EnterpriseUseCountData>> getEnterpriseUseCount(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getEnterpriseUseCount(ZPRequestBody);
  }

  /**
   * 获取推荐推荐简历分类列表
   */
  public Observable<BaseResponse<List<RecommendResumeTypeData>>> getRecommendResumeTypeList(
      int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getRecommendResumeTypeList(ZPRequestBody);
  }

  /**
   * 获取简历列表
   */
  public Observable<BaseResponse<List<ResumeItemData>>> getResumeList(int userId,
      SearchResumeParameters searchResumeParameters, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", 0);
    ZPRequestBody.put("myUid", userId);
    ZPRequestBody.put("sex", searchResumeParameters.getSex());
    ZPRequestBody.put("wtid", searchResumeParameters.getWorkExpId());
    ZPRequestBody.put("quaid", searchResumeParameters.getEducationId());
    ZPRequestBody.put("jnid", searchResumeParameters.getWorkNatureId());
    ZPRequestBody.put("tradeid", searchResumeParameters.getIndustryId());
    ZPRequestBody.put("jobType1", searchResumeParameters.getFirstClassId());
    ZPRequestBody.put("jobType2", searchResumeParameters.getSecondClassId());
    ZPRequestBody.put("province", searchResumeParameters.getProvince());
    ZPRequestBody.put("city", searchResumeParameters.getCity());
    ZPRequestBody.put("county", searchResumeParameters.getCounty());
    ZPRequestBody.put("town", searchResumeParameters.getTown());
    ZPRequestBody.put("name", searchResumeParameters.getTitle());
    ZPRequestBody.put("minWillMoney", searchResumeParameters.getMinWillMoney());
    ZPRequestBody.put("maxWillMoney", searchResumeParameters.getMaxWillMoney());
    ZPRequestBody.put("jobType2s", searchResumeParameters.getFilterResumeTypes());
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getResumeList(EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 获取用户积余额、查看次数等信息
   */
  public Observable<BaseResponse<AccountVipData>> getAccountVipInfo(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getAccountVipInfo(EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 获取刷新一次职位所需积分
   */
  public Observable<BaseResponse> getIntegralForRefreshPosition(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getIntegralForRefreshPosition(ZPRequestBody);
  }

  /**
   * 获取职位智能刷新信息
   */
  public Observable<BaseResponse<PositionAutoRefreshData>> getPositionRefreshInfo(int userId,
      int positionId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("relids", positionId);
    return mEnterpriseServices.getPositionRefreshInfo(ZPRequestBody);
  }

  /**
   * 获取刷新职位所需次数或积分
   */
  public Observable<BaseResponse> getIntegralCountForRefresh(int userId, int positionId, int payment,
      String dates, String times) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("relids", positionId);
    ZPRequestBody.put("payment", payment);
    ZPRequestBody.put("dates", dates);
    ZPRequestBody.put("times", times);
    return mEnterpriseServices.getIntegralForRefresh(ZPRequestBody);
  }

  /**
   * 刷新职位
   *
   * @param userId      用户id
   * @param positionIds 职位id
   * @param payment     支付方式
   * @param refreshWay  刷新方式
   * @param dates       选中日期
   * @param times       选中时间段
   */
  public Observable<BaseResponse> refreshPosition(int userId, String positionIds, int payment,
      int refreshWay, String dates, String times) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("relids", positionIds);
    ZPRequestBody.put("payment", payment);
    ZPRequestBody.put("lx", refreshWay);
    ZPRequestBody.put("dates", dates);
    ZPRequestBody.put("times", times);
    return mEnterpriseServices.refreshPosition(ZPRequestBody);
  }

  /**
   * 修改职位
   */
  public Observable<BaseResponse> updateJob(int userId, JobContentData jobContentData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", jobContentData.getType());
    ZPRequestBody.put("name", jobContentData.getName());
    ZPRequestBody.put("type1", jobContentData.getType1());
    ZPRequestBody.put("type2", jobContentData.getType2());
    ZPRequestBody.put("id", jobContentData.getId());
    ZPRequestBody.put("jnid", jobContentData.getOldjnid());
    ZPRequestBody.put("reljn", jobContentData.getNatureId());
    ZPRequestBody.put("number", jobContentData.getNumber());
    ZPRequestBody.put("des", jobContentData.getDes());
    ZPRequestBody.put("money", jobContentData.getMoney());
    ZPRequestBody.put("exp", jobContentData.getExp());
    ZPRequestBody.put("qua", jobContentData.getQua());
    ZPRequestBody.put("sheng", jobContentData.getSheng());
    ZPRequestBody.put("shi", jobContentData.getShi());
    ZPRequestBody.put("qu", jobContentData.getQu());
    ZPRequestBody.put("jie", jobContentData.getJie());
    ZPRequestBody.put("address", jobContentData.getAddress());
    ZPRequestBody.put("lng", jobContentData.getLng());
    ZPRequestBody.put("lat", jobContentData.getLat());
    ZPRequestBody.put("hr", jobContentData.getHr());
    return mEnterpriseServices.updateJob(ZPRequestBody);
  }

  /**
   * 检查职位名称是否热门
   */
  public Observable<BaseResponse> checkJobNameIsHot(int userId, String jobName) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("name", jobName);
    return mEnterpriseServices.checkJobNameIsHot(ZPRequestBody);
  }

  /**
   * 获取职位详情
   */
  public Observable<BaseResponse<JobContentData>> getJobInfo(int userId, int id) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", id);
    return mEnterpriseServices.getJobInfo(ZPRequestBody);
  }

  /**
   * 获取职位预览详情
   */
  public Observable<BaseResponse<PositionPreviewData>> getPositionPreviewInfo(int positionId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", positionId);
    return mEnterpriseServices.getPositionPreviewInfo(ZPRequestBody);
  }

  /**
   * 获取职位模板列表
   */
  public Observable<BaseResponse<List<JobTemplateItemData>>> getJobTemplateList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getJobTemplateList(ZPRequestBody);
  }

  /**
   * 获取福利列表
   */
  public Observable<BaseResponse<List<WelfareItemData>>> getWelfareList() {
    return mEnterpriseServices.getWelfareList();
  }

  /**
   * 批量新增職位福利
   *
   * @param type 類型1、職位2、模板
   */
  public Observable<BaseResponse> addJobWelfare(int userId, int type, int jobOrTemplateId,
      String welIds, String otherWelfare) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("lx", type);
    ZPRequestBody.put("relid", jobOrTemplateId);
    ZPRequestBody.put("welids", welIds);
    ZPRequestBody.put("welNames", otherWelfare);
    return mEnterpriseServices.addJobWelfare(ZPRequestBody);
  }

  /**
   * 获取简历接收邮箱列表
   */
  public Observable<BaseResponse<List<ResumeReceiveMailboxItemData>>> getResumeReceiveMailboxList(
      int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getResumeReceiveMailboxList(ZPRequestBody);
  }

  /**
   * 添加简历接收邮箱
   */
  public Observable<BaseResponse> addResumeReceiveMailbox(int jobOrTemplate, int jobOrTemplateId,
      String resumeReceiveMailboxs) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("lx", jobOrTemplate);
    ZPRequestBody.put("relid", jobOrTemplateId);
    ZPRequestBody.put("email", resumeReceiveMailboxs);
    return mEnterpriseServices.addResumeReceiveMailbox(ZPRequestBody);
  }

  /**
   * 获取hr列表
   */
  public Observable<BaseResponse<List<HrItemData>>> getHrList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getHrList(ZPRequestBody);
  }

  /**
   * 添加hr
   */
  public Observable<BaseResponse> addHr(int userId, HrItemData hrItemData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.putAndAes("uid1", userId);
    ZPRequestBody.putAndAes("Usex", 0);
    ZPRequestBody.put("photo", hrItemData.getPhoto());
    ZPRequestBody.putAndAes("name", hrItemData.getName());
    ZPRequestBody.putAndAes("mobile", hrItemData.getMobile());
    ZPRequestBody.putAndAes("email", hrItemData.getEmail());
    ZPRequestBody.putAndAes("position", hrItemData.getPosition());
    return mEnterpriseServices.addHr(ZPRequestBody);
  }

  /**
   * 修改hr
   */
  public Observable<BaseResponse> updateHr(int userId, HrItemData hrItemData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.putAndAes("uid1", userId);
    ZPRequestBody.putAndAes("id1", hrItemData.getId());
    ZPRequestBody.putAndAes("Usex", 0);
    ZPRequestBody.put("photo", hrItemData.getPhoto());
    ZPRequestBody.putAndAes("name", hrItemData.getName());
    ZPRequestBody.putAndAes("mobile", hrItemData.getMobile());
    ZPRequestBody.putAndAes("email", hrItemData.getEmail());
    ZPRequestBody.putAndAes("position", hrItemData.getPosition());
    return mEnterpriseServices.updateHr(ZPRequestBody);
  }

  /**
   * 删除hr
   */
  public Observable<BaseResponse> deleteHr(int userId, int hrId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", hrId);
    return mEnterpriseServices.deleteHr(ZPRequestBody);
  }

  /**
   * 获取用户中心数据
   */
  public Observable<BaseResponse<UserCenterData>> getUserCenterInfo(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getUserCenterInfo(ZPRequestBody);
  }

  /**
   * 获取公司信息
   */
  public Observable<BaseResponse<CompanyInfoDataV2>> getCompanyInfo(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getCompanyInfo(ZPRequestBody);
  }

  /**
   * 根据公司id获取公司名称
   */
  public Observable<BaseResponse> getCompanyNameById(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getCompanyNameById(ZPRequestBody);
  }

  /**
   * 修改公司信息
   *
   * @param userId          用户id
   * @param companyInfoData 公司资料
   */
  public Observable<BaseResponse> updateCompanyInfo(int userId, CompanyInfoDataV2 companyInfoData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.putAndAes("jmuid", userId);
    ZPRequestBody.putAndAes("name", companyInfoData.getName());
    ZPRequestBody.putAndAes("name2", companyInfoData.getName2());
    ZPRequestBody.put("logo", companyInfoData.getLogo());
    ZPRequestBody.putAndAes("jmprovince", companyInfoData.getProvince());
    ZPRequestBody.putAndAes("jmcity", companyInfoData.getCity());
    ZPRequestBody.putAndAes("jmcounty", companyInfoData.getCounty());
    ZPRequestBody.putAndAes("jmtown", companyInfoData.getTown());
    ZPRequestBody.putAndAes("address", companyInfoData.getAddress());
    ZPRequestBody.putAndAes("coordinate", companyInfoData.getCoordinate());
    ZPRequestBody.putAndAes("Info", companyInfoData.getInfo());
    ZPRequestBody.putAndAes("jmtradeid", companyInfoData.getTradeid());
    ZPRequestBody.putAndAes("jmproid", companyInfoData.getProid());
    ZPRequestBody.putAndAes("jmsizeid", companyInfoData.getSizeid());
    ZPRequestBody.putAndAes("traffic", companyInfoData.getTraffic());
    ZPRequestBody.putAndAes("url", companyInfoData.getUrl());
    return mEnterpriseServices.updateCompanyInfo(ZPRequestBody);
  }

  /**
   * 获取公司产品列表
   */
  public Observable<BaseResponse<List<ProductItemData>>> getProductsList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getProductsList(ZPRequestBody);
  }

  /**
   * 添加公司产品
   */
  public Observable<BaseResponse> addCompanyProduct(int userId, ProductItemData productItemData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("name", productItemData.getName());
    ZPRequestBody.put("pic", productItemData.getPic());
    ZPRequestBody.put("lx", productItemData.getLx());
    ZPRequestBody.put("url", productItemData.getUrl());
    ZPRequestBody.put("content", productItemData.getContent());
    return mEnterpriseServices.addCompanyProduct(ZPRequestBody);
  }

  /**
   * 修改公司产品
   */
  public Observable<BaseResponse> updateCompanyProduct(int userId, ProductItemData productItemData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", productItemData.getId());
    ZPRequestBody.put("name", productItemData.getName());
    ZPRequestBody.put("pic", productItemData.getPic());
    ZPRequestBody.put("lx", productItemData.getLx());
    ZPRequestBody.put("url", productItemData.getUrl());
    ZPRequestBody.put("content", productItemData.getContent());
    return mEnterpriseServices.updateCompanyProduct(ZPRequestBody);
  }

  /**
   * 删除公司产品
   */
  public Observable<BaseResponse> deleteCompanyProduct(int userId, int productId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", productId);
    return mEnterpriseServices.deleteCompanyProduct(ZPRequestBody);
  }

  /**
   * 获取公司风采
   */
  public Observable<BaseResponse<List<CompanyStyleItemData>>> getCompanyStyle(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getCompanyStyle(ZPRequestBody);
  }

  /**
   * 添加公司风采
   */
  public Observable<BaseResponse> addCompanyStyle(int userId, String pic) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("pic", pic);
    return mEnterpriseServices.addCompanyStyle(ZPRequestBody);
  }

  /**
   * 修改公司风采
   */
  public Observable<BaseResponse> updateCompanyStyle(int userId, int styleId, String pic) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", styleId);
    ZPRequestBody.put("pic", pic);
    return mEnterpriseServices.updateCompanyStyle(ZPRequestBody);
  }

  /**
   * 删除公司风采
   */
  public Observable<BaseResponse> deleteCompanyStyle(int userId, int styleId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", styleId);
    return mEnterpriseServices.deleteCompanyStyle(ZPRequestBody);
  }

  /**
   * 获取团队成员列表
   */
  public Observable<BaseResponse<List<TeamItemData>>> getTeamMemberList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getTeamMemberList(ZPRequestBody);
  }

  /**
   * 添加团队成员
   */
  public Observable<BaseResponse> addTeamMember(int userId, TeamItemData teamItemData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("photo", teamItemData.getPhoto());
    ZPRequestBody.put("name", teamItemData.getName());
    ZPRequestBody.put("des", teamItemData.getDes());
    ZPRequestBody.put("position", teamItemData.getPosition());
    return mEnterpriseServices.addTeamMember(ZPRequestBody);
  }

  /**
   * 修改团队成员
   */
  public Observable<BaseResponse> updateTeamMember(int userId, TeamItemData teamItemData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", teamItemData.getId());
    ZPRequestBody.put("photo", teamItemData.getPhoto());
    ZPRequestBody.put("name", teamItemData.getName());
    ZPRequestBody.put("des", teamItemData.getDes());
    ZPRequestBody.put("position", teamItemData.getPosition());
    return mEnterpriseServices.updateTeamMember(ZPRequestBody);
  }

  /**
   * 删除团队成员
   */
  public Observable<BaseResponse> deleteTeamMember(int userId, int memberId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", memberId);
    return mEnterpriseServices.deleteTeamMember(ZPRequestBody);
  }

  /**
   * 获取接收的简历列表
   */
  public Observable<BaseResponse<List<ResumeItemData>>> getReceiveResumeList(int userId,
      GetReceivedResumeListParameters receiveResumeListParameters, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("islook", receiveResumeListParameters.getIsLook());
    ZPRequestBody.put("types", receiveResumeListParameters.getResumeTypes());
    ZPRequestBody.put("type", receiveResumeListParameters.getResumeType());
    ZPRequestBody.put("states", receiveResumeListParameters.getResumeStates());
    ZPRequestBody.put("state", receiveResumeListParameters.getResumeState());
    ZPRequestBody.put("relid", receiveResumeListParameters.getPositionId());
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getReceiveResumeList(ZPRequestBody);
  }

  /**
   * 获取收藏简历列表
   */
  public Observable<BaseResponse<List<ResumeItemData>>> getCollectionResumeList(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getCollectionResumeList(ZPRequestBody);
  }

  /**
   * 添加或删除收藏
   */
  public Observable<BaseResponse> addOrDeleteCollection(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("resid", resumeId);
    return mEnterpriseServices.addOrDeleteCollection(ZPRequestBody);
  }

  /**
   * 更新公司联系信息
   */
  public Observable<BaseResponse> updateContractInfo(int userId, String contracts, String phone,
      String QQ) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("lxr", contracts);
    ZPRequestBody.put("phone", phone);
    ZPRequestBody.put("qq", QQ);
    return mEnterpriseServices.updateContractInfo(ZPRequestBody);
  }

  /**
   * 获取营业执照认证信息
   */
  public Observable<BaseResponse<CertificateData>> getCertificateInfo(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getCertificateInfo(ZPRequestBody);
  }

  /**
   * 提交营业执照认证信息
   */
  public Observable<BaseResponse> submitCertificationInfo(int userId, String name, String pic) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("pic", pic);
    return mEnterpriseServices.submitCertificationInfo(ZPRequestBody);
  }

  /**
   * 检查用户是否认证
   */
  public Observable<BaseResponse> checkCertification(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.checkCertification(ZPRequestBody);
  }

  /**
   * 获取简历详情
   */
  public Observable<BaseResponse<ResumeCareerObjectiveData>> getResumeCareerObjective(int userId,
      int resumeId, int snapshotID, int sendHistoryID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    ZPRequestBody.put("kzid", snapshotID);
    ZPRequestBody.put("jaID", sendHistoryID);
    return mEnterpriseServices.getResumeCareerObjective(ZPRequestBody);
  }

  /**
   * 获取简历教育背景
   */
  public Observable<BaseResponse<List<ResumeEduBackgroundData>>> getResumeEduBackground(int userId,
      int resumeId, int snapshotID, int sendHistoryID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("kzid", snapshotID);
    ZPRequestBody.put("jaID", sendHistoryID);
    return mEnterpriseServices.getResumeEduBackground(ZPRequestBody);
  }

  /**
   * 获取简历工作经历
   */
  public Observable<BaseResponse<List<ResumeWorkExpData>>> getResumeWorkExp(int userId, int resumeId,
      int snapshotID, int sendID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("kzid", snapshotID);
    ZPRequestBody.put("jaID", sendID);
    return mEnterpriseServices.getResumeWorkExp(ZPRequestBody);
  }

  /**
   * 获取简历专业技能
   */
  public Observable<BaseResponse<List<ResumeSkillData>>> getResumeSkill(int userId, int resumeId,
      int snapshotID, int sendHistoryID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("kzid", snapshotID);
    ZPRequestBody.put("jaID", sendHistoryID);
    return mEnterpriseServices.getResumeSkill(ZPRequestBody);
  }

  /**
   * 获取简历语言能力
   */
  public Observable<BaseResponse<List<ResumeLanguageData>>> getResumeLanguageProficiency(int userId,
      int resumeId, int snapshotID, int sendHistoryID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("kzid", snapshotID);
    ZPRequestBody.put("jaID", sendHistoryID);
    return mEnterpriseServices.getResumeLanguageProficiency(ZPRequestBody);
  }

  /**
   * 获取简历在校情况
   */
  public Observable<BaseResponse<List<ResumeSchoolSituationData>>> getResumeSchoolSituation(
      int userId, int resumeId, int snapshotID, int sendHistoryID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("kzid", snapshotID);
    ZPRequestBody.put("jaID", sendHistoryID);
    return mEnterpriseServices.getResumeSchoolSituation(ZPRequestBody);
  }

  /**
   * 获取简历在校情况
   */
  public Observable<BaseResponse<List<ResumeCertificateData>>> getResumeCertificate(int userId,
      int resumeId, int snapshotID, int sendHistoryID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("kzid", snapshotID);
    ZPRequestBody.put("jaID", sendHistoryID);
    return mEnterpriseServices.getResumeCertificate(ZPRequestBody);
  }

  /**
   * 获取简历基本信息
   */
  public Observable<BaseResponse<ResumeBaseInfoData>> getResumeBaseInfo(int userId, int snapshotID,
      int sendHistoryID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", userId);
    ZPRequestBody.put("kzid", snapshotID);
    ZPRequestBody.put("jaID", sendHistoryID);
    return mEnterpriseServices.getResumeBaseInfo(ZPRequestBody);
  }

  /**
   * 修改简历状态
   */
  public Observable<BaseResponse> updateResumeStates(int userId, int positionId, int resumeId,
      int state, String reason) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("relid", positionId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("state", state);
    ZPRequestBody.put("msg", reason);
    return mEnterpriseServices.updateResumeStates(ZPRequestBody);
  }

  /**
   * 判断简历是否收藏
   */
  public Observable<BaseResponse> checkResumeCollection(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("resid", resumeId);
    return mEnterpriseServices.checkResumeCollection(ZPRequestBody);
  }

  /**
   * 获取简历关联的视频
   *
   * @param userId
   * @param resumeId
   * @return
   */
  public Observable<BaseResponse<List<VideoData>>> getResumeAttachVideo(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("id", resumeId);
    ZPRequestBody.put("type", VideoType.VIDEO_TYPE_RESUME);
    return mEnterpriseServices.getResumeAttachVideo(ZPRequestBody);
  }

  /**
   * 收藏/取消收藏简历
   */
  public Observable<BaseResponse> collectionResume(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("resid", resumeId);
    return mEnterpriseServices.collectionResume(ZPRequestBody);
  }

  /**
   * 检查简历状态
   */
  public Observable<BaseResponse<ResumeStateData>> checkResumeState(int userId, int resid) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("resid", resid);
    return mEnterpriseServices.checkResumeState(ZPRequestBody);
  }

  /**
   * 获取全部在线职位
   */
  public Observable<BaseResponse<List<PositionItemBean>>> getOnlinePosition(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getOnlinePosition(ZPRequestBody);
  }

  /**
   * 获取邀请投递可用职位
   */
  public Observable<BaseResponse<List<HasStateOnlinePositionData>>> getDeliveryPositionList(
      int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    return mEnterpriseServices.getDeliveryPositionList(ZPRequestBody);
  }

  /**
   * 获取面试职位信息
   */
  public Observable<BaseResponse<PositionItemBean>> getInterviewPositionInfo(int userId,
      int positionId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", positionId);
    return mEnterpriseServices.getInterviewPositionInfo(ZPRequestBody);
  }

  /**
   * 发送面试邀请
   */
  public Observable<BaseResponse> sendInvitationInterview(int userId, int positionId, int resumeId,
      String date, String address, String contract, String contractPhone, int isSms,
      String remark) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("relid", positionId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("viewDate", date);
    ZPRequestBody.put("viewAddress", address);
    ZPRequestBody.put("lxr", contract);
    ZPRequestBody.put("phone", contractPhone);
    ZPRequestBody.put("isSms", isSms);
    ZPRequestBody.put("remark", remark);
    ZPRequestBody.put("wstype", ECommonApiConstants.APP_ORIGIN);
    return mEnterpriseServices.sendInvitationInterview(ZPRequestBody);
  }

  /**
   * 发送投递邀请
   */
  public Observable<BaseResponse> sendInvitationDelivery(int userId, int positionId, String resumeIds,
      String remark) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("relid", positionId);
    ZPRequestBody.put("resids", resumeIds);
    ZPRequestBody.put("remark", remark);
    return mEnterpriseServices.sendInvitationDelivery(ZPRequestBody);
  }

  /**
   * 判斷簡歷是否已經邀請投遞過
   */
  public Observable<BaseResponse> checkResumeIsInvitationDelivery(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("resid", resumeId);
    return mEnterpriseServices.checkResumeIsInvitationDelivery(ZPRequestBody);
  }

  /**
   * 获取下载一个简历需要消耗的积分或次数
   */
  public Observable<BaseResponse> getDownloadResumeIntegral(int downloadMethod) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("downloadType", downloadMethod);
    return mEnterpriseServices.getDownloadResumeIntegral(ZPRequestBody);
  }

  /**
   * 下载简历
   */
  public Observable<BaseResponse> downloadResume(int userId, int positionId, int resumeId,
      int downloadMethod) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cuid", userId);
    ZPRequestBody.put("relid", positionId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("downloadType", downloadMethod);
    return mEnterpriseServices.downloadResume(ZPRequestBody);
  }

  /**
   * 提交意见反馈
   */
  public Observable<BaseResponse> submitFeedback(int type, String content, String email,
      String phone) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("content", content);
    ZPRequestBody.put("email", email);
    ZPRequestBody.put("phone", phone);
    return mEnterpriseServices.submitFeedback(ZPRequestBody);
  }

  /**
   * 根据地理位置获取地理位置信息
   */
  public Observable<BaseResponse<AreaOptionsData>> getAddressInfoByAddressName(int addressType,
      String addressName) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", addressType);
    ZPRequestBody.put("name", addressName);
    return mEnterpriseServices.getAddressInfoByAddressName(ZPRequestBody);
  }

  /**
   * 获取关于我们信息
   */
  public Observable<BaseResponse<AboutUsData>> getAboutUsInfo() {
    return mEnterpriseServices.getAboutUsInfo();
  }

  /**
   * 获取app版本信息
   */
  public Observable<BaseResponse<AppVersionData>> getAppVersionInfo() {
    return mEnterpriseServices.getAppVersionInfo();
  }

  /**
   * 获取首页banner
   */
  public Observable<BaseResponse<List<HomeBannerData>>> getHomeBannerList() {
    return mEnterpriseServices.getHomeBannerList();
  }

  /**
   * 获取简历消息列表
   *
   * @param userType   用户类型 0、个人1、企业
   * @param lookStatus 0、已查看1、未查看-1、全部
   */
  public Observable<BaseResponse<List<MessageItemData>>> getMessageList(int userId, int userType,
      int messageType, int lookStatus, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("lx", userType);
    ZPRequestBody.put("type", messageType);
    ZPRequestBody.put("look", lookStatus);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getMessageList(ZPRequestBody);
  }

  /**
   * 获取企业专属客服信息
   */
  public Observable<BaseResponse<CustomServiceData>> getCustomServiceInfo(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getCustomServiceInfo(ZPRequestBody);
  }

  /**
   * 获取用户订单列表
   */
  public Observable<BaseResponse<List<OrderItemData>>> getOrderList(int userId, int type, int isPay,
      int isEffect, int invoiceID, int pageSize, int pageIndex) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("ispay", isPay);
    ZPRequestBody.put("iseffect", isEffect);
    ZPRequestBody.put("invoiceID", invoiceID);
    ZPRequestBody.put("pageSize", pageSize);
    ZPRequestBody.put("pageIndex", pageIndex);
    return mEnterpriseServices.getOrderList(ZPRequestBody);
  }

  /**
   * 申请发票
   *
   * @param userId      用户id
   * @param invoiceData 发票信息
   */
  public Observable<BaseResponse> applyInvoice(int userId, String orderIds, InvoiceData invoiceData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("oids", orderIds);
    ZPRequestBody.put("invoiceNo", invoiceData.getInvoiceNo());
    ZPRequestBody.put("type", invoiceData.getType());
    if (invoiceData.getType() == 1) {
      ZPRequestBody.put("regAddress", invoiceData.getRegAddress());
      ZPRequestBody.put("regPhone", invoiceData.getRegPhone());
      ZPRequestBody.put("bankName", invoiceData.getBankName());
      ZPRequestBody.put("bankAccount", invoiceData.getBankAccount());
    }
    ZPRequestBody.put("name", invoiceData.getName());
    ZPRequestBody.put("province", invoiceData.getProvince());
    ZPRequestBody.put("city", invoiceData.getCity());
    ZPRequestBody.put("county", invoiceData.getCounty());
    ZPRequestBody.put("address", invoiceData.getAddress());
    ZPRequestBody.put("mobile", invoiceData.getMobile());
    ZPRequestBody.put("postcode", invoiceData.getPostcode());
    return mEnterpriseServices.applyInvoice(ZPRequestBody);
  }

  /**
   * 重新提交发票
   */
  public Observable<BaseResponse> reapplyInvoice(int userId, String orderId,
      InvoiceData invoiceData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", orderId);
    ZPRequestBody.put("invoiceNo", invoiceData.getInvoiceNo());
    ZPRequestBody.put("type", invoiceData.getType());
    if (invoiceData.getType() == 1) {
      ZPRequestBody.put("regAddress", invoiceData.getRegAddress());
      ZPRequestBody.put("regPhone", invoiceData.getRegPhone());
      ZPRequestBody.put("bankName", invoiceData.getBankName());
      ZPRequestBody.put("bankAccount", invoiceData.getBankAccount());
    }
    ZPRequestBody.put("name", invoiceData.getName());
    ZPRequestBody.put("province", invoiceData.getProvince());
    ZPRequestBody.put("city", invoiceData.getCity());
    ZPRequestBody.put("county", invoiceData.getCounty());
    ZPRequestBody.put("address", invoiceData.getAddress());
    ZPRequestBody.put("mobile", invoiceData.getMobile());
    ZPRequestBody.put("postcode", invoiceData.getPostcode());
    return mEnterpriseServices.reapplyInvoice(ZPRequestBody);
  }

  /**
   * 获取发票详情
   */
  public Observable<BaseResponse<InvoiceData>> getInvoiceDetails(int userId, int invoiceId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", invoiceId);
    return mEnterpriseServices.getInvoiceDetails(ZPRequestBody);
  }

  /**
   * 获取开票历史
   */
  public Observable<BaseResponse<List<InvoiceData>>> getInvoiceRecordList(int userId, int invoiceType,
      int invoiceStatus, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", invoiceType);
    ZPRequestBody.put("status", invoiceStatus);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getInvoiceRecordList(ZPRequestBody);
  }

  /**
   * 创建支付订单
   *
   * @param userId     用户id
   * @param orderType  订单类型3、购买积分4、短信充值
   * @param orderPrice 金额
   */
  public Observable<BaseResponse> createPaymentOrder(int userId, int orderType, int orderPrice) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", orderType);
    ZPRequestBody.put("price", orderPrice);
    return mEnterpriseServices.createPaymentOrder(ZPRequestBody);
  }

  /**
   * 获取支付宝订单信息
   */
  public Observable<BaseResponse> getAlipayOrderInfo(String par) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("para", par);
    return mEnterpriseServices.getAlipayOrderInfo(ZPRequestBody);
  }

  /**
   * 获取积分充值比例
   */
  public Observable<BaseResponse> getIntegralConversionRatio() {
    return mEnterpriseServices.getIntegralConversionRatio();
  }

  /**
   * 获取短信充值比例
   */
  public Observable<BaseResponse> getSmsConversionRatio() {
    return mEnterpriseServices.getSmsConversionRatio();
  }

  /**
   * 生效订单
   */
  public Observable<BaseResponse> activateOrder(int userId, int orderId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", orderId);
    return mEnterpriseServices.activateOrder(ZPRequestBody);
  }

  /**
   * 删除订单
   */
  public Observable<BaseResponse> deleteOrder(int userId, int orderId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("ids", orderId);
    return mEnterpriseServices.deleteOrder(ZPRequestBody);
  }

  /**
   * 创建会员办理或续费订单
   *
   * @param orderType 订单类型1购买会员，2续费
   * @param vipLevel  401半年会员，501一年会员
   */
  public Observable<BaseResponse> createVipOrder(int userId, int orderType, int vipLevel) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", orderType);
    ZPRequestBody.put("level", vipLevel);
    return mEnterpriseServices.createVipOrder(ZPRequestBody);
  }

  /**
   * 获取订单支付结果
   */
  public Observable<BaseResponse<OrderItemData>> getPaymentResult(int userId, String orderId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", orderId);
    return mEnterpriseServices.getPaymentResult(ZPRequestBody);
  }

  /**
   * 获取会员套餐列表
   */
  public Observable<BaseResponse<List<MemberServicesData>>> getMemberServicesList() {
    return mEnterpriseServices.getMemberServicesList();
  }

  /**
   * 检查公司信息是否完善
   */
  public Observable<BaseResponse> checkCompanyInfoFill(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.checkCompanyInfoFill(ZPRequestBody);
  }

  /**
   * 扫码登录
   */
  public Observable<BaseResponse> scanQrCodeLogin(String channel, String content) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("channel", channel);
    ZPRequestBody.put("content", content);
    return mEnterpriseServices.scanQrCodeLogin(ZPRequestBody);
  }

  /**
   * 获取首页校园招聘会
   *
   * @param city 城市id
   */
  public Observable<BaseResponse<List<SchoolRecruitmentItemData>>> getHomeSchoolRecruitmentList(
      int city) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("city", city);
    return mEnterpriseServices.getHomeSchoolRecruitmentList(ZPRequestBody);
  }

  /**
   * 获取校园招聘列表
   *
   * @param cityId    城市id
   * @param title     标题
   * @param startDate 开始时间
   * @param pageIndex 页码
   * @param pageSize  分页大小
   */
  public Observable<BaseResponse<List<SchoolRecruitItemData>>> getSchoolRecruitList(int cityId,
      String title, String startDate, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("city", cityId);
    ZPRequestBody.put("title", title);
    ZPRequestBody.put("ksdata", startDate);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getSchoolRecruitList(ZPRequestBody);
  }

  /**
   * 获取校园招聘会详情
   */
  public Observable<BaseResponse<SchoolRecruitDetailsData>> getSchoolRecruitDetails(
      int schoolRecruitId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", schoolRecruitId);
    return mEnterpriseServices.getSchoolRecruitDetails(ZPRequestBody);
  }

  /**
   * 获取高校详情
   *
   * @param schoolId 高校id
   */
  public Observable<BaseResponse<SchoolDetailsData>> getSchoolDetailsInfo(int schoolId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", schoolId);
    return mEnterpriseServices.getSchoolDetailsInfo(ZPRequestBody);
  }

  /**
   * 获取高校招聘会信息
   *
   * @param schoolId    高校id
   * @param jobFairType 招聘会类型
   * @param title       招聘会标题
   * @param pageIndex   分页页码
   * @param pageSize    分页size
   */
  public Observable<BaseResponse<List<SchoolJobFairItemData>>> getSchoolJobFairList(int schoolId,
      int jobFairType, String title, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("gaoxiaoid", schoolId);
    ZPRequestBody.put("type", jobFairType);
    ZPRequestBody.put("title", title);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getSchoolJobFairList(ZPRequestBody);
  }

  /**
   * 获取教职工分类列表
   */
  public Observable<BaseResponse<List<FacultyTypeItemData>>> getFacultyTypeList() {
    return mEnterpriseServices.getFacultyTypeList();
  }

  /**
   * 获取高校教职工招聘列表
   *
   * @param schoolId 学校id
   * @param typeId   招聘类型id
   * @param title    搜索关键词
   */
  public Observable<BaseResponse<List<FacultyRecruitItemData>>> getFacultyRecruitList(int schoolId,
      int typeId, String title, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("gaoxiaoid", schoolId);
    ZPRequestBody.put("ntid3", typeId);
    ZPRequestBody.put("title", title);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getFacultyRecruitList(ZPRequestBody);
  }

  /**
   * 招聘会报名
   *
   * @param userId          用户id
   * @param jobFairId       招聘会id
   * @param companyInfoData 公司信息
   * @param jobs            招聘信息
   */
  public Observable<BaseResponse> jobFairRegistration(int userId, int jobFairId,
      CompanyInfoDataV2 companyInfoData, String jobs) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("nid", jobFairId);
    ZPRequestBody.put("comName", companyInfoData.getName());
    ZPRequestBody.put("tradeid", companyInfoData.getTradeid());
    ZPRequestBody.put("lxr", companyInfoData.getLxr());
    ZPRequestBody.put("proid", companyInfoData.getProid());
    ZPRequestBody.put("phone", companyInfoData.getPhone());
    ZPRequestBody.put("sizeid", companyInfoData.getSizeid());
    ZPRequestBody.put("province", companyInfoData.getProvince());
    ZPRequestBody.put("city", companyInfoData.getCity());
    ZPRequestBody.put("county", companyInfoData.getCounty());
    ZPRequestBody.put("town", companyInfoData.getTown());
    ZPRequestBody.put("address", companyInfoData.getAddress());
    ZPRequestBody.put("info", companyInfoData.getInfo());
    ZPRequestBody.put("jobs", jobs);
    return mEnterpriseServices.jobFairRegistration(ZPRequestBody);
  }

  /**
   * 检查校园招聘报名状态
   */
  public Observable<BaseResponse> checkSchoolRecruitRegisterStatus(int userId, int schoolRecruitId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("nid", schoolRecruitId);
    return mEnterpriseServices.checkSchoolRecruitRegisterStatus(ZPRequestBody);
  }

  /**
   * 获取教职工招聘详情
   *
   * @param newsId 详情id
   */
  public Observable<BaseResponse<NewsData>> getFacultyRecruitDetails(int newsId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", newsId);
    return mEnterpriseServices.getFacultyRecruitDetails(ZPRequestBody);
  }

  /**
   * 检查系统状态
   */
  public Observable<BaseResponse> checkSystemStatus() {
    return mEnterpriseServices.checkSystemStatus();
  }

  /**
   * 获取每日任务信息
   */
  public Observable<BaseResponse<DailyTaskData>> getDailyTaskInfo(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getDailyTaskInfo(ZPRequestBody);
  }

  /**
   * 获取积分任务列表
   *
   * @param userId 用户id
   */
  public Observable<BaseResponse<List<IntegralTaskItemData>>> getIntegralTaskList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getIntegralTaskList(ZPRequestBody);
  }

  /**
   * 获取积分权益列表
   */
  public Observable<BaseResponse<List<IntegralRightsItemData>>> getIntegralRightsList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getIntegralRightsList(ZPRequestBody);
  }

  /**
   * 获取积分充值列表
   */
  public Observable<BaseResponse<List<RechargeIntegralItemData>>> getRechargeIntegralList() {
    return mEnterpriseServices.getRechargeIntegralList();
  }

  /**
   * 获取邀请记录列表
   */
  public Observable<BaseResponse<List<InviteRecordItemData>>> getInviteRecordList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getInviteRecordList(ZPRequestBody);
  }

  /**
   * 获取邀请未完成列表
   */
  public Observable<BaseResponse<List<InviteRecordItemData>>> getInviteUndoneList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getInviteUndoneList(ZPRequestBody);
  }

  /**
   * 获取分享url目标链接
   */
  public Observable<BaseResponse> getShareTargetUrl(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", userId);
    return mEnterpriseServices.getShareTargetUrl(ZPRequestBody);
  }

  /**
   * 检查是否已签到
   */
  public Observable<BaseResponse> checkIsSignIn(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.checkIsSignIn(ZPRequestBody);
  }

  /**
   * 签到
   */
  public Observable<BaseResponse> signIn(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.signIn(ZPRequestBody);
  }

  /**
   * 检查简历信息是否完善
   */
  public Observable<BaseResponse> checkResumeInfoPerfected(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.checkResumeInfoPerfected(ZPRequestBody);
  }

  /**
   * 获取简历个人信息
   */
  public Observable<BaseResponse<ResumePersonalData>> getResumePersonalData(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getResumePersonalData(ZPRequestBody);
  }

  /**
   * 更新简历个人信息
   */
  public Observable<BaseResponse> createResumeStepOne(int userId,
      ResumePersonalData resumePersonalData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("ubname", resumePersonalData.getName());
    ZPRequestBody.put("sex", resumePersonalData.getSex());
    ZPRequestBody.put("birthday", resumePersonalData.getBirthday());
    ZPRequestBody.put("province", resumePersonalData.getProvinceJZ());
    ZPRequestBody.put("city", resumePersonalData.getCityJZ());
    ZPRequestBody.put("county", resumePersonalData.getCountyJZ());
    ZPRequestBody.put("town", resumePersonalData.getTownJZ());
    ZPRequestBody.put("quaId", resumePersonalData.getQuaId());
    ZPRequestBody.put("graduationTime", resumePersonalData.getGraduationTime());
    ZPRequestBody.put("jobType1", resumePersonalData.getJobType1());
    ZPRequestBody.put("jobType2", resumePersonalData.getJobType2());
    ZPRequestBody.put("detailsName", resumePersonalData.getDetailsName());
    ZPRequestBody.put("detailsName2", resumePersonalData.getDetailsName2());
    return mEnterpriseServices.createResumeOneStep(ZPRequestBody);
  }

  /**
   * 上传附件简历
   */
  public Observable<BaseResponse> uploadAttachedResume(int userId, int resumeId, String filePath,
      String fileType) {
    File file = new File(filePath);
    RequestBody fileBody = RequestBody.create(MediaType.parse("multipart/form-data"), file);
    MultipartBody mRequestBody = new MultipartBody.Builder()
        .setType(MultipartBody.FORM)
        .addFormDataPart("imgFile", CheckUtils.encode(file.getName()), fileBody)
        .addFormDataPart("action", "uploadFile")
        .addFormDataPart("para", AESOperator.safeEncrypt(userId + "|" + fileType + "|" + resumeId))
        .build();
    return mEnterpriseServices.uploadAttachmentResume(
        EnterpriseApiConstants.I_OPERATION_ATTACHMENT_RESUME, mRequestBody);
  }

  /**
   * 删除附件简历
   */
  public Observable<BaseResponse> deleteAttachmentResume(int userId, int resumeId) {
    MultipartBody multipartBody = new MultipartBody.Builder()
        .setType(MultipartBody.FORM)
        .addFormDataPart("action", "deleteFile")
        .addFormDataPart("para", AESOperator.safeEncrypt(userId + "|" + resumeId))
        .build();
    return mEnterpriseServices.deleteAttachmentResume(
        EnterpriseApiConstants.I_OPERATION_ATTACHMENT_RESUME, multipartBody);
  }

  /**
   * 获取置顶关键词生效时间
   */
  public Observable<BaseResponse> getJobKeywordTopEffectiveDate(int userId, int location,
      int city, String keyword, int topDays) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("location", location);
    ZPRequestBody.put("city", city);
    ZPRequestBody.put("key", keyword);
    ZPRequestBody.put("days", topDays);
    return mEnterpriseServices.getPositionTopKeywordEffectiveDate(ZPRequestBody);
  }

  /**
   * 获取职位置顶剩余天数
   */
  public Observable<BaseResponse> getPositionTopDaysBalance(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getPositionTopTaysBalance(ZPRequestBody);
  }

  /**
   * 获取职位已置顶区域
   */
  public Observable<BaseResponse<List<PositionTopCityItemData>>> getPositionTopCityList(int userId,
      int positionId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("relid", positionId);
    ZPRequestBody.put("lanmu", 1);
    return mEnterpriseServices.getPositionTopCityList(ZPRequestBody);
  }

  /**
   * 添加职位置顶
   */
  public Observable<BaseResponse> addPositionTop(int userId, int location, int positionId, int days,
      int paymentMethod, int city, String keywords) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("lanmu", 1);
    ZPRequestBody.put("location", location);
    ZPRequestBody.put("relid", positionId);
    ZPRequestBody.put("days", days);
    ZPRequestBody.put("payment", paymentMethod);
    ZPRequestBody.put("city", city);
    ZPRequestBody.put("key", keywords);
    return mEnterpriseServices.addPositionTop(ZPRequestBody);
  }

  /**
   * 检查隐私条款版本
   */
  public Observable<BaseResponse<AppVersionData>> checkAgreementVersion() {
    return mEnterpriseServices.checkAgreementVersion();
  }

  /**
   * 获取客服信息
   */
  public Observable<BaseResponse<CustomServiceData>> getServicesInfo() {
    return mEnterpriseServices.getServicesInfo();
  }

  /**
   * 获取分享信息
   */
  public Observable<BaseResponse<ShareInfoData>> getShareContent(int shareType, int shareSubType,
      int shareInfoId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", shareType);
    ZPRequestBody.put("subType", shareSubType);
    ZPRequestBody.put("id", shareInfoId);
    return mEnterpriseServices.getShareContent(ZPRequestBody);
  }

  public Observable<BaseResponse<List<VideoUnreadMsgItemData>>> getVideoUnreadMsgList(int userID,
      int type, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userID);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getVideoUnreadMsgList(ZPRequestBody);
  }

  /**
   * 设置消息已读（v2）
   */
  public Observable<BaseResponse> setupMsgReadV2(int msgId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", msgId);
    return mEnterpriseServices.setupMsgReadV2(ZPRequestBody);
  }

  /**
   * 获取视频报名列表
   *
   * @param videoId  视频id
   * @param userType 报名状态
   */
  public Observable<BaseResponse<List<SignUpUserItemData>>> getSignUpUserList(int videoId,
      int userType, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", videoId);
    ZPRequestBody.put("type", userType);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mEnterpriseServices.getSignUpUserList(ZPRequestBody);
  }

  /**
   * 更新视频报名状态
   */
  public Observable<BaseResponse> updateSignUpUserStatus(int recordId, int status) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", recordId);
    ZPRequestBody.put("state", status);
    return mEnterpriseServices.updateSignUpUserStatus(ZPRequestBody);
  }

  /**
   * 检查是否存在会话
   */
  public Observable<BaseResponse<Integer>> checkHasConversation(int userID, int cUserID,
      int resumeID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cUserID", cUserID);
    ZPRequestBody.put("userID", userID);
    ZPRequestBody.put("resID", resumeID);
    return mEnterpriseServices.checkHasConversation(EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 获取直聊余量
   */
  public Observable<BaseResponse<GreetBalanceData>> getGreetBalance(int userID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userID);
    return mEnterpriseServices.getGreetBalance(EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 获取职位关联视频
   */
  public Observable<BaseResponse<List<OnlineVideoData>>> getJobLinkVideos(int userID, int jobID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userID);
    ZPRequestBody.put("id", jobID);
    ZPRequestBody.put("type", VideoType.VIDEO_TYPE_RECRUIT);
    return mEnterpriseServices.getJobLinkVideos(ZPRequestBody);
  }

  /**
   * 获取分享信息
   */
  public Observable<BaseResponse<ShareInfoData>> getShareInfo(
      @CommonApiConstants.ShareType int shareType, int shareInfoId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", shareType);
    ZPRequestBody.put("id", shareInfoId);
    return mEnterpriseServices.getShareInfo(ZPRequestBody);
  }

  /**
   * 修改公司资料V2
   */
  public Observable<BaseResponse> updateSingleCompanyInfo(int userId, int type,
      CompanyInfoDataV2 companyInfoData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("name", companyInfoData.getName());
    ZPRequestBody.put("name2", companyInfoData.getName2());
    ZPRequestBody.put("logo", companyInfoData.getTempLogoPath());
    ZPRequestBody.put("picId", companyInfoData.getTempLogoId());
    ZPRequestBody.put("province", companyInfoData.getProvince());
    ZPRequestBody.put("city", companyInfoData.getCity());
    ZPRequestBody.put("county", companyInfoData.getCounty());
    ZPRequestBody.put("town", companyInfoData.getTown());
    ZPRequestBody.put("address", companyInfoData.getAddress());
    ZPRequestBody.put("coordinate", companyInfoData.getCoordinate());
    ZPRequestBody.put("Info", companyInfoData.getInfo());
    ZPRequestBody.put("tradeid", companyInfoData.getTradeid());
    ZPRequestBody.put("proid", companyInfoData.getProid());
    ZPRequestBody.put("sizeid", companyInfoData.getSizeid());
    ZPRequestBody.put("traffic", companyInfoData.getTraffic());
    ZPRequestBody.put("url", companyInfoData.getUrl());
    return mEnterpriseServices.updateSingleCompanyInfo(
        EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 修改公司资料V2
   */
  public Observable<BaseResponse> updateCompanyInfoV2(int userId, int type, String content) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("content", content);
    return mEnterpriseServices.updateCompanyInfoV2(EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 提交公司认领
   */
  public Observable<BaseResponse> submitClaimCompany(int userID, int type, int companyID,
      String companyName) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userID);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("otherId", companyID);
    ZPRequestBody.put("otherName", companyName);
    return mEnterpriseServices.submitClaimCompany(EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 获取用户邀约剩余数量
   */
  public Observable<BaseResponse<UserVipBalanceData>> getUserInviteBalance(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mEnterpriseServices.getUserInviteBalance(EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 刷新全部职位
   *
   * @return
   */
  public Observable<BaseResponse> refreshAllJob() {
    return mEnterpriseServices.refreshAllJob();
  }
}
