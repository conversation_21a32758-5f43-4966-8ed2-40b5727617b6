package com.bxkj.enterprise.data.source

import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.data.ShareInfoData
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.util.kotlin.objectEncrypt
import com.bxkj.enterprise.api.BusinessApiV2
import com.bxkj.enterprise.api.BusinessApi
import com.bxkj.enterprise.data.*
import com.bxkj.enterprise.data.source.datasource.AccountInfoDataSource
import com.bxkj.enterprise.data.source.datasource.AccountInfoDataSource.CheckCompanyInfoFillCallBack
import com.lzf.easyfloat.permission.rom.RomUtils
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @Description:
 */
class AccountInfoRepo @Inject constructor(
  private val _businessApi: BusinessApi,
  private val _coroutinesApi: BusinessApiV2,
) : BaseRepo(), AccountInfoDataSource {

  suspend fun checkHasPartTimeSlots(resumeID: Int): ReqResponse<Nothing> {
    return httpRequest {
      _coroutinesApi.checkHasPartTimeSlots(ZPRequestBody().apply {
        put("resid", resumeID)
      })
    }
  }

  suspend fun deductPartTimeSlots(resumeID: Int): ReqResponse<Nothing> {
    return httpRequest {
      _coroutinesApi.deductPartTimeSlots(ZPRequestBody().apply {
        put("resid", resumeID)
      })
    }
  }

  suspend fun updateBusinessContactInfo(businessContactInfoBean: BusinessContactInfoBean): ReqResponse<Nothing> {
    return httpRequest {
      _coroutinesApi.updateBusinessContactInfo(
        ZPRequestBody().apply {
          put("name", businessContactInfoBean.userName)
          put("tx", businessContactInfoBean.photo)
          put("mobile", businessContactInfoBean.userMobile)
          put("weixin", businessContactInfoBean.weixin)
          put("Email", businessContactInfoBean.email)
        }.objectEncrypt()
      )
    }
  }

  /**
   * 获取企业联系人信息
   */
  suspend fun getBusinessContactInfo(): ReqResponse<BusinessContactInfoBean> {
    return httpRequest {
      _coroutinesApi.getBusinessContactInfo()
    }
  }

  /**
   * 添加短信套餐包订单
   */
  suspend fun addSmsPackageOrder(id: Int): ReqResponse<SmsPackageOrderBean> {
    return httpRequest {
      _coroutinesApi.addSmsPackageOrder(
        ZPRequestBody().apply {
          put("id", id)
        }
      )
    }
  }

  /**
   * 获取短信套餐包列表
   */
  suspend fun getSmsPackageList(): ReqResponse<List<SmsPackageBean>> {
    return httpRequest {
      _coroutinesApi.getSmsPackageList()
    }
  }

  /**
   * 获取发送短信余量
   */
  suspend fun getSmsBalance(): ReqResponse<SmsBalanceBean> {
    return httpRequest {
      _coroutinesApi.getSmsBalance()
    }
  }

  suspend fun getCompanyProductList(userId: Int): ReqResponse<List<ProductItemData>> {
    return httpRequest {
      _coroutinesApi.getCompanyProductList(
        ZPRequestBody().apply {
          put("uid", userId)
        }
      )
    }
  }

  suspend fun deleteCompanyStylePic(userId: Int, id: Int): ReqResponse<Nothing> {
    return httpRequest {
      _coroutinesApi.deleteCompanyStylePic(
        ZPRequestBody().apply {
          put("uid", userId)
          put("id", id)
        }
      )
    }
  }

  /**
   * 上传公司风采图片
   */
  suspend fun uploadCompanyStylePic(userId: Int, base64pic: String): ReqResponse<Nothing> {
    return httpRequest {
      _coroutinesApi.uploadCompanyStylePic(
        ZPRequestBody().apply {
          put("uid", userId)
          put("pic", base64pic)
        }
      )
    }
  }

  /**
   * 获取专属广告
   */
  suspend fun getExclusiveAD(): ReqResponse<List<ExclusiveADData>> {
    return httpRequest {
      _coroutinesApi.getExclusiveAD()
    }
  }

  /**
   * 编辑工作地点
   */
  suspend fun editCompanyAddress(companyAddressData: CompanyAddressData): ReqResponse<Nothing> {
    return httpRequest {
      _coroutinesApi.editCompanyAddress(companyAddressData)
    }
  }

  /**
   * 删除公司地址
   */
  suspend fun deleteCompanyAddress(addressId: Int): ReqResponse<Nothing> {
    return httpRequest {
      _coroutinesApi.deleteCompanyAddress(
        ZPRequestBody().apply {
          put("id", addressId)
        }
      )
    }
  }

  /**
   * 添加工作地点
   */
  suspend fun addCompanyAddress(companyAddressData: CompanyAddressData): ReqResponse<Nothing> {
    val strParams = StringBuilder()
    if (companyAddressData.provinceId == null) {
      return ReqResponse.Failure(RespondThrowable.getParamsError("请选择省份"))
    }
    strParams.append(companyAddressData.provinceId).append("_")
    if (companyAddressData.cityId == null) {
      return ReqResponse.Failure(RespondThrowable.getParamsError("请选择城市"))
    }
    strParams.append(companyAddressData.cityId).append("_")
    if (companyAddressData.countyId == null) {
      return ReqResponse.Failure(RespondThrowable.getParamsError("请选择区/县"))
    }
    strParams.append(companyAddressData.countyId).append("_")
    strParams.append(companyAddressData.townId).append("_")
    if (companyAddressData.address.isNullOrEmpty()) {
      return ReqResponse.Failure(RespondThrowable.getParamsError("请完善详细地址"))
    }
    strParams.append(companyAddressData.address).append("_")
    strParams.append(companyAddressData.lng).append("_")
    strParams.append(companyAddressData.lat)
    return httpRequest {
      _coroutinesApi.addCompanyAddress(
        ZPRequestBody().apply {
          put("Cyaddress", strParams.toString())
        }
      )
    }
  }

  /**
   * 获取公司工作地点列表
   */
  suspend fun getCompanyAddressList(): ReqResponse<List<CompanyAddressData>> {
    return httpRequest {
      _coroutinesApi.getCompanyAddressList()
    }
  }

  /**
   * 设置职位同步状态
   */
  suspend fun setupSyncJobOpenState(syncJob: Int): ReqResponse<Nothing> {
    return httpRequest {
      _coroutinesApi.setupSyncJobOpenState(
        ZPRequestBody().apply {
          put("jobTongbu", syncJob)
        }
      )
    }
  }

  /**
   * 获取全网同步开启状态
   */
  suspend fun getSyncJobOpenState(): ReqResponse<Int> {
    return httpRequest {
      _coroutinesApi.getSyncJobOpenState()
        .apply {
          if (requestSuccess()) {
            data = msg.toInt()
          }
        }
    }
  }

  /**
   * 获取视频面试余量
   */
  suspend fun getVideoCallBalance(): ReqResponse<VideoCallBalanceData> {
    return httpRequest {
      _coroutinesApi.getVideoCallBalance()
    }
  }

  fun uploadMomentFile(
    filePath: String,
    params: UploadFileRequestParams,
    callback: ResultDataCallBack<String>
  ) {
    _businessApi.uploadFile(filePath, params)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse.msg)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  override fun getCompanyInfo(userId: Int, callBack: ResultDataCallBack<CompanyInfoDataV2>) {
    _businessApi.getCompanyInfo(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess(baseResponse.data as CompanyInfoDataV2)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onError(respondThrowable)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }
      })
  }

  override fun checkCompanyInfoFill(userId: Int, callBack: CheckCompanyInfoFillCallBack) {
    _businessApi.checkCompanyInfoFill(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onInfoFill()
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onInfoNoFill()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }
      })
  }

  override fun checkCertification(userId: Int, callBack: ResultDataCallBack<AuthenticatedData>) {
    _businessApi.checkCertification(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess(AuthenticatedData(baseResponse.status, baseResponse.msg))
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onSuccess(
            AuthenticatedData(
              respondThrowable.errCode,
              respondThrowable.errMsg
            )
          )
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }
      })
  }

  override fun getAccountVipInfo(
    userId: Int,
    callBack: ResultDataCallBack<com.bxkj.jrzp.user.data.AccountVipData>
  ) {
    _businessApi.getAccountVipInfo(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess(baseResponse.data as com.bxkj.jrzp.user.data.AccountVipData)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onError(respondThrowable)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }
      })
  }

  override fun checkResumeInfoCompleted(userId: Int, callBack: ResultCallBack) {
    _businessApi.checkResumeInfoPerfected(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess()
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onError(respondThrowable)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }
      })
  }

  override fun getUserCenterInfo(userId: Int, callBack: ResultDataCallBack<UserCenterData>) {
    _businessApi.getUserCenterInfo(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess(baseResponse.data as UserCenterData)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onError(respondThrowable)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }
      })
  }

  override fun getServicesInfo(callBack: ResultDataCallBack<CustomServiceData>) {
    _businessApi.servicesInfo
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess(baseResponse.data as CustomServiceData)
        }

        override fun onError(respondThrowable: RespondThrowable) {}
        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }
      })
  }

  fun getVideoUnreadMsgList(
    userId: Int,
    type: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<VideoUnreadMsgItemData>>
  ) {
    _businessApi.getVideoUnreadMsgList(userId, type, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun setupMsgReadV2(msgId: Int, callback: ResultCallBack) {
    _businessApi.setupMsgReadV2(msgId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getSignUpUserList(
    videoId: Int,
    status: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<SignUpUserItemData>>
  ) {
    _businessApi.getSignUpUserList(videoId, status, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun updateSignUpUserStatus(recordId: Int, status: Int, callback: ResultCallBack) {
    _businessApi.updateSignUpUserStatus(recordId, status)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  /**
   * 获取分享信息
   */
  suspend fun getShareContent(type: Int, subType: Int, infoId: Int): ReqResponse<ShareInfoData> {
    return httpRequest {
      _coroutinesApi.getShareContent(
        ZPRequestBody().apply {
          put("type", type)
          put("subType", subType)
          put("id", infoId)
        }
      )
    }
  }

  private fun getMobileTag(): Int {
    return when {
      RomUtils.checkIsHuaweiRom() -> {
        1
      }

      else -> {
        2
      }
    }
  }
}