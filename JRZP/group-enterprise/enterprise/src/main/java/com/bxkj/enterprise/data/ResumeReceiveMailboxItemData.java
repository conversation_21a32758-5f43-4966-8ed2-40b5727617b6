package com.bxkj.enterprise.data;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description: 简历接收邮箱item
 * @TODO: TODO
 * @date 2018/7/27
 */
public class ResumeReceiveMailboxItemData implements Parcelable {

    private int id;
    private String email;

    public ResumeReceiveMailboxItemData() {
    }

    public ResumeReceiveMailboxItemData(String email) {
        this.email = email;
    }

    protected ResumeReceiveMailboxItemData(Parcel in) {
        id = in.readInt();
        email = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeString(email);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<ResumeReceiveMailboxItemData> CREATOR = new Creator<ResumeReceiveMailboxItemData>() {
        @Override
        public ResumeReceiveMailboxItemData createFromParcel(Parcel in) {
            return new ResumeReceiveMailboxItemData(in);
        }

        @Override
        public ResumeReceiveMailboxItemData[] newArray(int size) {
            return new ResumeReceiveMailboxItemData[size];
        }
    };

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof ResumeReceiveMailboxItemData) {
            ResumeReceiveMailboxItemData resumeReceiveMailboxItemData = (ResumeReceiveMailboxItemData) obj;
            return resumeReceiveMailboxItemData.email.equals(this.email);
        }
        return super.equals(obj);
    }
}
