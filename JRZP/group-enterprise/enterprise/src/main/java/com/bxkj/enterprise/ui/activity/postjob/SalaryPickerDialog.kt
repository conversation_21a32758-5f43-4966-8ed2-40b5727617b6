package com.bxkj.enterprise.ui.activity.postjob

import android.view.ViewGroup
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.bigkoo.pickerview.adapter.ArrayWheelAdapter
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.enterprise.BR
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.DialogSalaryPickerBinding

/**
 *
 * @author: sanjin
 * @date: 2022/7/15
 */
class SalaryPickerDialog(
  private val confirmCallback: (salaryData: SalaryData) -> Unit,
  private val showExtraOptions: Boolean = false,
  private val initData: SalaryData? = null
) : BaseDBDialogFragment<DialogSalaryPickerBinding, SalaryPickerViewModel>() {

  private val salaryArray by lazy { resources.getStringArray(R.array.work_salary_v2) }
  private val salaryUnitArray by lazy { resources.getStringArray(R.array.job_salary_unit) }
  private val salaryPayCycleArray by lazy { resources.getStringArray(R.array.job_salary_pay_cycle) }

  override fun getViewModelClass(): Class<SalaryPickerViewModel> =
    SalaryPickerViewModel::class.java

  override fun getLayoutId(): Int = R.layout.dialog_salary_picker

  override fun initPage() {
    viewBinding.viewModel = viewModel

    enableDragClose(false)

    initWheelView()

    viewBinding.tvClose.setOnClickListener {
      dismiss()
    }

    subscribeViewModelEvent()

    viewModel.start(showExtraOptions, initData)
  }

  override fun enableBottomSheet(): Boolean {
    return true
  }

  private fun subscribeViewModelEvent() {
    viewModel.confirmSalaryEvent.observe(this, EventObserver {
      //非自定义区间情况才设置薪资范围
      if (!viewModel.currentCustomMode()) {
        setupSalaryRange(viewBinding.wvStandardSalary.currentItem)
      }
      setupSalaryUnit(viewBinding.wvSalaryUnit.currentItem)
      setupPaymentCycle(viewBinding.wvSettlementCycle.currentItem)

      confirmCallback(it)
      dismiss()
    })
  }

  private fun initWheelView() {
    viewBinding.wvStandardSalary.apply {
      isCenterLabel(true)
      setIsOptions(true)
      setCyclic(false)
      setTextSize(18f)
      adapter = ArrayWheelAdapter(salaryArray.toList())
      setOnItemSelectedListener {
        setupSalaryRange(it)
      }
    }

    viewBinding.wvSalaryUnit.apply {
      isCenterLabel(true)
      setIsOptions(true)
      setCyclic(false)
      setTextSize(18f)
      adapter = ArrayWheelAdapter(salaryUnitArray.toList())
      setOnItemSelectedListener {
        setupSalaryUnit(it)
      }
    }

    viewBinding.wvSettlementCycle.apply {
      isCenterLabel(true)
      setIsOptions(true)
      setCyclic(false)
      setTextSize(18f)
      adapter = ArrayWheelAdapter(salaryPayCycleArray.toList())
      setOnItemSelectedListener {
        setupPaymentCycle(it)
      }
    }

    initData?.let {
      if (it.salaryRangeName.isNotEmpty()) {
        viewBinding.wvStandardSalary.currentItem = salaryArray.indexOf(it.salaryRangeName)
      }
      if (it.salaryUnitName.isNotEmpty()) {
        viewBinding.wvSalaryUnit.currentItem = salaryArray.indexOf(it.salaryUnitName)
      }
      if (it.paymentCycleName.isNotEmpty()) {
        viewBinding.wvSettlementCycle.currentItem = salaryArray.indexOf(it.paymentCycleName)
      }
    }
  }

  private fun setupPaymentCycle(it: Int) {
    viewModel.setPaymentCycle(it, salaryPayCycleArray[it])
  }

  private fun setupSalaryUnit(it: Int) {
    viewModel.setSalaryUnit(it, salaryUnitArray[it])
  }

  private fun setupSalaryRange(it: Int) {
    viewModel.setSalaryRange(it, salaryArray[it])
  }

  override fun onStart() {
    super.onStart()
    dialog?.window?.let {
      it.setWindowAnimations(R.style.BottomPopupAnim)
      it.findViewById<ViewGroup>(R.id.design_bottom_sheet)?.let { layout ->
        val layoutParams = layout.layoutParams
        layoutParams.height = dip(260)
      }
    }
  }
}

class SalaryData : BaseObservable() {
  var salaryRangeId: Int = 0
  var salaryRangeName: String = ""
  var salaryUnitId: Int = 0
  var salaryUnitName: String = ""
  var paymentCycleId: Int = 0
  var paymentCycleName: String = ""

  @Bindable
  var minSalary: String = ""
    set(value) {
      field = value
      notifyPropertyChanged(BR.minSalary)
    }

  @Bindable
  var maxSalary: String = ""
    set(value) {
      field = value
      notifyPropertyChanged(BR.maxSalary)
    }

  fun hasCustomSalary(): Boolean {
    return ((maxSalary.isNotBlank()) && maxSalary.getOrDefault("0").toInt() > 0)
  }

  override fun toString(): String {
    return "SalaryData(salaryRangeId=$salaryRangeId, salaryRangeName='$salaryRangeName', salaryUnitId=$salaryUnitId, salaryUnitName='$salaryUnitName', paymentCycleId=$paymentCycleId, paymentCycleName='$paymentCycleName', minSalary='$minSalary', maxSalary='$maxSalary')"
  }
}