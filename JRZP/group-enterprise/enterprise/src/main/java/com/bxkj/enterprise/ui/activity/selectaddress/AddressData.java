package com.bxkj.enterprise.ui.activity.selectaddress;

import android.os.Parcel;
import android.os.Parcelable;

import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.util.CheckUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.selectaddress
 * @Description:
 * @TODO: TODO
 * @date 2018/8/1
 */
public class AddressData implements Parcelable {

    private int provinceId;
    private String provinceName;
    private int cityId;
    private String cityName;
    private int areaId;
    private String areaName;
    private int streetId;
    private String streetName;

    public AddressData(AreaOptionsData province, AreaOptionsData city, AreaOptionsData area, AreaOptionsData street) {
        if (province != null) {
            provinceId = province.getId();
            provinceName = province.getName();
        }
        if (city != null) {
            cityId = city.getId();
            cityName = city.getName();
        }
        if (area != null) {
            areaId = area.getId();
            areaName = area.getName();
        }
        if (street != null) {
            streetId = street.getId();
            streetName = street.getName();
        }
    }

    protected AddressData(Parcel in) {
        provinceId = in.readInt();
        provinceName = in.readString();
        cityId = in.readInt();
        cityName = in.readString();
        areaId = in.readInt();
        areaName = in.readString();
        streetId = in.readInt();
        streetName = in.readString();
    }

    public static final Creator<AddressData> CREATOR = new Creator<AddressData>() {
        @Override
        public AddressData createFromParcel(Parcel in) {
            return new AddressData(in);
        }

        @Override
        public AddressData[] newArray(int size) {
            return new AddressData[size];
        }
    };

    public int getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(int provinceId) {
        this.provinceId = provinceId;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public int getAreaId() {
        return areaId;
    }

    public void setAreaId(int areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public int getStreetId() {
        return streetId;
    }

    public void setStreetId(int streetId) {
        this.streetId = streetId;
    }

    public String getStreetName() {
        return streetName;
    }

    public void setStreetName(String streetName) {
        this.streetName = streetName;
    }

    public String getAddress() {
        String address = "";
        if (provinceName != null) {
            address += provinceName;
        }
        if (cityName != null) {
            address += cityName;
        }
        if (areaName != null) {
            address += areaName;
        }
        if (streetName != null) {
            address += streetName;
        }
        return address;
    }

    public boolean checkAddressEmpty() {
        return CheckUtils.isNullOrEmpty(provinceName) || CheckUtils.isNullOrEmpty(cityName) || CheckUtils.isNullOrEmpty(areaName) || CheckUtils.isNullOrEmpty(streetName);
    }

    public String getAreaAndStreet() {
        String address = "";
        if (areaName != null) {
            address += areaName;
        }
        if (streetName != null) {
            address += streetName;
        }
        return address;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeInt(provinceId);
        parcel.writeString(provinceName);
        parcel.writeInt(cityId);
        parcel.writeString(cityName);
        parcel.writeInt(areaId);
        parcel.writeString(areaName);
        parcel.writeInt(streetId);
        parcel.writeString(streetName);
    }

    /**
     * 省和市是否为空
     * @return
     */
    public boolean provinceAndCityIsNull(){
        return CheckUtils.isNullOrEmpty(provinceName)&&CheckUtils.isNullOrEmpty(cityName);
    }
}
