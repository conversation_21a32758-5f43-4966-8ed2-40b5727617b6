package com.bxkj.enterprise.mvp.presenter;

import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.api.parameters.SearchResumeParameters;
import com.bxkj.enterprise.data.ResumeItemData;
import com.bxkj.enterprise.mvp.contract.ResumeListContract;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.mvp.presenter
 * @Description: ResumeList
 * @TODO: TODO
 * @date 2018/3/27
 */

public class ResumeListPresenter extends ResumeListContract.Presenter {

    private static final String TAG = ResumeListPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public ResumeListPresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    public void getResumeList(int userId, SearchResumeParameters searchResumeParameters, int pageIndex, int pageSize) {
        mBusinessApi.getResumeList(userId, searchResumeParameters, pageIndex, pageSize)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        List<ResumeItemData> resumeItemDataList = (List<ResumeItemData>) baseResponse.getDataList();
                        mView.getResumeListSuccess(resumeItemDataList, false);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.isNoDataError()) {
                            mView.onResultNoData();
                        } else {
                            mView.onRequestError(respondThrowable);
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
