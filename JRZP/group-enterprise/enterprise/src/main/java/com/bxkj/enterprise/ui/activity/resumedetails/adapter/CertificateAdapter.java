package com.bxkj.enterprise.ui.activity.resumedetails.adapter;

import android.content.Context;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.ResumeCertificateData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.itemviewbinder
 * @Description: 语言能力适配器
 * @TODO: TODO
 * @date 2018/5/11
 */

public class CertificateAdapter extends SuperAdapter<ResumeCertificateData> {
    public CertificateAdapter(Context context, List<ResumeCertificateData> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, ResumeCertificateData certificateItemData, int position) {
        holder.setText(R.id.tv_name_and_level, certificateItemData.getType() + "|" + certificateItemData.getName());
        holder.setText(R.id.tv_desc, certificateItemData.getRemark());
    }
}
