package com.bxkj.enterprise.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.enterprise.BR;
import com.bxkj.enterprise.ui.activity.postjob.SalaryData;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description: 职位详情
 * @TODO: TODO
 * @date 2018/7/27
 */
public class JobContentData extends BaseObservable {

    /**
     * id : 2 name : asp.net开发工程师 type1 : 19 type2 : 23 type1Name : 计算机、互联网类 type2Name : 软件工程师 money :
     * 0 moneyName : 面议 number : 0 exp : 0 wtName : 不限 qua : 4 quaName : 中技 jnid : 7 jnName : 全职 sheng
     * : 28009 shengName : 浙江 shi : 28590 shiName : 杭州 qu : 28615 quName : 滨江区 jie : 28616 jieName :
     * 浦沿街道 address : 伟业路1号 lng : 0 lat : 0 hr : 1 hrName : 刘双锋 hrEmail : <EMAIL> hrMobile :
     * 15868123456 jobWelfareList : [{"id":26320,"welid":10,"welName":"交通补助"},{"id":26321,"welid":9,"welName":"餐补"},{"id":26322,"welid":8,"welName":"通讯补贴"}]
     */

    private int uid;
    private int id;
    private String name;
    private int type;
    private int type1;
    private int type2;
    private String type1Name;
    private String type2Name;
    //薪资
    private int money;
    private String moneyName = "";
    private int number;
    private int exp = 3;
    private String wtName;
    private int qua;
    private String quaName;
    //工作性质
    private int oldjnid = 7;
    private int sheng;
    private String shengName = "";
    private int shi;
    private String shiName = "";
    private int qu;
    private String quName = "";
    private int jie;
    private String jieName = "";
    private String address;
    private String lng;
    private String lat;
    private int hr;
    private String hrName;
    private String hrEmail;
    private String hrMobile;
    private String des;
    private int dizhiid;
    private List<WelfareItemData> jobWelfareList;

    @SerializedName("edate2")
    private String deadline;

    private String validity;

    private String deal;

    @SerializedName("reljn")
    private int natureId;

    @SerializedName("jnName")
    private String natureName;

    private List<OtherWelfareItemData> otherWelfareList;

    private int isMoban = 0;

    @SerializedName("moneyUnit")
    private int salaryUnitId; //薪资单位

    @SerializedName("moneyUnitName")
    private String salaryUnitName = ""; //薪资单位

    @SerializedName("moneyJiesuan")
    private int salaryPaymentCycleId; //付款周期

    @SerializedName("moneyJiesuanName")
    private String salaryPaymentCycleName = ""; //付款周期

    @SerializedName("minMoney")
    private int minSalary;

    @SerializedName("maxMoney")
    private int maxSalary;

    @SerializedName("shenfen")
    private int identityRequirement = 1;  //1、学生可做2、非学生可做

    @SerializedName("jnid")
    private int partner = 1;  //合伙性质

    @Bindable
    public int getIdentityRequirement() {
        return identityRequirement;
    }

    public void setIdentityRequirement(int identityRequirement) {
        this.identityRequirement = identityRequirement;
    }

    @Bindable
    public int getPartner() {
        return partner;
    }

    public void setPartner(int partner) {
        this.partner = partner;
    }

    public int getMinSalary() {
        return minSalary;
    }

    public void setMinSalary(int minSalary) {
        this.minSalary = minSalary;
    }

    public int getMaxSalary() {
        return maxSalary;
    }

    public void setMaxSalary(int maxSalary) {
        this.maxSalary = maxSalary;
        notifyPropertyChanged(BR.formatSalaryText);
    }

    public int getSalaryUnitId() {
        return salaryUnitId;
    }

    public void setSalaryUnitId(int salaryUnitId) {
        this.salaryUnitId = salaryUnitId;
    }

    public String getSalaryUnitName() {
        return salaryUnitName;
    }

    public void setSalaryUnitName(String salaryUnitName) {
        this.salaryUnitName = salaryUnitName;
    }

    public int getSalaryPaymentCycleId() {
        return salaryPaymentCycleId;
    }

    public void setSalaryPaymentCycleId(int salaryPaymentCycleId) {
        this.salaryPaymentCycleId = salaryPaymentCycleId;
    }

    public String getSalaryPaymentCycleName() {
        return salaryPaymentCycleName;
    }

    public void setSalaryPaymentCycleName(String salaryPaymentCycleName) {
        this.salaryPaymentCycleName = salaryPaymentCycleName;
        notifyPropertyChanged(BR.formatSalaryText);
    }

    @Bindable
    public String getFormatTypeName() {
        final StringBuilder jobTypeBuilder = new StringBuilder();
        if (!CheckUtils.isNullOrEmpty(type1Name)) {
            jobTypeBuilder.append(type1Name).append("-");
        }
        if (!CheckUtils.isNullOrEmpty(type2Name)) {
            jobTypeBuilder.append(type2Name);
        }
        return jobTypeBuilder.toString();
    }

    public int isMoban() {
        return isMoban;
    }

    public void setMoban(int moban) {
        isMoban = moban;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    @Bindable
    public int getNatureId() {
        return natureId;
    }

    public void setNatureId(int natureId) {
        this.natureId = natureId;
        notifyPropertyChanged(BR.natureId);
    }

    @Bindable
    public String getNatureName() {
        return natureName;
    }

    public void setNatureName(String natureName) {
        this.natureName = natureName;
        notifyPropertyChanged(BR.natureName);
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Bindable
    public String getValidity() {
        return validity;
    }

    public void setValidity(String validity) {
        this.validity = validity;
        notifyPropertyChanged(BR.validity);
    }

    public String getDeadline() {
        return deadline;
    }

    public void setDeadline(String deadline) {
        this.deadline = deadline;
    }

    @Bindable
    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
        notifyPropertyChanged(BR.des);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Bindable
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        notifyPropertyChanged(BR.name);
    }

    public int getType1() {
        return type1;
    }

    public void setType1(int type1) {
        this.type1 = type1;
    }

    public int getType2() {
        return type2;
    }

    public void setType2(int type2) {
        this.type2 = type2;
    }

    public String getType1Name() {
        return type1Name;
    }

    public void setType1Name(String type1Name) {
        this.type1Name = type1Name;
        notifyPropertyChanged(BR.formatTypeName);
    }

    public String getType2Name() {
        return type2Name;
    }

    public void setType2Name(String type2Name) {
        this.type2Name = type2Name;
        notifyPropertyChanged(BR.formatTypeName);
    }

    public int getMoney() {
        return money;
    }

    public void setMoney(int money) {
        this.money = money;
    }

    public String getMoneyName() {
        return moneyName;
    }

    public void setMoneyName(String moneyName) {
        this.moneyName = moneyName;
        notifyPropertyChanged(BR.formatSalaryText);
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public int getExp() {
        return exp;
    }

    public void setExp(int exp) {
        this.exp = exp;
    }

    @Bindable
    public String getWtName() {
        return (!CheckUtils.isNullOrEmpty(wtName) && wtName.equals("请选择")) ? "不限" : wtName;
    }

    public void setWtName(String wtName) {
        this.wtName = wtName;
        notifyPropertyChanged(BR.wtName);
    }

    public int getQua() {
        return qua;
    }

    public void setQua(int qua) {
        this.qua = qua;
    }

    @Bindable
    public String getQuaName() {
        return (!CheckUtils.isNullOrEmpty(quaName) && quaName.equals("请选择")) ? "不限" : quaName;
    }

    public void setQuaName(String quaName) {
        this.quaName = quaName;
        notifyPropertyChanged(BR.quaName);
    }

    public int getOldjnid() {
        return oldjnid;
    }

    public void setOldjnid(int oldjnid) {
        this.oldjnid = oldjnid;
    }

    public int getSheng() {
        return sheng;
    }

    public void setSheng(int sheng) {
        this.sheng = sheng;
    }

    public String getShengName() {
        return shengName;
    }

    public void setShengName(String shengName) {
        this.shengName = shengName;
    }

    public int getShi() {
        return shi;
    }

    public void setShi(int shi) {
        this.shi = shi;
    }

    public String getShiName() {
        return shiName;
    }

    public void setShiName(String shiName) {
        this.shiName = shiName;
    }

    public int getQu() {
        return qu;
    }

    public void setQu(int qu) {
        this.qu = qu;
    }

    public String getQuName() {
        return quName;
    }

    public void setQuName(String quName) {
        this.quName = quName;
    }

    public int getJie() {
        return jie;
    }

    public void setJie(int jie) {
        this.jie = jie;
    }

    public String getJieName() {
        return jieName;
    }

    public void setJieName(String jieName) {
        this.jieName = jieName;
    }

    public int getDizhiid() {
        return dizhiid;
    }

    public void setDizhiid(int dizhiid) {
        this.dizhiid = dizhiid;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
        notifyPropertyChanged(BR.fullAddressText);
    }

    public String getLng() {
        return CheckUtils.isNullOrEmpty(lng) ? "0" : lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLat() {
        return CheckUtils.isNullOrEmpty(lat) ? "0" : lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public int getHr() {
        return hr;
    }

    public void setHr(int hr) {
        this.hr = hr;
    }

    @Bindable
    public String getHrName() {
        return hrName;
    }

    public void setHrName(String hrName) {
        this.hrName = hrName;
        notifyPropertyChanged(BR.hrName);
    }

    @Bindable
    public String getHrEmail() {
        return hrEmail;
    }

    public void setHrEmail(String hrEmail) {
        this.hrEmail = hrEmail;
        notifyPropertyChanged(BR.hrEmail);
    }

    @Bindable
    public String getHrMobile() {
        return hrMobile;
    }

    public void setHrMobile(String hrMobile) {
        this.hrMobile = hrMobile;
        notifyPropertyChanged(BR.hrMobile);
    }

    public String getDeal() {
        return deal;
    }

    public void setDeal(String deal) {
        this.deal = deal;
    }

    public List<WelfareItemData> getJobWelfareList() {
        return jobWelfareList;
    }

    public void setJobWelfareList(List<WelfareItemData> jobWelfareList) {
        this.jobWelfareList = jobWelfareList;
        notifyPropertyChanged(BR.welfare);
    }

    public List<OtherWelfareItemData> getOtherWelfareList() {
        return otherWelfareList;
    }

    public void setOtherWelfareList(List<OtherWelfareItemData> otherWelfareList) {
        this.otherWelfareList = otherWelfareList;
        if (CheckUtils.isNullOrEmpty(otherWelfareList)) {
            deal = "";
            return;
        }
        StringBuilder otherWelfareText = new StringBuilder();
        for (OtherWelfareItemData otherWelfareItemData : otherWelfareList) {
            otherWelfareText.append(otherWelfareItemData.getText()).append(",");
        }
        deal = otherWelfareText.substring(0, otherWelfareText.lastIndexOf(","));
        notifyPropertyChanged(BR.welfare);
    }

    public AreaOptionsData getProvinceData() {
        return new AreaOptionsData(sheng, shengName);
    }

    public AreaOptionsData getCityData() {
        return new AreaOptionsData(shi, shiName);
    }

    public AreaOptionsData getAreaData() {
        return new AreaOptionsData(qu, quName);
    }

    public AreaOptionsData getStreetData() {
        return new AreaOptionsData(jie, jieName);
    }

    @Bindable
    public String getWelfare() {
        if (CheckUtils.isNullOrEmpty(jobWelfareList) && CheckUtils.isNullOrEmpty(otherWelfareList)) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (WelfareItemData jobWelfareListBean : jobWelfareList) {
            stringBuilder.append(jobWelfareListBean.getName()).append(",");
        }
        if (!CheckUtils.isNullOrEmpty(deal)) {
            return stringBuilder.append(deal).toString();
        }
        return stringBuilder.substring(0, stringBuilder.lastIndexOf(","));
    }

    public String getWelfareIds() {
        if (CheckUtils.isNullOrEmpty(jobWelfareList)) {
            return "";
        }
        StringBuffer idBuffer = new StringBuffer();
        for (WelfareItemData jobWelfareListBean : jobWelfareList) {
            idBuffer.append(jobWelfareListBean.getId()).append(",");
        }
        return idBuffer.substring(0, idBuffer.lastIndexOf(","));
    }

    @Bindable
    public String getFullAddressText() {
        final StringBuilder addressBuilder = new StringBuilder();
        if (!CheckUtils.isNullOrEmpty(shengName)) {
            addressBuilder.append(shengName);
        }
        if (!CheckUtils.isNullOrEmpty(shiName)) {
            addressBuilder.append(shiName);
        }
        if (!CheckUtils.isNullOrEmpty(quName)) {
            addressBuilder.append(quName);
        }
        if (!CheckUtils.isNullOrEmpty(jieName)) {
            addressBuilder.append(jieName);
        }
        if (!CheckUtils.isNullOrEmpty(address)) {
            addressBuilder.append(address);
        }
        return addressBuilder.toString();
    }

    public String getTypeFormatText() {
        String jobTypeText = "社招";
        switch (type) {
            case 2: {
                jobTypeText = "校招";
                break;
            }
            case -1: {
                jobTypeText = "社招+校招";
                break;
            }
            default: {
                jobTypeText = "社招";
                break;
            }
        }
        return jobTypeText;
    }

    public boolean showOtherSalaryOptions() {
        return natureId == 2 || natureId == 3;
    }

    @Bindable
    public String getFormatSalaryText() {
        final StringBuilder salaryBuilder = new StringBuilder();
        if (maxSalary > 0) {
            salaryBuilder.append(minSalary).append("-").append(maxSalary).append("元");
        } else {
            salaryBuilder.append(moneyName);
        }
        if (showOtherSalaryOptions()) {
            if (!CheckUtils.isNullOrEmpty(salaryUnitName)) {
                salaryBuilder.append("/").append(salaryUnitName);
            }
            if (!CheckUtils.isNullOrEmpty(salaryPaymentCycleName)) {
                salaryBuilder.append("/").append(salaryPaymentCycleName);
            }
        }
        return salaryBuilder.toString();
    }

    public void resetSalaryOptions() {
        money = 0;
        moneyName = "";
        minSalary = 0;
        maxSalary = 0;
        salaryUnitId = 0;
        salaryUnitName = "";
        salaryPaymentCycleId = 0;
        salaryPaymentCycleName = "";
        notifyPropertyChanged(BR.formatSalaryText);
    }

    public SalaryData getComboSalaryData() {
        final SalaryData salaryData = new SalaryData();
        salaryData.setMinSalary(minSalary == 0 ? "" : String.valueOf(minSalary));
        salaryData.setMaxSalary(maxSalary == 0 ? "" : String.valueOf(maxSalary));
        salaryData.setSalaryRangeId(money);
        salaryData.setSalaryRangeName(moneyName);
        salaryData.setSalaryUnitId(salaryUnitId);
        salaryData.setSalaryUnitName(salaryUnitName);
        salaryData.setPaymentCycleId(salaryPaymentCycleId);
        salaryData.setPaymentCycleName(salaryPaymentCycleName);
        return salaryData;
    }

    public String getEmptyTips() {
        if (CheckUtils.isNullOrEmpty(name)) {
            return "请填写职位名称";
        }
        if (type2 == 0) {
            return "请选择职位类型";
        }
        if (natureId == 0) {
            return "请选择工作性质";
        }
        if (natureId == 2 && identityRequirement == 0) {
            return "请选择身份要求";
        }
        if (natureId == 4 && partner == 0) {
            return "请选择用工性质";
        }
        if (money == 0 && minSalary == 0 && maxSalary == 0) {
            return "请选择/填写职位薪资";
        }
        if (dizhiid == 0) {
            return "请完善工作地点";
        }
        if (number == 0) {
            return "请填写招聘人数";
        }
        if (CheckUtils.isNullOrEmpty(des)) {
            return "请填写职位描述";
        }
        if (hr == 0) {
            return "请选择HR";
        }
        return "";
    }
}
