package com.bxkj.enterprise.ui.activity.udpatephonenumber

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.therouter.TheRouter
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.util.router.RouterNavigation.PersonalMainActivity
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseActivityUpdatePhoneNumberBinding

/**
 * Description:
 * Author:Sanjin
 **/
class UpdatePhoneNumberActivity :
    BaseDBActivity<EnterpriseActivityUpdatePhoneNumberBinding, UpdatePhoneNumberViewModel>() {

    companion object {

        private const val EXTRA_ORIGIN_PHONE_NUMBER = "extra_origin_phone_number"
        const val EXTRA_RESULT_PHONE_NUMBER = "extra_result_phone_number"

        fun newIntent(context: Context, originPhoneNumber: String): Intent {
            return Intent(context, UpdatePhoneNumberActivity::class.java).apply {
                putExtra(EXTRA_ORIGIN_PHONE_NUMBER, originPhoneNumber)
            }
        }
    }

    private val originPhoneNumber by lazy { intent.getStringExtra(EXTRA_ORIGIN_PHONE_NUMBER) }

    override fun getViewModelClass(): Class<UpdatePhoneNumberViewModel> = UpdatePhoneNumberViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_activity_update_phone_number

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        subscribeViewModelEvent()

        viewModel.start(originPhoneNumber)
    }

    private fun subscribeViewModelEvent() {
        viewModel.updateSuccessEvent.observe(this, EventObserver {
            TipsDialog().setTitle("提示")
                .setContent("绑定手机修改成功\n请使用新手机号重新登录")
                .setOnConfirmClickListener {
                    UserUtils.removeUserData()
                    Router.getInstance()
                        .to(PersonalMainActivity.URL)
                        .withFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .start()
                    TheRouter.build(RouterNavigation.LoginActivity.URL)
                        .navigation()
                }.show(supportFragmentManager)
        })
    }
}