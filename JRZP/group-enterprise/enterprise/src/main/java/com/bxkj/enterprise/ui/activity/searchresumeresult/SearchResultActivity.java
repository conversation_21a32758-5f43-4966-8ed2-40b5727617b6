package com.bxkj.enterprise.ui.activity.searchresumeresult;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.common.base.BaseListActivity;
import com.bxkj.common.constants.AppConstants;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.SPUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.common.widget.dropdown.DropDownPopup;
import com.bxkj.common.widget.filterpopup.FilterOption;
import com.bxkj.common.widget.filterpopup.FilterOptionsGroup;
import com.bxkj.common.widget.filterpopup.FilterUtils;
import com.bxkj.common.widget.filterpopup.FilterView;
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.ecommon.mvp.contract.GetAreaListContract;
import com.bxkj.ecommon.mvp.persenter.GetAreaListPresenter;
import com.bxkj.ecommon.util.recyclerutil.RecycleViewDivider;
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.api.parameters.SearchResumeParameters;
import com.bxkj.enterprise.data.ResumeItemData;
import com.bxkj.enterprise.mvp.contract.ResumeListContract;
import com.bxkj.enterprise.mvp.presenter.ResumeListPresenter;
import com.bxkj.enterprise.ui.activity.invitingdelivery.InvitationDeliveryNavigation;
import com.bxkj.enterprise.ui.activity.postjob.PostJobV2Activity;
import com.bxkj.enterprise.ui.activity.resumedetails.ApplicantResumeDetailActivityV2;
import com.bxkj.enterprise.ui.activity.searchresume.SearchResumeActivity;
import com.bxkj.enterprise.ui.activity.searchresume.SearchSqlLiteHelper;
import com.bxkj.enterprise.weight.filterareapopup.FilterAreaView;
import com.bxkj.enterprise.weight.filterareapopup.FilterAreaView.OnAreaItemClickListener;
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity;
import com.zaaach.citypicker.CityPicker;
import com.zaaach.citypicker.adapter.OnPickListener;
import com.zaaach.citypicker.model.City;
import com.zaaach.citypicker.model.LocatedCity;

import java.util.Collections;
import java.util.List;

import javax.inject.Inject;

/**
 * @version V1.0
 * @Description: 搜索简历结果
 * @TODO: TODO
 * @date 2018/8/20
 */
public class SearchResultActivity extends BaseListActivity
  implements ResumeListContract.View, GetAreaListContract.View, SearchResultContract.View {

  private static final String PARAMETERS = "parameters";

  private static final String NEED_SEARCH_TEXT = "need_search_text";

  private static final int FILTER_SALARY_TAG = 1;

  private static final int FILTER_WORK_EXP_TAG = 2;

  private static final int FILTER_EDUCATION_TAG = 3;

  private static final int TO_SEARCH_CODE = 4;

  private static final int TO_INVITE_MULTIPLE_RESUME_CODE = 5;

  @Inject
  ResumeListPresenter mResumeListPresenter;

  @Inject
  GetAreaListPresenter mGetAreaListPresenter;

  @Inject
  SearchResultPresenter mSearchResultPresenter;

  private TextView tvSearchText;

  private TextView tvPageTitle;

  private View vLine;

  private TextView tvArea;

  private TextView tvSalary;

  private TextView tvWorkExp;

  private TextView tvEducation;

  private ConstraintLayout clFilterBar;

  private ImageView ivAllSelect;

  private TextView tvSelectedCount;

  private TextView tvInvite;

  private SearchResumeParameters mSearchResumeParameters;

  private SearchResultAdapter mSearchResultAdapter;

  private DropDownPopup mDropDownPopup;

  private FilterAreaView mFilterAreaView;

  private FilterView mFilterSalaryView, mFilterWorkExpView, mFilterEducationView;

  private CityPicker mCityPicker;

  private ActionDialog mNoCityTipsDialog;

  private final ActivityResultLauncher<Intent> launcher = registerForActivityResult(
    new StartActivityForResult(), result -> {
      if (result.getResultCode() == CityPickerActivity.RESULT_SELECT_CITY_SUCCESS) {
        mNoCityTipsDialog.dismiss();
      }
    });

  public static void start(Context context, SearchResumeParameters searchResumeParameters,
    boolean needSearchText) {
    Intent starter = new Intent(context, SearchResultActivity.class);
    starter.putExtra(NEED_SEARCH_TEXT, needSearchText);
    Bundle bundle = new Bundle();
    bundle.putParcelable(PARAMETERS, searchResumeParameters);
    starter.putExtras(bundle);
    context.startActivity(starter);
  }

  public static Intent newIntent(Context context,
    SearchResumeParameters searchResumeParameters) {
    Intent intent = new Intent(context, SearchResultActivity.class);
    intent.putExtra(PARAMETERS, searchResumeParameters);
    return intent;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_search_result;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mResumeListPresenter);
    presenters.add(mGetAreaListPresenter);
    presenters.add(mSearchResultPresenter);
    return presenters;
  }

  @Override
  protected void initIntent(Intent intent) {
    mSearchResumeParameters = intent.getParcelableExtra(PARAMETERS);
  }

  @Override
  protected void initPage() {
    super.initPage();
    bindView(getWindow().getDecorView());

    if (mSearchResumeParameters == null) {
      mSearchResumeParameters = new SearchResumeParameters();
    }
    mSearchResumeParameters.setCity(UserUtils.getUserSelectedCityId());
    if (getIntent().getBooleanExtra(NEED_SEARCH_TEXT, false)) {
      tvSearchText.setVisibility(View.VISIBLE);
      tvSearchText.setText(mSearchResumeParameters.getTitle());
    } else {
      tvPageTitle.setVisibility(View.VISIBLE);
    }

    getPageStatusLayout().show(PageStatusConfigFactory.newLoadingConfig());
    getStatusBarManager().titleBar(findViewById(R.id.cl_title_bar)).statusBarDarkFont(true).init();
    initCityPicker();
    setupDropDownMenu();

    setupSearchResultList();
    getRefreshLayoutManager().refreshPage();
    mSearchResultPresenter.getUserInviteBalance(getMUserID());
  }

  private void setupSearchResultList() {
    mSearchResultAdapter =
      new SearchResultAdapter(this, null, R.layout.enterprise_recycler_search_result_item);
    mSearchResultAdapter.registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
      @Override
      public void onChanged() {
        refreshSelectResumeCount();
      }
    });
    mSearchResultAdapter.setOnSelectedItemsChangeListener(
      selectedItems -> {
        refreshSelectResumeCount();
      });
    getRecyclerView().setLayoutManager(new LinearLayoutManager(this));
    getRecyclerView().addItemDecoration(
      new RecycleViewDivider(this, LinearLayoutManager.VERTICAL, DensityUtils.dp2px(this, 8),
        getMColor(R.color.common_f4f4f4), true));
    getRecyclerView().setAdapter(mSearchResultAdapter);
    mSearchResultAdapter.setOnItemClickListener((view, position) -> {
      ResumeItemData resumeItemData = mSearchResultAdapter.getData().get(position);
      if (view.getId() == R.id.tv_conversation) {
        mSearchResultPresenter.invitePreCheck(getMUserID(),
          Collections.singletonList(resumeItemData));
      } else {
        startActivity(
          ApplicantResumeDetailActivityV2.newIntent(SearchResultActivity.this,
            resumeItemData.getId(),
            resumeItemData.getUbInfo().getUid()));
      }
    });
  }

  private void refreshSelectResumeCount() {
    tvSelectedCount.setText(getString(R.string.search_result_selected_count_format,
      mSearchResultAdapter.getSelectedItems().size(), mSearchResultAdapter.getData().size()));
    tvInvite.setEnabled(mSearchResultAdapter.getSelectedItems().size() != 0);
    ivAllSelect.setSelected(
      mSearchResultAdapter.getSelectedItems().size() == mSearchResultAdapter.getData().size());
  }

  private void setupDropDownMenu() {
    mDropDownPopup = new DropDownPopup(this, vLine);
    mDropDownPopup.setOnItemExpandStatusChangeListener(
      (index, opened) -> clFilterBar.getChildAt(index).setSelected(opened));
    setupFilterAreaView();
    setupFilterSalaryView();
    setupFilterWorkExpView();
    setupFilterEducationView();
    mDropDownPopup.addContentViews(mFilterAreaView, mFilterSalaryView, mFilterWorkExpView,
      mFilterEducationView);
  }

  /**
   * 初始化筛选地区view
   */
  private void setupFilterAreaView() {
    mFilterAreaView = new FilterAreaView(this);
    mFilterAreaView.setOnAreaItemClickListener(new OnAreaItemClickListener() {
      @Override
      public void onAreaItemClicked(int position) {
        AreaOptionsData selectArea = mFilterAreaView.getAreaData().get(position);
        mSearchResumeParameters.setCounty(selectArea.getId());
        mSearchResumeParameters.setTown(ECommonApiConstants.NO_ID);
        mDropDownPopup.close();
        getRefreshLayoutManager().refreshPage();
      }

      @Override
      public void onChangeCityClicked() {
        mCityPicker.show();
      }
    });
  }

  /**
   * 创建筛选薪资view
   */
  private void setupFilterSalaryView() {
    String[] salary = getResources().getStringArray(R.array.work_salary);
    mFilterSalaryView = new FilterView.Builder(this)
      .setHeight(DensityUtils.dp2px(this, 400))
      .setItemClickedDismiss(true)
      .setBottomBarVisible(View.GONE)
      .build();
    mFilterSalaryView.addGroupItems(
      new FilterOptionsGroup<FilterOption>(FILTER_SALARY_TAG,
        FilterUtils.parseFilterOptions(salary), true));
    mFilterSalaryView.setOnFilterConfirmListener(positionHolderMap -> {
      mDropDownPopup.close();
      mSearchResumeParameters.setWillMoney(salary[positionHolderMap.get(FILTER_SALARY_TAG)]);
      tvSalary.setSelected(false);
      getPageStatusLayout().show(PageStatusConfigFactory.newLoadingConfig());
      getRefreshLayoutManager().refreshPage();
    });
  }

  /**
   * 创建筛选工作经验view
   */
  private void setupFilterWorkExpView() {
    mFilterWorkExpView = new FilterView.Builder(this)
      .setHeight(DensityUtils.dp2px(this, 400))
      .setItemClickedDismiss(true)
      .setBottomBarVisible(View.GONE)
      .build();
    mFilterWorkExpView.addGroupItems(new FilterOptionsGroup(FILTER_WORK_EXP_TAG,
      FilterUtils.parseFilterOptions(getResources().getStringArray(R.array.work_exp))));
    mFilterWorkExpView.setOnFilterConfirmListener(positionHolderMap -> {
      mDropDownPopup.close();
      mSearchResumeParameters.setWorkExpId(positionHolderMap.get(FILTER_WORK_EXP_TAG) + 1);
      tvWorkExp.setSelected(false);
      getPageStatusLayout().show(PageStatusConfigFactory.newLoadingConfig());
      getRefreshLayoutManager().refreshPage();
    });
  }

  /**
   * 创建筛选学历view
   */
  private void setupFilterEducationView() {
    mFilterEducationView = new FilterView.Builder(this)
      .setHeight(DensityUtils.dp2px(this, 400))
      .setItemClickedDismiss(true)
      .setBottomBarVisible(View.GONE)
      .build();
    mFilterEducationView.addGroupItems(new FilterOptionsGroup<FilterOption>(FILTER_EDUCATION_TAG,
      FilterUtils.parseFilterOptions(getResources().getStringArray(R.array.education))));
    mFilterEducationView.setOnFilterConfirmListener(positionHolderMap -> {
      mDropDownPopup.close();
      mSearchResumeParameters.setEducationId(positionHolderMap.get(FILTER_EDUCATION_TAG) + 1);
      tvEducation.setSelected(false);
      getPageStatusLayout().show(PageStatusConfigFactory.newLoadingConfig());
      getRefreshLayoutManager().refreshPage();
    });
  }

  @Override
  protected void loadData() {
    mResumeListPresenter.getResumeList(getMUserID(), mSearchResumeParameters,
      getRefreshLayoutManager().getCurrentPage(), 30);
  }

  private void initCityPicker() {
    mCityPicker = new CityPicker()
      .setLocatedCity(
        new LocatedCity(UserUtils.getUserLocateCityName(), ECommonApiConstants.NO_TEXT,
          String.valueOf(UserUtils.getUserLocateCityId())))
      .setFragmentManager(getSupportFragmentManager())
      .enableAnimation(true)
      .setAnimationStyle(R.style.common_RightPopupAnim)
      .setOnPickListener(new OnPickListener() {
        @Override
        public void onPick(int position, City data) {
          if (data != null) {
            mFilterAreaView.setCurrentCity(data.getName());
            mSearchResumeParameters.setCity(Integer.parseInt(data.getCode()));
            mGetAreaListPresenter.getAreaList(ECommonApiConstants.GET_AREA_TYPE,
              Integer.parseInt(data.getCode()));
            UserUtils.saveUserSelectedCityInfo(Integer.parseInt(data.getCode()), data.getName());
            getRefreshLayoutManager().refreshPage();
          }
        }

        @Override
        public void onLocate() {

        }
      });
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.iv_back) {
      finish();
    } else if (view.getId() == R.id.tv_search_text) {
      startActivityForResult(SearchResumeActivity.newIntent(this, true,
        SearchSqlLiteHelper.SEARCH_RESUME_RECORD_TABLE_NAME), TO_SEARCH_CODE);
    } else if (view.getId() == R.id.tv_area) {
      //未选择城市
      if (UserUtils.getUserSelectedCityId() == 0) {
        mNoCityTipsDialog = new ActionDialog.Builder()
          .setTitle("提示")
          .setContent("选择期望城市后可切换区域")
          .setOnConfirmClickListener((dialog) -> {
            launcher.launch(CityPickerActivity.newIntent(this));
          })
          .setCancelable(false).build();
        mNoCityTipsDialog.show(getSupportFragmentManager());
      } else {
        mFilterAreaView.setCurrentCity(UserUtils.getUserSelectedCityName());
        if (CheckUtils.isNullOrEmpty(mFilterAreaView.getAreaData())) {
          mGetAreaListPresenter.getAreaList(ECommonApiConstants.GET_AREA_TYPE,
            UserUtils.getUserSelectedCityId());
        }
        mDropDownPopup.showItemAsDropDown(0);
      }
    } else if (view.getId() == R.id.tv_salary) {
      mDropDownPopup.showItemAsDropDown(1);
    } else if (view.getId() == R.id.tv_work_exp) {
      mDropDownPopup.showItemAsDropDown(2);
    } else if (view.getId() == R.id.tv_education) {
      mDropDownPopup.showItemAsDropDown(3);
    } else if (view.getId() == R.id.iv_all_selected || view.getId() == R.id.tv_all_selected) {
      if (mSearchResultAdapter.getData().size() != mSearchResultAdapter.getSelectedItems().size()) {
        mSearchResultAdapter.selectAll();
      } else {
        mSearchResultAdapter.clearSelected();
      }
    } else {
      if (mSearchResultAdapter.getSelectedItems().isEmpty()) {
        showToast("请选择简历后邀约");
      } else {
        mSearchResultPresenter
          .invitePreCheck(getMUserID(), mSearchResultAdapter.getSelectedItems());
      }
    }
  }

  @Override
  public void getResumeListSuccess(List<ResumeItemData> resumeItemDataList, boolean noMore) {
    getPageStatusLayout().hidden();
    getRefreshLayoutManager().setNoMoreData(noMore);
    getRefreshLayoutManager().finishRefreshOrLoadMore();
    if (getRefreshLayoutManager().currentFirstPage()) {
      mSearchResultAdapter.clearSelected();
      mSearchResultAdapter.reset(resumeItemDataList);
      return;
    }
    mSearchResultAdapter.addAll(resumeItemDataList);
  }

  @Override
  public void getAreaListSuccess(int type, List<AreaOptionsData> optionsDataList) {
    if (type == ECommonApiConstants.GET_AREA_TYPE) {
      optionsDataList.add(0, new AreaOptionsData(0,
        getString(R.string.filter_area_default_all_format, SPUtils.getInstance().getString(
          AppConstants.SP_USER_CITY))));
      mFilterAreaView.setAreaData(optionsDataList);
    }
  }

  @Override
  public void hasPosition(List<ResumeItemData> selectResumeItems, boolean hasPosition) {
    if (hasPosition) {
      if (selectResumeItems.size() == 1) {
        ResumeItemData resumeInfo = selectResumeItems.get(0);
        InvitationDeliveryNavigation
          .create(new int[] { resumeInfo.getId() }, resumeInfo.getUbInfo().getName())
          .startForResult(SearchResultActivity.this, TO_INVITE_MULTIPLE_RESUME_CODE);
      } else {
        int[] resumeIDs = new int[selectResumeItems.size()];
        for (int i = 0; i < selectResumeItems.size(); i++) {
          resumeIDs[i] = selectResumeItems.get(i).getId();
        }
        InvitationDeliveryNavigation.create(resumeIDs, "")
          .startForResult(this, TO_INVITE_MULTIPLE_RESUME_CODE);
      }
    } else {
      new EActionDialog.Builder()
        .setContent(getString(R.string.no_position_tips))
        .setConfirmText(getString(R.string.go_to_post))
        .setOnConfirmClickListener((actionDialog, inputText) -> {
            actionDialog.dismiss();
            startActivity(PostJobV2Activity.newIntent(this));
          }
        )
        .build().show(getSupportFragmentManager(), EActionDialog.TAG);
    }
  }

  @Override
  public void getUserInviteBalanceSuccess(int balance) {
    tvInvite.setText(getString(R.string.enterprise_select_resume_count_format, balance));
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (requestCode == TO_SEARCH_CODE && resultCode == RESULT_OK && data != null) {
      String searchText = data.getStringExtra(SearchResumeActivity.SEARCH_TEXT);
      tvSearchText.setText(searchText);
      mSearchResumeParameters.setTitle(searchText);
      getRefreshLayoutManager().refreshPage();
    } else if (requestCode == TO_INVITE_MULTIPLE_RESUME_CODE && resultCode == RESULT_OK) {
      mSearchResultAdapter.clearSelected();
      getRefreshLayoutManager().refreshPage();
      mSearchResultPresenter.getUserInviteBalance(getMUserID());
    }
  }

  @Override
  public void onBackPressed() {
    if (mDropDownPopup.isShowing()) {
      mDropDownPopup.close();
    } else {
      super.onBackPressed();
    }
  }

  private void bindView(View bindSource) {
    tvSearchText = bindSource.findViewById(R.id.tv_search_text);
    tvPageTitle = bindSource.findViewById(R.id.tv_page_title);
    vLine = bindSource.findViewById(R.id.v_line);
    tvArea = bindSource.findViewById(R.id.tv_area);
    tvSalary = bindSource.findViewById(R.id.tv_salary);
    tvWorkExp = bindSource.findViewById(R.id.tv_work_exp);
    tvEducation = bindSource.findViewById(R.id.tv_education);
    clFilterBar = bindSource.findViewById(R.id.cl_filter_bar);
    ivAllSelect = bindSource.findViewById(R.id.iv_all_selected);
    tvSelectedCount = bindSource.findViewById(R.id.tv_selected_count);
    tvInvite = bindSource.findViewById(R.id.tv_conversation);
    bindSource.findViewById(R.id.iv_back).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_search_text).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_area).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_salary).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_work_exp).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_education).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.iv_all_selected).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_all_selected).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_conversation).setOnClickListener(v -> onViewClicked(v));
  }
}
