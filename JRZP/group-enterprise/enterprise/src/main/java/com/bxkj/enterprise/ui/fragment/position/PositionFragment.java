package com.bxkj.enterprise.ui.fragment.position;

import android.view.View;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import com.bxkj.common.base.BaseDaggerFragment;
import com.bxkj.common.enums.AuthenticationType;
import com.therouter.router.Route;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.kotlin.ResourceUtils;
import com.bxkj.common.util.rxbus.RxBus;
import com.bxkj.common.util.rxbus.RxMsgCode;
import com.bxkj.common.widget.DropDownMenuView;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.common.widget.dropdown.DropDownPopup;
import com.bxkj.ecommon.adapter.CommonTabLayoutAdapter;
import com.bxkj.ecommon.widget.dialogfragment.ETipsDialog;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.AuthenticatedData;
import com.bxkj.enterprise.mvp.contract.CheckCertificationContract;
import com.bxkj.enterprise.mvp.contract.CheckCompanyInfoFillContract;
import com.bxkj.enterprise.mvp.presenter.CheckCertificationPresenter;
import com.bxkj.enterprise.mvp.presenter.CheckCompanyInfoFillPresenter;
import com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoNavigation;
import com.bxkj.jrzp.user.mine.ui.membercenter.MemberCenterWebNavigation;
import java.util.List;
import javax.inject.Inject;
import net.lucode.hackware.magicindicator.MagicIndicator;
import net.lucode.hackware.magicindicator.ViewPagerHelper;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;

/**
 * @Description: 我发布的职位
 * @TODO: TODO
 */
@Route(path = IPostedJobsFragment.PATH)
public class PositionFragment extends BaseDaggerFragment
  implements CheckCertificationContract.View, CheckCompanyInfoFillContract.View,
  JobAllRefreshContract.View {

  public static Fragment newInstance() {
    return new PositionFragment();
  }

  @Inject
  CheckCertificationPresenter mCheckCertificationPresenter;

  @Inject
  CheckCompanyInfoFillPresenter mCheckCompanyInfoFillPresenter;

  @Inject
  JobAllRefreshPresenter mJobAllRefreshPresenter;

  private ViewPager vpPositionContent;

  private MagicIndicator tabLayout;

  private ConstraintLayout clTitleBar;

  private TextView tvJobType;

  private TextView tvPostJob;

  private DropDownPopup mDropDownPopup;

  @Override
  public void noVipTips(@NonNull final String errMsg) {
    new ActionDialog.Builder()
      .setTitle("提示")
      .setContent(errMsg)
      .setConfirmText("开通会员")
      .setOnConfirmClickListener(dialog -> {
        MemberCenterWebNavigation.create().start();
      }).build().show(getChildFragmentManager());
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mCheckCertificationPresenter);
    presenters.add(mCheckCompanyInfoFillPresenter);
    presenters.add(mJobAllRefreshPresenter);
    return presenters;
  }

  @Override
  public int getLayoutId() {
    return R.layout.enterprise_fragment_position;
  }

  @Override
  public void initImmersionBar() {
    getStatusBarManager().titleBar(clTitleBar).statusBarDarkFont(true, 0.4f)
      .navigationBarColor(R.color.common_white).init();
  }

  @Override
  public void initPage() {
    bindView(getView());

    initSwitchJobTypePopup();

    PositionChildFragmentAdapter positionChildFragmentAdapter =
      new PositionChildFragmentAdapter(getChildFragmentManager(),
        getResources().getStringArray(R.array.enterprise_publish_position_classify));
    vpPositionContent.setAdapter(positionChildFragmentAdapter);
    vpPositionContent.setOffscreenPageLimit(4);
    final CommonNavigator commonNavigator = new CommonNavigator(getContext());
    commonNavigator.setAdjustMode(true);
    commonNavigator.setAdapter(new CommonTabLayoutAdapter(vpPositionContent));
    tabLayout.setNavigator(commonNavigator);
    ViewPagerHelper.bind(tabLayout, vpPositionContent);

    if (getArguments() != null && getArguments().getBoolean(IPostedJobsFragment.EXTRA_HIDE_BACK,
      false)) {
      rootView.findViewById(R.id.iv_left).setVisibility(View.GONE);
      tvPostJob.setVisibility(View.VISIBLE);
    } else {
      tvPostJob.setVisibility(View.GONE);
    }
  }

  private void initSwitchJobTypePopup() {
    String[] jobTypes = ResourceUtils.getResArray(this, R.array.my_job_list_job_type);
    DropDownMenuView jobTypeMenuView = new DropDownMenuView(getParentActivity());
    jobTypeMenuView.setData(jobTypes);
    jobTypeMenuView.setOnItemClickListener((view, position) -> {
      int jobType;
      switch (position) {
        case 1: {
          jobType = JobType.NORMAL_TYPE;
          break;
        }
        case 2: {
          jobType = JobType.PART_TIME;
          break;
        }
        case 3: {
          jobType = JobType.SCHOOL_TYPE;
          break;
        }
        default: {
          jobType = JobType.ALL_TYPE;
          break;
        }
      }
      RxBus.get()
        .post(new RxBus.Message(RxMsgCode.ACTION_SWITCH_MY_JOB_LIST_TYPE, jobType));
      tvJobType.setText(jobTypes[position]);
      mDropDownPopup.close();
    });

    mDropDownPopup = new DropDownPopup(getParentActivity(), clTitleBar);
    mDropDownPopup.addContentViews(jobTypeMenuView);
  }

  private void onViewClicked(View v) {
    if (v.getId() == R.id.iv_left) {
      getParentActivity().finish();
    } else if (v.getId() == R.id.tv_job_type) {
      mDropDownPopup.showAsDropDown();
    } else if (v.getId() == R.id.tv_right) {
      refreshAllJob();
    } else {
      postJobPreCheck();
    }
  }

  public void postJobPreCheck() {
    mCheckCompanyInfoFillPresenter.checkCompanyInfoFill(getUserId());
  }

  private void refreshAllJob() {
    mJobAllRefreshPresenter.refreshAllJob();
  }

  @Override
  public void authenticated(AuthenticatedData authenticated) {
    if (authenticated.getState() == 10001) {
      new PostJobMenuDialog().show(getChildFragmentManager());
    } else {
      new ETipsDialog()
        .setTitle(getString(R.string.certificate))
        .setContent(authenticated.getMsg())
        .setConfirmText(authenticated.getCertificateText())
        .setOnConfirmClickListener(() -> {
            if (authenticated.canToCertificate()) {
              BusinessBasicInfoNavigation.navigate(true, AuthenticationType.ENTERPRISE, false)
                .start();
            }
          }
        )
        .show(getChildFragmentManager(), ETipsDialog.TAG);
    }
  }

  @Override
  public void companyInfoFill() {
    mCheckCertificationPresenter.checkCertification(getUserId());
  }

  @Override
  public void companyInfoNotFill(String msg) {
    new ETipsDialog()
      .setTitle(getString(R.string.company_info_not_fill))
      .setContent(getString(R.string.company_info_not_fill_content))
      .setOnConfirmClickListener(() ->
        BusinessBasicInfoNavigation.navigate(true, AuthenticationType.ENTERPRISE, false).start()
      )
      .show(getChildFragmentManager(), ETipsDialog.TAG);
  }

  @Override
  public void refreshSuccess() {
    showToast("刷新成功");
  }

  @Override
  public void refreshFailed(@NonNull String errMsg) {
    showToast(errMsg);
  }

  private void bindView(View bindSource) {
    vpPositionContent = bindSource.findViewById(R.id.vp_position_content);
    tabLayout = bindSource.findViewById(R.id.tab_layout);
    clTitleBar = bindSource.findViewById(R.id.title_bar);
    tvJobType = bindSource.findViewById(R.id.tv_job_type);
    tvPostJob = bindSource.findViewById(R.id.tv_post_job);
    bindSource.findViewById(R.id.iv_left).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_job_type).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_right).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_post_job).setOnClickListener(v -> onViewClicked(v));
  }
}
