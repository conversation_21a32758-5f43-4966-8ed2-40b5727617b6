package com.bxkj.enterprise.mvp.contract;

import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.mvp.contract
 * @Description: Industry
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface IndustryContract {
    interface View extends BaseView {
        void getIndustryListSuccess(List<PickerOptionsData> optionsItemDataList);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getIndustryList();
    }
}
