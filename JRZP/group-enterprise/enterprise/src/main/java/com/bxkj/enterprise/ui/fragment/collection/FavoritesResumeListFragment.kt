package com.bxkj.enterprise.ui.fragment.collection

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.BFragmentFavoritesResumeListBinding
import com.bxkj.enterprise.ui.activity.resumedetails.ApplicantResumeNavigation
import com.bxkj.enterprise.data.ResumeItemDataV2
import com.tencent.qcloud.ugckit.component.swipemenu.SwipeMenuLayout

/**
 * Description: 收藏、下载的简历
 * Author:Sanjin
 * Date:2024/3/21
 **/
class FavoritesResumeListFragment :
  BaseDBFragment<BFragmentFavoritesResumeListBinding, FavoritesResumeListViewModel>() {

  private val pageType by lazy {
    arguments?.getInt(EXTRA_PAGE_TYPE).getOrDefault(PAGE_TYPE_FAVORITES)
  }

  private val toResumeDetailsLauncher = registerForActivityResult(StartActivityForResult()) {
    viewModel.refreshList()
  }

  override fun getViewModelClass(): Class<FavoritesResumeListViewModel> =
    FavoritesResumeListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.b_fragment_favorites_resume_list

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupResumeListAdapter()

    viewModel.start(pageType)
  }

  private fun setupResumeListAdapter() {
    val resumeListAdapter = object : SimpleDBListAdapter<ResumeItemDataV2>(
      requireContext(),
      R.layout.enterprise_recycler_received_resume_item_v2
    ) {
      override fun convert(
        holder: SuperViewHolder,
        viewType: Int,
        item: ResumeItemDataV2,
        position: Int
      ) {
        super.convert(holder, viewType, item, position)
        if (pageType == PAGE_TYPE_DOWNLOAD) {
          (holder.itemView as SwipeMenuLayout).isSwipeEnable = false
          holder.findViewById<TextView>(R.id.tv_state).visibility = View.INVISIBLE
          holder.findViewById<View>(R.id.v_split).visibility = View.GONE
          holder.findViewById<TextView>(R.id.tv_date).visibility = View.GONE
        } else {
          holder.findViewById<TextView>(R.id.tv_delete).text =
            getString(R.string.b_favorites_cancel)
        }
      }
    }.apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          data.get(position).let { item ->
            if (v.id == R.id.tv_delete) {
              ActionDialog.Builder()
                .setContent("取消收藏此简历")
                .setOnConfirmClickListener {
                  viewBinding.includeResumeList.recyclerContent.smoothCloseMenu()
                  viewModel.cancelFavoriteResume(item)
                }.build().show(childFragmentManager)
            } else {
              toResumeDetailsLauncher.launch(
                ApplicantResumeNavigation.navigate(
                  item.ubInfo?.uid.getOrDefault(),
                  item.id
                ).createIntent(requireContext())
              )
            }
          }
        }
      }, R.id.tv_delete)
    }

    viewBinding.includeResumeList.recyclerContent.apply {
      layoutManager = LinearLayoutManager(requireContext())
      addItemDecoration(
        LineItemDecoration.Builder()
          .divider(getResDrawable(R.drawable.divider_8))
          .drawHeader(true)
          .drawFoot(true)
          .build()
      )
    }

    viewModel.listViewModel.setAdapter(resumeListAdapter)
  }

  companion object {

    const val PAGE_TYPE_FAVORITES = 1
    const val PAGE_TYPE_DOWNLOAD = 2

    private const val EXTRA_PAGE_TYPE = "PAGE_TYPE"

    /**
     * 根据[pageType]展示不同的页面
     * [PAGE_TYPE_FAVORITES]:收藏的简历
     * [PAGE_TYPE_DOWNLOAD]:下载的简历
     */
    fun newInstance(pageType: Int): Fragment {
      return FavoritesResumeListFragment().apply {
        arguments = bundleOf(EXTRA_PAGE_TYPE to pageType)
      }
    }
  }
}