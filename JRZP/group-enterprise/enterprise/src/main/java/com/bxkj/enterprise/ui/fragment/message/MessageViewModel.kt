package com.bxkj.enterprise.ui.fragment.message

import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.base.mvvm.callback.ResultListCallBack
import com.bxkj.ecommon.constants.ECommonApiConstants
import com.bxkj.enterprise.data.MessageItemData
import com.bxkj.enterprise.data.source.MessageRepo
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.message
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/8/7
 * @version V1.0
 */
class MessageViewModel @Inject constructor(
  private val mMessageRepo: MessageRepo
) : BaseViewModel() {

  val listViewModel = RefreshListViewModel()

  init {
    listViewModel.setOnLoadDataListener { currentPage ->
      mMessageRepo.getMessageList(getSelfUserID(),
        AppConstants.BUSINESS_TYPE,
        MessageRepo.MESSAGE_TYPE_ALL,
        MessageRepo.MESSAGE_LOOK_STATUS_NOT_VIDEO,
        currentPage,
        ECommonApiConstants.DEFAULT_PAGE_SIZE,
        object :
            ResultListCallBack<List<MessageItemData>> {
          override fun onSuccess(data: List<MessageItemData>?) {
            listViewModel.addAll(data)
          }

          override fun onNoMoreData() {
            listViewModel.noMoreData()
          }

          override fun onError(respondThrowable: RespondThrowable) {
            listViewModel.loadError()
          }
        })
    }
  }
}