package com.bxkj.enterprise.ui.fragment.schoolrecruitlist

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.FragmentSchoolRecruitListBinding
import com.bxkj.enterprise.ui.activity.schoolrecruiteditor.SchoolRecruitEditorActivity
import com.bxkj.enterprise.ui.activity.schoolrecruitmanagement.SchoolRecruitManagementActivity
import com.bxkj.enterprise.ui.activity.schoolrecruitresume.SchoolRecruitResumeActivity

/**
 * 校招列表
 * @author: sanjin
 * @date: 2022/4/14
 */
class SchoolRecruitListFragment :
    BaseDBFragment<FragmentSchoolRecruitListBinding, SchoolRecruitListV2ViewModel>(),
    View.OnClickListener {

    companion object {

        const val STATE_ALL = -1
        const val STATE_PASSED = 0
        const val STATE_PENDING = 1
        const val STATE_FAILED = 2

        private const val EXTRA_STATE = "STATE"

        fun newInstance(state: Int): Fragment {
            return SchoolRecruitListFragment().apply {
                arguments = bundleOf(EXTRA_STATE to state)
            }
        }
    }

    private var listAdapter: SchoolRecruitListV2Adapter? = null

    override fun getViewModelClass(): Class<SchoolRecruitListV2ViewModel> =
        SchoolRecruitListV2ViewModel::class.java

    override fun getLayoutId(): Int = R.layout.fragment_school_recruit_list

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        viewBinding.onClickListener = this

        setupSchoolRecruitListAdapter()

        subscribeParentShowSelectAllEvent()

        subscribeSchoolInfoChangeEvent()

        subscribeViewModelEvent()

        viewModel.start(getArgumentsPageState())
    }

    private fun subscribeSchoolInfoChangeEvent() {
        addDisposable(RxBus.get().toObservable(RxBus.Message::class.java).subscribe {
            if (it.code == RxMsgCode.ACTION_SCHOOL_RECRUIT_INFO_CHANGE_SUCCESS) {
                it.msg?.let { msg ->
                    val ignoreState = (msg as IntArray)
                    if (!ignoreState.contains(getArgumentsPageState())) {
                        viewModel.reloadData()
                    }
                } ?: let {
                    viewModel.reloadData()
                }
            }
        })
    }

    private fun subscribeParentShowSelectAllEvent() {
        (requireActivity() as SchoolRecruitManagementActivity).viewModel.showSelectAll.observe(this) {
            if (it == false) {
                listAdapter?.setShowSelect(it)
                viewModel.setShowSelectAll(it)
            }
        }
    }

    private fun subscribeViewModelEvent() {
        viewModel.notifySchoolRecruitDataChangeEvent.observe(this) {
            RxBus.get().post(
                RxBus.Message(
                    RxMsgCode.ACTION_SCHOOL_RECRUIT_INFO_CHANGE_SUCCESS,
                    intArrayOf(getArgumentsPageState())
                )
            )
        }
    }

    private fun getArgumentsPageState() = requireArguments().getInt(EXTRA_STATE)

    fun setShowSelectAll(show: Boolean) {
        listAdapter?.setShowSelect(show)
        viewModel.setShowSelectAll(show)
    }

    private fun setupSchoolRecruitListAdapter() {
        listAdapter = SchoolRecruitListV2Adapter(requireContext(), viewModel).apply {
            setOnItemClickListener(object : SuperItemClickListener {
                override fun onClick(v: View, position: Int) {
                    val item = data[position]
                    when (v.id) {
                        R.id.iv_edit -> {
                            if (item.isReviewing()) {
                                TipsDialog()
                                    .setContent("该校招信息审核中，无法修改")
                                    .show(childFragmentManager)
                            } else {
                                startActivity(
                                    SchoolRecruitEditorActivity.newIntent(
                                        requireContext(),
                                        item.id
                                    )
                                )
                            }
                        }
                        R.id.tv_delivery_count -> {
                            if (item.isReviewing()) {
                                TipsDialog()
                                    .setContent("该校招信息审核中，无法查看投递信息")
                                    .show(childFragmentManager)
                            } else {
                                startActivity(
                                    SchoolRecruitResumeActivity.newIntent(
                                        requireContext(),
                                        item.id
                                    )
                                )
                            }
                        }
                        R.id.iv_delete -> {
                            ActionDialog.Builder()
                                .setContent(getString(R.string.school_recruit_management_delete_tips))
                                .setOnConfirmClickListener {
                                    item?.checked = true
                                    viewModel.deleteSelected()
                                }.build().show(childFragmentManager)
                        }
                    }
                }
            }, R.id.iv_edit, R.id.tv_delivery_count, R.id.iv_delete)
        }

        viewBinding.includeSchoolList.recyclerContent.apply {
            layoutManager = LinearLayoutManager(requireActivity())
            addItemDecoration(
                LineItemDecoration.Builder()
                    .divider(getResDrawable(R.drawable.divider_f4f4f4_6))
                    .build()
            )
        }

        viewModel.schoolRecruitListViewModel.setAdapter(listAdapter)
    }

    override fun onClick(v: View?) {
        v?.let {
            if (v.id == R.id.tv_confirm_delete) {
                ActionDialog.Builder()
                    .setContent(getString(R.string.school_recruit_management_delete_tips))
                    .setOnConfirmClickListener {
                        viewModel.deleteSelected()
                    }.build().show(childFragmentManager)
            }
        }
    }
}