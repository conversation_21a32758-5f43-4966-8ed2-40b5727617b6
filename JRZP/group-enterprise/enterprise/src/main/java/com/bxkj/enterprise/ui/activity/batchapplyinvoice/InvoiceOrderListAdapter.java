package com.bxkj.enterprise.ui.activity.batchapplyinvoice;

import android.content.Context;
import android.widget.ImageView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.OrderItemData;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.batchapplyinvoice
 * @Description:
 * @TODO: TODO
 * @date 2018/10/19
 */
public class InvoiceOrderListAdapter extends SuperAdapter<OrderItemData> {

  private List<OrderItemData> mSelectedOrderItems;

  public InvoiceOrderListAdapter(Context context, List<OrderItemData> list, int layoutResId) {
    super(context, layoutResId, list);
    mSelectedOrderItems = new ArrayList<>();
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType, OrderItemData orderItemData,
      int position) {
    holder.setText(R.id.tv_order_name, orderItemData.getContent());
    holder.setText(R.id.tv_order_price,
        String.format(mContext.getString(R.string.price_format), orderItemData.getPrice()));
    holder.setText(R.id.tv_order_type, String.format(mContext.getString(R.string.order_type_format),
        orderItemData.getTypeName()));
    holder.setText(R.id.tv_order_time, orderItemData.getTime());

    ImageView ivSelected = holder.findViewById(R.id.iv_selected);
    ivSelected.setSelected(mSelectedOrderItems.contains(orderItemData));
    holder.itemView.setOnClickListener(view -> {
      if (mSelectedOrderItems.contains(orderItemData)) {
        mSelectedOrderItems.remove(orderItemData);
      } else {
        mSelectedOrderItems.add(orderItemData);
      }
      notifyItemChanged(position);
      if (SuperItemClickListener != null) {
        SuperItemClickListener.onClick(view, position);
      }
    });
  }

  public List<OrderItemData> getSelectedOrderItems() {
    return mSelectedOrderItems;
  }

  public void setAllSelected(boolean selected) {
    if (selected) {
      mSelectedOrderItems.clear();
      mSelectedOrderItems.addAll(mList);
    } else {
      mSelectedOrderItems.clear();
    }
    notifyDataSetChanged();
    if (SuperItemClickListener != null) {
      SuperItemClickListener.onClick(null, 0);
    }
  }
}
