package com.bxkj.enterprise.data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 专业技能
 * @TODO: TODO
 * @date 2018/5/11
 */

public class ResumeSkillData {
    private int id;
    private String name;
    private String degree;
    private String remark;

    private List<ResumeSkillData> professionalSkillItemDataList;

    public ResumeSkillData() {
    }

    public ResumeSkillData(List<ResumeSkillData> professionalSkillItemDataList) {
        this.professionalSkillItemDataList = professionalSkillItemDataList;
    }

    public List<ResumeSkillData> getProfessionalSkillItemDataList() {
        return professionalSkillItemDataList;
    }

    public void setProfessionalSkillItemDataList(List<ResumeSkillData> professionalSkillItemDataList) {
        this.professionalSkillItemDataList = professionalSkillItemDataList;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
