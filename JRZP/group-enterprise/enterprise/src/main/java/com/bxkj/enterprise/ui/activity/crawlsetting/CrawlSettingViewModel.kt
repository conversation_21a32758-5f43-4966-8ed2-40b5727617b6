package com.bxkj.enterprise.ui.activity.crawlsetting

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.enterprise.data.source.AccountInfoRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/10/26
 */
class CrawlSettingViewModel @Inject constructor(
    private val accountInfoRepo: AccountInfoRepo
) : BaseViewModel() {

    val crawlOpenState = MutableLiveData<Boolean>()

    fun start() {
        getSyncJobOpenState()
    }

    private fun getSyncJobOpenState() {
        viewModelScope.launch {
            accountInfoRepo.getSyncJobOpenState()
                .handleResult({
                    it?.let {
                        crawlOpenState.value = (it == 1)
                    }
                })
        }
    }

    fun switchSyncJobOpenState(opened: Boolean) {
        showLoading()
        viewModelScope.launch {
            accountInfoRepo.setupSyncJobOpenState(if (opened) 1 else 0)
                .handleResult({

                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }
}