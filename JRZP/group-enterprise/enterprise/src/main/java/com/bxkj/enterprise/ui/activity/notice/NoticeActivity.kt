package com.bxkj.enterprise.ui.activity.notice

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.ecommon.base.EBaseDBActivity
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseActivityNoticeBinding
import com.bxkj.enterprise.ui.fragment.message.MessageFeedbackAdapter
import com.bxkj.enterprise.ui.fragment.message.MessageViewModel

class NoticeActivity : EBaseDBActivity<EnterpriseActivityNoticeBinding, MessageViewModel>() {

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, NoticeActivity::class.java)
        }
    }

    override fun getViewModelClass(): Class<MessageViewModel> = MessageViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_activity_notice

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        setupMessageList()
        viewModel.listViewModel.refresh()
    }

    private fun setupMessageList() {
        val messageListAdapter =
            MessageFeedbackAdapter(this, R.layout.enterprise_recycler_message_feedback_item)
        val messageList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
        messageList.layoutManager = LinearLayoutManager(this)
        viewModel.listViewModel.setAdapter(messageListAdapter)
    }
}