package com.bxkj.enterprise.ui.activity.resumedetails

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/27
 * @version: V1.0
 */
class ApplicantResumeNavigation {
  companion object {
    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/applicantresume"

    const val EXTRA_APPLICANT_ID = "applicant_id"
    const val EXTRA_RESUME_ID = "resume_id"

    fun navigate(applicantUserId: Int, resumeId: Int): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_APPLICANT_ID, applicantUserId)
        .withInt(EXTRA_RESUME_ID, resumeId)
    }
  }
}