package com.bxkj.enterprise.ui.activity.addressmanagement

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseActivityAddressManagementBinding
import com.bxkj.enterprise.ui.activity.selectaddressbymap.SelectAddressByMapActivity
import com.sanjindev.pagestatelayout.OnStateSetUpListener

/**
 *
 * @author: sanjin
 * @date: 2022/10/31
 */
class AddressManagementActivity :
    BaseDBActivity<EnterpriseActivityAddressManagementBinding, AddressManagementViewModel>() {

    companion object {

        private const val EXTRA_SELECTED_ADDRESS_ID = "SELECTED_ADDRESS_ID"

        const val EXTRA_RESULT_ADDRESS = "RESULT_ADDRESS"

        fun newIntent(context: Context, selectedAddressId: Int? = 0): Intent {
            return Intent(context, AddressManagementActivity::class.java).apply {
                putExtra(EXTRA_SELECTED_ADDRESS_ID, selectedAddressId)
            }
        }
    }

    private val _editAddressLauncher = registerForActivityResult(StartActivityForResult()) {
        if (it.resultCode == SelectAddressByMapActivity.RESULT_ADDRESS_CHANGE) {
            viewModel.refreshPage()
        }
    }

    override fun getViewModelClass(): Class<AddressManagementViewModel> =
        AddressManagementViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_activity_address_management

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        viewBinding.tvAdd.setOnClickListener {
            _editAddressLauncher.launch(SelectAddressByMapActivity.newIntent(this@AddressManagementActivity))
        }

        setupAddressListAdapter()

        subscribeViewModelEvent()

        viewModel.refreshPage()
    }

    private fun subscribeViewModelEvent() {
        viewModel.addressList.observe(this) {
            viewBinding.pslAddressList.hidden()
        }

        viewModel.showEmptyPageCommand.observe(this, EventObserver {
            viewBinding.pslAddressList.show(ErrorPageState::class.java,
                object : OnStateSetUpListener<ErrorPageState> {
                    override fun onStateSetUp(pageState: ErrorPageState) {
                        pageState.apply {
                            setImage(R.drawable.common_ic_no_content)
                            setContent("没有地址")
                            setNextOptionText("立即创建")
                            setNextOptionClickListener {
                                _editAddressLauncher.launch(
                                    SelectAddressByMapActivity.newIntent(
                                        this@AddressManagementActivity
                                    )
                                )
                            }
                        }
                    }
                })
        })

        viewModel.showErrorPageCommand.observe(this, EventObserver {
            viewBinding.pslAddressList.show(ErrorPageState::class.java,
                object : OnStateSetUpListener<ErrorPageState> {
                    override fun onStateSetUp(pageState: ErrorPageState) {
                        pageState.apply {
                            setNextOptionClickListener { viewModel.refreshPage() }
                        }
                    }
                })
        })
    }

    private fun setupAddressListAdapter() {
        val addressListAdapter = CompanyAddressListAdapter().apply {
            setOnItemClickListener(
                object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        getData()?.get(position)?.let { clickItem ->
                            if (v.id == R.id.tv_edit) {
                                _editAddressLauncher.launch(
                                    SelectAddressByMapActivity.newIntent(
                                        this@AddressManagementActivity,
                                        clickItem
                                    )
                                )
                            } else {
                                setResult(RESULT_OK, Intent().apply {
                                    putExtra(EXTRA_RESULT_ADDRESS, clickItem)
                                })
                                finish()
                            }
                        }
                    }
                }, R.id.tv_edit
            )
        }
        addressListAdapter.setSelectedAddressId(intent.getIntExtra(EXTRA_SELECTED_ADDRESS_ID, 0))
        viewBinding.recyclerAddressList.apply {
            layoutManager = LinearLayoutManager(this@AddressManagementActivity)
            addItemDecoration(
                LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_f4f4f4))
                    .drawFoot(true)
                    .build()
            )
            adapter = addressListAdapter
        }
    }
}