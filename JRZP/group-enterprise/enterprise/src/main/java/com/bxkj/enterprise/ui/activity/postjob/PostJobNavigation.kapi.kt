package com.bxkj.enterprise.ui.activity.postjob

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

class PostJobNavigation {

    companion object {

        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/postjob"

        const val EXTRA_JOB_TYPE = "JOB_TYPE"
        const val TYPE_FULL_TIME = 0
        const val TYPE_DEPART_TIME = 1

        const val EXTRA_NEXT_STEP = "NEXT_SETP"
        const val NEXT_BACK = 1
        const val NEXT_BIND_VIDEO = 0

        fun navigate(jobType: Int = TYPE_FULL_TIME, nextStep: Int = NEXT_BIND_VIDEO): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withInt(EXTRA_JOB_TYPE, jobType)
                .withInt(EXTRA_NEXT_STEP, nextStep)
        }
    }
}