package com.bxkj.enterprise.ui.activity.companyproducts;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.enterprise.data.ProductItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.companyproducts
 * @Description: CompanyProducts
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface CompanyProductsContract {
    interface View extends BaseView {
        void getProductsListSuccess(List<ProductItemData> productItemDataList);
        void noProducts();
        void deleteSuccess();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void getProductsList(int userId);

        abstract void deleteProduct(int userId, int productId);
    }
}
