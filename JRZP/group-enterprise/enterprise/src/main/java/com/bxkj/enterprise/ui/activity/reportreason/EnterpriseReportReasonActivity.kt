package com.bxkj.enterprise.ui.activity.reportreason

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.ecommon.base.EBaseDBActivity
import com.bxkj.enterprise.R
import com.bxkj.enterprise.R.layout
import com.bxkj.enterprise.databinding.EnterpriseActivityReportReasonBinding
import com.bxkj.enterprise.ui.activity.reportreason.EnterprsieReportReasonItem.DiffCallback

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/8
 * @version: V1.0
 */
class EnterpriseReportReasonActivity :
  EBaseDBActivity<EnterpriseActivityReportReasonBinding, EnterpriseReportReasonViewModel>() {

  companion object {

    const val EXTRA_RESULT_REASON = "RESULT_REASON"

    fun newIntent(context: Context): Intent {
      return Intent(context, EnterpriseReportReasonActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<EnterpriseReportReasonViewModel> =
    EnterpriseReportReasonViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_activity_report_reason

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupReasonAdapter()
    viewModel.start()
  }

  private fun setupReasonAdapter() {
    val reportReasonListAdapter =
      SimpleDiffListAdapter(
        layoutId = layout.enterprise_recycler_report_reason_item,
        itemCallback = DiffCallback()
      ).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            getData()?.let {
              backAndResult(it[position])
            }
          }
        })
      }
    viewBinding.recyclerReason.layoutManager = LinearLayoutManager(this)
    viewBinding.recyclerReason.adapter = reportReasonListAdapter
  }

  private fun backAndResult(enterprsieReportReasonItem: EnterprsieReportReasonItem) {
    val resultIntent = Intent()
    resultIntent.putExtra(EXTRA_RESULT_REASON, enterprsieReportReasonItem.title)
    setResult(Activity.RESULT_OK, resultIntent)
    finish()
  }
}