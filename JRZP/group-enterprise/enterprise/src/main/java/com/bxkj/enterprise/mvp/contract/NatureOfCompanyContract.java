package com.bxkj.enterprise.mvp.contract;

import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.mvp.contract
 * @Description: NatureOfCompany
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface NatureOfCompanyContract {
    interface View extends BaseView {
        void getNatureOfCompanySuccess(List<PickerOptionsData> pickerOptionsData);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getNatureOfCompany();

    }
}
