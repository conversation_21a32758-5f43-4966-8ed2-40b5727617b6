package com.bxkj.enterprise.ui.activity.searchresumeresult;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.imageloder.base.ImageLoader;
import com.bxkj.common.util.TimeUtils;
import com.bxkj.common.util.imageloader.GlideLoadConfig;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.ResumeItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.searchresult
 * @Description:
 * @TODO: TODO
 * @date 2018/8/20
 */
public class SearchResultAdapter extends SuperAdapter<ResumeItemData> {

  public SearchResultAdapter(Context context, List<ResumeItemData> list, int layoutResId) {
    super(context, layoutResId, list);
  }

  @Override
  protected Boolean openMultiSelect() {
    return true;
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType, ResumeItemData resumeItemData,
      int position) {
    ResumeItemData.UbInfoBean info = resumeItemData.getUbInfo();
    holder.setText(R.id.tv_name, info.getName());
    holder.setText(R.id.tv_about, resumeItemData.getAbout());
    holder.setText(R.id.tv_position, mContext.getResources()
        .getString(R.string.position_format, resumeItemData.getDetailsName2()));
    holder.setText(R.id.tv_time, TimeUtils.isToday(resumeItemData.getEdate1()) ? mContext.getString(
        R.string.resume_today_update) : resumeItemData.getEdate1().split("\\s")[0]);
    ImageLoader.loadImage(mContext, new GlideLoadConfig.Builder().url(info.getRealAvatar())
        .error(info.getSex() == 0 ? R.drawable.ic_default_avatar_man
            : R.drawable.ic_default_avatar_woman)
        .circle()
        .into(holder.findViewById(R.id.iv_header))
        .build());
    holder.findViewById(R.id.tv_topped)
        .setVisibility(resumeItemData.getTop() == 1 ? View.VISIBLE : View.GONE);

    final ImageView ivSelect = holder.findViewById(R.id.iv_select);
    ivSelect.setSelected(getSelectedItems().contains(resumeItemData));
    ivSelect.setOnClickListener(v -> selectItem(resumeItemData));

    setOnChildClickListener(position, holder.itemView, holder.findViewById(R.id.tv_conversation));
  }
}
