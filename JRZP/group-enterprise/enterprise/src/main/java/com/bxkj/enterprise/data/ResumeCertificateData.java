package com.bxkj.enterprise.data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description:
 * @TODO: TODO
 * @date 2018/5/12
 */

public class ResumeCertificateData {

    private int id;
    private String type;
    private String name;
    private String remark;
    private List<ResumeCertificateData> certificateItemDataList;

    public ResumeCertificateData() {
    }

    public ResumeCertificateData(List<ResumeCertificateData> certificateItemDataList) {
        this.certificateItemDataList = certificateItemDataList;
    }

    public List<ResumeCertificateData> getCertificateItemDataList() {
        return certificateItemDataList;
    }

    public void setCertificateItemDataList(List<ResumeCertificateData> certificateItemDataList) {
        this.certificateItemDataList = certificateItemDataList;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
