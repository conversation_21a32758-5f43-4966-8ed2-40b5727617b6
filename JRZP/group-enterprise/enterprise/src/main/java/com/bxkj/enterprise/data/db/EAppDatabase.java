package com.bxkj.enterprise.data.db;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import android.content.Context;

/**
 * @version V1.0
 * @Description:
 */
@Database(entities = {SearchResumeRecord.class}, version = 1)
public abstract
class EAppDatabase extends RoomDatabase {

  private static final String DATABASE_NAME = "database_ejrzp";

  private static EAppDatabase INSTANCE;

  public abstract SearchResumeRecordDao searchResumeRecordModel();

  public static EAppDatabase getInstance(Context context) {
    if (INSTANCE == null) {
      INSTANCE = Room.databaseBuilder(context.getApplicationContext(), EAppDatabase.class,
              DATABASE_NAME)
          .allowMainThreadQueries()
          .build();
    }
    return INSTANCE;
  }

  public static void destroyInstance() {
    INSTANCE = null;
  }
}
