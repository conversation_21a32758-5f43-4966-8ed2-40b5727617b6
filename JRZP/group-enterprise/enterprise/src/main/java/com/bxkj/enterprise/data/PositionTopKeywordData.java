package com.bxkj.enterprise.data;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.enterprise.BR;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.data
 * @Description: 置顶关键字
 * @date 2019/10/26
 */
public class PositionTopKeywordData extends BaseObservable implements Parcelable {
    private String keyword;
    private String effectiveDate;

    protected PositionTopKeywordData(Parcel in) {
        keyword = in.readString();
        effectiveDate = in.readString();
    }

    public static final Creator<PositionTopKeywordData> CREATOR = new Creator<PositionTopKeywordData>() {
        @Override
        public PositionTopKeywordData createFromParcel(Parcel in) {
            return new PositionTopKeywordData(in);
        }

        @Override
        public PositionTopKeywordData[] newArray(int size) {
            return new PositionTopKeywordData[size];
        }
    };

    public PositionTopKeywordData() {
    }

    public static PositionTopKeywordData getDefault() {
        return new PositionTopKeywordData();
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Bindable
    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
        notifyPropertyChanged(BR.effectiveDate);
    }

    public void clear(){
        setKeyword("");
        setEffectiveDate("");
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(keyword);
        dest.writeString(effectiveDate);
    }

    @Override
    public String toString() {
        return "PositionTopKeywordData{" +
                "keyword='" + keyword + '\'' +
                ", effectiveDate='" + effectiveDate + '\'' +
                '}';
    }
}
