package com.bxkj.enterprise.ui.fragment.collection;

import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.ResumeItemData;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.collection
 * @Description: Collection
 * @TODO: TODO
 * @date 2018/3/27
 */

public class CollectionPresenter extends CollectionContract.Presenter {

    private static final String TAG = CollectionPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public CollectionPresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    void getCollectionResumeList(int userId, int pageIndex, int pageSize) {
        mBusinessApi.getCollectionResumeList(userId, pageIndex, pageSize)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getResumeListSuccess((List<ResumeItemData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.getErrCode() == 30002) {
                            mView.onResultNoData();
                            return;
                        }
                        mView.onRequestError(respondThrowable);
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void deleteCollection(int userId, int resumeId, int position) {
        mBusinessApi.addOrDeleteCollection(userId, resumeId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.getErrCode() == 10002) {
                            mView.deleteCollectionSuccess(position);
                        } else {
                            mView.onError(respondThrowable.getErrMsg());
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
