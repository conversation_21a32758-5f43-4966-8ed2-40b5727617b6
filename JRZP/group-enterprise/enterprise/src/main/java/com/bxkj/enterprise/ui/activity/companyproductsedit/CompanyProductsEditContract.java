package com.bxkj.enterprise.ui.activity.companyproductsedit;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.enterprise.data.ProductItemData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.companyproductsedit
 * @Description: CompanyProductsEdit
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface CompanyProductsEditContract {
    interface View extends BaseView {
        void addCompanyProductSuccess();

        void updateCompanyProductSuccess();

        void hasIntegralReward(int integral);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void addCompanyProduct(int userId, ProductItemData productItemData);

        abstract void updateCompanyProduct(int userId, ProductItemData productItemData);
    }
}
