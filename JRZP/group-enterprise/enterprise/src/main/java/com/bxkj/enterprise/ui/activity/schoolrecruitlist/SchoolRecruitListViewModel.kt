package com.bxkj.enterprise.ui.activity.schoolrecruitlist

import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.ecommon.constants.ECommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.UserUtils
import com.bxkj.common.base.mvvm.callback.ResultListCallBack
import com.bxkj.enterprise.data.SchoolRecruitItemData
import com.bxkj.enterprise.data.source.SchoolRecruitListRepo
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.schoolrecruitlist
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/6/13
 * @version V1.0
 */
class SchoolRecruitListViewModel @Inject constructor(
  schoolRecruitListRepo: SchoolRecruitListRepo
) : BaseViewModel() {

  private lateinit var mListViewModel: RefreshListViewModel
  private lateinit var mSearchTitle: String
  private lateinit var mSearchStartDate: String
  private val mSchoolRecruitListRepo = schoolRecruitListRepo

  init {
    initSearchParams()
    setupListViewModel()
  }

  private fun initSearchParams() {
    mSearchTitle = ""
    mSearchStartDate = ""
  }

  private fun setupListViewModel() {
    mListViewModel = RefreshListViewModel()
    mListViewModel.setOnLoadDataListener { currentPage ->
      mSchoolRecruitListRepo.getSchoolRecruitList(
        UserUtils.getUserSelectedCityId(),
        mSearchTitle,
        mSearchStartDate,
        currentPage,
        ECommonApiConstants.DEFAULT_PAGE_SIZE,
        object :
            ResultListCallBack<List<SchoolRecruitItemData>> {
          override fun onSuccess(data: List<SchoolRecruitItemData>?) {
            if (currentPage == 1) {
              mListViewModel.reset(data)
            } else {
              mListViewModel.addAll(data)
            }
          }

          override fun onNoMoreData() {
            mListViewModel.noMoreData()
          }

          override fun onError(respondThrowable: RespondThrowable) {
            mListViewModel.loadError()
          }

        })
    }
  }

  public fun searchByText(searchText: String) {
    mSearchTitle = searchText
    mListViewModel.refresh()
  }

  public fun getListViewModel(): RefreshListViewModel {
    return mListViewModel
  }

}