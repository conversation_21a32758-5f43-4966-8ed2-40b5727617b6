package com.bxkj.enterprise.data

import androidx.recyclerview.widget.DiffUtil

/**
 * Description: 会员权益
 * Author:Sanjin
 * Date:2024/3/13
 **/
data class MemberBenefitsData(
  var data: List<BenefitsItem?>?,
  var renzheng: Int?,
  var renzhengName: String?,
  var titleImg: String?,
  var xianPrice1: String?,
  var yuanPrice1: String?,
  var youxiaoqi: String?,
  var color: String?
) {

  class DiffCallback : DiffUtil.ItemCallback<MemberBenefitsData>() {
    override fun areItemsTheSame(
      oldItem: MemberBenefitsData,
      newItem: MemberBenefitsData
    ): Boolean {
      return oldItem == newItem
    }

    override fun areContentsTheSame(
      oldItem: MemberBenefitsData,
      newItem: MemberBenefitsData
    ): Boolean {
      return oldItem == newItem
    }
  }
}

data class BenefitsItem(
  var count: String?,
  var img: String?,
  var name: String?
) {
  class DiffCallback : DiffUtil.ItemCallback<BenefitsItem>() {
    override fun areItemsTheSame(oldItem: BenefitsItem, newItem: BenefitsItem): Boolean {
      return oldItem == newItem
    }

    override fun areContentsTheSame(oldItem: BenefitsItem, newItem: BenefitsItem): Boolean {
      return oldItem == newItem
    }
  }
}