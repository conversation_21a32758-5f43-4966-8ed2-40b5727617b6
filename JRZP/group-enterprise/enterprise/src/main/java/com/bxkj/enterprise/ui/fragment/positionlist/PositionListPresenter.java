package com.bxkj.enterprise.ui.fragment.positionlist;

import androidx.annotation.NonNull;

import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.constants.OnceTag;
import com.bxkj.common.data.ShareInfoData;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.JobPageTipsData;

import com.bxkj.enterprise.data.PositionItemBean;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;
import com.bxkj.common.once.Once;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.fragment.positionlist
 * @Description: PositionList
 * @TODO: TODO
 * @date 2018/3/27
 */

public class PositionListPresenter extends PositionListContract.Presenter {

    private static final String TAG = PositionListPresenter.class.getSimpleName();

    private BusinessApi mBusinessApi;

    @Inject
    public PositionListPresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    void getPositionListByMultiple(int userId, int type, String positionTitle, String positionStatus,
            int pageIndex, int pageSize) {
        int status = 0;
        switch (positionStatus) {
            case PositionListFragment.TAG_RUNNING:
                status = 3;
                break;
            case PositionListFragment.TAG_END:
                status = 4;
                break;
            case PositionListFragment.TAG_REVIEW:
                status = 0;
                break;
            case PositionListFragment.TAG_FAIL:
                status = 1;
                break;
        }
        mBusinessApi.getPositionListByMultiple(userId, type, positionTitle, status, pageIndex,
                        pageSize)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getPositionListSuccess((List<PositionItemBean>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.isNoDataError()) {
                            mView.onResultNoData();
                            if (pageIndex == 1 && positionStatus.equals(PositionListFragment.TAG_RUNNING)) {
                                Once.clearDone(OnceTag.JOB_LIST_SHOW_SHARE_TIPS);
                            }
                        } else {
                            mView.onRequestError(respondThrowable);
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void updatePositionState(int userId, int positionId, int position, int state) {
        mView.showLoading();
        mBusinessApi.updatePositionState(userId, String.valueOf(positionId), state)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.hiddenLoading();
                        mView.updatePositionStateSuccess(state, position);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                        if (respondThrowable.getErrCode() == 30006) {
                            mView.showVipJobTips(respondThrowable.getErrMsg());
                        } else {
                            mView.onError(respondThrowable.getErrMsg());
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void deletePosition(int userId, int positionId, int position) {
        mView.showLoading();
        mBusinessApi.deletePosition(userId, positionId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.hiddenLoading();
                        mView.deletePositionSuccess(position);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void getShareInfo(PositionItemBean positionItemBean) {
        mView.showLoading();
        mBusinessApi.getShareInfo(CommonApiConstants.SHARE_JOB, positionItemBean.getId())
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(@NonNull BaseResponse baseResponse) {
                        mView.hiddenLoading();
                        ShareInfoData shareInfoData = (ShareInfoData) baseResponse.getData();
                        if (shareInfoData != null) {
                            shareInfoData.setShareUrl(
                                    "http://m.jrzp.com/jobfenxiang/view.aspx?relID=" + positionItemBean.getId());
                            mView.getShareInfoSuccess(shareInfoData);
                        }
                    }

                    @Override
                    protected void onError(@NonNull RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    void getVipTips() {
        mBusinessApi.getJobPageVipTips()
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(@NonNull BaseResponse baseResponse) {
                        mView.getVipTipsSuccess((JobPageTipsData) baseResponse.getData());
                    }

                    @Override
                    protected void onError(@NonNull RespondThrowable respondThrowable) {
                        mView.getVipTipsSuccess(null);
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
