package com.bxkj.enterprise.ui.activity.minesearchresume;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.enterprise.data.db.EAppDatabase;
import com.bxkj.enterprise.data.db.SearchResumeRecord;

import java.util.List;

import javax.inject.Inject;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.minesearchresume
 * @Description: MineSearchResume
 * @TODO: TODO
 * @date 2018/3/27
 */

public class MineSearchResumePresenter extends MineSearchResumeContract.Presenter {

    private static final String TAG = MineSearchResumePresenter.class.getSimpleName();
    private EAppDatabase mDb;

    @Inject
    public MineSearchResumePresenter(EAppDatabase appDatabase) {
        mDb = appDatabase;
    }


    @Override
    void getRecordList() {
        List<SearchResumeRecord> searchResumeRecords = mDb.searchResumeRecordModel().getAll();
        if (!CheckUtils.isNullOrEmpty(searchResumeRecords)) {
            mView.getRecordListSuccess(searchResumeRecords);
        } else {
            mView.noRecord();
        }
    }

    @Override
    void addOrReplaceRecord(String content) {
        if (mDb.searchResumeRecordModel().findRecordByContent(content) != null) {
            mDb.searchResumeRecordModel().deleteRecordByContent(content);
        }
        mDb.searchResumeRecordModel().insert(new SearchResumeRecord(content));
    }

    @Override
    void deleteRecordByContent(int position, String content) {
        if (mDb.searchResumeRecordModel().deleteRecordByContent(content) > 0) {
            mView.deleteRecordSuccess(position);
        }
    }

    @Override
    void clearRecord() {
        mDb.searchResumeRecordModel().clear();
    }
}
