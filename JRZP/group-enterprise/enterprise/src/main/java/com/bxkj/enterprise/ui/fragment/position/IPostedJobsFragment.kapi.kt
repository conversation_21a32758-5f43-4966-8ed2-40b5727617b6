package com.bxkj.enterprise.ui.fragment.position

import androidx.fragment.app.Fragment
import com.bxkj.common.util.router.Router
import com.bxkj.enterprise.EnterpriseConstants

/**
 *
 * @author: sanjin
 * @date: 2022/7/30
 */
class IPostedJobsFragment {
    companion object {

        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/ipostedjobs"

        const val EXTRA_HIDE_BACK = "HIDE_BACK"

        fun create(hideBack: Boolean = false): Fragment {
            return Router.getInstance()
                .to(PATH)
                .withBoolean(EXTRA_HIDE_BACK, hideBack)
                .createFragment() as Fragment
        }
    }
}