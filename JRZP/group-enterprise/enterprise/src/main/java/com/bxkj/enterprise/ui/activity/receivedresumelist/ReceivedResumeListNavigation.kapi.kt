package com.bxkj.enterprise.ui.activity.receivedresumelist

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

class ReceivedResumeListNavigation {

  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/receivedresumelist"

    const val EXTRA_TARGET_INDEX = "TARGET_INDEX"

    fun navigate(targetIndex: Int = 0): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_TARGET_INDEX, targetIndex)
    }
  }
}