package com.bxkj.enterprise.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description: 接受简历消息
 * @TODO: TODO
 * @date 2018/9/18
 */
public class MessageItemData {

    /**
     * id : 89
     * lx : 1
     * type : 6
     * otherid : 55641
     * uid : 0
     * cuid : 51704
     * content : 刘女士向您职位：销售投递了一份简历
     * look : 0
     * date : 2018-09-18 11:12:14
     * pageIndex : 0
     * pageSize : 0
     * company : null
     * relId : 0
     * relName : null
     * state : 0
     */

    private int id;
    private int lx;
    private int type;
    private int otherid;
    private int uid;
    private int cuid;
    private String content;
    private int look;
    private String date;
    private int pageIndex;
    private int pageSize;
    private Object company;
    private int relId;
    private int resid;
    private Object relName;
    private int state;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getLx() {
        return lx;
    }

    public void setLx(int lx) {
        this.lx = lx;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getOtherid() {
        return otherid;
    }

    public void setOtherid(int otherid) {
        this.otherid = otherid;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getCuid() {
        return cuid;
    }

    public void setCuid(int cuid) {
        this.cuid = cuid;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getLook() {
        return look;
    }

    public void setLook(int look) {
        this.look = look;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public Object getCompany() {
        return company;
    }

    public void setCompany(Object company) {
        this.company = company;
    }

    public int getRelId() {
        return relId;
    }

    public void setRelId(int relId) {
        this.relId = relId;
    }

    public int getResid() {
        return resid;
    }

    public void setResid(int resid) {
        this.resid = resid;
    }

    public Object getRelName() {
        return relName;
    }

    public void setRelName(Object relName) {
        this.relName = relName;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }
}
