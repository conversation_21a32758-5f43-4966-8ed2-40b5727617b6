package com.bxkj.enterprise.ui.activity.resumedetails;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.data.ResumeStateData;
import com.bxkj.common.data.ShareInfoData;
import com.bxkj.common.enums.ChatRole;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.SystemUtil;
import com.bxkj.common.util.router.EnterpriseCommonRouter;
import com.bxkj.common.util.router.Router;
import com.bxkj.common.widget.CommonTitleBar;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.ecommon.util.recyclerutil.RecycleViewDivider;
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog;
import com.bxkj.ecommon.widget.dialogfragment.ETipsDialog;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.AuthenticatedData;
import com.bxkj.enterprise.data.GreetBalanceData;
import com.bxkj.enterprise.data.PositionItemBean;
import com.bxkj.enterprise.data.ResumeBaseInfoData;
import com.bxkj.enterprise.data.ResumeCareerObjectiveData;
import com.bxkj.enterprise.data.ResumeCertificateData;
import com.bxkj.enterprise.data.ResumeEduBackgroundData;
import com.bxkj.enterprise.data.ResumeLanguageData;
import com.bxkj.enterprise.data.ResumeSchoolSituationData;
import com.bxkj.enterprise.data.ResumeSkillData;
import com.bxkj.enterprise.data.ResumeWorkExpData;
import com.bxkj.enterprise.mvp.contract.CheckCertificationContract;
import com.bxkj.enterprise.mvp.contract.CheckCompanyInfoFillContract;
import com.bxkj.enterprise.mvp.contract.OnlinePositionContract;
import com.bxkj.enterprise.mvp.presenter.CheckCertificationPresenter;
import com.bxkj.enterprise.mvp.presenter.CheckCompanyInfoFillPresenter;
import com.bxkj.enterprise.mvp.presenter.OnlinePositionPresenter;
import com.bxkj.enterprise.ui.activity.companyinfo.BusinessBasicInfoNavigation;
import com.bxkj.enterprise.ui.activity.conversation.BusinessChatContentActivity;
import com.bxkj.enterprise.ui.activity.conversation.BusinessChatContentNavigation;
import com.bxkj.enterprise.ui.activity.eliminate.EliminateActivity;
import com.bxkj.enterprise.ui.activity.invitingdelivery.InvitationDeliveryNavigation;
import com.bxkj.enterprise.ui.activity.postjob.PostJobV2Activity;
import com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder.ApplicantSelfEvaluationViewBinder;
import com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder.BaseInfoViewBinder;
import com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder.CareerObjectiveViewBinder;
import com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder.CertificateViewBinder;
import com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder.EduBackgroundViewBinder;
import com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder.LanguageViewBinder;
import com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder.SchoolSituationViewBinder;
import com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder.SkillViewBinder;
import com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder.WorkExpViewBinder;
import com.bxkj.enterprise.ui.activity.resumedownload.MVResumeDownloadActivity;
import com.bxkj.enterprise.ui.activity.selectsayhellojob.SelectSayHelloJobActivity;
import com.bxkj.jrzp.support.feature.ui.filepreview.FilePreviewActivity;
import com.bxkj.jrzp.user.mine.ui.membercenter.MemberCenterWebNavigation;
import com.bxkj.share.ShareOpID;
import com.bxkj.share.ShareOption;
import com.bxkj.share.ui.StandardShareDialog;
import com.bxkj.video.data.VideoData;
import com.tencent.qcloud.ugckit.utils.FileUtils;
import java.util.Collections;
import java.util.List;
import javax.inject.Inject;

/**
 * @Description: 求职者简历详情
 * @date 2018/8/14
 */
//@Route(path = ApplicantResumeNavigation.PATH)
public class ApplicantResumeDetailsActivity extends BaseDaggerActivity
  implements ResumeDetailsContract.View, OnlinePositionContract.View,
  CheckCertificationContract.View, CheckCompanyInfoFillContract.View {

  public static final String EXTRA_SNAPSHOT_ID = "SNAPSHOT_ID";

  public static final String EXTRA_SEND_HISTORY_ID = "SEND_ID";

  private static final String EXTRA_IS_ONLY_VIEW = "IS_ONLY_VIEW";

  private static final String EXTRA_RELATE_JOB_ID = "RELATE_JOB_ID";

  private static final String EXTRA_RELATE_JOB_NAME = "RELATE_JOB_NAME";

  private static final int TO_INVITATION_CODE = 1;

  private static final int TO_ELIMINATE_CODE = 2;

  private static final int TO_INVITATION_DELIVERY_CODE = 3;

  private static final int TO_DOWNLOAD_RESUME_CODE = 4;

  private static final int TO_POST_JOB_CODE = 5;

  @Inject
  ResumeDetailsPresenter mResumeDetailsPresenter;

  @Inject
  OnlinePositionPresenter mOnlinePositionPresenter;

  @Inject
  CheckCertificationPresenter mCheckCertificationPresenter;

  @Inject
  CheckCompanyInfoFillPresenter mCheckCompanyInfoFillPresenter;

  private RecyclerView recyclerResumeContent;

  private LinearLayout llReceiveBar;

  private LinearLayout llActiveBar;

  private FrameLayout flOtherBar;

  private TextView tvOther;

  private TextView tvInviteInterview;

  private TextView tvEliminate;

  private TextView tvInvite;

  private TextView tvCall;

  private CommonTitleBar titleBar;

  private MultiTypeAdapter mResumeAdapter;

  private ResumeCareerObjectiveData mResumeCareerObjectiveData;

  private ResumeBaseInfoData mResumeBaseInfoData;

  private int mPositionId;

  private String mName;

  private boolean mHasPosition;

  private boolean mHasConversation;

  private boolean hasViewContactWayPermission = false;

  public static Intent newIntent(Context context, int resumeId, int geekUserId) {
    return newIntent(context, resumeId, geekUserId, 0, 0);
  }

  public static Intent newIntent(Context context, int resumeId, int applicantId, int relateJobId,
    String relateJobName) {
    return newIntent(context, resumeId, applicantId, 0, 0, false, relateJobId, relateJobName);
  }

  public static Intent newIntent(Context context, int resumeId, int applicantId, int snapshotID,
    int sendHistoryID) {
    return newIntent(context, resumeId, applicantId, snapshotID, sendHistoryID, false);
  }

  public static Intent newIntent(Context context, int resumeId, int applicantId, int snapshotID,
    int sendHistoryID, boolean onlyView) {
    return newIntent(context, resumeId, applicantId, snapshotID, sendHistoryID, onlyView, 0, "");
  }

  public static Intent newIntent(Context context, int resumeId, int applicantId, int snapshotID,
    int sendHistoryID, boolean onlyView, int relateJobId, String relateJobName) {
    Intent starter = new Intent(context, ApplicantResumeDetailsActivity.class);
    starter.putExtra(ApplicantResumeNavigation.EXTRA_RESUME_ID, resumeId);
    starter.putExtra(ApplicantResumeNavigation.EXTRA_APPLICANT_ID, applicantId);
    starter.putExtra(EXTRA_SNAPSHOT_ID, snapshotID);
    starter.putExtra(EXTRA_SEND_HISTORY_ID, sendHistoryID);
    starter.putExtra(EXTRA_IS_ONLY_VIEW, onlyView);
    starter.putExtra(EXTRA_RELATE_JOB_ID, relateJobId);
    starter.putExtra(EXTRA_RELATE_JOB_NAME, relateJobName);
    return starter;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_resume_details;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mResumeDetailsPresenter);
    presenters.add(mOnlinePositionPresenter);
    presenters.add(mCheckCertificationPresenter);
    presenters.add(mCheckCompanyInfoFillPresenter);
    return presenters;
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());
    titleBar.setTitle(getString(R.string.resume_details));
    titleBar.setRightOptionTwoClickListener(v -> mResumeDetailsPresenter.collectionResume());

    mResumeAdapter = new MultiTypeAdapter(this);
    mResumeAdapter.register(ResumeBaseInfoData.class, new BaseInfoViewBinder(this));
    mResumeAdapter.register(ResumeCareerObjectiveData.class, new CareerObjectiveViewBinder(this,
      attachResumeUrl -> {
        if (hasViewContactWayPermission) {
          startActivity(
            FilePreviewActivity.Companion.newIntent(
              ApplicantResumeDetailsActivity.this,
              CommonApiConstants.BASE_JRZP_IMG_URL + attachResumeUrl
            )
          );
        } else {
          toDownloadResume();
        }
        return null;
      }));
    mResumeAdapter.register(ResumeEduBackgroundData.class, new EduBackgroundViewBinder(this));
    mResumeAdapter.register(ResumeWorkExpData.class, new WorkExpViewBinder(this));
    mResumeAdapter.register(String.class, new ApplicantSelfEvaluationViewBinder());
    mResumeAdapter.register(ResumeSkillData.class, new SkillViewBinder(this));
    mResumeAdapter.register(ResumeLanguageData.class, new LanguageViewBinder(this));
    mResumeAdapter.register(ResumeSchoolSituationData.class, new SchoolSituationViewBinder(this));
    mResumeAdapter.register(ResumeCertificateData.class, new CertificateViewBinder(this));
    recyclerResumeContent.setLayoutManager(new LinearLayoutManager(this));
    recyclerResumeContent.addItemDecoration(
      new RecycleViewDivider(this, LinearLayoutManager.VERTICAL, DensityUtils
        .dp2px(this, 8), getMColor(R.color.common_f4f4f4)));
    recyclerResumeContent.setAdapter(mResumeAdapter);
  }

  @Override
  protected void onResume() {
    super.onResume();
    mResumeDetailsPresenter.viewCheckCompanyInfoFill();
    mResumeDetailsPresenter.checkHasConversation();
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_eliminate) {
      startActivityForResult(
        EliminateActivity.newIntent(this, mPositionId, mResumeDetailsPresenter.getResumeId(),
          mName), TO_ELIMINATE_CODE);
    } else if (view.getId() == R.id.tv_receive_bar_conversation) {
      if (mHasConversation) {
        startActivity(
          BusinessChatContentActivity.Companion.newIntent(this, ChatRole.BUSINESS,
            mResumeDetailsPresenter.getApplicantUserId(),
            0, 0, "", false));
      } else {
        createChat();
      }
      //new IconSelectDialog.Builder()
      //  .addItem(R.drawable.enterprise_ic_normal_interview, "线下面试")
      //  .setOnItemClickListener((v, position) -> InviteInterviewNavigation.create(
      //      mPositionId,
      //      mResumeDetailsPresenter.getResumeId(),
      //      mName)
      //    .startForResult(ApplicantResumeDetailsActivity.this, TO_INVITATION_CODE))
      //  .build().show(getSupportFragmentManager());
    } else if (view.getId() == R.id.tv_conversation) {
      if (mHasPosition) {
        if (mHasConversation) {
          startActivity(
            BusinessChatContentActivity.Companion.newIntent(this, ChatRole.BUSINESS,
              mResumeDetailsPresenter.getApplicantUserId(),
              0, 0, "", false));
        } else {
          createChat();
        }
      } else {
        checkCompanyInfoFill();
      }
    } else if (view.getId() == R.id.tv_receive_bar_call) {
      if (mResumeBaseInfoData == null) {
        showToast("数据加载中，请稍候");
      } else {
        if (!CheckUtils.isNullOrEmpty(mResumeBaseInfoData.getPhone())) {
          SystemUtil.callPhone(this,mResumeBaseInfoData.getPhone());
        } else {
          showToast("未留联系方式");
        }
      }
    } else {
      toDownloadResume();
    }
  }

  private void toDownloadResume() {
    if (mHasPosition) {
      startActivityForResult(
        MVResumeDownloadActivity.newIntent(this, mResumeDetailsPresenter.getResumeId()),
        TO_DOWNLOAD_RESUME_CODE);
    } else {
      checkCompanyInfoFill();
    }
  }

  private void checkCompanyInfoFill() {
    mCheckCompanyInfoFillPresenter.checkCompanyInfoFill(getMUserID());
  }

  @Override
  public void resumeState(ResumeStateData resumeStateData) {
    llActiveBar.setVisibility(View.GONE);   //打电话、邀请投递
    llReceiveBar.setVisibility(View.GONE);  //不合适、邀请面试、打电话
    flOtherBar.setVisibility(View.GONE);    //其他情况
    mResumeDetailsPresenter.checkResumeCollection();
    if (resumeStateData == null) {
      llActiveBar.setVisibility(View.VISIBLE);
      //判断简历是否邀请
      mResumeDetailsPresenter.checkResumeIsInvitationDelivery();
      return;
    }
    hasViewContactWayPermission = true;
    int resumeState = resumeStateData.getState();
    if (resumeState == 2) {
      showOtherBar(getString(R.string.resume_details_eliminated));
    } else if (resumeState == 4) {
      showOtherBar(getString(R.string.resume_details_interview_rejected));
    } else {
      if (resumeStateData.getType() != 2) {
        mPositionId = resumeStateData.getRelid();
        llReceiveBar.setVisibility(View.VISIBLE);
        if (resumeState == 3) {     //已邀请面试
          //tvInviteInterview.setEnabled(false);
          //tvInviteInterview.setText(getString(R.string.resume_details_invited));
        } else if (resumeState == 5) {  //已接受面试
          tvEliminate.setVisibility(View.GONE);
          //tvInviteInterview.setEnabled(false);
          //tvInviteInterview.setText(getString(R.string.accepted_interview));
        }
      } else {
        llActiveBar.setVisibility(View.VISIBLE);
        mResumeDetailsPresenter.checkResumeIsInvitationDelivery();
      }
    }
  }

  @Override
  public void isInvitationDelivery() {
    tvInvite.setEnabled(false);
    tvInvite.setText(R.string.resume_details_invited);
  }

  @Override
  public void resumeIsCollected(boolean collected) {
    if (collected) {
      titleBar.setRightImageTwo(R.drawable.ic_job_collection_sel);
    } else {
      titleBar.setRightImageTwo(R.drawable.ic_job_collection_nor);
    }
  }

  @Override
  public void hasIntegralReward(int integral) {
  }

  @Override
  public void resumeDeleted() {
    showToast("该简历已删除");
    finish();
  }

  @Override
  public void getShareInfoSuccess(ShareInfoData shareInfoData) {
    new StandardShareDialog.Builder()
      .setShareTitle(shareInfoData.getTitle())
      .setShareContent(shareInfoData.getContent())
      .setShareUrl(shareInfoData.getShareUrl())
      .setShareMiniProgramPath("/pages/self/sonPage/company/resumeDetail/resumeDetail?uid="
        + mResumeDetailsPresenter.getApplicantUserId()
        + "&id="
        + mResumeDetailsPresenter.getResumeId())
      .setShareBitmap(FileUtils.captureView(recyclerResumeContent))
      .setShareOptions(Collections.singletonList(ShareOption.get(
        R.drawable.ic_page_options_share_to_wechat, "微信", ShareOpID.SHARE_WECHAT
      )))
      .build().show(getSupportFragmentManager());
  }

  @Override
  public void viewCheckCompanyInfoNoComplete() {
    new EActionDialog.Builder()
      .setCancelable(false)
      .setTitle(getString(R.string.company_info_not_fill))
      .setContent(getString(R.string.resume_details_company_info_not_fill))
      .setOnConfirmClickListener((actionDialog, inputText) -> {
          actionDialog.dismiss();
          Router.getInstance()
            .to(EnterpriseCommonRouter.FillInTheInfoActivity.URL)
            .withInt(EnterpriseCommonRouter.FillInTheInfoActivity.USER_ID, getMUserID())
            .start();
        }
      ).setOnCancelClickListener(this::finish)
      .build().show(getSupportFragmentManager(), EActionDialog.TAG);
  }

  @Override
  public void viewCheckCompanyInfoCompleted() {
    mResumeDetailsPresenter.viewCheckCertification();
  }

  @Override
  public void viewCheckCertificated() {
    if (mResumeBaseInfoData == null) {
      mOnlinePositionPresenter.getOnlinePosition(getMUserID());
      mResumeDetailsPresenter.getResumeBaseInfo();
    }
  }

  @Override
  public void viewCheckNoCertificated(AuthenticatedData authenticatedData) {
    if (authenticatedData.getState() == 30002) {
      new EActionDialog.Builder()
        .setCancelable(false)
        .setTitle(getString(R.string.resume_details_no_certificate_tips))
        .setContent(getString(R.string.resume_details_no_certificate_content))
        .setOnConfirmClickListener((actionDialog, inputText) -> {
            actionDialog.dismiss();
            BusinessBasicInfoNavigation.navigate(true).start();
          }
        ).setOnCancelClickListener(this::finish)
        .build().show(getSupportFragmentManager(), EActionDialog.TAG);
    } else if (authenticatedData.getState() == 30003) {
      new ETipsDialog()
        .setMCancelable(false)
        .setTitle(getString(R.string.resume_details_no_certificate_tips))
        .setContent(getString(R.string.resume_details_certificate_content))
        .setOnConfirmClickListener(this::finish)
        .show(getSupportFragmentManager(), ETipsDialog.TAG);
    } else {
      new EActionDialog.Builder()
        .setCancelable(false)
        .setTitle(getString(R.string.resume_details_error_certificate_tips))
        .setContent(getString(R.string.resume_details_error_certificate_content_format,
          authenticatedData.getMsg()))
        .setOnConfirmClickListener((actionDialog, inputText) -> {
          actionDialog.dismiss();
          if (authenticatedData.canToCertificate()) {
            BusinessBasicInfoNavigation.navigate(true).start();
          }
        }).setOnCancelClickListener(this::finish)
        .build().show(getSupportFragmentManager(), EActionDialog.TAG);
    }
  }

  @Override
  public void hasConversation() {
    mHasConversation = true;
    tvInvite.setText(getString(R.string.resume_details_chat));
    tvInviteInterview.setText(getString(R.string.resume_details_chat));
  }

  @Override
  public void noConversation() {
    mHasConversation = false;
    tvInvite.setText(getString(R.string.resume_details_say_hello));
    tvInviteInterview.setText(getString(R.string.resume_details_say_hello));
  }

  @Override
  public void hasGreetBalance() {
    createChat();
  }

  /**
   * 创建会话
   */
  private void createChat() {
    final int prePageJobId = getIntent().getIntExtra(EXTRA_RELATE_JOB_ID, 0);
    if (prePageJobId == 0) {
      startActivity(SelectSayHelloJobActivity.newIntent(this,
        mResumeDetailsPresenter.getApplicantUserId(), mResumeDetailsPresenter.getResumeId()));
    } else {
      mResumeDetailsPresenter.inviteSendResume(prePageJobId);
    }
  }

  @Override
  public void inviteSendResumeSuccess() {
    startActivity(BusinessChatContentActivity.newIntent(
      this,
      ChatRole.BUSINESS,
      mResumeDetailsPresenter.getApplicantUserId()
    ));
  }

  @Override
  public void noGreetBalance(GreetBalanceData greetBalanceData) {
    if (greetBalanceData.getYaoyueCount() == 0) {
      new ActionDialog.Builder()
        .setTitle(getString(R.string.common_tips))
        .setContent(getString(R.string.resume_details_no_greet_balance_tips))
        .setConfirmText(getString(R.string.resume_details_member_upgrade_text))
        .setOnConfirmClickListener((dialog) -> {
          MemberCenterWebNavigation.Companion.create().start();
        }).build().show(getSupportFragmentManager());
    } else {
      new ActionDialog.Builder()
        .setTitle(getString(R.string.common_tips))
        .setContent(getString(R.string.resume_details_no_greet_invite_tips))
        .setCenterText("邀请投递")
        .setOnCenterOptionsClickListener(() -> {
          InvitationDeliveryNavigation.create(new int[] { mResumeDetailsPresenter.getResumeId() })
            .startForResult(this, TO_INVITATION_DELIVERY_CODE);
        })
        .setConfirmText(getString(R.string.resume_details_member_upgrade_text))
        .setOnConfirmClickListener((dialog) -> {
          MemberCenterWebNavigation.Companion.create().start();
        }).build().show(getSupportFragmentManager());
    }
  }

  @Override
  public void showNoInviteBalanceTipsDialog(final String tips) {
    new ActionDialog.Builder()
      .setTitle(getString(R.string.common_tips))
      .setContent(tips)
      .setConfirmText("升级会员")
      .setOnConfirmClickListener((dialog) -> {
        MemberCenterWebNavigation.Companion.create().start();
      }).build().show(getSupportFragmentManager());
  }

  @Override
  public void showNoMemberTipsDialog(final String tips) {
    new ActionDialog.Builder()
      .setTitle(getString(R.string.common_tips))
      .setContent(tips)
      .setConfirmText("开通会员")
      .setOnConfirmClickListener((dialog) -> {
        MemberCenterWebNavigation.Companion.create().start();
      }).build().show(getSupportFragmentManager());
  }

  /**
   * 显示其他情况操作栏并设置text
   */
  private void showOtherBar(String text) {
    flOtherBar.setVisibility(View.VISIBLE);
    tvOther.setText(text);
  }

  @Override
  public void getResumeBaseInfoSuccess(ResumeBaseInfoData resumeBaseInfoData) {
    mName = resumeBaseInfoData.getName();
    mResumeBaseInfoData = resumeBaseInfoData;
    mResumeDetailsPresenter.getResumeAttachVideo();
  }

  @Override
  public void getResumeAttachVideoSuccess(List<VideoData> attachVideos) {
    mResumeBaseInfoData.setAttachVideos(attachVideos);
    mResumeDetailsPresenter.getResumeCareerObjective();
  }

  @Override
  public void getCareerObjectiveSuccess(ResumeCareerObjectiveData resumeCareerObjectiveData) {
    tvCall.setVisibility(resumeCareerObjectiveData.getState() == 0 ? View.VISIBLE : View.GONE);
    if (resumeCareerObjectiveData.getState() == 0 && resumeCareerObjectiveData.getKzid() == 0) {
      titleBar.setRightImage(R.drawable.ic_chat_share);
      titleBar.setRightOptionClickListener(v -> {
        mResumeDetailsPresenter.getResumeShareInfo();
      });
    }
    mResumeBaseInfoData.setPosition(resumeCareerObjectiveData.getDetailsName());
    mResumeBaseInfoData.setSalary(resumeCareerObjectiveData.getWillMoney());
    mResumeBaseInfoData.setWorkExp(resumeCareerObjectiveData.getWtName());
    mResumeBaseInfoData.setEditTime(resumeCareerObjectiveData.getEditTime());
    mResumeAdapter.add(mResumeBaseInfoData);
    mResumeCareerObjectiveData = resumeCareerObjectiveData;
    mResumeAdapter.add(resumeCareerObjectiveData);
    mResumeDetailsPresenter.getResumeEduBackground();
  }

  @Override
  public void getEduBackgroundSuccess(ResumeEduBackgroundData resumeEduBackgroundData) {
    if (resumeEduBackgroundData != null) {
      mResumeAdapter.add(resumeEduBackgroundData);
    }
    mResumeDetailsPresenter.getResumeWorkExp();
  }

  @Override
  public void getWorkExpSuccess(ResumeWorkExpData resumeWorkExpData) {
    if (resumeWorkExpData != null) {
      mResumeAdapter.add(resumeWorkExpData);
    }
    if (!CheckUtils.isNullOrEmpty(mResumeCareerObjectiveData.getPingjia())) {
      mResumeAdapter.add(mResumeCareerObjectiveData.getPingjia());
    }
    mResumeDetailsPresenter.getResumeSkill();
  }

  @Override
  public void getSkillSuccess(ResumeSkillData resumeSkillData) {
    if (!CheckUtils.isNullOrEmpty(resumeSkillData.getProfessionalSkillItemDataList())) {
      mResumeAdapter.add(resumeSkillData);
    }
    mResumeDetailsPresenter.getResumeLanguage();
  }

  @Override
  public void getLanguageSuccess(ResumeLanguageData resumeLanguageData) {
    if (!CheckUtils.isNullOrEmpty(resumeLanguageData.getLanguageSkillsItemDataList())) {
      mResumeAdapter.add(resumeLanguageData);
    }
    mResumeDetailsPresenter.getResumeSchoolSituation();
  }

  @Override
  public void getSchoolSituationSuccess(ResumeSchoolSituationData resumeSchoolSituationData) {
    if (!CheckUtils.isNullOrEmpty(resumeSchoolSituationData.getSchoolSituationItemData())) {
      mResumeAdapter.add(resumeSchoolSituationData);
    }
    mResumeDetailsPresenter.getResumeCertificate();
  }

  @Override
  public void getCertificateSuccess(ResumeCertificateData resumeCertificateData) {
    if (!CheckUtils.isNullOrEmpty(resumeCertificateData.getCertificateItemDataList())) {
      mResumeAdapter.add(resumeCertificateData);
    }
  }

  @Override
  public void getOnlinePositionSuccess(List<PositionItemBean> positionItemBeanList) {
    mHasPosition = !CheckUtils.isNullOrEmpty(positionItemBeanList);
    if (getIntent().getBooleanExtra(EXTRA_IS_ONLY_VIEW, false)) {
      llReceiveBar.setVisibility(View.VISIBLE);
      tvEliminate.setVisibility(View.GONE);
      tvInviteInterview.setVisibility(View.GONE);
    } else {
      mResumeDetailsPresenter.checkResumeStatus();
    }
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (resultCode == RESULT_OK) {
      if (requestCode == TO_ELIMINATE_CODE
        || requestCode == TO_INVITATION_CODE
        || requestCode == TO_DOWNLOAD_RESUME_CODE) {
        mResumeDetailsPresenter.checkResumeStatus();
        setResult(RESULT_OK);
      } else if (requestCode == TO_INVITATION_DELIVERY_CODE) {
        BusinessChatContentNavigation
          .create(ChatRole.BUSINESS, mResumeDetailsPresenter.getApplicantUserId())
          .start();
        mResumeDetailsPresenter.checkResumeIsInvitationDelivery();
        setResult(RESULT_OK);
      } else {
        mOnlinePositionPresenter.getOnlinePosition(getMUserID());
      }
    }
  }

  @Override
  public void authenticated(AuthenticatedData authenticated) {
    if (authenticated.getState() == 10001) {
      new EActionDialog.Builder()
        .setContent(getString(R.string.no_position_tips))
        .setConfirmText(getString(R.string.go_to_post))
        .setOnConfirmClickListener((actionDialog, inputText) -> {
            actionDialog.dismiss();
            startActivityForResult(PostJobV2Activity.newIntent(this), TO_POST_JOB_CODE);
          }
        )
        .build().show(getSupportFragmentManager(), EActionDialog.TAG);
    } else {
      new ETipsDialog()
        .setTitle(getString(R.string.certificate))
        .setContent(authenticated.getMsg())
        .setConfirmText(authenticated.getCertificateText())
        .setOnConfirmClickListener(() -> {
            if (authenticated.canToCertificate()) {
              Router.getInstance()
                .to(EnterpriseCommonRouter.FillInTheInfoActivity.URL)
                .withInt(EnterpriseCommonRouter.FillInTheInfoActivity.USER_ID, getMUserID())
                .withInt(
                  EnterpriseCommonRouter.TO_ACTIVITY_TYPE,
                  EnterpriseCommonRouter.FillInTheInfoActivity.UPDATE
                )
                .start();
            }
          }
        )
        .show(getSupportFragmentManager(), ETipsDialog.TAG);
    }
  }

  @Override
  public void companyInfoFill() {
    mCheckCertificationPresenter.checkCertification(getMUserID());
  }

  @Override
  public void companyInfoNotFill(String msg) {
    new ETipsDialog()
      .setTitle(getString(R.string.company_info_not_fill))
      .setContent(getString(R.string.company_info_not_fill_content))
      .setOnConfirmClickListener(() ->
        Router.getInstance()
          .to(EnterpriseCommonRouter.FillInTheInfoActivity.URL)
          .withInt(EnterpriseCommonRouter.FillInTheInfoActivity.USER_ID, getMUserID())
          .withInt(
            EnterpriseCommonRouter.TO_ACTIVITY_TYPE,
            EnterpriseCommonRouter.FillInTheInfoActivity.UPDATE
          )
          .start()
      )
      .show(getSupportFragmentManager(), ETipsDialog.TAG);
  }

  private void bindView(View bindSource) {
    recyclerResumeContent = bindSource.findViewById(R.id.recycler_resume_content);
    llReceiveBar = bindSource.findViewById(R.id.ll_receive_bar);
    llActiveBar = bindSource.findViewById(R.id.ll_active_bar);
    flOtherBar = bindSource.findViewById(R.id.fl_over_state_bar);
    tvOther = bindSource.findViewById(R.id.tv_over_state_text);
    tvInviteInterview = bindSource.findViewById(R.id.tv_receive_bar_conversation);
    tvEliminate = bindSource.findViewById(R.id.tv_eliminate);
    tvInvite = bindSource.findViewById(R.id.tv_conversation);
    tvCall = bindSource.findViewById(R.id.tv_call);
    titleBar = bindSource.findViewById(R.id.title_bar);
    bindSource.findViewById(R.id.tv_eliminate).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_receive_bar_conversation)
      .setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_conversation).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_receive_bar_call).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_call).setOnClickListener(v -> onViewClicked(v));
  }
}
