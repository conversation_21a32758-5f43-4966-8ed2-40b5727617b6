package com.bxkj.enterprise.ui.fragment.schoolrecruitlist

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.appendItem
import com.bxkj.enterprise.data.SchoolRecruitV2Data
import com.bxkj.enterprise.data.source.SchoolRecruitV2Repo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/4/14
 */
class SchoolRecruitListV2ViewModel @Inject constructor(
    private val schoolRecruitV2Repo: SchoolRecruitV2Repo
) : BaseViewModel() {

    val notifySchoolRecruitDataChangeEvent = LiveEvent<Nothing>()

    val schoolRecruitListViewModel = RefreshListViewModel()

    val showSelectAll = MutableLiveData<Boolean>().apply { value = false }

    val selectAll = MutableLiveData<Boolean>().apply { value = false }

    val hasSelectedItem = MutableLiveData<Boolean>().apply { value = false }

    private var state: Int = SchoolRecruitListFragment.STATE_ALL

    init {
        schoolRecruitListViewModel.setOnLoadDataListener {
            viewModelScope.launch {
                schoolRecruitV2Repo.getSchoolRecruitListV2(
                    "",
                    state,
                    it,
                    CommonApiConstants.DEFAULT_PAGE_SIZE
                ).handleResult({
                    it?.let {
                        if (it.dataList.isNullOrEmpty()) {
                            schoolRecruitListViewModel.noMoreData()
                        } else {
                            schoolRecruitListViewModel.autoAddAll(it.dataList)
                        }
                    } ?: let {
                        schoolRecruitListViewModel.noMoreData()
                    }
                    checkSelectState()
                }, {
                    if (it.isNoDataError) {
                        schoolRecruitListViewModel.noMoreData()
                    } else {
                        schoolRecruitListViewModel.loadError()
                    }
                })
            }
        }
    }

    fun start(state: Int) {
        this.state = state
        schoolRecruitListViewModel.refresh()
    }

    override fun reloadData() {
        schoolRecruitListViewModel.refresh()
    }

    fun setShowSelectAll(show: Boolean) {
        showSelectAll.value = show
        if (!show) {
            setAllItemSelectState(false)
            selectAll.value = false
        }
    }

    /**
     * 检查是否已经全选
     */
    fun checkSelectState() {
        selectAll.value = schoolRecruitListViewModel.data.none {
            !(it as SchoolRecruitV2Data).checked
        }

        hasSelectedItem.value =
            schoolRecruitListViewModel.data.any { (it as SchoolRecruitV2Data).checked }
    }

    fun switchSelectAllState() {
        if (schoolRecruitListViewModel.data.isNotEmpty()) {
            val targetState = selectAll.value?.not()
            setAllItemSelectState(targetState)
            checkSelectState()
        }
    }

    private fun setAllItemSelectState(selected: Boolean?) {
        schoolRecruitListViewModel.data.forEach {
            (it as SchoolRecruitV2Data).checked = selected ?: false
        }
    }

    fun deleteSchoolRecruit(schoolRecruitIDs: String) {
        showLoading()
        viewModelScope.launch {
            schoolRecruitV2Repo.deleteSchoolRecruit(schoolRecruitIDs)
                .handleResult({
                    schoolRecruitListViewModel.data.removeAll {
                        (it as SchoolRecruitV2Data).checked
                    }
                    schoolRecruitListViewModel.notifyDataSetChange()
                    if (schoolRecruitListViewModel.data.size == 0) {
                        schoolRecruitListViewModel.refresh()
                    }
                    checkSelectState()
                    notifySchoolRecruitDataChangeEvent.call()
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    fun deleteSelected() {
        deleteSchoolRecruit(schoolRecruitListViewModel.data.filter { (it as SchoolRecruitV2Data).checked }
            .map { (it as SchoolRecruitV2Data).id.toString() }.appendItem())
    }
}