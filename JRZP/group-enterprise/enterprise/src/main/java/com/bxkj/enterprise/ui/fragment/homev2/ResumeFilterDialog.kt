package com.bxkj.enterprise.ui.fragment.homev2

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.widget.filterpopup.FilterOption
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseDialogResumeFilterBinding
import com.bxkj.enterprise.ui.fragment.recommendresume.ResumeFilterParams
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity
import com.zaaach.citypicker.model.City

/**
 *
 * @author: sanjin
 * @date: 2022/11/14
 */
class ResumeFilterDialog(
  private val _onFilterParamsChange: (filterParams: ResumeFilterParams) -> Unit,
) : DialogFragment() {

  private val _resumeFilterParams = ResumeFilterParams()

  private var _genderOptionListAdapter: ResumeFilterOptionListAdapter<PickerOptionsData>? = null
  private var _salaryOptionListAdapter: ResumeFilterOptionListAdapter<SalaryRange>? = null

  private lateinit var viewBinding: EnterpriseDialogResumeFilterBinding

  private val _selectCityLauncher = registerForActivityResult(StartActivityForResult()) {
    if (it.resultCode == CityPickerActivity.RESULT_SELECT_CITY_SUCCESS) {
      it.data?.let { data ->
        val resultCity = data.getParcelableExtra<City>(CityPickerActivity.EXTRA_RESULT_CITY)
        updateSelectedCity(resultCity)
      }
    }
  }

  fun setParams(params: ResumeFilterParams) {
    _resumeFilterParams.assign(params)
  }

  override fun onStart() {
    super.onStart()
    dialog?.let {
      it.window?.let { window ->
        window.setLayout(
          (DensityUtils.getScreenWidth(context) * 0.8f).toInt(),
          LayoutParams.MATCH_PARENT
        )
        window.setGravity(Gravity.RIGHT)
      }
    }
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    setStyle(STYLE_NO_TITLE, R.style.RightInDialogStyle)
  }

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View {
    val view = inflater.inflate(R.layout.enterprise_dialog_resume_filter, container, false)
    viewBinding = EnterpriseDialogResumeFilterBinding.bind(view)
    return viewBinding.root
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    initView()
  }

  private fun initView() {
    setupAddressLayoutClickListener()

    setupAgeRangeSlider()
    setupGenderOptionList()
    setupSalaryOptionList()

    setupButtonClickListener()
  }

  private fun updateSelectedCity(city: City?) {
    city?.let {
      viewBinding.tvCity.text = it.name
      _resumeFilterParams.city = it.code.getOrDefault("0").toInt()
      _resumeFilterParams.cityName = it.name
    }
  }

  private fun setupAddressLayoutClickListener() {
    viewBinding.tvCity.text = _resumeFilterParams.cityName
    viewBinding.llAddress.setOnClickListener {
      _selectCityLauncher.launch(CityPickerActivity.newIntent(activity, false))
    }
  }

  private fun setupAgeRangeSlider() {
    setupAgeRangeValues()

    viewBinding.rangeAge.let {
      it.clearOnChangeListeners()
      it.addOnChangeListener { slider, _, _ ->
        val currentAgeRange = slider.values
        val minAge = currentAgeRange[0].toInt()
        val maxAge = currentAgeRange[1].toInt()
        _resumeFilterParams.minAge = minAge
        _resumeFilterParams.maxAge = maxAge
        viewBinding.tvAgeRange.text = appendAgeRange(minAge, maxAge)
      }
    }
  }

  private fun appendAgeRange(minAge: Int, maxAge: Int): String = buildString {
    append("${minAge}岁-")
    append((if (maxAge == 45) "45岁以上" else "${maxAge}岁"))
  }

  private fun setupButtonClickListener() {
    view?.findViewById<TextView>(R.id.tv_reset)?.setOnClickListener {
      resetFilterParams()
    }

    view?.findViewById<TextView>(R.id.tv_confirm)?.setOnClickListener {
      _genderOptionListAdapter?.let {
        it.getData()?.get(it.getSelectedPosition())?.let { gender ->
          _resumeFilterParams.gender = gender.id
        }
      }
      _salaryOptionListAdapter?.let {
        it.getData()?.get(it.getSelectedPosition())?.let { salaryRange ->
          _resumeFilterParams.minSalary = salaryRange.minSalary
          _resumeFilterParams.maxSalary = salaryRange.maxSalary
        }
      }
      _onFilterParamsChange.invoke(_resumeFilterParams)
      dismiss()
    }
  }

  private fun resetFilterParams() {
    _resumeFilterParams.reset()
    _genderOptionListAdapter?.resetSelected()
    _salaryOptionListAdapter?.resetSelected()
    setupAgeRangeValues()
    _onFilterParamsChange.invoke(_resumeFilterParams)
  }

  private fun setupAgeRangeValues() {
    viewBinding.tvAgeRange.text =
      appendAgeRange(_resumeFilterParams.minAge, _resumeFilterParams.maxAge)
    viewBinding.rangeAge.values = mutableListOf(
      _resumeFilterParams.minAge.toFloat(),
      _resumeFilterParams.maxAge.toFloat()
    )
  }

  private fun setupGenderOptionList() {
    val genderOptionList = listOf(
      PickerOptionsData(-1, "不限"),
      PickerOptionsData(0, "男"),
      PickerOptionsData(1, "女")
    )
    _genderOptionListAdapter = ResumeFilterOptionListAdapter<PickerOptionsData>().apply {
      submitList(
        genderOptionList
      )
      setSelectedPosition(genderOptionList.indexOfFirst {
        it.id == _resumeFilterParams.gender
      })
    }
    view?.findViewById<RecyclerView>(R.id.recycler_gender)?.let {
      it.layoutManager = GridLayoutManager(requireContext(), 3)
      it.addItemDecoration(GridItemDecoration(requireContext().getResDrawable(R.drawable.divider_8)))
      it.adapter = _genderOptionListAdapter
    }
  }

  private fun setupSalaryOptionList() {
    val salaryRangeList = listOf(
      SalaryRange(0, 0, "不限"),
      SalaryRange(4000, 5500, "4K-5.5K"),
      SalaryRange(5500, 7000, "5.5K-7K"),
      SalaryRange(7000, 9000, "7K-9K"),
      SalaryRange(9000, 12000, "9K-12K"),
      SalaryRange(12000, Int.MAX_VALUE, "12K以上")
    )
    _salaryOptionListAdapter = ResumeFilterOptionListAdapter<SalaryRange>().apply {
      submitList(
        salaryRangeList
      )
      setSelectedPosition(salaryRangeList.indexOfFirst {
        it.minSalary == _resumeFilterParams.minSalary && it.maxSalary == _resumeFilterParams.maxSalary
      })
    }
    view?.findViewById<RecyclerView>(R.id.recycler_salary_range)?.let {
      it.layoutManager = GridLayoutManager(requireContext(), 3)
      it.addItemDecoration(GridItemDecoration(requireContext().getResDrawable(R.drawable.divider_8)))
      it.adapter = _salaryOptionListAdapter
    }
  }

  data class SalaryRange(
    val minSalary: Int = 0,
    val maxSalary: Int = Int.MAX_VALUE,
    val showText: String = "",
  ) : FilterOption {
    override fun getName(): String {
      return showText
    }
  }
}