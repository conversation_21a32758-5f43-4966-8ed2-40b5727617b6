package com.bxkj.enterprise.ui.activity.rechargeintegral

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.util.DensityUtils
import com.bxkj.ecommon.base.EBaseDBActivity
import com.bxkj.ecommon.util.recyclerutil.RecycleViewGridDivider
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseActivityRechargeIntegralBinding
import com.bxkj.enterprise.ui.activity.ordermanagement.OrderManagementActivity
import com.bxkj.enterprise.ui.activity.paymentorder.PaymentOrderActivity

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.ui.activity.rechargeintegral
 * @Description:积分充值
 * <AUTHOR>
 * @date 2019/8/12
 * @version V1.0
 */
class RechargeIntegralActivity :
  EBaseDBActivity<EnterpriseActivityRechargeIntegralBinding, RechargeIntegralViewModel>() {

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, RechargeIntegralActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<RechargeIntegralViewModel> =
    RechargeIntegralViewModel::class.java

  override fun getLayoutId(): Int = R.layout.enterprise_activity_recharge_integral

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    setupRechargeIntegralList()
    subscribeRechargeIntegralViewModelEvent()
    viewModel.start()

    viewBinding.titleBar.setRightOptionClickListener {
      OrderManagementActivity.start(this, true)
    }
  }

  private fun subscribeRechargeIntegralViewModelEvent() {
    viewModel.createOrderEvent.observe(this, Observer { orderId ->
      orderId?.let {
        viewModel.selectedItem.value?.let {
          startActivity(PaymentOrderActivity.newIntent(this, orderId, it.price.toString()))
        }
      }
    })
  }

  private fun setupRechargeIntegralList() {
    val rechargeIntegralListAdapter = RechargeIntegralListAdapter(
      this,
      R.layout.enterprise_recycler_recharge_integral_item,
      viewModel
    )
    val recyclerRechargeIntegralList =
      viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerRechargeIntegralList.layoutManager =
      GridLayoutManager(this, 3, LinearLayoutManager.VERTICAL, false)
    recyclerRechargeIntegralList.adapter = rechargeIntegralListAdapter
    recyclerRechargeIntegralList.addItemDecoration(
      RecycleViewGridDivider(
        DensityUtils.dp2px(this, 10f),
        getResColor(R.color.common_white),
        true
      )
    )
    viewModel.listViewModel.setAdapter(rechargeIntegralListAdapter)
  }

}