package com.bxkj.enterprise.ui.activity.myvideomanagement

import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/29
 * @version: V1.0
 */
class MyVideoManagementViewModel @Inject constructor() : BaseViewModel() {

  val openEdit = MutableLiveData<Boolean>().apply { value = false }

  fun openOrCloseEdit() {
    openEdit.value?.let {
      openEdit.value = it.not()
    }
  }

  fun closeEdit() {
    openEdit.value = false
  }

}