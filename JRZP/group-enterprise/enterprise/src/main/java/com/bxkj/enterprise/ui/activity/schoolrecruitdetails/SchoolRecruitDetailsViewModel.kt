package com.bxkj.enterprise.ui.activity.schoolrecruitdetails

import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.TimeUtils
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.enterprise.data.SchoolRecruitDetailsData
import com.bxkj.enterprise.data.source.SchoolRecruitDetailsRepo
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.Date
import java.util.concurrent.TimeUnit
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.schoolrecruitdetails
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/6/13
 * @version V1.0
 */
class SchoolRecruitDetailsViewModel @Inject constructor(
  schoolRecruitDetailsRepo: SchoolRecruitDetailsRepo
) : BaseViewModel() {

  private var mCountdownDisposable: Disposable? = null
  private val mSchoolRecruitDetailsRepo = schoolRecruitDetailsRepo

  val schoolRecruitDetailsData = MutableLiveData<SchoolRecruitDetailsData>()
  val startCountDown = MutableLiveData<String>()
  val toSchoolHomeCommand = LiveEvent<Int>()
  val toSignUpCommand = LiveEvent<Int>()
  val signUpButtonText = MutableLiveData<String>()
  val signUpEnabled = ObservableBoolean()

  public fun getSchoolRecruitDetails(recruitId: Int) {
    mSchoolRecruitDetailsRepo.getSchoolRecruitDetails(recruitId, object :
      ResultDataCallBack<SchoolRecruitDetailsData> {
      override fun onSuccess(data: SchoolRecruitDetailsData?) {
        handleRecruitStatus(data)
        schoolRecruitDetailsData.value = data
      }

      override fun onError(respondThrowable: RespondThrowable) {
        showToast(respondThrowable.errMsg)
      }
    })
  }

  public fun checkSchoolRecruitStatus(userId: Int, recruitId: Int) {
    mSchoolRecruitDetailsRepo.checkSchoolRecruitStatus(userId, recruitId, object :
      ResultCallBack {
      override fun onSuccess() {
        signUpEnabled.set(true)
        signUpButtonText.value = "报名参会"
      }

      override fun onError(respondThrowable: RespondThrowable) {
        signUpEnabled.set(false)
        if (respondThrowable.errCode == 30002) {
          signUpButtonText.value = "报名已结束"
        } else if (respondThrowable.errCode == 30003) {
          signUpButtonText.value = "您已报名"
        }
      }
    })
  }

  private fun handleRecruitStatus(data: SchoolRecruitDetailsData?) {
    data?.let {
      if (it.dateNameFlag == 1) {
        val startDiffSecond = TimeUtils.getSecondDifference(
          Date(),
          TimeUtils.stringToDate("yyyy-MM-dd HH:mm", data.ksdate)
        )
        if (startDiffSecond <= 0) {
          data.dateNameFlag = 0
          schoolRecruitDetailsData.value = data
          return
        }
        Observable.interval(0, 1, TimeUnit.SECONDS)
          .take(startDiffSecond + 1)
          .map<Long> { aLong -> startDiffSecond - aLong }
          .subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread())
          .subscribe(object : Observer<Long> {
            override fun onSubscribe(d: Disposable) {
              mCountdownDisposable = d
            }

            override fun onNext(aLong: Long) {
              startCountDown.value =
                TimeUtils.second2DayMinutesAndSeconds(aLong, "天", "时", "分", "秒")
            }

            override fun onError(e: Throwable) {}

            override fun onComplete() {
              signUpButtonText.value = "报名已结束"
              data.dateNameFlag = 0
              data.baomingKey = 0
              signUpEnabled.set(false)
              schoolRecruitDetailsData.value = data
            }
          })
      }
    }
  }

  override fun onCleared() {
    super.onCleared()
    mCountdownDisposable?.let {
      if (!it.isDisposed) {
        it.dispose()
      }
    }
  }

  public fun toSchoolHome() {
    schoolRecruitDetailsData.value?.let {
      toSchoolHomeCommand.value = it.gaoxiaoid
    }
  }

  public fun toSignUp() {
    schoolRecruitDetailsData.value?.let {
      toSignUpCommand.value = it.id
    }
  }

}