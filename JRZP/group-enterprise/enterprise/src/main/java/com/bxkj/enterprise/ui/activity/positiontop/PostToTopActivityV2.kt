package com.bxkj.enterprise.ui.activity.positiontop

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxBus.Message
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.enterprise.R
import com.bxkj.enterprise.data.PositionTopKeywordData
import com.bxkj.enterprise.databinding.BActivityPostToTopBinding
import com.bxkj.enterprise.ui.activity.beanmall.BeanMallFunctionItem
import com.bxkj.enterprise.ui.activity.buyfunctionpackages.BuyFunctionPackagesActivity
import com.bxkj.enterprise.ui.activity.membercenter.MemberCenterActivityV3
import com.bxkj.enterprise.ui.activity.positiontopkeywords.PositionTopKeywordsActivity
import com.bxkj.enterprise.ui.fragment.positionlist.PositionListFragment
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerActivity
import com.zaaach.citypicker.model.City

/**
 * description: 职位置顶
 * author:Sanjin
 * date:2024/12/3
 **/
class PostToTopActivityV2 : BaseDBActivity<BActivityPostToTopBinding, PostToTopViewModel>(),
  OnClickListener {

  private var postToTopCostPopup: MenuPopup? = null

  private val toAddKeywordsLauncher = registerForActivityResult(StartActivityForResult()) {
    if (it.resultCode == PositionTopKeywordsActivity.RESULT_SUCCESS) {
      it.data?.let {
        val keywords =
          it.getParcelableArrayListExtra<PositionTopKeywordData>(PositionTopKeywordsActivity.EXTRA_KEYWORDS)
        viewModel.setKeywords(keywords?.filter { !it.keyword.isNullOrBlank() }
          ?.joinToString("|") { item -> item.keyword }.getOrDefault())
      }
    }
  }

  private val toAddCityLauncher = registerForActivityResult(StartActivityForResult()) {
    if (it.resultCode == CityPickerActivity.RESULT_SELECT_CITY_SUCCESS) {
      it.data?.let {
        val city = it.getParcelableExtra<City>(CityPickerActivity.EXTRA_RESULT_CITY)
        viewModel.setCity(city)
      }
    }
  }

  private val toBuyPackageLauncher = registerForActivityResult(StartActivityForResult()) {
    viewModel.refreshBalance()
  }

  override fun getViewModelClass(): Class<PostToTopViewModel> = PostToTopViewModel::class.java

  override fun getLayoutId(): Int = R.layout.b_activity_post_to_top

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    viewModel.start(intent.getIntExtra(EXTRA_JOB_ID, 0))
    viewModel.selectTopDays()

    subscribeViewModelEvent()
  }

  override fun onClick(v: View?) {
    v?.let {
      when (it.id) {
        R.id.clv_top_days -> {
          postToTopCostPopup?.show()
        }

        R.id.clv_top_city -> {
          toAddCityLauncher.launch(CityPickerActivity.newIntent(this, false))
        }

        R.id.ll_pay_by_days -> {
          viewModel.setPayByDay()
          viewBinding.ivPayByDays.visibility = View.VISIBLE
          viewBinding.ivPayByBean.visibility = View.GONE
        }

        R.id.ll_pay_by_bean -> {
          viewModel.setPayByBean()
          viewBinding.ivPayByBean.visibility = View.VISIBLE
          viewBinding.ivPayByDays.visibility = View.GONE
        }

        else -> {}
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.toppedCityList.observe(this) {
      if (it.isNullOrEmpty()) {
        viewBinding.clvToppedCity.visibility = View.GONE
      } else {
        viewBinding.clvToppedCity.visibility = View.VISIBLE
        viewBinding.clvToppedCity.setContent(it.joinToString(separator = "、") { it.cityName })
      }
    }

    viewModel.postToTopCostList.observe(this) { list ->
      if (postToTopCostPopup == null) {
        postToTopCostPopup = MenuPopup.Builder(this)
          .setData(list)
          .setOnItemClickListener { _, position ->
            viewModel.selectTopDays(list[position])
          }.build()
      }
    }

    viewModel.toAddKeywordsCommand.observe(this, EventObserver {
      toAddKeywordsLauncher.launch(
        PositionTopKeywordsActivity.newIntent(
          this,
          it.cityId,
          it.topDays,
        )
      )
    })

    viewModel.topSuccessEvent.observe(this, EventObserver {
      RxBus.get().post(
        Message(RxMsgCode.REFRESH_POSITION_LIST_CODE, PositionListFragment.TAG_RUNNING)
      )
      showToast(getString(R.string.position_top_success))
      setResult(RESULT_OK)
      finish()
    })

    viewModel.topFailedEvent.observe(this, EventObserver {
      ActionDialog.Builder()
        .setTitle(getString(R.string.tips))
        .setContent(it)
        .setConfirmText("去购买")
        .setOnConfirmClickListener { dialog: ActionDialog? ->
          toBuyPackageLauncher.launch(
            BuyFunctionPackagesActivity.newIntent(
              this,
              BeanMallFunctionItem.TOP
            )
          )
        }
        .build()
        .show(supportFragmentManager)
    })

    viewModel.noVipTipsEvent.observe(this, EventObserver {
      ActionDialog.Builder()
        .setTitle(getString(R.string.tips))
        .setContent(it)
        .setConfirmText("去购买")
        .setOnConfirmClickListener { dialog: ActionDialog? ->
          toBuyPackageLauncher.launch(
            MemberCenterActivityV3.newIntent(this)
          )
        }
        .build()
        .show(supportFragmentManager)
    })
  }

  companion object {

    private const val EXTRA_JOB_ID = "JOB_ID"

    @JvmStatic
    fun newIntent(context: Context, jobId: Int): Intent {
      return Intent(context, PostToTopActivityV2::class.java).apply {
        putExtra(EXTRA_JOB_ID, jobId)
      }
    }
  }
}