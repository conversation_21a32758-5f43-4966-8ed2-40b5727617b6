package com.bxkj.enterprise.ui.activity.resumereceive;

import android.content.Context;
import android.widget.ImageView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.ResumeReceiveMailboxItemData;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.resumereceive
 * @Description: 简历接收邮箱列表适配器
 * @TODO: TODO
 * @date 2018/7/27
 */
public class ResumeReceiveListAdapter extends SuperAdapter<ResumeReceiveMailboxItemData> {

  private List<ResumeReceiveMailboxItemData> mSelectedItems;

  public ResumeReceiveListAdapter(Context context, List<ResumeReceiveMailboxItemData> list,
      int layoutResId) {
    super(context, layoutResId, list);
    mSelectedItems = new ArrayList<>();
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType,
      ResumeReceiveMailboxItemData resumeReceiveMailboxItemData, int position) {
    holder.setText(R.id.tv_item, resumeReceiveMailboxItemData.getEmail());
    ImageView ivSelected = holder.findViewById(R.id.iv_selected);
    ivSelected.setSelected(mSelectedItems.contains(resumeReceiveMailboxItemData));
    holder.itemView.setOnClickListener(view -> {
      if (mSelectedItems.contains(resumeReceiveMailboxItemData)) {
        mSelectedItems.remove(resumeReceiveMailboxItemData);
      } else {
        mSelectedItems.add(resumeReceiveMailboxItemData);
      }
      notifyItemChanged(position);
    });
    setOnChildClickListener(position, holder.findViewById(R.id.iv_delete));
  }

  @Override
  public int removeAt(int position) {
    if (mSelectedItems.contains(mList.get(position))) {
      mSelectedItems.remove(mList.get(position));
    }
    return super.removeAt(position);
  }

  public List<ResumeReceiveMailboxItemData> getSelectedItems() {
    return mSelectedItems;
  }

  public void setSelectedItems(
      List<ResumeReceiveMailboxItemData> resumeReceiveMailboxItemDataList) {
    if (resumeReceiveMailboxItemDataList != null) {
      mSelectedItems = resumeReceiveMailboxItemDataList;
    }
  }
}
