package com.bxkj.enterprise.data.source

import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.base.mvvm.callback.ResultListCallBack
import com.bxkj.common.util.kotlin.objectEncrypt
import com.bxkj.enterprise.api.BusinessApiV2
import com.bxkj.enterprise.api.BusinessApi
import com.bxkj.enterprise.api.EnterpriseApiConstants
import com.bxkj.enterprise.data.MessageData
import com.bxkj.enterprise.data.MessageItemData
import com.bxkj.enterprise.data.SystemMsgData
import com.bxkj.enterprise.data.UnreadVideoMsgData
import com.bxkj.enterprise.data.source.datasource.MessageDataSource
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data.source
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2019/8/7
 * @version V1.0
 */
class MessageRepo @Inject constructor(
    private val businessApi: BusinessApi,
    private val _coroutinesApi: BusinessApiV2,
) : BaseRepo(), MessageDataSource {


    suspend fun sendHelloSms(friendUserId: Int, content: String): ReqResponse<Nothing> {
        return httpRequest {
            _coroutinesApi.sendHelloSms(
                ZPRequestBody().apply {
                    put("uid", friendUserId)
                    put("content", content)
                }.objectEncrypt()
            )
        }
    }

    companion object {

        const val MESSAGE_TYPE_ALL = 0
        const val MESSAGE_LOOK_STATUS_NOT_VIDEO = -1
    }

    override fun getMessageList(
        userId: Int,
        userType: Int,
        messageType: Int,
        lookStatus: Int,
        pageIndex: Int,
        pageSize: Int,
        callBack: ResultListCallBack<List<MessageItemData>>,
    ) {
        businessApi.getMessageList(userId, userType, messageType, lookStatus, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callBack.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30001) {
                        callBack.onNoMoreData()
                    } else {
                        callBack.onError(respondThrowable)
                    }
                }
            })
    }

    /**
     * 检查是否存在未读视频消息
     */
    suspend fun checkHasUnreadVideoMsg(userID: Int): ReqResponse<UnreadVideoMsgData> {
        return httpRequest {
            _coroutinesApi.checkHasUnreadVideoMsg(ZPRequestBody().apply {
                put("userID", userID)
            }).apply {
                if (status == 10001) {
                    data = UnreadVideoMsgData(msg == "true")
                }
            }
        }
    }

    /**
     * 获取消息列表
     */
    suspend fun getMessageList(
        userId: Int, @EnterpriseApiConstants.MsgType queryStatus: String, pageIndex: Int, pageSize: Int,
    ): ReqResponse<List<MessageData>> {
        return httpRequest {
            _coroutinesApi.getMessageList(
                ZPRequestBody()
                    .apply {
                        put("uid", userId)
                        put("look", "-1")
                        put("lx", 1)
                        put("types", queryStatus)
                        put("pageIndex", pageIndex)
                        put("pageSize", pageSize)
                    }
            )
        }
    }

    /**
     * 设置消息已读
     */
    suspend fun setMsgRead(msgId: Int): ReqResponse<Nothing> {
        return httpRequest {
            _coroutinesApi.setMsgRead(
                ZPRequestBody()
                    .apply {
                        put("id", msgId)
                    }
            )
        }
    }

    /**
     * 获取系统消息列表
     */
    suspend fun getSystemMsgList(
        userId: Int,
        pageIndex: Int,
        pageSize: Int,
    ): ReqResponse<List<SystemMsgData>> {
        return httpRequest {
            _coroutinesApi.getSystemMsgList(
                ZPRequestBody()
                    .apply {
                        put("uid", userId)
                        put("pageIndex", pageIndex)
                        put("pageSize", pageSize)
                    }
            )
        }
    }

    /**
     * 设置系统消息已读
     */
    suspend fun setSystemMsgRead(userId: Int): ReqResponse<Nothing> {
        return httpRequest {
            _coroutinesApi.setSystemMsgRead(
                ZPRequestBody()
                    .apply {
                        put("uid", userId)
                    }
            )
        }
    }

    /**
     * 获取未读数量[userType]用户类型 0个人 1企业
     */
    suspend fun getUnreadMsgCount(userId: Int, userType: Int, msgStates: String): ReqResponse<Int> {
        return httpRequest {
            _coroutinesApi.getUnreadMsgCount(
                ZPRequestBody().apply {
                    put("uid", userId)
                    put("lx", userType)
                    put("types", msgStates)
                    put("look", 0)
                }
            ).apply {
                if (status == 10001) {
                    data = count.toInt()
                }
            }
        }
    }
}