package com.bxkj.enterprise.ui.activity.selecttimeperiod;

import android.content.Context;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.R;

import com.elvishew.xlog.XLog;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.selecttimeperiod
 * @Description: 时间段列表适配器
 * @TODO: TODO
 * @date 2018/7/25
 */
public class TimePeriodListAdapter extends SuperAdapter<String> {

  private List<String> mSelectItems;

  public TimePeriodListAdapter(Context context, String[] list, int layoutResId) {
    super(context, layoutResId, Arrays.asList(list));
    mSelectItems = new ArrayList<>();
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType, String s, int position) {
    holder.setText(R.id.tv_item, s);
    XLog.d(mSelectItems.contains(s) + "item:" + s);
    holder.findViewById(R.id.tv_item).setSelected(mSelectItems.contains(s));
    holder.itemView.setOnClickListener(view -> {
      if (mSelectItems.contains(s)) {
        mSelectItems.remove(s);
      } else {
        mSelectItems.add(s);
      }
      notifyItemChanged(position);
    });
  }

  public List<String> getSelectItems() {
    return mSelectItems;
  }

  public void setSelectItems(List<String> selectItems) {
    mSelectItems.clear();
    mSelectItems.addAll(selectItems);
    notifyDataSetChanged();
  }
}
