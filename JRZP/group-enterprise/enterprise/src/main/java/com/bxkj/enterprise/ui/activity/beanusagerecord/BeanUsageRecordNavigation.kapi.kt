package com.bxkj.enterprise.ui.activity.beanusagerecord

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 * Description:
 * Author:Sanjin
 * Date:2024/2/26
 **/
class BeanUsageRecordNavigation {

    companion object {

        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/bean_usage_record"

        fun create(): RouterNavigator {
            return Router.getInstance().to(PATH)
        }
    }
}