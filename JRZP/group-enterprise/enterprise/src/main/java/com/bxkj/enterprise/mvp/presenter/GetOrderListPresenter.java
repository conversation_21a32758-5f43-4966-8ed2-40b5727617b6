package com.bxkj.enterprise.mvp.presenter;

import com.bxkj.common.di.module.ApplicationModule;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.OrderItemData;
import com.bxkj.enterprise.mvp.contract.GetOrderListContract;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.mvp.presenter
 * @Description: GetOrderList
 * @TODO: TODO
 * @date 2018/3/27
 */

public class GetOrderListPresenter extends GetOrderListContract.Presenter {

    private static final String TAG = GetOrderListPresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;
    private int mUserId;

    @Inject
    public GetOrderListPresenter(@Named(ApplicationModule.USER_ID) int userId, BusinessApi businessApi) {
        mBusinessApi = businessApi;
        mUserId = userId;
    }

    @Override
    public void getOrderList(int type, int isPay, int isEffect, int invoiceId, int pageSize, int pageIndex) {
        mBusinessApi.getOrderList(mUserId, type, isPay, isEffect, invoiceId, pageSize, pageIndex)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getOrderListSuccess((List<OrderItemData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.getErrCode() != 30002) {
                            mView.onRequestError(respondThrowable);
                        } else {
                            mView.onResultNoData();
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });

    }
}
