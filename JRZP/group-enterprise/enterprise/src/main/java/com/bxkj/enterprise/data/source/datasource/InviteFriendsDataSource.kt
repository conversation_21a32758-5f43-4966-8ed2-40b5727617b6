package com.bxkj.enterprise.data.source.datasource

import com.bxkj.common.base.mvvm.callback.ResultListCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.enterprise.data.InviteRecordItemData

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.data.source.datasource
 * @Description:
 * <AUTHOR>
 * @date 2019/8/13
 * @version V1.0
 */
interface InviteFriendsDataSource {
  fun getInviteRecordList(userId: Int, callBack: ResultListCallBack<List<InviteRecordItemData>>)

  fun getInviteUndoneList(userId: Int, callBack: ResultListCallBack<List<InviteRecordItemData>>)

  fun getShareTargetUrl(userId: Int, callback: ResultDataCallBack<String>)
}