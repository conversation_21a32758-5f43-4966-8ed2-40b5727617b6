package com.bxkj.enterprise.ui.activity.resumedetails.adapter;

import android.content.Context;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.ResumeEduBackgroundData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.itemviewbinder
 * @Description:
 * @TODO: TODO
 * @date 2018/5/9
 */

public class EduBackgroundAdapter extends SuperAdapter<ResumeEduBackgroundData> {
  public EduBackgroundAdapter(Context context, List<ResumeEduBackgroundData> list,
      int layoutResId) {
    super(context, layoutResId, list);
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType,
      ResumeEduBackgroundData eduBackgroundItemData, int position) {
    holder.setText(R.id.tv_edu_time,
        mContext.getString(R.string.resume_details_time_between, eduBackgroundItemData.getDate1(),
            eduBackgroundItemData.getDate2()));
    holder.setText(R.id.tv_edu_about, eduBackgroundItemData.getDescText());
  }
}
