package com.bxkj.enterprise.ui.fragment.talentpool

import androidx.fragment.app.Fragment
import com.bxkj.common.util.router.Router
import com.bxkj.enterprise.EnterpriseConstants

class TalentPoolFragmentNavigation {

    companion object {
        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/talentpool"

        fun create(): Fragment {
            return Router.getInstance().to(PATH).createFragment() as Fragment
        }
    }
}