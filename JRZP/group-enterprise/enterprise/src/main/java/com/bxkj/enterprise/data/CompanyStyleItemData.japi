package com.bxkj.enterprise.data;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description: 公司风采
 * @TODO: TODO
 * @date 2018/8/8
 */
public class CompanyStyleItemData {

    private int id;

    private String pic;

    private String domain;

    private boolean isAddItem;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public boolean isAddItem() {
        return isAddItem;
    }

    public void setAddItem(final boolean addItem) {
        isAddItem = addItem;
    }

    public String getRealImgUrl() {
        return domain + pic;
    }

    public static class DiffCallback extends DiffUtil.ItemCallback<CompanyStyleItemData> {

        @Override
        public boolean areItemsTheSame(@NonNull final CompanyStyleItemData oldItem,
                @NonNull final CompanyStyleItemData newItem) {
            return oldItem.id == newItem.id;
        }

        @Override
        public boolean areContentsTheSame(@NonNull final CompanyStyleItemData oldItem,
                @NonNull final CompanyStyleItemData newItem) {
            return oldItem.pic != null && newItem.pic != null && oldItem.pic.equals(newItem.pic);
        }
    }
}
