package com.bxkj.enterprise.ui.activity.crawlsetting

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.EnterpriseActivityCrawlSettingBinding

/**
 *
 * @author: sanjin
 * @date: 2022/10/26
 */
@Route(path = CrawlSettingNavigation.PATH)
class CrawlSettingActivity :
    BaseDBActivity<EnterpriseActivityCrawlSettingBinding, CrawlSettingViewModel>() {

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, CrawlSettingActivity::class.java)
        }
    }

    override fun getViewModelClass(): Class<CrawlSettingViewModel> =
        CrawlSettingViewModel::class.java

    override fun getLayoutId(): Int = R.layout.enterprise_activity_crawl_setting

    override fun initPage(savedInstanceState: Bundle?) {

        subscribeViewModelEvent()

        setupSwitchClickListener()

        viewModel.start()
    }

    private fun subscribeViewModelEvent() {
        viewModel.crawlOpenState.observe(this) {
            viewBinding.switchSyncJob.toggleSwitch(it)
        }
    }

    private fun setupSwitchClickListener() {
        viewBinding.switchSyncJob.setOnClickListener {
            viewModel.switchSyncJobOpenState(viewBinding.switchSyncJob.isOpened)
        }
    }
}