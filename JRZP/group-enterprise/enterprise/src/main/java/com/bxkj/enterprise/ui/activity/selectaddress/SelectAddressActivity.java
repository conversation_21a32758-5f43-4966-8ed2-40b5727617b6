package com.bxkj.enterprise.ui.activity.selectaddress;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.baidu.mapapi.map.BaiduMap;
import com.baidu.mapapi.map.MapStatus;
import com.baidu.mapapi.map.MapStatusUpdateFactory;
import com.baidu.mapapi.map.MapView;
import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.search.core.SearchResult;
import com.baidu.mapapi.search.geocode.GeoCodeOption;
import com.baidu.mapapi.search.geocode.GeoCodeResult;
import com.baidu.mapapi.search.geocode.GeoCoder;
import com.baidu.mapapi.search.geocode.OnGetGeoCoderResultListener;
import com.baidu.mapapi.search.geocode.ReverseGeoCodeResult;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.util.location.LocationManager;
import com.bxkj.common.util.location.ZPLocation;
import com.bxkj.ecommon.constants.ECommonApiConstants;
import com.bxkj.ecommon.util.map.LngLat;
import com.bxkj.ecommon.widget.adresspickerdialog.AddressPickerDialog;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.mvp.contract.AddressInfoContract;
import com.bxkj.enterprise.mvp.presenter.AddressInfoPresenter;
import com.jakewharton.rxbinding2.widget.RxTextView;
import io.reactivex.disposables.Disposable;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.ui.selectaddress
 * @Description: 选择地址
 * @TODO: TODO
 * @date 2018/6/23
 */
public class SelectAddressActivity extends BaseDaggerActivity implements AddressInfoContract.View {

  public static final String ADDRESS_DATA = "address_data";
  public static final String DETAILS_ADDRESS = "details_address";
  public static final String LNGLAT = "lnglat";

  @Inject
  AddressInfoPresenter mAddressInfoPresenter;

  private TextView tvRegion;
  private EditText etDetails;
  private MapView mapAddress;
  private ImageView ivPosition;
  private ImageView ivPositionShadow;
  private LinearLayout llDetailsAddress;

  private BaiduMap mBaiduMap;
  private GeoCoder mGeoCoder;

  private AddressPickerDialog mAddressPickerDialog;
  private AddressData mSelectedAddressData;

  private Disposable mTextChangeDisposable;

  public static Intent newIntent(Context context, AddressData addressData, String detailsAddress,
    LngLat lngLat) {
    Intent starter = new Intent(context, SelectAddressActivity.class);
    Bundle bundle = new Bundle();
    bundle.putParcelable(ADDRESS_DATA, addressData);
    bundle.putString(DETAILS_ADDRESS, detailsAddress);
    bundle.putParcelable(LNGLAT, lngLat);
    starter.putExtras(bundle);
    return starter;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_select_address;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mAddressInfoPresenter);
    return presenters;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.select_multi_address_title))
      .setRightText(getString(R.string.common_save))
      .setRightOptionClickListener(view -> {
        if (mSelectedAddressData.checkAddressEmpty()) {
          showToast(getString(R.string.select_address_no_selected));
          return;
        }
        String detailsAddress = etDetails.getText().toString();
        if (CheckUtils.isNullOrEmpty(detailsAddress)) {
          showToast(getString(R.string.select_address_details_not_be_null));
          return;
        }
        Intent intent = new Intent();
        Bundle bundle = new Bundle();
        bundle.putParcelable(ADDRESS_DATA, mSelectedAddressData);
        bundle.putString(DETAILS_ADDRESS, detailsAddress);
        LatLng latLng = mBaiduMap.getMapStatus().target;
        LngLat lngLat = new LngLat(latLng.longitude, latLng.latitude);
        bundle.putParcelable(LNGLAT, lngLat);
        intent.putExtras(bundle);
        setResult(RESULT_OK, intent);
        finish();
      });
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    final Intent intent = getIntent();

    mSelectedAddressData = intent.getParcelableExtra(ADDRESS_DATA);
    String detailsAddress = intent.getStringExtra(DETAILS_ADDRESS);
    if (mSelectedAddressData != null) {
      tvRegion.setText(mSelectedAddressData.getAddress());
    }
    if (!CheckUtils.isNullOrEmpty(detailsAddress)) {
      etDetails.setText(detailsAddress);
    }

    setupMap();
    setupAddressPickerDialog();
    handleTextChange();
  }

  private void checkHasAddressInfo() {
    if (mSelectedAddressData.provinceAndCityIsNull()) {
      LocationManager.INSTANCE.requestLocation(this, new LocationManager.OnLocationListener() {
        @Override
        public void onSuccess(@NotNull ZPLocation location) {
          mBaiduMap.animateMapStatus(MapStatusUpdateFactory.newLatLng(
            new LatLng(location.getLatitude(), location.getLongitude())));
          mAddressInfoPresenter.getAddressInfoByAddressName(ECommonApiConstants.GET_CITY_TYPE,
            location.getCity().substring(0, location.getCity().length() - 1));
        }

        @Override
        public void onFailed() {
          mBaiduMap.animateMapStatus(
            MapStatusUpdateFactory.newLatLng(new LatLng(39.912057, 116.405419)));
        }
      });
    }
  }

  private void handleTextChange() {
    mTextChangeDisposable = RxTextView.textChanges(etDetails)
      .debounce(1000, TimeUnit.MILLISECONDS)
      .subscribe(charSequence -> {
          if (mSelectedAddressData == null || CheckUtils.isNullOrEmpty(
            mSelectedAddressData.getCityName()) || charSequence.length() == 0) {
            return;
          } else {
            mGeoCoder.geocode(new GeoCodeOption()
              .city(mSelectedAddressData.getCityName())
              .address(mSelectedAddressData.getAreaAndStreet() + charSequence.toString()));
          }
        }
      );
  }

  private void setupMap() {
    LngLat lngLat = getIntent().getParcelableExtra(LNGLAT);
    mBaiduMap = mapAddress.getMap();
    mBaiduMap.setCompassEnable(false);
    if (lngLat != null) {
      mBaiduMap.animateMapStatus(MapStatusUpdateFactory.newLatLng(
        new LatLng(lngLat.getLatitude(), lngLat.getLongitude())));
    }
    mGeoCoder = GeoCoder.newInstance();
    mGeoCoder.setOnGetGeoCodeResultListener(new OnGetGeoCoderResultListener() {
      @Override
      public void onGetGeoCodeResult(GeoCodeResult geoCodeResult) {
        if (geoCodeResult == null || geoCodeResult.error != SearchResult.ERRORNO.NO_ERROR) {
          //没有检索到结果
          showToast(getString(R.string.select_address_not_search_position));
          return;
        }
        mBaiduMap.animateMapStatus(MapStatusUpdateFactory.newLatLng(geoCodeResult.getLocation()));
      }

      @Override
      public void onGetReverseGeoCodeResult(ReverseGeoCodeResult reverseGeoCodeResult) {

      }
    });

    mBaiduMap.setOnMapStatusChangeListener(new BaiduMap.OnMapStatusChangeListener() {
      @Override
      public void onMapStatusChangeStart(MapStatus mapStatus) {
        ivPosition.animate().translationY(-15);
        ivPositionShadow.animate().scaleX(2.0f);
        ivPositionShadow.animate().scaleY(2.0f);
      }

      @Override
      public void onMapStatusChangeStart(MapStatus mapStatus, int i) {
      }

      @Override
      public void onMapStatusChange(MapStatus mapStatus) {

      }

      @Override
      public void onMapStatusChangeFinish(MapStatus mapStatus) {
        ivPosition.animate().translationY(0);
        ivPositionShadow.animate().scaleX(1.0f);
        ivPositionShadow.animate().scaleY(1.0f);
      }
    });

    mBaiduMap.setOnMapLoadedCallback(() -> checkHasAddressInfo());
  }

  private void setupAddressPickerDialog() {
    mAddressPickerDialog = new AddressPickerDialog();
    mAddressPickerDialog.setOnSelectedListener((province, city, area, street) -> {
      mSelectedAddressData = new AddressData(province, city, area, street);
      tvRegion.setText(mSelectedAddressData.getAddress());
      if (!CheckUtils.isNullOrEmpty(mSelectedAddressData.getCityName())) {
        mGeoCoder.geocode(new GeoCodeOption()
          .city(mSelectedAddressData.getCityName())
          .address(
            (CheckUtils.isNullOrEmpty(mSelectedAddressData.getAreaAndStreet()) ? getString(
              R.string.select_address_city_center)
              : mSelectedAddressData.getAreaAndStreet())
              + etDetails.getText().toString()));
      }
    });
  }

  private void onViewClicked() {
    if (mSelectedAddressData != null) {
      mAddressPickerDialog.setSelected(mSelectedAddressData.getProvinceName(),
        mSelectedAddressData.getCityName(), mSelectedAddressData.getAreaName(),
        mSelectedAddressData.getStreetName());
    }
    mAddressPickerDialog.show(getSupportFragmentManager(),
      AddressPickerDialog.class.getSimpleName());
  }

  @Override
  public void getAddressInfoSuccess(AreaOptionsData areaOptionsData) {
    mSelectedAddressData.setProvinceName(areaOptionsData.getPName());
    mSelectedAddressData.setProvinceId(areaOptionsData.getPid());
    mSelectedAddressData.setCityName(areaOptionsData.getName());
    mSelectedAddressData.setCityId(areaOptionsData.getId());
    tvRegion.setText(mSelectedAddressData.getAddress());
  }

  @Override
  protected void onPause() {
    super.onPause();
    mapAddress.onPause();
  }

  @Override
  protected void onResume() {
    super.onResume();
    mapAddress.onResume();
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    mGeoCoder.destroy();
    mapAddress.onDestroy();
    if (mTextChangeDisposable != null && !mTextChangeDisposable.isDisposed()) {
      mTextChangeDisposable.dispose();
    }
  }

  private void bindView(View bindSource) {
    tvRegion = bindSource.findViewById(R.id.tv_region);
    etDetails = bindSource.findViewById(R.id.et_details);
    mapAddress = bindSource.findViewById(R.id.map_address);
    ivPosition = bindSource.findViewById(R.id.iv_position);
    ivPositionShadow = bindSource.findViewById(R.id.iv_position_shadow);
    llDetailsAddress = bindSource.findViewById(R.id.ll_details_address);
    bindSource.findViewById(R.id.tv_region).setOnClickListener(v -> onViewClicked());
  }
}
