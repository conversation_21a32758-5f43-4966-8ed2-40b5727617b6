package com.bxkj.enterprise.ui.activity.minesearchresume;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.util.TitleBarManager;
import com.therouter.router.Route;
import com.bigkoo.pickerview.view.OptionsPickerView;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.data.JobTypeData;
import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.util.picker.OneOptionPicker;
import com.bxkj.common.widget.popup.menupopup.MenuPopup;
import com.bxkj.ecommon.widget.ClearEditText;
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.api.parameters.SearchResumeParameters;
import com.bxkj.enterprise.data.db.SearchResumeRecord;
import com.bxkj.enterprise.mvp.contract.IndustryContract;
import com.bxkj.enterprise.mvp.presenter.IndustryPresenter;
import com.bxkj.enterprise.ui.activity.postjob.PostJobNavigation;
import com.bxkj.enterprise.ui.activity.searchresume.SearchResumeRecordListAdapter;
import com.bxkj.enterprise.ui.activity.searchresumeresult.SearchResultActivity;
import com.bxkj.enterprise.ui.activity.selectarea.SelectAreaActivity;
import com.bxkj.jrzp.support.feature.ui.seletjobtype.SelectJobTypeActivity;
import java.util.Collections;
import java.util.List;
import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.searchresume
 * @Description: 搜索简历
 * @TODO: TODO
 * @date 2018/8/16
 */
@Route(path = SearchResumeNavigation.PATH)
public class MineSearchResumeActivity extends BaseDaggerActivity
  implements IndustryContract.View, MineSearchResumeContract.View {

  private static final int TO_SELECT_AREA_CODE = 1;

  @Inject
  IndustryPresenter mIndustryPresenter;

  @Inject
  MineSearchResumePresenter mMineSearchResumePresenter;

  private ClearEditText etSearchJob;

  private TextView tvLocate;

  private TextView tvArea;

  private
  TextView tvExpectSalary;

  private TextView tvWorkExp;

  private TextView tvEducation;

  private TextView tvSex;

  private TextView tvWorkType;

  private RecyclerView recyclerSearchRecord;

  private TextView tvClearSearchRecord;

  private TextView tvExpandOrCollapse;

  private LinearLayout llMoreOptions;

  private SearchResumeParameters mSearchResumeParameters;

  private OptionsPickerView mWorkSalaryPicker;

  private OptionsPickerView mWorkExpPicker;

  private OptionsPickerView mWorkEducationPicker;

  private MenuPopup mSexMenuPopup;

  private SearchResumeRecordListAdapter mSearchResumeRecordListAdapter;

  private ActivityResultLauncher<Intent> mSelectJobTypeLauncher = registerForActivityResult(
    new StartActivityForResult(),
    result -> {
      if (result.getResultCode() == RESULT_OK && result.getData() != null) {
        List<JobTypeData> jobTypeList = result.getData()
          .getParcelableArrayListExtra(SelectJobTypeActivity.EXTRA_SELECTED_JOB_TYPE);
        if (jobTypeList != null) {
          JobTypeData selectedJobType = jobTypeList.get(0);
          mSearchResumeParameters.setFirstClassId(selectedJobType.getPid());
          mSearchResumeParameters.setSecondClassId(selectedJobType.getId());
        }
      }
    });

  public static void start(Context context) {
    Intent starter = new Intent(context, MineSearchResumeActivity.class);
    context.startActivity(starter);
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mIndustryPresenter);
    presenters.add(mMineSearchResumePresenter);
    return presenters;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_acitivty_mine_search_resume;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.search_resume));
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    tvLocate.setText(UserUtils.getUserSelectedCityName());
    mSearchResumeParameters = new SearchResumeParameters();
    mIndustryPresenter.getIndustryList();
    initWorkSalaryPicker();
    initWorkExpPicker();
    initWorkEducationPicker();
    initSexSelectPopup();

    initSearchRecord();
  }

  @Override
  protected void onResume() {
    super.onResume();
    mMineSearchResumePresenter.getRecordList();
  }

  /**
   * 初始化历史记录
   */
  private void initSearchRecord() {
    mSearchResumeRecordListAdapter = new SearchResumeRecordListAdapter(this, null,
      R.layout.enterprise_recycler_search_resume_record_item);
    recyclerSearchRecord.setLayoutManager(new LinearLayoutManager(this));
    recyclerSearchRecord.setAdapter(mSearchResumeRecordListAdapter);

    mSearchResumeRecordListAdapter.setOnItemClickListener((view, position) -> {
      SearchResumeRecord selectItem = mSearchResumeRecordListAdapter.getData().get(position);
      if (view.getId() == R.id.iv_delete_record) {
        new EActionDialog.Builder()
          .setTitle("删除")
          .setContent("删除历史记录" + "“" + selectItem.getContent() + "”")
          .setOnConfirmClickListener((actionDialog, inputText) -> {
            actionDialog.dismiss();
            mMineSearchResumePresenter.deleteRecordByContent(position, selectItem.getContent());
          }).build().show(getSupportFragmentManager(), EActionDialog.TAG);
      } else {
        startSearch(selectItem.getContent());
      }
    });
  }

  /**
   * 初始化职位薪资picker
   */
  private void initWorkSalaryPicker() {
    String[] workSalary = getResources().getStringArray(R.array.work_salary);
    mWorkSalaryPicker = new OneOptionPicker(this)
      .setOptionsDataList(workSalary)
      .setOnConfirmListener(position -> {
          tvExpectSalary.setText(workSalary[position]);
          mSearchResumeParameters.setWillMoney(workSalary[position]);
        }
      ).create();
  }

  /**
   * 初始化工作经验picker
   */
  private void initWorkExpPicker() {
    String[] workExp = getResources().getStringArray(R.array.work_exp);
    mWorkExpPicker = new OneOptionPicker(this)
      .setOptionsDataList(workExp)
      .setOnConfirmListener(position -> {
        tvWorkExp.setText(workExp[position]);
        mSearchResumeParameters.setWorkExpId(position + 1);
      }).create();
  }

  /**
   * 初始化学历picker
   */
  private void initWorkEducationPicker() {
    String[] workEducation = getResources().getStringArray(R.array.education);
    mWorkEducationPicker = new OneOptionPicker(this)
      .setOptionsDataList(workEducation)
      .setOnConfirmListener(position -> {
        tvEducation.setText(workEducation[position]);
        mSearchResumeParameters.setEducationId(position + 1);
      }).create();
  }

  /**
   * 性别选择popup
   */
  private void initSexSelectPopup() {
    String[] sexOptions = getResources().getStringArray(R.array.sex_options);
    mSexMenuPopup = new MenuPopup.Builder(this)
      .setShowMode(MenuPopup.BOTTOM)
      .setData(sexOptions)
      .setOnItemClickListener((view, position) -> {
        tvSex.setText(sexOptions[position]);
        mSearchResumeParameters.setSex(position);
      }).build();
  }

  private void onViewClicked(View view) {
    if (view.getId() == R.id.tv_area) {
      startActivityForResult(SelectAreaActivity.newIntent(this, true), TO_SELECT_AREA_CODE);
    } else if (view.getId() == R.id.tv_work_type) {
      mSelectJobTypeLauncher.launch(
        SelectJobTypeActivity.Companion.newIntent(this, PostJobNavigation.TYPE_FULL_TIME));
    } else if (view.getId() == R.id.tv_expect_salary) {
      mWorkSalaryPicker.show();
    } else if (view.getId() == R.id.tv_work_exp) {
      mWorkExpPicker.show();
    } else if (view.getId() == R.id.tv_education) {
      mWorkEducationPicker.show();
    } else if (view.getId() == R.id.tv_sex) {
      mSexMenuPopup.show();
    } else if (view.getId() == R.id.tv_expand_or_collapse) {
      changOptionsExpandStatus();
    } else if (view.getId() == R.id.tv_search_resume) {
      startSearch(etSearchJob.getText().toString());
    } else {
      new EActionDialog.Builder()
        .setTitle(getString(R.string.common_delete))
        .setContent(getString(R.string.common_confirm_clear_search_record))
        .setOnConfirmClickListener((actionDialog, inputText) -> {
          actionDialog.dismiss();
          mMineSearchResumePresenter.clearRecord();
          mSearchResumeRecordListAdapter.clear();
          tvClearSearchRecord.setVisibility(View.GONE);
        }).build().show(getSupportFragmentManager(), EActionDialog.TAG);
    }
  }

  private void changOptionsExpandStatus() {
    if (llMoreOptions.isShown()) {
      llMoreOptions.setVisibility(View.GONE);
      tvExpandOrCollapse.setText(getString(R.string.expand_more_options));
      tvExpandOrCollapse.setCompoundDrawablesWithIntrinsicBounds(0, 0,
        R.drawable.ic_search_job_expand_options, 0);
    } else {
      llMoreOptions.setVisibility(View.VISIBLE);
      tvExpandOrCollapse.setText(getString(R.string.collapse_more_options));
      tvExpandOrCollapse.setCompoundDrawablesWithIntrinsicBounds(0, 0,
        R.drawable.ic_search_job_collapse_options, 0);
    }
  }

  private void startSearch(String searchText) {
    mSearchResumeParameters.setTitle(searchText);
    if (!CheckUtils.isNullOrEmpty(searchText)) {
      mMineSearchResumePresenter.addOrReplaceRecord(searchText);
    }
    SearchResultActivity.start(this, mSearchResumeParameters, false);
  }

  @Override
  public void getIndustryListSuccess(List<PickerOptionsData> optionsItemDataList) {
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if (resultCode == RESULT_OK && data != null) {
      if (requestCode == TO_SELECT_AREA_CODE) {
        AreaOptionsData areaOptionsData = data.getParcelableExtra(SelectAreaActivity.RESULT_DATA);
        tvLocate.setText(UserUtils.getUserSelectedCityName());
        tvArea.setText(areaOptionsData.getName());
        mSearchResumeParameters.setCounty(areaOptionsData.getId());
      }
    }
  }

  @Override
  public void getRecordListSuccess(List<SearchResumeRecord> searchResumeRecordList) {
    tvClearSearchRecord.setVisibility(View.VISIBLE);
    Collections.reverse(searchResumeRecordList);
    mSearchResumeRecordListAdapter.reset(searchResumeRecordList);
  }

  @Override
  public void noRecord() {
    tvClearSearchRecord.setVisibility(View.GONE);
  }

  @Override
  public void deleteRecordSuccess(int position) {
    if (mSearchResumeRecordListAdapter.removeAt(position) == 0) {
      tvClearSearchRecord.setVisibility(View.GONE);
    }
  }

  private void bindView(View bindSource) {
    etSearchJob = bindSource.findViewById(R.id.et_search_job);
    tvLocate = bindSource.findViewById(R.id.tv_locate);
    tvArea = bindSource.findViewById(R.id.tv_area);
    tvExpectSalary = bindSource.findViewById(R.id.tv_expect_salary);
    tvWorkExp = bindSource.findViewById(R.id.tv_work_exp);
    tvEducation = bindSource.findViewById(R.id.tv_education);
    tvSex = bindSource.findViewById(R.id.tv_sex);
    tvWorkType = bindSource.findViewById(R.id.tv_work_type);
    recyclerSearchRecord = bindSource.findViewById(R.id.recycler_search_record);
    tvClearSearchRecord = bindSource.findViewById(R.id.tv_clear_search_record);
    tvExpandOrCollapse = bindSource.findViewById(R.id.tv_expand_or_collapse);
    llMoreOptions = bindSource.findViewById(R.id.ll_more_options);
    bindSource.findViewById(R.id.tv_area).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_work_type).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_expect_salary).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_work_exp).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_education).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_sex).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_expand_or_collapse).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_search_resume).setOnClickListener(v -> onViewClicked(v));
    bindSource.findViewById(R.id.tv_clear_search_record).setOnClickListener(v -> onViewClicked(v));
  }
}
