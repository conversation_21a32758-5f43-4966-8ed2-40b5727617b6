package com.bxkj.enterprise.ui.activity.selectrealtejob

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants
import com.bxkj.enterprise.data.PositionItemBean

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/11
 * @version: V1.0
 */
class SelectRelateJobNavigation {

  companion object {

    const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/selectvideolinkjob"

    const val EXTRA_SELECTED_ITEMS = "RESULT_SELECTED_ITEMS"

    fun navigate(selectedItems: List<PositionItemBean>? = null): RouterNavigator {
      return Router.getInstance().to(PATH).apply {
        if (!selectedItems.isNullOrEmpty()) {
          withParcelableArrayList(EXTRA_SELECTED_ITEMS, ArrayList(selectedItems))
        }
      }
    }
  }
}