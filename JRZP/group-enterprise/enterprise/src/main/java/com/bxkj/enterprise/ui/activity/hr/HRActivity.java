package com.bxkj.enterprise.ui.activity.hr;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.widget.dialog.ActionDialog;
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory;
import com.bxkj.common.widget.pagestatuslayout.PageStatusLayout;
import com.bxkj.ecommon.util.recyclerutil.RecycleViewDivider;
import com.bxkj.ecommon.widget.dialogfragment.EActionDialog;
import com.bxkj.enterprise.R;
import com.bxkj.enterprise.data.HrItemData;
import com.bxkj.enterprise.ui.activity.hredit.HREditActivityV2;
import com.therouter.router.Route;
import java.util.List;
import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.hr
 * @Description: HR
 * @TODO: TODO
 * @date 2018/7/30
 */
@Route(path = HRManagementNavigation.PATH)
public class HRActivity extends BaseDaggerActivity implements HRContract.View {

  private static final String IS_SELECTOR = "is_selector";
  public static final String RESULT_HR = "result_hr";

  @Inject
  HRPresenter mHRPresenter;

  RecyclerView recyclerContent;

  private HRListAdapter mHrListAdapter;
  private boolean mIsSelector;
  private PageStatusLayout mEmptyLayout;

  private final ActivityResultLauncher<Intent> mHrEditLauncher = registerForActivityResult(
    new StartActivityForResult(),
    result -> {
      if (result.getResultCode() == RESULT_OK) {
        mHRPresenter.getHrList(getMUserID());
      }
    });

  public static Intent newIntent(Context context, boolean isSelector) {
    Intent intent = new Intent(context, HRActivity.class);
    intent.putExtra(IS_SELECTOR, isSelector);
    return intent;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.enterprise_activity_hr;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mHRPresenter);
    return presenters;
  }

  @Override
  protected void initIntent(Intent intent) {
    mIsSelector = intent.getBooleanExtra(IS_SELECTOR, false);
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.hr_manager));
  }

  @Override
  protected void initPage() {
    recyclerContent = findViewById(R.id.recycler_content);
    findViewById(R.id.fl_add).setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View v) {
        toAddHrInfo();
      }
    });

    mEmptyLayout = PageStatusLayout.wrap(recyclerContent);
    mHrListAdapter =
      new HRListAdapter(this, null, R.layout.enterprise_recycler_hr_item, mIsSelector);
    recyclerContent.setLayoutManager(new LinearLayoutManager(this));
    recyclerContent.addItemDecoration(
      new RecycleViewDivider(this, LinearLayoutManager.VERTICAL, DensityUtils
        .dp2px(this, 8), getMColor(R.color.common_f4f4f4), true));
    recyclerContent.setAdapter(mHrListAdapter);
    mHrListAdapter.setOnItemClickListener((view, position) -> {
      final HrItemData hrItem = mHrListAdapter.getData().get(position);
      if (view.getId() == R.id.tv_delete) {
        new ActionDialog.Builder()
          .setContent(getString(R.string.enterprise_hr_delete_tips))
          .setOnConfirmClickListener((dialog) -> {
              if (position < mHrListAdapter.getData().size()) {
                mHRPresenter.deleteHr(getMUserID(), hrItem.getId(),
                  position);
              } else {
                showToast("删除失败，请刷新页面后重试");
              }
            }
          )
          .build()
          .show(getSupportFragmentManager(), EActionDialog.TAG);
      } else if (view.getId() == R.id.tv_edit) {
        toHrInfoEditPage(hrItem);
      } else {
        if (mIsSelector) {
          finish();
          //          backAndResultHrInfo(hrItem);
        } else {
          toHrInfoEditPage(hrItem);
        }
      }
    });

    mHRPresenter.getHrList(getMUserID());
  }

  private void toAddHrInfo() {
    toHrInfoEditPage(null);
  }

  private void toHrInfoEditPage(HrItemData hrItem) {
    mHrEditLauncher.launch(HREditActivityV2.newIntent(this, hrItem));
  }

  @Override
  public void getHrListSuccess(List<HrItemData> hrItemDataList) {
    mEmptyLayout.hidden();
    mHrListAdapter.reset(hrItemDataList);
  }

  @Override
  public void noHr() {
    mEmptyLayout.show(PageStatusConfigFactory.newErrorConfig()
      .setImg(R.drawable.common_ic_no_content)
      .setText("暂无HR")
      .setBtnText("马上添加")
      .setOnButtonClickListener(() -> toAddHrInfo()));
  }

  @Override
  public void deleteHrSuccess(int position) {
    mHRPresenter.getHrList(getMUserID());
  }

  private void setupResultData(HrItemData hrItem) {
    Intent intent = new Intent();
    Bundle bundle = new Bundle();
    bundle.putParcelable(RESULT_HR, hrItem);
    intent.putExtras(bundle);
    setResult(RESULT_OK, intent);
  }

  @Override
  public void finish() {
    if (mIsSelector) {
      if (mHrListAdapter.getData().size() > mHrListAdapter.getSelectedPosition()) {
        setupResultData(mHrListAdapter.getData().get(mHrListAdapter.getSelectedPosition()));
      }
    }
    super.finish();
  }
}
