package com.bxkj.enterprise.ui.activity.invitationinterview

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.enterprise.EnterpriseConstants

/**
 *
 * @author: YangXin
 * @date: 2021/3/16
 */
class InviteInterviewNavigation {

    companion object {

        const val PATH = "${EnterpriseConstants.ENTERPRISE_DIRECTORY}/inviteinterview"

        const val INTERVIEW_NORMAL = 0
        const val INTERVIEW_VIDEO = 1

        const val EXTRA_JOB_ID = "JOB_ID"
        const val EXTRA_RESUME_ID = "RESUME_ID"
        const val EXTRA_INVITE_USER_NAME = "USER_NAME"
        const val EXTRA_INTERVIEW_METHOD = "INTERVIEW_METHOD"
        const val EXTRA_INTERVIEW_ID = "INTERVIEW_ID"

        @JvmStatic
        @JvmOverloads
        fun create(
            jobId: Int,
            resumeId: Int,
            inviteUserName: String,
            interviewId: Int = 0
        ): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withInt(EXTRA_JOB_ID, jobId)
                .withInt(EXTRA_RESUME_ID, resumeId)
                .withString(EXTRA_INVITE_USER_NAME, inviteUserName)
                .withInt(EXTRA_INTERVIEW_METHOD, INTERVIEW_NORMAL)
                .withInt(EXTRA_INTERVIEW_ID, interviewId)
        }
    }
}