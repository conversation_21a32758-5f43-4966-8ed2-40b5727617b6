package com.bxkj.enterprise.mvp.presenter;


import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.api.EnterpriseApiConstants;
import com.bxkj.enterprise.mvp.contract.UpdatePositionStateContract;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.mvp.presenter
 * @Description: UpdatePositionState
 * @TODO: TODO
 * @date 2018/3/27
 */

public class UpdatePositionStatePresenter extends UpdatePositionStateContract.Presenter {

    private static final String TAG = UpdatePositionStatePresenter.class.getSimpleName();
    private BusinessApi mBusinessApi;

    @Inject
    public UpdatePositionStatePresenter(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    public void updatePositionState(int userId, int positionId, int position, int state) {
        mView.showLoading(state == EnterpriseApiConstants.POSITION_OFF_LINE ? "下线中" : "上线中");
        mBusinessApi.updatePositionState(userId, String.valueOf(positionId), state)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.updatePositionStateSuccess(state, position);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.hiddenLoading();
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        mView.hiddenLoading();
                    }
                });
    }
}
