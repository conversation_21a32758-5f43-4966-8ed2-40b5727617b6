package com.bxkj.enterprise.ui.activity.positionpreview

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.enterprise.data.PositionPreviewData
import com.bxkj.enterprise.data.WelfareItemData
import com.bxkj.enterprise.data.source.MyJobRepo
import com.bxkj.video.VideoType
import com.bxkj.video.data.VideoData
import com.bxkj.video.repository.OpenVideoRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @date 2024/11/20
 * <AUTHOR>
 */
class JobPreviewViewModel @Inject constructor(
  private val openVideoRepo: OpenVideoRepository,
  private val myJobRepo: MyJobRepo
) : BaseViewModel() {

  val jobDetails = MutableLiveData<PositionPreviewData>()
  val jobOptionText = MutableLiveData<String>()

  val loadJobDetailsSuccessEvent = MutableLiveData<VMEvent<Unit>>()
  val refreshJobAddressEvent = MutableLiveData<VMEvent<Array<String>>>()
  val hiddenMapEvent = MutableLiveData<VMEvent<Unit>>()
  val showWelfareListEvent = MutableLiveData<VMEvent<List<WelfareItemData>>>()
  val hiddenWelfareListEvent = MutableLiveData<VMEvent<Unit>>()
  val showRelateVideoEvent = MutableLiveData<VMEvent<List<VideoData>>>()
  val hiddenRelateVideoEvent = MutableLiveData<VMEvent<Unit>>()
  val showSwitchJobStateCommand = MutableLiveData<VMEvent<String>>()
  val updateJobStateSuccessCommand = MutableLiveData<VMEvent<Unit>>()
  val toEditJobCommand=MutableLiveData<VMEvent<Int>>()

  private var jobId: Int = 0

  fun start(jobId: Int) {
    this.jobId = jobId
    refreshJobInfo()
    refreshRelateVideos()
  }

  fun refreshJobInfo() {
    viewModelScope.launch {
      myJobRepo.getJobDetails(jobId)
        .handleResult({
          it?.let {
            loadJobDetailsSuccessEvent.value = VMEvent(Unit)
            jobDetails.value = it
            refreshJobOptionsText()
            refreshJobAddress()
            refreshWelfareList()
          }
        }, {
          showToast(it.errMsg)
        })
    }
  }

  fun refreshRelateVideos() {
    viewModelScope.launch {
      openVideoRepo.getInfoLinkVideos(getSelfUserID(), jobId, VideoType.VIDEO_TYPE_RECRUIT)
        .handleResult({
          it?.let {
            showRelateVideoEvent.value = VMEvent(it)
          } ?: let {
            hiddenWelfareListEvent.value = VMEvent(Unit)
          }
        }, {
          hiddenRelateVideoEvent.value = VMEvent(Unit)
        })
    }
  }

  fun switchJobState() {
    jobDetails.value?.let { jobInfo ->
      val targetState = if (jobInfo.state == 3) 4 else 3
      showLoading(if (targetState == 3) "上线中..." else "下线中...")
      viewModelScope.launch {
        myJobRepo.updateJobState(getSelfUserID(), jobId.toString(), targetState)
          .handleResult({
            updateJobStateSuccessCommand.value = VMEvent(Unit)
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    }
  }

  fun showSwitchJobStateTipsDialog() {
    jobDetails.value?.let {
      if (it.state == 3) { //3在线、4下线
        showSwitchJobStateCommand.value = VMEvent("确认下线所选职位？")
      } else {
        showSwitchJobStateCommand.value = VMEvent("确认上线所选职位？")
      }
    }
  }

  fun toEditJob(){
    jobDetails.value?.id?.let {
      toEditJobCommand.value=VMEvent(it)
    }
  }

  private fun refreshJobOptionsText() {
    jobDetails.value?.let {
      jobOptionText.value = if (it.state == 3) "下线" else "上线"
    }
  }

  private fun refreshJobAddress() {
    jobDetails.value?.let {
      if (it.com.coordinate.isNullOrEmpty()) {
        hiddenMapEvent.value = VMEvent(Unit)
      } else {
        refreshJobAddressEvent.value = VMEvent(it.com.coordinate.split(",").toTypedArray())
      }
    }
  }

  private fun refreshWelfareList() {
    jobDetails.value?.let {
      it.jobWelfareList?.let { list ->
        showWelfareListEvent.value = VMEvent(list)
      } ?: let {
        hiddenWelfareListEvent.value = VMEvent(Unit)
      }
    }
  }
}