package com.bxkj.enterprise.ui.fragment.inviterecord

import android.content.Context
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.enterprise.BR
import com.bxkj.enterprise.data.InviteRecordItemData

/**
 * @Project: ejrzp
 * @Package com.bxkj.enterprise.ui.fragment.inviterecord
 * @Description:
 * <AUTHOR>
 * @date 2019/8/13
 * @version V1.0
 */
class InviteRecordListAdapter constructor(context: Context,layoutId:Int) :
    SuperAdapter<InviteRecordItemData>(context, layoutId){
    override fun convert(holder: SuperViewHolder, viewType: Int, item: InviteRecordItemData?, position: Int) {
        item?.let {
            holder.bind(BR.inviteRecordItem,item)
        }
    }
}