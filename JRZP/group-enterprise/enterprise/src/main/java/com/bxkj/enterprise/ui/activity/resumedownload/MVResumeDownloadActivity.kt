package com.bxkj.enterprise.ui.activity.resumedownload

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.lifecycle.Observer
import com.therouter.router.Route
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.popup.OptionsPickerPopup
import com.bxkj.enterprise.R
import com.bxkj.enterprise.databinding.ActivityMvResumeDownloadBinding
import com.bxkj.enterprise.ui.activity.membercenter.MemberCenterNavigation
import com.bxkj.jrzp.user.mine.ui.membercenter.MemberCenterWebNavigation

/**
 *
 * @author: YangXin
 * @date: 2021/6/21
 */
@Route(path = ResumeDownloadNavigation.PATH)
class MVResumeDownloadActivity :
  BaseDBActivity<ActivityMvResumeDownloadBinding, MVResumeDownloadViewModel>(), OnClickListener {

  companion object {

    private const val TO_RECHARGE_CODE = 1

    @JvmStatic
    fun newIntent(context: Context, resumeId: Int): Intent {
      return Intent(context, MVResumeDownloadActivity::class.java)
        .apply {
          putExtra(ResumeDownloadNavigation.EXTRA_RESUME_ID, resumeId)
        }
    }
  }

  private var onlineJobPicker: OptionsPickerPopup? = null

  private val extraResumeId by lazy {
    intent.getIntExtra(ResumeDownloadNavigation.EXTRA_RESUME_ID,
      CommonApiConstants.NO_ID)
  }

  override fun getViewModelClass(): Class<MVResumeDownloadViewModel> =
    MVResumeDownloadViewModel::class.java

  override fun getLayoutId(): Int = R.layout.activity_mv_resume_download

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    viewModel.refresh(extraResumeId)
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.ll_job) {
        onlineJobPicker?.show()
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.downloadPaymentMethod.observe(this, Observer {
      viewModel.getDownloadConsumeByMethod(it)
    })

    viewModel.onlineJobList.observe(this, Observer {
      onlineJobPicker = OptionsPickerPopup(this)
        .setFirstOptions(it)
        .setOnConfirmClickListener { firstPosition, _, _ ->
          viewModel.setupSelectedJob(it[firstPosition])
        }
    })

    viewModel.resumeDownloadInsufficientBalanceEvent.observe(this, Observer {
      ActionDialog.Builder()
        .setContent(it)
        .setOnConfirmClickListener {
          MemberCenterNavigation.create().start()
        }.build().show(supportFragmentManager)
    })

    viewModel.resumeDownloadSuccessful.observe(this, Observer {
      showToast(getString(R.string.resume_download_success))
      setResult(RESULT_OK, intent)
      finish()
    })
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == TO_RECHARGE_CODE) {
      viewModel.refresh(extraResumeId)
    }
  }
}