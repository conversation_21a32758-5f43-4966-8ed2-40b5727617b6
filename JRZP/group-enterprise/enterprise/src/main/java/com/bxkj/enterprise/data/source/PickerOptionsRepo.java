package com.bxkj.enterprise.data.source;

import androidx.annotation.NonNull;

import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack;
import com.bxkj.common.base.mvvm.BaseRepo;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.enterprise.api.BusinessApi;
import com.bxkj.enterprise.data.source.datasource.PickerOptionsSource;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data.source
 * @Description:
 * @TODO: TODO
 * @date 2019/6/16
 */
public class PickerOptionsRepo extends BaseRepo implements PickerOptionsSource {

    private BusinessApi mBusinessApi;

    @Inject
    public PickerOptionsRepo(BusinessApi businessApi) {
        mBusinessApi = businessApi;
    }

    @Override
    public void getIndustryList(ResultDataCallBack<List<PickerOptionsData>> callBack) {
        mBusinessApi.getIndustryList()
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(@NonNull BaseResponse baseResponse) {
                        callBack.onSuccess((List<PickerOptionsData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(@NonNull RespondThrowable respondThrowable) {
                        callBack.onError(respondThrowable);
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    public void getNatureOfCompany(ResultDataCallBack<List<PickerOptionsData>> callBack) {
        mBusinessApi.getNatureOfCompany()
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(@NonNull BaseResponse baseResponse) {
                        callBack.onSuccess((List<PickerOptionsData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(@NonNull RespondThrowable respondThrowable) {
                        callBack.onError(respondThrowable);
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
