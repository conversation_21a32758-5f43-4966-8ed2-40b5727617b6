package com.bxkj.ecommon.util.map;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.util
 * @Description:
 * @TODO: TODO
 * @date 2018/4/26
 */

public class LngLat implements Parcelable {
    private double longitude;//经度
    private double latitude;//维度

    public LngLat() {
    }

    public LngLat(double longitude, double latitude) {
        this.longitude = longitude;
        this.latitude = latitude;
    }

    protected LngLat(Parcel in) {
        longitude = in.readDouble();
        latitude = in.readDouble();
    }

    public static final Creator<LngLat> CREATOR = new Creator<LngLat>() {
        @Override
        public LngLat createFromParcel(Parcel in) {
            return new LngLat(in);
        }

        @Override
        public LngLat[] newArray(int size) {
            return new LngLat[size];
        }
    };

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public String getString() {
        return longitude + "," + latitude;
    }

    @Override
    public String toString() {
        return "LngLat{" +
                "longitude=" + longitude +
                ", latitude=" + latitude +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeDouble(longitude);
        parcel.writeDouble(latitude);
    }
}
