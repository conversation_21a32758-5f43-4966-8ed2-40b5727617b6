package com.bxkj.ecommon.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;

import androidx.appcompat.widget.AppCompatEditText;

import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.bxkj.common.R;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.shop.util
 * @Description: 带清楚按钮的edittext
 * @TODO: TODO
 * @date 2018/3/2
 */

public class ClearEditText extends AppCompatEditText implements View.OnFocusChangeListener, TextWatcher {

    private Drawable mClearDrawable;
    private boolean hasFocus;
    private boolean isCustom = false;
    private boolean clearIconVisible = false;

    public ClearEditText(Context context) {
        this(context, null);
    }

    public ClearEditText(Context context, AttributeSet attrs) {
        this(context, attrs, R.attr.editTextStyle);
    }

    public ClearEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        init();
    }

    private void init() {
        setSingleLine(true);
        // 获取EditText的DrawableRight,假如没有设置使用默认的图片
//        mClearDrawable = getCompoundDrawables()[2];
        if (mClearDrawable == null) {
            mClearDrawable = getResources().getDrawable(
                    R.drawable.common_ic_delete);
        }
        mClearDrawable.setBounds(0, 0, mClearDrawable.getIntrinsicWidth(),
                mClearDrawable.getIntrinsicHeight());
        // 默认设置隐藏图标
        setClearIconVisible(clearIconVisible);
        // 设置焦点改变的监听
        setOnFocusChangeListener(this);
        // 设置输入框里面内容发生改变的监听
        addTextChangedListener(this);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_UP) {
            if (getCompoundDrawables()[2] != null) {
                boolean touchable = event.getX() > (getWidth() - getTotalPaddingRight())
                        && (event.getX() < ((getWidth() - getPaddingRight())));
                if (touchable) {
                    if (!isCustom) {
                        this.setText("");
                        if (OnCustomIconClickListener != null) {
                            OnCustomIconClickListener.onCustomIconClick();
                        }
                    }
                }
            }
        }
        return super.onTouchEvent(event);
    }

    //设置文字并将光标移动到最后
    public void setTextAndSelectedLastIndex(CharSequence charSequence) {
        setText(charSequence);
        setSelection(getText().length());
    }

    /**
     * 设置清除图标的显示与隐藏，调用setCompoundDrawables为EditText绘制上去
     */
    protected void setClearIconVisible(boolean visible) {
        Drawable right = visible ? mClearDrawable : null;
        setCompoundDrawables(getCompoundDrawables()[0],
                getCompoundDrawables()[1], right, getCompoundDrawables()[3]);
    }

    private OnCustomIconClickListener OnCustomIconClickListener;

    public interface OnCustomIconClickListener {
        void onCustomIconClick();
    }

    public void setOnCustomIconClickListener(OnCustomIconClickListener onCustomIconClickListener) {
        this.OnCustomIconClickListener = onCustomIconClickListener;
    }


    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    /**
     * 当输入框里面内容发生变化的时候回调的方法
     */
    @Override
    public void onTextChanged(CharSequence s, int start, int count, int after) {
        if (hasFocus) {
            setClearIconVisible(s.length() > 0);
        }
    }

    @Override
    public void afterTextChanged(Editable s) {

    }

    @Override
    public void setText(CharSequence text, BufferType type) {
        super.setText(text, type);
        if (text != null) {
            setSelection(text.length());
        }
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        this.hasFocus = hasFocus;
        if (onOverrideFocusChangeListener != null) {
            onOverrideFocusChangeListener.onFocusChange(v, hasFocus);
        }
        if (hasFocus) {
            setClearIconVisible(getText().length() > 0);
        } else {
            setClearIconVisible(false);
        }
    }

    private OnOverrideFocusChangeListener onOverrideFocusChangeListener;

    public void setOnOverrideFocusChangeListener(OnOverrideFocusChangeListener onOverrideFocusChangeListener) {
        this.onOverrideFocusChangeListener = onOverrideFocusChangeListener;
    }

    public interface OnOverrideFocusChangeListener {
        void onFocusChange(View v, boolean hasFocus);
    }
}
