package com.bxkj.ecommon.mvp.persenter;


import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.ecommon.network.ECommonApi;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.ecommon.mvp.contract.GetAreaListContract;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.presenter
 * @Description: GetAreaList
 * @TODO: TODO
 * @date 2018/3/27
 */

public class GetAreaListPresenter extends GetAreaListContract.Presenter {

    private static final String TAG = GetAreaListPresenter.class.getSimpleName();
    private ECommonApi mCommonApi;


    @Inject
    public GetAreaListPresenter(ECommonApi personalApi) {
        mCommonApi = personalApi;
    }

    @Override
    public void getAreaList(final int type, int parentId) {
        mCommonApi.getAreaList(type, parentId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        List<AreaOptionsData> areaOptionsDataList = (List<AreaOptionsData>) baseResponse.getDataList();
                        for (AreaOptionsData areaOptionsData : areaOptionsDataList) {
                            areaOptionsData.setType(type);
                        }
                        mView.getAreaListSuccess(type, areaOptionsDataList);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
