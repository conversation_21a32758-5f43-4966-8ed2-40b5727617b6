package com.bxkj.ecommon.base;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;
import com.bxkj.ecommon.R;
import com.gyf.immersionbar.ImmersionBar;
import dagger.android.support.AndroidSupportInjection;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.base
 * @Description: fragment基类
 * @TODO: TODO
 * @date 2019/4/3
 */
public abstract class EBaseDBFragment<DB extends ViewDataBinding, VM extends BaseViewModel>
    extends Fragment {

  @Inject
  ViewModelFactory mViewModelFactory;

  private DB mViewDataBinding;
  private VM mViewModel;

  private FragmentActivity mParentActivity;

  private ImmersionBar mStatusBarManager;

  private CompositeDisposable mCompositeDisposable;

  @Override
  public void onAttach(Context context) {
    AndroidSupportInjection.inject(this);
    super.onAttach(context);
    if (context instanceof FragmentActivity) {
      mParentActivity = (FragmentActivity) context;
    }
  }

  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
      @Nullable Bundle savedInstanceState) {
    mViewDataBinding = DataBindingUtil.inflate(inflater, getLayoutId(), container, false);
    mViewDataBinding.setLifecycleOwner(this);
    mViewModel = new ViewModelProvider(this, mViewModelFactory).get(getViewModelClass());
    mStatusBarManager = ImmersionBar.with(this, true).navigationBarColor(R.color.common_white);
    initViewModel(mViewModel);
    initPage();
    return mViewDataBinding.getRoot();
  }

  protected abstract void initPage();

  private void initViewModel(VM viewModel) {
    if (getParentActivity() instanceof EBaseDBActivity) {
      ((EBaseDBActivity) getParentActivity()).initViewModel(viewModel);
    }
  }

  protected void addDisposable(Disposable disposable) {
    if (mCompositeDisposable == null) {
      mCompositeDisposable = new CompositeDisposable();
    }
    mCompositeDisposable.add(disposable);
  }

  protected abstract Class<VM> getViewModelClass();

  protected abstract int getLayoutId();

  protected DB getDataBinding() {
    return mViewDataBinding;
  }

  protected VM getViewModel() {
    return mViewModel;
  }

  protected Activity getParentActivity() {
    return mParentActivity;
  }

  protected ImmersionBar getStatusBarManager() {
    return mStatusBarManager;
  }

  protected void showToast(String msg) {
    ((EBaseDBActivity) getParentActivity()).showToast(msg);
  }

  protected int getMUserID() {
    return ((EBaseNActivity) getParentActivity()).getLocalUserId();
  }

  protected int getMColor(@ColorRes int colorId) {
    return ContextCompat.getColor(getParentActivity(), colorId);
  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    if (mCompositeDisposable != null) {
      mCompositeDisposable.clear();
    }
  }
}

