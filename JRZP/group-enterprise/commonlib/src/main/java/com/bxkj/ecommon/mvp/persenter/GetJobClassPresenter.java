package com.bxkj.ecommon.mvp.persenter;


import com.bxkj.common.data.JobTypeData;
import com.bxkj.ecommon.mvp.contract.GetJobClassContract;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.ecommon.network.ECommonApi;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.network.exception.RespondThrowable;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.mvp.persenter
 * @Description: GetJobClass
 * @TODO: TODO
 * @date 2018/3/27
 */

public class GetJobClassPresenter extends GetJobClassContract.Presenter {

    private static final String TAG = GetJobClassPresenter.class.getSimpleName();
    private ECommonApi mAccountApi;

    @Inject
    public GetJobClassPresenter(ECommonApi accountApi) {
        mAccountApi = accountApi;
    }

    @Override
    public void getJobClassList(int type, final int firstClassId) {
        mAccountApi.getJobClassList(type, firstClassId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        if (firstClassId != 0) {
                            mView.getJobSecondClassListSuccess((List<JobTypeData>) baseResponse.getDataList());
                        } else {
                            mView.getJobFirstClassListSuccess((List<JobTypeData>) baseResponse.getDataList());
                        }
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
