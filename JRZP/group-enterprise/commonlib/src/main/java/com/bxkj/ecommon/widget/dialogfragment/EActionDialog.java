package com.bxkj.ecommon.widget.dialogfragment;

import android.annotation.SuppressLint;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.IntDef;
import androidx.fragment.app.FragmentManager;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.ecommon.R;
import com.bxkj.ecommon.constants.ECommonApiConstants;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget.dialog
 * @Description: 确认取消dialog
 * @TODO: TODO
 * @date 2018/4/16
 */

@SuppressLint("ValidFragment")
public class EActionDialog extends BaseDialogFragment implements View.OnClickListener {

  public static final String TAG = "ActionDialog";

  public static final int BASIC_TYPE = 1;
  public static final int EDIT_TYPE = 2;

  @IntDef({ BASIC_TYPE, EDIT_TYPE })
  @Retention(RetentionPolicy.SOURCE)
  public @interface ActionType {
  }

  private TextView tvTitle, tvContent, tvCancel;
  private TextView tvConfirm;
  private EditText etContent;

  private int mActionType;
  private String mTitle;
  private int mTitleIcon;
  private String mContent;
  private String mHint;
  private int mEditHeight;
  private String mConfirmText;
  private int mConfirmIcon;
  private int mContentGravity;

  private OnConfirmClickListener mOnConfirmClickListener;
  private OnCancelClickListener mOnCancelClickListener;

  public static class Builder {
    private int actionType;
    private boolean cancelable = true;
    private String title;
    private int titleIcon;
    private String content;
    private String hint;
    private int editHeight;
    private String confirmText = "确定";
    private int confirmIcon;
    private int contentGravity;
    private OnConfirmClickListener onConfirmClickListener;
    private OnCancelClickListener onCancelClickListener;

    public Builder setActionType(@ActionType int actionType) {
      this.actionType = actionType;
      return this;
    }

    public Builder setCancelable(boolean cancelable) {
      this.cancelable = cancelable;
      return this;
    }

    public Builder setTitle(String title) {
      this.title = title;
      return this;
    }

    public Builder setTitleIcon(@DrawableRes int titleIcon) {
      this.titleIcon = titleIcon;
      return this;
    }

    public Builder setContent(String content) {
      this.content = content;
      return this;
    }

    public Builder setContentGravity(int gravity) {
      this.contentGravity = gravity;
      return this;
    }

    public Builder setHint(String hint) {
      this.hint = hint;
      return this;
    }

    public Builder setEditHeight(int height) {
      this.editHeight = height;
      return this;
    }

    public Builder setConfirmText(String text) {
      this.confirmText = text;
      return this;
    }

    public Builder setConfirmIcon(@DrawableRes int icon) {
      this.confirmIcon = icon;
      return this;
    }

    public Builder setOnConfirmClickListener(OnConfirmClickListener onConfirmClickListener) {
      this.onConfirmClickListener = onConfirmClickListener;
      return this;
    }

    public Builder setOnCancelClickListener(OnCancelClickListener onCancelClickListener) {
      this.onCancelClickListener = onCancelClickListener;
      return this;
    }

    public EActionDialog build() {
      return new EActionDialog(this);
    }
  }

  @SuppressLint("ValidFragment")
  private EActionDialog(Builder builder) {
    mActionType = builder.actionType;
    mTitle = builder.title;
    mTitleIcon = builder.titleIcon;
    mContent = builder.content;
    mHint = builder.hint;
    mEditHeight = builder.editHeight;
    mConfirmText = builder.confirmText;
    mConfirmIcon = builder.confirmIcon;
    mContentGravity = builder.contentGravity;
    mOnConfirmClickListener = builder.onConfirmClickListener;
    mOnCancelClickListener = builder.onCancelClickListener;
    setCancelable(builder.cancelable);
  }

  @Override
  protected int getRootViewId() {
    return R.layout.enterprise_dialog_action;
  }

  @Override
  public void show(FragmentManager manager, String tag) {
    //        super.show(manager, tag);
    manager.beginTransaction().add(this, tag).commitAllowingStateLoss();
  }

  @Override
  protected void initView() {
    tvTitle = getRootView().findViewById(R.id.tv_dialog_title);
    tvContent = getRootView().findViewById(R.id.tv_dialog_content);
    etContent = getRootView().findViewById(R.id.et_content);
    tvCancel = getRootView().findViewById(R.id.tv_dialog_cancel);
    tvConfirm = getRootView().findViewById(R.id.tv_dialog_confirm);
    if (mContentGravity != 0) {
      tvContent.setGravity(mContentGravity);
    }

    if (mConfirmIcon != 0) {
      if (getContext() != null) {
        tvConfirm.setCompoundDrawablesRelativeWithIntrinsicBounds(mConfirmIcon, 0, 0, 0);
        tvConfirm.setCompoundDrawablePadding(DensityUtils.dp2px(getContext(), 4));
      }
    }
    tvConfirm.setText(mConfirmText);

    if (mActionType == EDIT_TYPE) {
      etContent.setVisibility(View.VISIBLE);
      etContent.setHint(CheckUtils.isNullOrEmpty(mHint) ? ECommonApiConstants.NO_TEXT : mHint);
      if (mEditHeight != 0) {
        ViewGroup.LayoutParams layoutParams = etContent.getLayoutParams();
        layoutParams.height = mEditHeight;
        etContent.setLayoutParams(layoutParams);
        etContent.setGravity(Gravity.TOP);
      }
    }

    if (!CheckUtils.isNullOrEmpty(mTitle)) {
      tvTitle.setVisibility(View.VISIBLE);
      tvTitle.setText(mTitle);
    }

    if (mTitleIcon != 0) {
      if (getContext() != null) {
        tvTitle.setCompoundDrawablesRelativeWithIntrinsicBounds(mTitleIcon, 0,
            0, 0);
        tvTitle.setCompoundDrawablePadding(DensityUtils.dp2px(getContext(), 4));
        tvTitle.setGravity(Gravity.CENTER);
      }
    }

    if (!CheckUtils.isNullOrEmpty(mContent)) {
      tvContent.setVisibility(View.VISIBLE);
      tvContent.setText(mContent);
    }

    tvCancel.setOnClickListener(this);
    getRootView().findViewById(R.id.fl_confirm).setOnClickListener(this);
  }

  @Override
  public void onClick(View view) {
    if (view.getId() == R.id.tv_dialog_cancel) {
      dismiss();
      if (mOnCancelClickListener != null) {
        mOnCancelClickListener.onCancelClick();
      }
    } else {
      if (mOnConfirmClickListener != null) {
        mOnConfirmClickListener.onConfirmClick(this,
            mActionType == EDIT_TYPE ? etContent.getText().toString()
                : ECommonApiConstants.NO_TEXT);
      }
    }
  }

  public EActionDialog setTitle(String title) {
    mTitle = title;
    return this;
  }

  public EActionDialog setContent(String content) {
    mContent = content;
    return this;
  }

  public EActionDialog setOnConfirmClickListener(OnConfirmClickListener onConfirmClickListener) {
    mOnConfirmClickListener = onConfirmClickListener;
    return this;
  }

  public interface OnConfirmClickListener {
    void onConfirmClick(EActionDialog actionDialog, String inputText);
  }

  public interface OnCancelClickListener {
    void onCancelClick();
  }

  public void show(FragmentManager manager) {
    this.show(manager, TAG);
  }
}

