[versions]
minSdkVersion = "22"
targetSdkVersion = "31"
compileSdkVersion = "35"
versionCode = "191"
versionName = "3.8.1"

agcp = "1.9.1.300"
agp = "8.4.2"
kotlin = "2.1.0"

appcompat = "1.6.1"
material = "1.12.0"
constraintlayout = "2.1.4"
pagingRuntimeKtx = "3.1.1"
flexbox = "2.0.1"
recyclerview = "1.3.2"
compose = "1.6.4"
compose-material = "1.3.1"
compose-activity = "1.7.2"

multidex = "2.0.1"
lifecycle-ktx = "2.8.7"
core-ktx = "1.15.0"
android-ktx = "1.10.1"

picture_library = "v3.11.2"
retrofit = "2.9.0"
okhttp = "4.9.2"
quicklogin = "3.5.1"

rxjava = "2.1.1"
rxrelay = "2.1.1"
rxbinding = "2.2.0"

dagger = "2.56"

# Tools
circleimageview = "3.1.0"
skeleton = "1.1.2"
shimmerlayout = "2.1.0"
consecutiveScroller = "4.6.4"
htmlText = "1.0"
banner = "2.2.3"
smartrefreshlayout = "1.1.0-alpha-27"
pickerview = "4.1.9"
magicindicator = "1.6.0"
photoView = "2.3.0"
spotlight = "2.0.5"
switchbutton = "1.0.4"
gifDrawable = "1.2.22"
tickerView = "2.0.2"
expandableTextView = "1.0.3"
pagestatelayout = "1.0.6"
com-github-getActivity-Toaster = "12.6"
easyFloat = "1.3.4"
balloon = "1.5.4"
simpleRatingBar = "1.5.0"
dataTimePicker = "0.6.3"
mpAndroidChart = "v3.1.0"
dialogx = "0.0.49"

vlionCore = "6.00.03"
xlog = "1.10.1"
exoplayer = "2.11.3"
zbar = "1.3.8"
jiaozivideoplayer = "7.7.0"
richEditor = "1.0.5"
tinyPinyin = "2.0.3.RELEASE"
tinypinyin-lexicons-android-cncity = "2.0.3"
github-PanelSwitchHelper = "v1.5.4"
htmlSpanner = "0.4"
therouter = "1.3.0-rc4"
cv4j = "0.1.2"
glide = "4.16.0"
immersionbar = "3.2.2"
xxpermissions = "21.3"
room = "2.6.1"
leakcanary = "2.14"
camera = "1.3.4"

baiduMapLocation = "9.6.4"
baiduMapComponent = "7.6.3"

tpns = "1.4.4.3-release"
crashreport = "4.1.9.3"

wechatSdkAndroidWithoutMta = "6.8.0"

[plugins]
kotlin-compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }

[libraries]
agcp = { module = "com.huawei.agconnect:agcp", version.ref = "agcp" }
agp = { module = "com.android.tools.build:gradle", version.ref = "agp" }
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }

appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
paging-runtime-ktx = { module = "androidx.paging:paging-runtime-ktx", version.ref = "pagingRuntimeKtx" }
flexbox = { module = "com.google.android:flexbox", version.ref = "flexbox" }
quicklogin = { module = "io.github.yidun:quicklogin", version.ref = "quicklogin" }
recyclerview = { group = "androidx.recyclerview", name = "recyclerview", version.ref = "recyclerview" }
compose-ui = { module = "androidx.compose.ui:ui", version.ref = "compose" }
compose-activity = { module = "androidx.activity:activity-compose", version.ref = "compose-activity" }
compose-material = { module = "androidx.compose.material3:material3", version.ref = "compose-material" }
compose-material3-icons-extended = { module = "androidx.compose.material:material-icons-extended", version.ref = "compose" }
compose-ui-preview = { module = "androidx.compose.ui:ui-tooling-preview", version.ref = "compose" }

multidex = { group = "androidx.multidex", name = "multidex", version.ref = "multidex" }
lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle-ktx" }
android-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "core-ktx" }
activity-ktx = { group = "androidx.activity", name = "activity-ktx", version.ref = "android-ktx" }

picture_selector = { module = "io.github.lucksiege:pictureselector", version.ref = "picture_library" }
picture_compress = { module = "io.github.lucksiege:compress", version.ref = "picture_library" }
picture_ucrop = { module = "io.github.lucksiege:ucrop", version.ref = "picture_library" }
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-adapter-rxjava2 = { group = "com.squareup.retrofit2", name = "adapter-rxjava2", version.ref = "retrofit" }
retrofit-converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }

okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
okhttp-interceptor-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }

rxjava = { group = "io.reactivex.rxjava2", name = "rxjava", version.ref = "rxjava" }
rxjava-rxandroid = { group = "io.reactivex.rxjava2", name = "rxandroid", version.ref = "rxjava" }
rxjava-rxrelay = { group = "com.jakewharton.rxrelay2", name = "rxrelay", version.ref = "rxrelay" }
rxjava-rxbinding = { group = "com.jakewharton.rxbinding2", name = "rxbinding", version.ref = "rxbinding" }

dagger = { group = "com.google.dagger", name = "dagger", version.ref = "dagger" }
dagger-complier = { group = "com.google.dagger", name = "dagger-compiler", version.ref = "dagger" }
dagger-android-support = { group = "com.google.dagger", name = "dagger-android-support", version.ref = "dagger" }
dagger-android-processor = { group = "com.google.dagger", name = "dagger-android-processor", version.ref = "dagger" }

# Tools
circleimageview = { module = "de.hdodenhof:circleimageview", version.ref = "circleimageview" }
skeleton = { module = "com.ethanhua:skeleton", version.ref = "skeleton" }
shimmerlayout = { module = "io.supercharge:shimmerlayout", version.ref = "shimmerlayout" }
consecutiveScroller = { module = "com.github.donkingliang:ConsecutiveScroller", version.ref = "consecutiveScroller" }
htmlText = { module = "com.github.wangchenyan:html-text", version.ref = "htmlText" }
banner = { module = "io.github.youth5201314:banner", version.ref = "banner" }
smartrefreshlayout = { module = "com.scwang.smartrefresh:SmartRefreshLayout", version.ref = "smartrefreshlayout" }
smartrefreshlayout-header = { module = "com.scwang.smartrefresh:SmartRefreshHeader", version.ref = "smartrefreshlayout" }
pickerview = { module = "com.contrarywind:Android-PickerView", version.ref = "pickerview" }
magicindicator = { module = "com.github.hackware1993:MagicIndicator", version.ref = "magicindicator" }
photoView = { module = "com.github.chrisbanes:PhotoView", version.ref = "photoView" }
spotlight = { module = "com.github.takusemba:spotlight", version.ref = "spotlight" }
switchbutton = { module = "com.github.iielse:switchbutton", version.ref = "switchbutton" }
gifDrawable = { module = "pl.droidsonroids.gif:android-gif-drawable", version.ref = "gifDrawable" }
tickerView = { module = "com.robinhood.ticker:ticker", version.ref = "tickerView" }
expandableTextView = { module = "cn.carbs.android:ExpandableTextView", version.ref = "expandableTextView" }
pagestatelayout = { module = "com.sanjindev:pagestatelayout", version.ref = "pagestatelayout" }
toaster = { module = "com.github.getActivity:Toaster", version.ref = "com-github-getActivity-Toaster" }
easyFloat = { module = "com.github.princekin-f:EasyFloat", version.ref = "easyFloat" }
balloon = { module = "com.github.skydoves:balloon", version.ref = "balloon" }
simpleRatingBar = { module = "com.github.ome450901:SimpleRatingBar", version.ref = "simpleRatingBar" }
dateTimePicker = { module = "com.github.loper7:DateTimePicker", version.ref = "dataTimePicker" }
mpAndroidChart = { module = "com.github.PhilJay:MPAndroidChart", version.ref = "mpAndroidChart" }
dialogx={module="com.kongzue.dialogx:DialogX",version.ref="dialogx"}

vlion-core = { module = "cn.vlion.inland:vlion-core", version.ref = "vlionCore" }
xlog = { module = "com.elvishew:xlog", version.ref = "xlog" }
exoplayer = { module = "com.google.android.exoplayer:exoplayer", version.ref = "exoplayer" }
zbar = { module = "com.github.bingoogolapple.BGAQRCode-Android:zbar", version.ref = "zbar" }
jiaozivideoplayer = { module = "cn.jzvd:jiaozivideoplayer", version.ref = "jiaozivideoplayer" }
richEditor = { module = "com.github.RexSuper:RichEditor", version.ref = "richEditor" }
tinyPinyin = { module = "io.github.biezhi:TinyPinyin", version.ref = "tinyPinyin" }
tinypinyin-lexicons-android-cncity = { module = "com.github.promeg:tinypinyin-lexicons-android-cncity", version.ref = "tinypinyin-lexicons-android-cncity" }
panelSwitchHelper = { module = "com.github.DSAppTeam:PanelSwitchHelper", version.ref = "github-PanelSwitchHelper" }
htmlSpanner = { module = "com.github.NightWhistler:HtmlSpanner", version.ref = "htmlSpanner" }
therouter-api = { module = "cn.therouter:router", version.ref = "therouter" }
therouter-apt = { module = "cn.therouter:apt", version.ref = "therouter" }
cv4j = { module = "com.cv4j:cv4j", version.ref = "cv4j" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
glide-integration-okhttp3 = { module = "com.github.bumptech.glide:okhttp3-integration", version.ref = "glide" }
glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
immersionbar = { module = "com.geyifeng.immersionbar:immersionbar", version.ref = "immersionbar" }
xxpermissions = { module = "com.github.getActivity:XXPermissions", version.ref = "xxpermissions" }
room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }
room-rxjava2 = { module = "androidx.room:room-rxjava2", version.ref = "room" }
leakcanary = { module = "com.squareup.leakcanary:leakcanary-android", version.ref = "leakcanary" }
camera-core = { module = "androidx.camera:camera-core", version.ref = "camera" }
camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "camera" }
camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "camera" }
camera-view = { module = "androidx.camera:camera-view", version.ref = "camera" }
camera-video = { module = "androidx.camera:camera-video", version.ref = "camera" }

#========================= 服务商 =========================#

# 百度地图
baiduMapSDK-LocationAll = { module = "com.baidu.lbsyun:BaiduMapSDK_Location_All", version.ref = "baiduMapLocation" }
baiduMapSDK-Util = { module = "com.baidu.lbsyun:BaiduMapSDK_Util", version.ref = "baiduMapComponent" }
baiduMapSDK-Search = { module = "com.baidu.lbsyun:BaiduMapSDK_Search", version.ref = "baiduMapComponent" }
baiduMapSDK-Map = { module = "com.baidu.lbsyun:BaiduMapSDK_Map", version.ref = "baiduMapComponent" }

# Tpns推送
tpns = { module = "com.tencent.tpns:tpns", version.ref = "tpns" }
tpns-vivo = { module = "com.tencent.tpns:vivo", version.ref = "tpns" }
tpns-xiaomi = { module = "com.tencent.tpns:xiaomi", version.ref = "tpns" }
tpns-huawei = { module = "com.tencent.tpns:huawei", version.ref = "tpns" }
tpns-oppo = { module = "com.tencent.tpns:oppo", version.ref = "tpns" }
# Bugly
carshreport = { module = "com.tencent.bugly:crashreport", version.ref = "crashreport" }

# 微信
wechat-sdk-android-without-mta = { module = "com.tencent.mm.opensdk:wechat-sdk-android-without-mta", version.ref = "wechatSdkAndroidWithoutMta" }
