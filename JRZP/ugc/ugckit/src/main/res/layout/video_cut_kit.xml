<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.tencent.qcloud.ugckit.component.slider.VideoCutView
        android:id="@+id/video_edit_view"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="20dp" />

    <TextView
        android:id="@+id/tv_choose_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/video_edit_view"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="10dp"
        android:textColor="@color/white" />

    <ImageView
        android:id="@+id/iv_rotate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/video_edit_view"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="10dp"
        android:background="@null"
        android:src="?attr/editerRotateIcon"
        android:text="rotate" />
</RelativeLayout>