<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginRight="5dp"
    android:orientation="vertical">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/filter_image"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_centerHorizontal="true"
        android:background="@color/transparent" />

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/filter_image_tint"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_centerHorizontal="true"
        android:background="@drawable/ic_effect5"
        android:visibility="invisible" />

    <TextView
        android:textSize="12dp"
        android:id="@+id/filter_tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/filter_image"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="5dp"
        android:textColor="@color/white" />

</RelativeLayout>