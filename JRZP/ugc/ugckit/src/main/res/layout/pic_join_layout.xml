<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0d0d0d"
    android:orientation="vertical">

    <com.tencent.qcloud.ugckit.component.TitleBarLayout
        android:id="@+id/titleBar_layout"
        android:layout_width="match_parent"
        android:layout_height="55dp" />

    <com.tencent.qcloud.ugckit.module.cut.VideoPlayLayout
        android:id="@+id/picture_play_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.tencent.qcloud.ugckit.module.picturetransition.PictureTransitionLayout
        android:id="@+id/picture_transition_layout"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="10dp" />

</LinearLayout>