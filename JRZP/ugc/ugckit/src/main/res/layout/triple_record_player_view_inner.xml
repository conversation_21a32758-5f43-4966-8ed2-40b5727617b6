<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:parentTag="LinearLayout"
    android:orientation="vertical" android:layout_width="match_parent"
    android:background="#000000"
    android:layout_height="match_parent">
    <com.tencent.qcloud.ugckit.module.mixrecord.MixRecordPlayerView
        android:id="@+id/triple_first"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        />
    <com.tencent.qcloud.ugckit.module.mixrecord.MixRecordPlayerView
        android:id="@+id/triple_second"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        />
    <com.tencent.qcloud.ugckit.module.mixrecord.MixRecordPlayerView
        android:id="@+id/triple_third"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        />
</LinearLayout>