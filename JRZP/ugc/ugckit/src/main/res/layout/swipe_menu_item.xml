<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="100dp"
    android:layout_height="100dp"
    android:layout_marginRight="6dp"
    android:descendantFocusability="beforeDescendants"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_alignParentBottom="true"
        android:layout_centerVertical="true"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignRight="@+id/iv_icon"
        android:layout_marginBottom="10dp"
        android:layout_marginRight="5dp"
        android:text="0:0"
        android:textColor="@color/white"
        android:textSize="@dimen/h4" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentRight="true"
        android:scaleType="centerCrop"
        android:src="?attr/pickerDelIcon" />
</RelativeLayout>