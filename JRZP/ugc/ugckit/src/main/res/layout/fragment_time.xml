<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="220dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="10dp"
        android:background="@color/editer_bottom"
        android:gravity="bottom"
        android:orientation="horizontal"
        android:paddingBottom="18dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp">

            <ImageView
                android:id="@+id/time_tv_cancel"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:background="@color/transparent" />

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/time_tv_cancel_select"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:background="@drawable/ic_effect5"
                android:visibility="invisible" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/time_tv_cancel"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:text="@string/none"
                android:textColor="@color/white"
                android:textSize="10dp" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp">

            <ImageView
                android:id="@+id/time_tv_speed"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:background="@color/transparent" />

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/time_tv_speed_select"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:background="@drawable/ic_effect5"
                android:visibility="invisible" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/time_tv_speed"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:text="@string/slow_motion"
                android:textColor="@color/white"
                android:textSize="10dp" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/layout_repeat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="20dp">

            <ImageView
                android:id="@+id/time_tv_repeat"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:background="@color/transparent" />

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/time_tv_repeat_select"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:background="@drawable/ic_effect5"
                android:visibility="invisible" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/time_tv_repeat"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:text="@string/repeate"
                android:textColor="@color/white"
                android:textSize="10dp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/layout_reverse"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/time_tv_reverse"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:background="@color/transparent" />

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/time_tv_reverse_select"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerHorizontal="true"
                android:background="@drawable/ic_effect5"
                android:visibility="invisible" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/time_tv_reverse"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:text="@string/reverse"
                android:textColor="@color/white"
                android:textSize="10dp" />
        </RelativeLayout>
    </LinearLayout>

</RelativeLayout>