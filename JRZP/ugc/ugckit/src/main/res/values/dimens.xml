<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <!-- umeng -->

    <dimen name="activity_horizontal_margin">16dp</dimen>

    <dimen name="title">18sp</dimen>

    <!--大小-->
    <!--h1-->
    <!--h2-->
    <!--h3-->
    <!--h4-->
    <dimen name="h4">12sp</dimen>
    <!--h5-->
    <dimen name="h5">13sp</dimen>
    <!--h6-->
    <!--h7-->
    <!--h8-->
    <dimen name="h8">16sp</dimen>
    <!--h9-->
    <!--h10-->
    <!--h11-->

    <!-- AVSDK *******************************************************************************************************************************-->

    <!-- 视频聊天中小窗口移动时的阈值 -->

    <dimen name="ugc_item_thumb_height">60dp</dimen>
    <dimen name="ugc_cut_margin">10dp</dimen>
    <!-- AVSDK *******************************************************************************************************************************-->

    <!-- UGC*******************************************************************************************************************************-->
    <dimen name="ugc_aspect_width">50dp</dimen>
    <dimen name="ugc_aspect_divider">18dp</dimen>
    <dimen name="ugc_progress_cursor">2dp</dimen>
    <dimen name="ugc_progress_min_pos">2dp</dimen>
    <dimen name="ugc_progress_divider">1dp</dimen>

    <!-- 视频进度条 -->
    <dimen name="video_progress_height">50dp</dimen>
    <dimen name="video_thumbnail_width">50dp</dimen>
    <!-- UGC*******************************************************************************************************************************-->
</resources>
