plugins {
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
  id 'pins-module'
  id 'kotlin-kapt'
  id 'kotlin-parcelize'
  id 'com.google.devtools.ksp'
  alias(libs.plugins.kotlin.compose.compiler)
}

android {

  namespace "com.bxkj.personal"

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()
    versionCode libs.versions.versionCode.get().toInteger()
    versionName libs.versions.versionName.get()
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    vectorDrawables {
      useSupportLibrary true
    }
  }

  buildFeatures {
    dataBinding = true
    compose = true
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }

  lintOptions {
    abortOnError false
  }

  resourcePrefix "c_"

  kotlinOptions {
    jvmTarget = '17'
  }

  packagingOptions {
    resources {
      excludes += '/META-INF/{AL2.0,LGPL2.1}'
    }
  }
}

dependencies {
  implementation fileTree(dir: 'libs', include: ['*.jar'])

  ksp libs.therouter.apt
  ksp libs.dagger.complier
  ksp libs.dagger.android.processor
  ksp libs.room.compiler

  implementation project(":group-support:share")
  implementation project(":group-support:chat")
  implementation project(":group-support:comment")
  implementation project(":group-support:feedback")
  implementation project(":group-support:upload")
  implementation project(':group-support:feature')
  implementation project(":group-support:scan")
  implementation project(":group-support:db")
  implementation project(":group-business:live")

  includeApi(':accountmodule')
  includeApi(":group-business:video")
  includeApi(":group-business:learning")
  includeApi(":group-business:user")
  implementation project(':group-support:yd_login')

  includeApi(":group-enterprise:enterprise")
  implementation project(":lib-common")
}
