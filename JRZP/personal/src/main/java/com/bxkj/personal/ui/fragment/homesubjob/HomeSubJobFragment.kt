package com.bxkj.personal.ui.fragment.homesubjob

import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.baidu.location.BDLocation
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.databinding.IncludeMvvmRefreshLayoutBinding
import com.bxkj.common.util.location.LocationManager
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory
import com.bxkj.personal.R
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2
import com.bxkj.personal.ui.fragment.home.GovRecruitmentFragment

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/3/14
 * @version: V1.0
 */
class HomeSubJobFragment : BaseDBFragment<IncludeMvvmRefreshLayoutBinding, HomeSubJobViewModel>() {

  companion object {
    fun newInstance(): Fragment {
      return HomeSubJobFragment()
    }
  }

  override fun getViewModelClass(): Class<HomeSubJobViewModel> = HomeSubJobViewModel::class.java

  override fun getLayoutId(): Int = R.layout.include_mvvm_refresh_layout

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.listViewModel = viewModel.jobListViewModel

    setupJobListAdapter()

    subscribeRefreshPageCommand()
  }

  override fun lazyLoadData() {
    super.lazyLoadData()
    viewModel.jobListViewModel.refresh()
  }

  private fun subscribeViewModelCommand() {
    viewModel.refreshLocationCommand.observe(this, Observer {
      getLocation(it)
    })
  }

  private fun getLocation(showLoading: Boolean = true) {
    if (showLoading) {
      viewModel.showLoadingLayout()
    }
    LocationManager.getLocationInfo(parentActivity,
      getString(R.string.permission_tips_title),
      getString(R.string.home_location_permission_desc),
      object : LocationManager.OnLocationListener {
        override fun onSuccess(location: BDLocation) {
          viewModel.getNearbyJobByLocation(location)
        }

        override fun onFailed() {
          viewModel.jobListViewModel.showPageStatus(PageStatusConfigFactory
            .newErrorConfig()
            .setText(getString(R.string.news_no_location_permission_tips))
            .setBtnText(getString(R.string.news_open_location_btn_text))
            .setOnButtonClickListener {
              getLocation()
            })
        }
      })
  }

  private fun subscribeRefreshPageCommand() {
    (parentFragment as GovRecruitmentFragment).viewModel.refreshJobPageCommand.observe(this, Observer {
      viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).scrollToPosition(0)
      viewModel.jobListViewModel.refresh(true)
    })
  }

  private fun setupJobListAdapter() {
    val jobListAdapter = object :
      SimpleDBListAdapter<com.bxkj.jrzp.user.data.JobData>(parentActivity,
        R.layout.personal_recycler_home_sub_job_item) {
      var lastPosition: Int = 0
      override fun convert(
        holder: SuperViewHolder,
        viewType: Int,
        item: com.bxkj.jrzp.user.data.JobData,
        position: Int,
      ) {
        super.convert(holder, viewType, item, position)
        if (position % 15 == 10) {
          if (lastPosition < position)
            viewModel.loadMore()
        }
      }
    }.apply {
      setOnItemClickListener(object :
        SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          startActivity(JobDetailsActivityV2.newIntent(parentActivity, data[position].id))
        }
      })
    }
    val jobList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    jobList.layoutManager = LinearLayoutManager(parentActivity)
    jobList.addItemDecoration(
      LineItemDecoration(
        ContextCompat.getDrawable(
          parentActivity,
          R.drawable.divider_f4f4f4_8
        ), LinearLayoutManager.VERTICAL
      )
    )
    viewModel.jobListViewModel.setAdapter(jobListAdapter)
  }

}