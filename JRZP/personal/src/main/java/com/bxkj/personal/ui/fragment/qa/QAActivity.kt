package com.bxkj.personal.ui.fragment.qa

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityQaBinding

/**
 *  问答
 * @author: sanjin
 * @date: 2022/8/31
 */
class QAActivity : BaseDBActivity<PersonalActivityQaBinding, BaseViewModel>() {

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, QAActivity::class.java)
        }
    }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_qa

    override fun initPage(savedInstanceState: Bundle?) {
        setupContentFragment()
    }

    private fun setupContentFragment() {
        supportFragmentManager.beginTransaction()
            .add(R.id.fl_content, QAFragment.newInstance())
            .commit()
    }
}