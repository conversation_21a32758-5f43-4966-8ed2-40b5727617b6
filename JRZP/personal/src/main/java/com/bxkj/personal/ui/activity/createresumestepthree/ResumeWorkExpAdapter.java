package com.bxkj.personal.ui.activity.createresumestepthree;

import android.content.Context;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.personal.R;
import com.bxkj.personal.data.WorkExpItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.createresumestepthree
 * @Description:
 * @TODO: TODO
 * @date 2018/11/28
 */
public class ResumeWorkExpAdapter extends SuperAdapter<WorkExpItemData> {
    public ResumeWorkExpAdapter(Context context, List<WorkExpItemData> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, WorkExpItemData workExpData, int position) {
        holder.setText(R.id.tv_start_to_end_time, workExpData.getDate1() + "-" + workExpData.getDate2());
        holder.setText(R.id.tv_about, workExpData.getConame() + " | " + workExpData.getJob());

        setOnChildClickListener(position, holder.findViewById(R.id.tv_delete), holder.findViewById(R.id.iv_edit));
    }
}
