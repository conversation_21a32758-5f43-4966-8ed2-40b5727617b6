package com.bxkj.personal.ui.activity.msgnotificationcontent

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.FeedbackMsgContentItemData
import com.bxkj.personal.databinding.PersonalActivityMsgNotificationContentBinding
import com.bxkj.personal.ui.activity.answerdetails.AnswerDetailsActivity
import com.bxkj.personal.ui.activity.questioninvite.QuestionInviteActivity

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.msgnotificationcontent
 * @Description:
 * <AUTHOR>
 * @date 2020/3/2
 * @version V1.0
 */
class NewMsgNotificationContentActivity : BaseDBActivity<PersonalActivityMsgNotificationContentBinding, MsgNotificationContentViewModel>() {

    companion object {
        const val EXTRA_SENDER_ID = "SENDER_ID"
        const val EXTRA_SENDER_NAME = "SENDER_NAME"

        fun newIntent(context: Context, senderId: Int, senderName: String): Intent {
            return Intent(context, NewMsgNotificationContentActivity::class.java)
                    .apply {
                        putExtra(EXTRA_SENDER_ID, senderId)
                        putExtra(EXTRA_SENDER_NAME, senderName)
                    }
        }
    }

    override fun getViewModelClass(): Class<MsgNotificationContentViewModel> = MsgNotificationContentViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_msg_notification_content

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupMsgContentListAdapter()
        viewModel.start(intent)
    }

    private fun setupMsgContentListAdapter() {
        val msgContentListAdapter = SimpleDBListAdapter<FeedbackMsgContentItemData>(
          this,
          layout.personal_recycler_msg_notification_content_item
        )
                .apply {
                  setOnItemClickListener(object :
                    SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                      val msgItem = data[position]
                      when (msgItem.type) {
                        40 -> {
                          startActivity(QuestionInviteActivity.newIntent(this@NewMsgNotificationContentActivity, msgItem.relId))
                        }
                        41 -> {
                          startActivity(AnswerDetailsActivity.newIntent(this@NewMsgNotificationContentActivity, msgItem.relName, msgItem.relId))
                        }
                        else -> {
                        }
                      }
                    }
                  })
                }
        val recyclerMsgContent = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
        recyclerMsgContent.layoutManager = LinearLayoutManager(this)
        viewModel.listViewModel.setAdapter(msgContentListAdapter)
    }

}