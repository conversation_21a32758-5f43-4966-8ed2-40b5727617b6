package com.bxkj.personal.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.enums.AuthenticationType;
import com.bxkj.personal.BR;
import com.bxkj.personal.R;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: gzgk
 * @Package com.bxkj.personal.data
 * @Description: 关注用户
 * @date 2020/2/11
 */
public class FollowItemData extends BaseObservable {
  private int type;
  private int id;
  private String nickName;
  private int sex;
  private String photo;
  private String dwName;
  private String dwLogo;
  private int actionCount;
  private boolean isFollowed;
  private boolean isInvited;
  private int rzType;
  private int level;

  private String tradeName;
  private int relCount;

  private String cityName;

  public int getType() {
    return type;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getNickName() {
    return nickName;
  }

  public void setNickName(String nickName) {
    this.nickName = nickName;
  }

  public int getSex() {
    return sex;
  }

  public void setSex(int sex) {
    this.sex = sex;
  }

  public String getPhoto() {
    return photo;
  }

  public void setPhoto(String photo) {
    this.photo = photo;
  }

  public String getDwName() {
    return dwName;
  }

  public void setDwName(String dwName) {
    this.dwName = dwName;
  }

  public String getDwLogo() {
    return dwLogo;
  }

  public void setDwLogo(String dwLogo) {
    this.dwLogo = dwLogo;
  }

  public int getActionCount() {
    return actionCount;
  }

  public void setActionCount(int actionCount) {
    this.actionCount = actionCount;
  }

  @Bindable
  public boolean isFollowed() {
    return isFollowed;
  }

  public void setFollowed(boolean followed) {
    isFollowed = followed;
    notifyPropertyChanged(BR.followed);
  }

  public boolean isSelf() {
    return UserUtils.logged() && id == UserUtils.getUserId();
  }

  public void addFollow() {
    setFollowed(true);
  }

  public void removeFollow() {
    setFollowed(false);
  }

  @Bindable
  public boolean isInvited() {
    return isInvited;
  }

  public void setInvited(boolean invited) {
    isInvited = invited;
    notifyPropertyChanged(BR.invited);
  }

  public int getRzType() {
    return rzType;
  }

  public void setRzType(int rzType) {
    this.rzType = rzType;
  }

  public boolean isEnterprise() {
    return type == 3;
  }

  public String getUserDesc() {
    StringBuilder userDesc = new StringBuilder();
    if (isAuthEnterprise()) {
      userDesc.append(tradeName);
      userDesc.append(" | ");
      userDesc.append("在招职位:");
      userDesc.append(relCount);
    } else if (isAuthSchool()) {
      userDesc.append(cityName);
      if (!CheckUtils.isNullOrEmpty(tradeName)) {
        userDesc.append(" | ");
        userDesc.append(tradeName);
      }
    } else {
      userDesc.append(actionCount);
      userDesc.append("条动态");
    }
    return userDesc.toString();
  }

  public boolean isAuthEnterprise() {
    return rzType == 2 || rzType == 3;
  }

  public boolean isAuthSchool() {
    return rzType == 4;
  }

  public boolean higherEnterpriseAuth(){
    return AuthenticationType.higherEnterpriseAuth(rzType);
  }

  public int getLevel() {
    return level;
  }

  public int getMemberLevelIcon() {
    switch (level) {
      case 1101: {
        return R.drawable.ic_member_bule;
      }
      case 2101: {
        return R.drawable.ic_member_gold;
      }
      case 3101: {
        return R.drawable.ic_member_diamond;
      }
      default: {
        return -1;
      }
    }
  }

  public boolean isVip() {
    return level > 101;
  }
}
