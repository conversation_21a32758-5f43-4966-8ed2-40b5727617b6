package com.bxkj.personal.ui.activity.videodetails

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.databinding.Observable
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.jzvd.JZDataSource
import cn.jzvd.Jzvd
import com.bxkj.common.adapter.OnItemLongClickListener
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.imageloder.base.ImageLoader
import com.bxkj.common.util.JZMediaExo
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.imageloader.GlideLoadConfig
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.InputDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.jrzp.support.comment.ui.replay.CommentReplyActivity
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.VideoItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.databinding.PersonalActivityVideoDetailsBinding
import com.bxkj.personal.ui.activity.gzuserhome.GzUserHomeActivity
import com.bxkj.personal.ui.activity.uploadavatar.UploadAvatarActivity
import com.bxkj.personal.weight.SendContractInfoDialog
import com.bxkj.personal.weight.pagemoreoptions.PageMoreOptionsPopup
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.videodetails
 * @Description: 视频详情
 * <AUTHOR>
 * @date 2019/12/27
 * @version V1.0
 */
class VideoDetailsActivity :
  BaseDBActivity<PersonalActivityVideoDetailsBinding, VideoDetailsViewModel>(),
  View.OnClickListener {

  companion object {

    const val EXTRA_VIDEO_ID = "VIDEO_ID"
    const val EXTRA_JUMP_TO_COMMENT = "JUMP_TO_COMMENT"
    const val EXTRA_LINK_POSITION = "LINK_POSITION"
    const val EXTRA_FOLLOW_STATUS = "FOLLOW_STATUS"
    const val EXTRA_START_PLAY = "START_PLAY"

    const val RESULT_FOLLOW_STATUS_CHANGE = Activity.RESULT_FIRST_USER + 1

    const val TO_COMMENT_REPLY_CODE = 1
    const val TO_VIDEO_AUTHOR_HOME_CODE = 2

    fun newIntent(
      context: Context,
      videoId: Int,
      jumpToComment: Boolean? = false,
      linkPosition: Int? = CommonApiConstants.NO_DATA,
      startPlay: Boolean? = false
    ): Intent {
      val intent = Intent(context, VideoDetailsActivity::class.java)
      intent.putExtra(EXTRA_VIDEO_ID, videoId)
      intent.putExtra(EXTRA_JUMP_TO_COMMENT, jumpToComment)
      intent.putExtra(EXTRA_LINK_POSITION, linkPosition)
      intent.putExtra(EXTRA_START_PLAY, startPlay)
      return intent
    }
  }

  @Inject
  lateinit var mAccountRepo: AccountRepo

  private var mCommentDialog: InputDialog? = null

  private var mSendContractInfoDialog: SendContractInfoDialog? = null

  private var mPageMoreOptionsPopup: PageMoreOptionsPopup? = null

  override fun getViewModelClass(): Class<VideoDetailsViewModel> = VideoDetailsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_video_details

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeVideoInfoChange()
    subscribeViewModelEvent()
    setupCommentListAdapter()

    viewModel.start(intent)
  }

  override fun onClick(v: View?) {
    v?.let {
      when (v.id) {
        R.id.iv_back -> {
          finish()
        }
        R.id.tv_follow -> {
          viewModel.followOrUnFollow()
        }
        R.id.tv_contract -> {
          mSendContractInfoDialog?.show()
        }
        R.id.tv_comment -> {
          showCommentDialog()
        }
        R.id.fl_comment -> {
          viewModel.callJumpToCommentCommand()
        }
        R.id.iv_like -> {
          viewModel.addOrRemoveLike()
        }
        R.id.iv_more, R.id.iv_share -> {
          viewModel.getShareInfo()
        }
        else -> {
        }
      }
    }
  }

  private fun subscribeVideoInfoChange() {
    viewModel.videoInfo.observe(this, Observer {
      mSendContractInfoDialog = SendContractInfoDialog(mAccountRepo, this, it.id)

      setupPageMoreOptionsPopup(it)

      viewBinding.videoPlayer.setUp(JZDataSource(it.video, null), Jzvd.SCREEN_NORMAL, JZMediaExo::class.java)
      viewBinding.videoPlayer.posterImageView.scaleType = ImageView.ScaleType.FIT_CENTER
      ImageLoader.loadImage(
        this,
        GlideLoadConfig.Builder().url(it.pic).into(viewBinding.videoPlayer.posterImageView).build()
      )
      if (intent.getBooleanExtra(EXTRA_START_PLAY, false)) {
        viewBinding.videoPlayer.startVideo()
      }
    })
  }

  private fun setupPageMoreOptionsPopup(it: VideoItemData) {
    val shareUrl = CommonApiConstants.SHARE_VIDEO_URL_PREFIX + it.id
    mPageMoreOptionsPopup = PageMoreOptionsPopup(
      this,
      it.getShareTitle(true),
      it.realShareContent,
      shareUrl,
      sharePhotoUrl = it.pic
    ).apply {
      setOnItemClickListener(object :
        SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          if (position == PageMoreOptionsPopup.COLLECTION_POSITION) {
            viewModel.addOrRemoveCollection()
          }
        }
      })
    }
    mPageMoreOptionsPopup?.setCollection(it.isStow)
    it.addOnPropertyChangedCallback(object : Observable.OnPropertyChangedCallback() {
      override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
        if (propertyId == BR.stow) {
          mPageMoreOptionsPopup?.setCollection((sender as VideoItemData?)?.isStow ?: false)
        }
      }
    })
  }

  private fun subscribeViewModelEvent() {
    viewModel.addCommentSuccessEvent.observe(this, Observer {
      hideCommentDialog()
    })

    viewModel.toUploadAvatarCommand.observe(this, Observer {
      showToast(getString(R.string.please_upload_avatar))
      startActivity(UploadAvatarActivity.newIntent(this))
    })

    viewModel.toAuthorPageComment.observe(this, Observer {
      startActivityForResult(
        GzUserHomeActivity.newIntent(this, it.userID),
        TO_VIDEO_AUTHOR_HOME_CODE
      )
    })

    viewModel.jumpToCommentCommand.observe(this, Observer {
      (viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
        it,
        0
      )
    })

    viewModel.followStatusChangeEvent.observe(this, Observer {
      setResult(RESULT_FOLLOW_STATUS_CHANGE, intent
        .apply {
          putExtra(EXTRA_FOLLOW_STATUS, it)
        })
    })

    viewModel.showShareCommand.observe(this, Observer { shareInfo ->
      mPageMoreOptionsPopup?.let {
        it.setShareInfo(shareInfo.title, shareInfo.content)
        it.showBottom()
      }
    })
  }

  private fun hideCommentDialog() {
    mCommentDialog?.let {
      if (it.isShowing) {
        it.dismiss()
      }
    }
  }

  /**
   * 评论dialog
   */
  private fun showCommentDialog() {
    if (checkToLogin()) {
      viewModel.checkAvatarIsUpload {
        mCommentDialog = InputDialog(this).apply {
          onSendClickListener = object : InputDialog.OnSendClickListener {
            override fun onSendClicked(commentDialog: InputDialog, content: String) {
              viewModel.addComment(content)
            }
          }
        }
        mCommentDialog?.show()
      }
    }
  }

  /**
   * 设置评论适配器
   */
  private fun setupCommentListAdapter() {
    val multiTypeAdapter = MultiTypeAdapter(this)
    multiTypeAdapter.register(VideoItemData::class.java).to(
      DefaultViewBinder<VideoItemData>(
          R.layout.personal_recycler_video_details_desc,
          BR.data,
          true
      ),
      DefaultViewBinder<VideoItemData>(
          R.layout.personal_recycler_recommend_video_item,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<VideoItemData> {
          override fun onItemClicked(v: View, position: Int, item: VideoItemData) {
            viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).scrollToPosition(0)
            viewModel.start(intent.apply {
              putExtra(EXTRA_VIDEO_ID, item.id)
              putExtra(EXTRA_JUMP_TO_COMMENT, false)
            })
          }
        })
      }
    ).withLinker { _, item ->
      if (item.isParent) {
        return@withLinker 0
      } else {
        return@withLinker 1
      }
    }
    multiTypeAdapter.register(
      CommentItemData::class.java, CommentViewBinder()
        .apply {
          setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<CommentItemData> {
            override fun onItemClicked(v: View, position: Int, item: CommentItemData) {
              when (v.id) {
                R.id.iv_avatar, R.id.tv_nick_name -> startActivity(
                  GzUserHomeActivity.newIntent(
                    this@VideoDetailsActivity,
                    item.uid
                  )
                )
                R.id.ll_reply -> {
                  startActivityForResult(
                    CommentReplyActivity.newIntent(
                      this@VideoDetailsActivity,
                      PersonalApiConstants.NEWS_TYPE_VIDEO,
                      viewModel.getVideoNewsId(),
                      position,
                      item
                    ), TO_COMMENT_REPLY_CODE
                  )
                }
                R.id.tv_like -> {
                  viewModel.commentLikeOrUnlike(item)
                }
                else -> showReplyDialog(position, item)
              }
            }
          }, R.id.iv_avatar, R.id.tv_nick_name, R.id.ll_reply, R.id.tv_like)
          setOnItemLongClickListener(object : OnItemLongClickListener<CommentItemData> {
            override fun onLongClicked(v: View, position: Int, item: CommentItemData): Boolean {
              if (UserUtils.logged()) {
                if (item.uid == localUserId) {
                  MenuPopup.Builder(this@VideoDetailsActivity)
                    .setData(resources.getStringArray(R.array.moment_details_delete_menu))
                    .setOnItemClickListener { _, menuPosition ->
                      if (menuPosition == 0) {
                        showDeleteCommentConfirmDialog(position, item)
                      }
                    }.build().show()
                }
              }
              return true
            }
          })
        })
    multiTypeAdapter.register(CommentItemData.NoCommentData::class.java,
      DefaultViewBinder<CommentItemData.NoCommentData>(
          R.layout.personal_recycler_no_comment_layout,
          BR.data,
          true
      )
        .apply {
          setOnItemClickListener(object :
            DefaultViewBinder.OnItemClickListener<CommentItemData.NoCommentData> {
            override fun onItemClicked(
              v: View,
              position: Int,
              item: CommentItemData.NoCommentData
            ) {
              showCommentDialog()
            }
          })
        })
    val recyclerCommentList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerCommentList.layoutManager = LinearLayoutManager(this)
    viewModel.commentListViewModel.setAdapter(multiTypeAdapter)
  }

  private fun showDeleteCommentConfirmDialog(position: Int, item: CommentItemData) {
    ActionDialog.Builder()
      .setTitle(getString(R.string.tips))
      .setContent(getString(R.string.delete_comment_confirm_tips))
      .setOnConfirmClickListener {
        viewModel.deleteComment(position, item)
      }.build().show(supportFragmentManager)
  }

  /**
   * 回复dialog
   */
  private fun showReplyDialog(commentPosition: Int, commentItemData: CommentItemData) {
    if (checkToLogin()) {
      viewModel.checkAvatarIsUpload {
        mCommentDialog = InputDialog(this)
          .apply {
            hint =
              <EMAIL>(R.string.reply_format, commentItemData.nickName)
            onSendClickListener = object : InputDialog.OnSendClickListener {
              override fun onSendClicked(commentDialog: InputDialog, content: String) {
                viewModel.addReply(commentPosition, commentItemData, content)
              }
            }
          }
        mCommentDialog?.show()
      }
    }
  }

  override fun onPause() {
    super.onPause()
    Jzvd.releaseAllVideos()
  }

  override fun onBackPressed() {
    if (Jzvd.backPress()) {
      return
    }
    super.onBackPressed()
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

  override fun onDestroy() {
    super.onDestroy()
    Jzvd.releaseAllVideos()
  }

}