package com.bxkj.personal.data

import com.google.gson.annotations.SerializedName

/**
 * author:Sanjin
 * date:2025/2/24
 **/
data class PartTimeIncomeDetails(
  @SerializedName("yewutichen")
  val totalBusinessCommission: String,  //业务提成
  @SerializedName("weixinPrice")
  val totalAddWxReward: String,  //添加微信奖励
  @SerializedName("tonghuaPrice")
  val totalCallReward: String, //通话接通奖励
  @SerializedName("comPrice")
  val totalRegAssistReward: String,  //协助注册奖励
  @SerializedName("huafeibutie")
  val totalPhoneSubsidy: String,
  @SerializedName("RecordCount")
  val totalAmount: String, //总收益
)