package com.bxkj.personal.data;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;
import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;
import androidx.recyclerview.widget.DiffUtil;

import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.utils.DistanceUtil;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TextUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.personal.BR;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2019/11/19
 */
public class UserMomentsItemData extends BaseObservable implements Parcelable {

    /**
     * sjid : 2
     * sjtype : 2
     * sjtypename : null
     * uid : 394156
     * gaoxiaoid : 38
     * gaoxiaoName : null
     * sjprice : 0
     * sj_lng : 112.940001
     * sj_lat : 28.159508
     * px : 0
     * content : 学校大食堂新开了一个窗口，饭菜全半价，而且味道也很不错，兄弟们抓紧过来。
     * pic : images_server/shejiao/100574307_14.jpg
     * address : 浙江大学体育场
     * sjdate : 21小时前
     * photo : images_server/Personal/394156/R0Z4D21221_2766340.jpg
     * name :
     * sjview : 0
     * sjstow : 0
     * plCount : 1
     * DzCount : 0
     * gaoxiaocity : 28590
     * gaoxiaocityName : 杭州
     */

    private int sjid;
    private int sjtype;
    private Object sjtypename;
    private int uid;
    private int gaoxiaoid;
    private Object gaoxiaoName;
    private int sjprice;
    private double sj_lng;
    private double sj_lat;
    private int px;
    private String content;
    private String pic;
    private String address;
    private String sjdate;
    private String photo;
    private String name;
    private int sjview;
    private int sjstow;
    private int plCount;
    private int DzCount;
    private int gaoxiaocity;
    private String gaoxiaocityName;
    private List<MomentPhotoItemData> picList;
    private int sjHasZan;
    private int sjHasStow;
    private List<MomentPaidUserItemData> userList;
    private int payCount;
    private String sjjuli;

    protected UserMomentsItemData(Parcel in) {
        sjid = in.readInt();
        sjtype = in.readInt();
        uid = in.readInt();
        gaoxiaoid = in.readInt();
        sjprice = in.readInt();
        sj_lng = in.readDouble();
        sj_lat = in.readDouble();
        px = in.readInt();
        content = in.readString();
        pic = in.readString();
        address = in.readString();
        sjdate = in.readString();
        photo = in.readString();
        name = in.readString();
        sjview = in.readInt();
        sjstow = in.readInt();
        plCount = in.readInt();
        DzCount = in.readInt();
        gaoxiaocity = in.readInt();
        gaoxiaocityName = in.readString();
        sjHasZan = in.readInt();
        sjHasStow = in.readInt();
        picList = in.createTypedArrayList(MomentPhotoItemData.CREATOR);
        userList = in.createTypedArrayList(MomentPaidUserItemData.CREATOR);
        payCount = in.readInt();
        sjjuli = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(sjid);
        dest.writeInt(sjtype);
        dest.writeInt(uid);
        dest.writeInt(gaoxiaoid);
        dest.writeInt(sjprice);
        dest.writeDouble(sj_lng);
        dest.writeDouble(sj_lat);
        dest.writeInt(px);
        dest.writeString(content);
        dest.writeString(pic);
        dest.writeString(address);
        dest.writeString(sjdate);
        dest.writeString(photo);
        dest.writeString(name);
        dest.writeInt(sjview);
        dest.writeInt(sjstow);
        dest.writeInt(plCount);
        dest.writeInt(DzCount);
        dest.writeInt(gaoxiaocity);
        dest.writeString(gaoxiaocityName);
        dest.writeInt(sjHasZan);
        dest.writeInt(sjHasStow);
        dest.writeTypedList(picList);
        dest.writeTypedList(userList);
        dest.writeInt(payCount);
        dest.writeString(sjjuli);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<UserMomentsItemData> CREATOR = new Creator<UserMomentsItemData>() {
        @Override
        public UserMomentsItemData createFromParcel(Parcel in) {
            return new UserMomentsItemData(in);
        }

        @Override
        public UserMomentsItemData[] newArray(int size) {
            return new UserMomentsItemData[size];
        }
    };

    public int getSjid() {
        return sjid;
    }

    public void setSjid(int sjid) {
        this.sjid = sjid;
    }

    public int getSjtype() {
        return sjtype;
    }

    public void setSjtype(int sjtype) {
        this.sjtype = sjtype;
    }

    public Object getSjtypename() {
        return sjtypename;
    }

    public void setSjtypename(Object sjtypename) {
        this.sjtypename = sjtypename;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getGaoxiaoid() {
        return gaoxiaoid;
    }

    public void setGaoxiaoid(int gaoxiaoid) {
        this.gaoxiaoid = gaoxiaoid;
    }

    public Object getGaoxiaoName() {
        return gaoxiaoName;
    }

    public void setGaoxiaoName(Object gaoxiaoName) {
        this.gaoxiaoName = gaoxiaoName;
    }

    public int getSjprice() {
        return sjprice;
    }

    public void setSjprice(int sjprice) {
        this.sjprice = sjprice;
    }

    public double getSj_lng() {
        return sj_lng;
    }

    public void setSj_lng(double sj_lng) {
        this.sj_lng = sj_lng;
    }

    public double getSj_lat() {
        return sj_lat;
    }

    public void setSj_lat(double sj_lat) {
        this.sj_lat = sj_lat;
    }

    public int getPx() {
        return px;
    }

    public void setPx(int px) {
        this.px = px;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSjdate() {
        return sjdate;
    }

    public void setSjdate(String sjdate) {
        this.sjdate = sjdate;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getSjview() {
        return sjview;
    }

    public void setSjview(int sjview) {
        this.sjview = sjview;
    }

    public String getSjjuli() {
        if (CheckUtils.isNullOrEmpty(sjjuli)) {
            return "";
        }
        if (sjjuli.contains("m")) {
            return sjjuli;
        }
        return sjjuli + "km";
    }

    public void setSjjuli(String sjjuli) {
        this.sjjuli = sjjuli;
    }

    @Bindable
    public int getSjstow() {
        return sjstow;
    }

    public void setSjstow(int sjstow) {
        this.sjstow = sjstow;
        notifyPropertyChanged(BR.sjstow);
    }

    public int getPlCount() {
        return plCount;
    }

    public void setPlCount(int plCount) {
        this.plCount = plCount;
    }

    public List<MomentPaidUserItemData> getUserList() {
        return userList;
    }

    public void setUserList(List<MomentPaidUserItemData> userList) {
        this.userList = userList;
    }

    public int getPayCount() {
        return payCount;
    }

    public void setPayCount(int payCount) {
        this.payCount = payCount;
    }

    @Bindable
    public int getDzCount() {
        return DzCount;
    }

    public void setDzCount(int DzCount) {
        this.DzCount = DzCount;
        notifyPropertyChanged(BR.dzCount);
    }

    /**
     * 添加一个点赞
     */
    public void addLike() {
        setSjHasZan(1);
        setDzCount(DzCount + 1);
    }

    /**
     * 取消一个点赞
     */
    public void removeLike() {
        setSjHasZan(0);
        setDzCount(DzCount - 1);
    }

    public int getGaoxiaocity() {
        return gaoxiaocity;
    }

    public void setGaoxiaocity(int gaoxiaocity) {
        this.gaoxiaocity = gaoxiaocity;
    }

    public String getGaoxiaocityName() {
        return gaoxiaocityName;
    }

    public void setGaoxiaocityName(String gaoxiaocityName) {
        this.gaoxiaocityName = gaoxiaocityName;
    }

    public List<MomentPhotoItemData> getPicList() {
        return picList;
    }

    public void setPicList(List<MomentPhotoItemData> picList) {
        this.picList = picList;
    }

    @Bindable
    public int getSjHasZan() {
        return sjHasZan;
    }

    public void setSjHasZan(int sjHasZan) {
        this.sjHasZan = sjHasZan;
        notifyPropertyChanged(BR.sjHasZan);
    }

    @Bindable
    public int getSjHasStow() {
        return sjHasStow;
    }

    public void setSjHasStow(int sjHasStow) {
        this.sjHasStow = sjHasStow;
        notifyPropertyChanged(BR.sjHasStow);
    }

    /**
     * 添加一个收藏
     */
    public void addCollection() {
        setSjHasStow(1);
        setSjstow(sjstow + 1);
    }

    /**
     * 取消一个收藏
     */
    public void removeCollection() {
        setSjHasStow(0);
        setSjstow(sjstow - 1);
    }

    /**
     * 添加一个查看
     */
    public void addViews() {
        setSjview(sjview + 1);
    }

    /**
     * 添加一条评论
     */
    public void addComment() {
        setPlCount(plCount + 1);
    }

    /**
     * 获取距离
     *
     * @return
     */
    public String getDistance() {
        if (sj_lat != 0 && sj_lng != 0 && UserUtils.getUserLocationLng() != CommonApiConstants.NO_DATA && UserUtils.getUserLocationLat() != CommonApiConstants.NO_DATA) {
            double distance = DistanceUtil.getDistance(new LatLng(sj_lat, sj_lng), new LatLng(UserUtils.getUserLocationLat(), UserUtils.getUserLocationLng()));
            return TextUtils.formatDistance(distance);
        }
        return CommonApiConstants.NO_TEXT;
    }

    /**
     * 添加
     *
     * @param num
     */
    public void addComment(int num) {
        setPlCount(plCount + num);
    }

    public boolean isSelf() {
        return UserUtils.logged() && uid == UserUtils.getUserId();
    }

    public static class ItemDiffCallBack extends DiffUtil.ItemCallback<UserMomentsItemData> {

        @Override
        public boolean areItemsTheSame(@NonNull UserMomentsItemData oldItem, @NonNull UserMomentsItemData newItem) {
            return oldItem.sjid == newItem.sjid;
        }

        @Override
        public boolean areContentsTheSame(@NonNull UserMomentsItemData oldItem, @NonNull UserMomentsItemData newItem) {
            return oldItem.sjid == newItem.sjid;
        }
    }
}
