package com.bxkj.personal.api;

import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.data.EncryptReqParams;
import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.data.UploadFileRequestParams;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CommonServices;
import com.bxkj.common.network.ZPRequestBody;
import com.bxkj.common.util.AESOperator;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TextUtils;
import com.bxkj.common.widget.filterpopup.FilterOptionData;
import com.bxkj.jrzp.support.comment.data.CommentItemData;
import com.bxkj.jrzp.support.comment.data.CommentSuccessResultData;
import com.bxkj.jrzp.support.db.entry.UserActionRecord;
import com.bxkj.jrzp.user.data.JobData;
import com.bxkj.jrzp.user.mine.data.ServiceItemData;
import com.bxkj.jrzp.user.mine.data.UserHomeData;
import com.bxkj.jrzp.userhome.data.UserQuestionItemData;
import com.bxkj.personal.data.AboutUsData;
import com.bxkj.personal.data.AnswerDetailsData;
import com.bxkj.personal.data.AnswerItemData;
import com.bxkj.personal.data.CertificateItemData;
import com.bxkj.personal.data.CertificationData;
import com.bxkj.personal.data.CollectCompanyItemData;
import com.bxkj.personal.data.CompanyDetailsData;
import com.bxkj.personal.data.CreateResumeData;
import com.bxkj.personal.data.DefaultAvatarItemData;
import com.bxkj.personal.data.DepartmentItemData;
import com.bxkj.personal.data.EduBackgroundData;
import com.bxkj.personal.data.EduBackgroundItemData;
import com.bxkj.personal.data.EnterpriseInfoData;
import com.bxkj.personal.data.FacultyRecruitDetailsData;
import com.bxkj.personal.data.FansItemData;
import com.bxkj.personal.data.FeedbackMsgContentItemData;
import com.bxkj.personal.data.FeedbackMsgItemData;
import com.bxkj.personal.data.FollowItemData;
import com.bxkj.personal.data.FollowedJobFairItemData;
import com.bxkj.personal.data.GzNewsDetailsData;
import com.bxkj.personal.data.InvitationsToDeliveryItemData;
import com.bxkj.personal.data.InviteUserItemData;
import com.bxkj.personal.data.JobDetailsData;
import com.bxkj.personal.data.LanguageSkillsItemData;
import com.bxkj.personal.data.MessageItemData;
import com.bxkj.personal.data.MomentDetailsData;
import com.bxkj.personal.data.MyCollectionJobData;
import com.bxkj.personal.data.NewsData;
import com.bxkj.personal.data.NewsDetailsData;
import com.bxkj.personal.data.NewsItemData;
import com.bxkj.personal.data.NewsPageResponse;
import com.bxkj.personal.data.NewsTypeItemData;
import com.bxkj.personal.data.OrderItemData;
import com.bxkj.personal.data.PaidUserItemData;
import com.bxkj.personal.data.ProfessionalSkillItemData;
import com.bxkj.personal.data.QARankItemData;
import com.bxkj.personal.data.QAUserData;
import com.bxkj.personal.data.QuestionData;
import com.bxkj.personal.data.QuestionItemData;
import com.bxkj.personal.data.QuickRecruitmentData;
import com.bxkj.personal.data.QuickRecruitmentPublishData;
import com.bxkj.personal.data.RechargeDiscountItemData;
import com.bxkj.personal.data.RecommendNewsItemData;
import com.bxkj.personal.data.ResumeBasicData;
import com.bxkj.personal.data.ResumeDefaultData;
import com.bxkj.personal.data.ResumeDeliveryRecordBean;
import com.bxkj.personal.data.ResumeItemData;
import com.bxkj.personal.data.ResumePersonalData;
import com.bxkj.personal.data.ResumeTopDiscountItemData;
import com.bxkj.personal.data.SchoolMateItemData;
import com.bxkj.personal.data.SchoolRecruitDetailsData;
import com.bxkj.personal.data.SchoolSituationItemData;
import com.bxkj.personal.data.SearchHotKeyItemData;
import com.bxkj.personal.data.SearchJobResultItemData;
import com.bxkj.personal.data.SeenMeBusinessData;
import com.bxkj.personal.data.SignUpUserItemData;
import com.bxkj.personal.data.StudyNewsDetailsData;
import com.bxkj.personal.data.StudyNewsItemData;
import com.bxkj.personal.data.SubscriptionItemData;
import com.bxkj.personal.data.SystemMsgData;
import com.bxkj.personal.data.UniversityIdData;
import com.bxkj.personal.data.UniversityItemData;
import com.bxkj.personal.data.UserBasicInfoData;
import com.bxkj.personal.data.UserCenterPersonalData;
import com.bxkj.personal.data.UserHistoryItemData;
import com.bxkj.personal.data.UserInfoData;
import com.bxkj.personal.data.UserMomentsItemData;
import com.bxkj.personal.data.UserNoticeItemData;
import com.bxkj.personal.data.UserResumeData;
import com.bxkj.personal.data.UserSignInData;
import com.bxkj.personal.data.VersionData;
import com.bxkj.personal.data.VideoItemData;
import com.bxkj.personal.data.VideoTypeItemData;
import com.bxkj.personal.data.VideoUnreadMsgItemData;
import com.bxkj.personal.data.WechatLoginResultData;
import com.bxkj.personal.data.WorkExpData;
import com.bxkj.personal.data.WorkExpItemData;
import com.bxkj.personal.ui.activity.postnotice.PostNoticeRequest;
import com.bxkj.personal.ui.fragment.homenews.HomeNewsRequestOptions;
import com.bxkj.support.upload.data.UploadFileItem;
import com.bxkj.video.VideoType;
import com.bxkj.video.data.VideoData;
import com.google.gson.Gson;
import io.reactivex.Observable;
import java.io.File;
import java.util.List;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Retrofit;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.network
 * @Description:
 * @TODO: TODO
 * @date 2018/3/30
 */

public class PersonalApi {

  private PersonalServices mPersonalServices;
  private CommonServices mCommonServices;

  public PersonalApi(Retrofit retrofit) {
    mPersonalServices = retrofit.create(PersonalServices.class);
    mCommonServices = retrofit.create(CommonServices.class);
  }

  public Observable<BaseResponse<List<VideoData>>> getResumeAttachVideo(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("id", resumeId);
    ZPRequestBody.put("type", VideoType.VIDEO_TYPE_RESUME);
    return mPersonalServices.getResumeAttachVideo(ZPRequestBody);
  }

  /**
   * 获取职位列表
   *
   * @param cityId        城市id
   * @param firstClassId  大类id
   * @param secondClassId 子类id
   * @param pageIndex     当前页码
   * @param pageSize      分页大小
   */
  public Observable<BaseResponse<List<JobData>>> getHotJobList(int provinceId, int cityId,
      int firstClassId, int secondClassId, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("sheng", provinceId);
    ZPRequestBody.put("shi", cityId);
    ZPRequestBody.put("jid1", firstClassId);
    ZPRequestBody.put("jid2", secondClassId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getHomeJobList(ZPRequestBody);
  }

  /**
   * 获取公司性质列表
   */
  public Observable<BaseResponse<List<FilterOptionData>>> getNaturesOfCompany() {
    return mPersonalServices.getNaturesOfCompany();
  }

  /**
   * 获取工作经验选项
   */
  public Observable<BaseResponse<List<FilterOptionData>>> getWorkingExp() {
    return mPersonalServices.getWorkingExp();
  }

  /**
   * 获取学历列表
   */
  public Observable<BaseResponse<List<FilterOptionData>>> getEducation() {
    return mPersonalServices.getEducation();
  }

  /**
   * 获取薪资范围
   */
  public Observable<BaseResponse<List<FilterOptionData>>> getSalaryRange() {
    return mPersonalServices.getSalaryRange();
  }

  /**
   * 获取职位列表
   *
   * @param userID            个人用户ID 用于查询沟通状态
   * @param cityId            城市id
   * @param areaId            区id
   * @param jobFirstId        大类id
   * @param jobSecondId       子类id
   * @param natureOfCompanyId 公司性质
   * @param natureOfJobId     职位性质
   * @param degreeId          学历
   * @param salaryId          薪资
   * @param workingExpId      工作经验
   * @param timeId            发布时间
   * @param pageIndex         分页页码
   * @param pageSize          分页size
   */
  public Observable<BaseResponse<List<JobData>>> getJobList(int userID, int provinceId, int companyId,
      String title, int cityId, int areaId, int streetId, int jobFirstId,
      int jobSecondId, int natureOfCompanyId, int natureOfJobId,
      int degreeId, int salaryId, int workingExpId, int timeId, int sort, String longitude,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userID);
    ZPRequestBody.put("cid", companyId);
    ZPRequestBody.put("name", title);
    ZPRequestBody.put("sheng", provinceId);
    ZPRequestBody.put("shi", cityId);
    ZPRequestBody.put("qu", areaId);
    ZPRequestBody.put("jie", streetId);
    ZPRequestBody.put("jid1", jobFirstId);
    ZPRequestBody.put("jid2", jobSecondId);
    ZPRequestBody.put("proid", natureOfCompanyId);
    ZPRequestBody.put("jnid", natureOfJobId);
    ZPRequestBody.put("quaid", degreeId);
    ZPRequestBody.put("moneyid", salaryId);
    ZPRequestBody.put("expid", workingExpId);
    ZPRequestBody.put("time", timeId);
    ZPRequestBody.put("px", sort);
    ZPRequestBody.put("longitude", longitude);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getJobList(ZPRequestBody);
  }

  /**
   * 根据标题获取职位数量
   *
   * @param title 职位标题
   */
  public Observable<BaseResponse> getJobsCountByTitle(int cityId, String title) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("shi", cityId);
    ZPRequestBody.put("name", title);
    return mPersonalServices.getJobsCountByTitle(ZPRequestBody);
  }

  /**
   * 获取职位详情
   *
   * @param jobId 职位id
   */
  public Observable<BaseResponse<JobDetailsData>> getJobDetailsInfo(int jobId, int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", jobId);
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getJobDetailsInfo(ZPRequestBody);
  }

  /**
   * 检查职位是否收藏
   */
  public Observable<BaseResponse> checkJobCollection(int uid, int jobId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", uid);
    ZPRequestBody.put("relid", jobId);
    return mPersonalServices.checkJobCollection(ZPRequestBody);
  }

  /**
   * 收藏职位
   *
   * @param companyId 发布者id
   * @param jobId     职位id
   */
  public Observable<BaseResponse> collectionJob(int userId, int companyId, int jobId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("cuid", companyId);
    ZPRequestBody.put("relid", jobId);
    return mPersonalServices.collectionJob(ZPRequestBody);
  }

  /**
   * 取消收藏职位
   */
  public Observable<BaseResponse> uncollectionJob(int userId, int jobId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("relid", jobId);
    return mPersonalServices.uncollectionJob(ZPRequestBody);
  }

  /**
   * 获取可用简历列表
   */
  public Observable<BaseResponse<List<ResumeItemData>>> getAvailableResumeList(int userId,
      int jobId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("relid", jobId);
    return mPersonalServices.getAvailableResumeList(ZPRequestBody);
  }

  /**
   * 提交简历
   *
   * @param companyUserId 发布用户id
   * @param jobId         职位id
   * @param resumeId      简历id
   * @param type          类型，1、用户主动投递2、系统推荐
   */
  public Observable<BaseResponse> submitResume(int userId, int companyUserId, int jobId, int resumeId,
      int type, int from) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("cuid", companyUserId);
    ZPRequestBody.put("relid", jobId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("ly", from);
    ZPRequestBody.put(CommonApiConstants.SOURCE_PARAMS_NAME, CommonApiConstants.SOURCE_ANDROID);
    return mPersonalServices.submitResume(ZPRequestBody);
  }

  /**
   * 获取公司详细信息
   *
   * @param companyId 公司id
   */
  public Observable<BaseResponse<CompanyDetailsData>> getCompanyDetailsInfo(int companyId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", companyId);
    return mPersonalServices.getCompanyDetailsData(ZPRequestBody);
  }

  /**
   * 分页获取资讯列表
   *
   * @param firstType  一级分类
   * @param secondType 二级分类
   * @param provinceId 省id
   * @param cityId     市id
   * @param pageIndex  页码
   * @param pageSize   分页大小
   * @param title      标题（用于模糊查询）
   */
  public Observable<BaseResponse<List<NewsData>>> getNewsList(int firstType, int secondType,
      int provinceId, int cityId, int areaId, int pageIndex, int pageSize, String title) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type1", firstType);
    ZPRequestBody.put("type2", secondType);
    ZPRequestBody.put("sheng", provinceId);
    ZPRequestBody.put("shi", cityId);
    ZPRequestBody.put("qu", areaId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    ZPRequestBody.put("title", title);
    return mPersonalServices.getNewsList(ZPRequestBody);
  }

  /**
   * 获取资讯详情
   *
   * @param id        资讯id
   * @param firstType 类别1编号，如果为2，则必填，其他可填0
   */
  public Observable<BaseResponse<NewsDetailsData>> getNewsDetails(int id, int firstType) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", id);
    ZPRequestBody.put("type1", firstType);
    return mPersonalServices.getNewsDetails(ZPRequestBody);
  }

  /**
   * 获取用户订阅列表
   */
  public Observable<BaseResponse<List<SubscriptionItemData>>> getUserSubscriptionList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getUserSubscriptionList(ZPRequestBody);
  }

  /**
   * 添加订阅
   *
   * @param subId       订阅大类编号
   * @param subSecondId 订阅子类编号
   * @param cityId      城市id
   * @param keywords    订阅关键字
   */
  public Observable<BaseResponse> subscription(int userId, int subId, int subSecondId, int cityId,
      String keywords) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type1", subId);
    ZPRequestBody.put("type2", subSecondId);
    ZPRequestBody.put("shi", cityId);
    ZPRequestBody.put("keywords", keywords);
    return mCommonServices.basePost(
        CommonApiConstants.BASE_URL + PersonalApiConstants.I_ADD_SUBSCRIPTION, ZPRequestBody);
  }

  /**
   * 取消订阅
   */
  public Observable<BaseResponse> unsubcribe(int userId, int subId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", subId);
    return mCommonServices.basePost(
        CommonApiConstants.BASE_URL + PersonalApiConstants.I_UNSUBSCRIBE, ZPRequestBody);
  }

  /**
   * 获取速聘职位列表
   */
  public Observable<BaseResponse<List<QuickRecruitmentData>>> getQuickRecruitmentList(int provinceId,
      int cityId, int areaId, int streetId, String title, int pageSize, int pageIndex) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("sheng", provinceId);
    ZPRequestBody.put("shi", cityId);
    ZPRequestBody.put("xian", areaId);
    ZPRequestBody.put("jie", streetId);
    ZPRequestBody.put("name", title);
    ZPRequestBody.put("pageSize", pageSize);
    ZPRequestBody.put("pageIndex", pageIndex);
    return mPersonalServices.getQuickRecruitmentList(ZPRequestBody);
  }

  /**
   * 发布速聘职位
   */
  public Observable<BaseResponse> addQuickRecruitment(String name, int number, String companyName,
      String contract, String phone, String email
      , String qq, int provinceId, int cityId, int areaId, int streetId, String detailsAddress,
      String publishDate, String endDate, String desc, String managerPwd) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("num", number);
    ZPRequestBody.put("coname", companyName);
    ZPRequestBody.put("lxr", contract);
    ZPRequestBody.put("phone", phone);
    ZPRequestBody.put("email", email);
    ZPRequestBody.put("qq", qq);
    ZPRequestBody.put("sheng", provinceId);
    ZPRequestBody.put("shi", cityId);
    ZPRequestBody.put("xian", areaId);
    ZPRequestBody.put("jie", streetId);
    ZPRequestBody.put("address", detailsAddress);
    ZPRequestBody.put("date", publishDate);
    ZPRequestBody.put("jsdate", endDate);
    ZPRequestBody.put("des", desc);
    ZPRequestBody.putAndMD5("pwd", managerPwd);
    return mPersonalServices.addQuickRecruitment(ZPRequestBody);
  }

  /**
   * 修改速聘职位
   */
  public Observable<BaseResponse> updateQuickRecruitment(String name, int number, String companyName,
      String contract, String phone, String email
      , String qq, int provinceId, int cityId, int areaId, int streetId, String detailsAddress,
      String publishDate, String endDate, String desc, String managerPwd, int id) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("num", number);
    ZPRequestBody.put("coname", companyName);
    ZPRequestBody.put("lxr", contract);
    ZPRequestBody.put("phone", phone);
    ZPRequestBody.put("email", email);
    ZPRequestBody.put("qq", qq);
    ZPRequestBody.put("sheng", provinceId);
    ZPRequestBody.put("shi", cityId);
    ZPRequestBody.put("xian", areaId);
    ZPRequestBody.put("jie", streetId);
    ZPRequestBody.put("address", detailsAddress);
    ZPRequestBody.put("date", publishDate);
    ZPRequestBody.put("jsdata", endDate);
    ZPRequestBody.put("des", desc);
    ZPRequestBody.putAndMD5("pwd", managerPwd);
    ZPRequestBody.put("id", id);
    return mPersonalServices.updateQuickRecruitment(ZPRequestBody);
  }

  /**
   * 删除速聘
   */
  public Observable<BaseResponse> deleteQuickRecruitment(int id, String pwd) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", id);
    ZPRequestBody.putAndMD5("pwd", pwd);
    return mPersonalServices.deleteQuickRecruitment(ZPRequestBody);
  }

  /**
   * 获取速聘详情
   */
  public Observable<BaseResponse<QuickRecruitmentPublishData>> getQuickRecruitmentDetails(int id) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", id);
    return mPersonalServices.getQuickRecruitmentDetails(ZPRequestBody);
  }

  /**
   * 根据地理位置获取地理位置信息
   */
  public Observable<BaseResponse<AreaOptionsData>> getAddressInfoByAddressName(int addressType,
      String addressName, int parentID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", addressType);
    ZPRequestBody.put("name", addressName);
    ZPRequestBody.put("pid", parentID);
    return mPersonalServices.getAddressInfoByAddressName(ZPRequestBody);
  }

  /**
   * 速聘登录
   */
  public Observable<BaseResponse> quickRecruitmentLogin(int id, String pwd) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", id);
    ZPRequestBody.putAndMD5("pwd", pwd);
    return mPersonalServices.quickRecruitmentLogin(ZPRequestBody);
  }

  /**
   * 获取用户中心个人资料
   */
  public Observable<BaseResponse<UserCenterPersonalData>> getUserCenterPersonalData(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", userId);
    return mPersonalServices.getUserCenterPersonalData(ZPRequestBody);
  }

  /**
   * 获取信息关联视频
   */
  public Observable<BaseResponse<List<VideoData>>> getInfoLinkVideos(int userID, int infoID,
      int videoType) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userID);
    ZPRequestBody.put("id", infoID);
    ZPRequestBody.put("type", videoType);
    return mPersonalServices.getInfoLinkVideos(ZPRequestBody);
  }

  /**
   * 获取用户收藏职位总数
   */
  public Observable<BaseResponse> getUserCollectionJobCount(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getUserCollectionJobCount(ZPRequestBody);
  }

  /**
   * 获取用户申请职位总数
   *
   * @param isPersonal   是否为个人
   * @param isEnterprise 是否为企业
   */
  public Observable<BaseResponse> getUserSubmitRecordCount(int userId, int isPersonal,
      int isEnterprise) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("uisdel", isPersonal);
    ZPRequestBody.put("cisdel", isEnterprise);
    return mPersonalServices.getUserSubmitRecordCount(ZPRequestBody);
  }

  /**
   * 获取用户申请记录
   */
  public Observable<BaseResponse<List<ResumeDeliveryRecordBean>>> getUserApplyRecord(int userId,
      int isPersonal, int isEnterprise, String state, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("uisdel", isPersonal);
    ZPRequestBody.put("cisdel", isEnterprise);
    ZPRequestBody.put("type", 0);
    ZPRequestBody.put(state.equals("3,4,5,6") ? "states" : "state", state);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getUserApplyRecord(ZPRequestBody);
  }

  /**
   * 批量删除申请记录
   */
  public Observable<BaseResponse> batchDeletionSubmitRecord(int userId, String selectedIds) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("ids", selectedIds);
    return mPersonalServices.batchDeletionSubmitRecord(ZPRequestBody);
  }

  /**
   * 分页获取用户收藏的职位
   */
  public Observable<BaseResponse<List<MyCollectionJobData>>> getUserCollectionJobs(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getUserCollectionJobs(ZPRequestBody);
  }

  /**
   * 获取用户后台简历列表
   */
  public Observable<BaseResponse<List<UserResumeData>>> getUserResumeList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getUserResumeList(ZPRequestBody);
  }

  /**
   * 删除简历
   */
  public Observable<BaseResponse> deleteResume(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    return mPersonalServices.deleteResume(ZPRequestBody);
  }

  /**
   * 获取简历信息
   */
  public Observable<BaseResponse<ResumeBasicData>> getResumeInfo(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    return mPersonalServices.getResumeInfo(ZPRequestBody);
  }

  /**
   * 刷新简历
   */
  public Observable<BaseResponse> refreshResume(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    return mPersonalServices.refreshResume(ZPRequestBody);
  }

  /**
   * 修改简历名称
   */
  public Observable<BaseResponse> updateResumeName(int userId, int resumeId, String name) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    ZPRequestBody.put("name", name);
    return mPersonalServices.updateResumeName(ZPRequestBody);
  }

  /**
   * 修改简历公开状态
   *
   * @param userId   用户id
   * @param resumeId 简历id
   * @param state    状态（0、对所有公开 1、对认证企业公开 2、完全保密）
   */
  public Observable<BaseResponse> updateResumeOpenState(int userId, int resumeId, int state) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    ZPRequestBody.put("state", state);
    return mPersonalServices.updateResumeOpenState(ZPRequestBody);
  }

  /**
   * 设置简历为默认简历
   */
  public Observable<BaseResponse> setUpResumeIsDefault(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    return mPersonalServices.setUpResumeIsDefault(ZPRequestBody);
  }

  /**
   * 设置简历刷新状态
   *
   * @param resumeId 简历id
   * @param state    状态
   * @return
   */
  public Observable<BaseResponse> setupResumeAutoRefresh(int resumeId, int state) {
    final ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("state", state);
    return mPersonalServices.setupResumeAutoRefresh(EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 更新简历求职意向
   */
  public Observable<BaseResponse> updateCareerObjective(int userId, int resumeId, String positionName,
      int workNatureId, int expectIndustryId, int expectPositionFirstId
      , int expectPositionSecondId, String expectSalary, int dutyTimeId, int provinceId, int cityId,
      int county, int town, int workExpId, String originalPosition) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    ZPRequestBody.put("detailsName", positionName);
    ZPRequestBody.put("jnid", workNatureId);
    ZPRequestBody.put("tradeid", expectIndustryId);
    ZPRequestBody.put("jobid1", expectPositionFirstId);
    ZPRequestBody.put("jobid2", expectPositionSecondId);
    ZPRequestBody.put("willMoney", expectSalary);
    ZPRequestBody.put("daogang", dutyTimeId);
    ZPRequestBody.put("sheng", provinceId);
    ZPRequestBody.put("shi", cityId);
    ZPRequestBody.put("zhen", county);
    ZPRequestBody.put("xiang", town);
    ZPRequestBody.put("wtid", workExpId);
    ZPRequestBody.put("detailsName2", originalPosition);
    return mPersonalServices.updateCareerObjective(ZPRequestBody);
  }

  /**
   * 获取工作经历列表
   */
  public Observable<BaseResponse<List<WorkExpItemData>>> getWorkExpList(int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    return mPersonalServices.getWorkExpList(ZPRequestBody);
  }

  /**
   * 获取工作经历详情
   */
  public Observable<BaseResponse<WorkExpData>> getWorkExpDetails(int uid, int id) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", uid);
    ZPRequestBody.put("id", id);
    return mPersonalServices.getWorkExpDetails(ZPRequestBody);
  }

  /**
   * 添加工作经历
   */
  public Observable<BaseResponse> addWorkExp(int userId, int resumeId, String startDate,
      String endDate, String companyName
      , int industryId, String positionName, String positionDes, int companyNatureId,
      int companySizeId, String workPlace
      , String department, String reasonForLeaving) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("date1", startDate);
    ZPRequestBody.put("date2", endDate);
    ZPRequestBody.put("coname", companyName);
    ZPRequestBody.put("tradeid", industryId);
    ZPRequestBody.put("job", positionName);
    ZPRequestBody.put("des", positionDes);
    ZPRequestBody.put("proid", companyNatureId);
    ZPRequestBody.put("sizeid", companySizeId);
    ZPRequestBody.put("coaddress", workPlace);
    ZPRequestBody.put("part", department);
    ZPRequestBody.put("reason", reasonForLeaving);
    return mPersonalServices.addWorkExp(ZPRequestBody);
  }

  /**
   * 修改工作经历
   */
  public Observable<BaseResponse> updateWorkExp(int userId, int id, String startDate, String endDate,
      String companyName
      , int industryId, String positionName, String positionDes, int companyNatureId,
      int companySizeId, String workPlace
      , String department, String reasonForLeaving) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", id);
    ZPRequestBody.put("date1", startDate);
    ZPRequestBody.put("date2", endDate);
    ZPRequestBody.put("coname", companyName);
    ZPRequestBody.put("tradeid", industryId);
    ZPRequestBody.put("job", positionName);
    ZPRequestBody.put("des", positionDes);
    ZPRequestBody.put("proid", companyNatureId);
    ZPRequestBody.put("sizeid", companySizeId);
    ZPRequestBody.put("coaddress", workPlace);
    ZPRequestBody.put("part", department);
    ZPRequestBody.put("reason", reasonForLeaving);
    return mPersonalServices.updateWorkExp(ZPRequestBody);
  }

  /**
   * 删除工作经历
   */
  public Observable<BaseResponse> deleteWorkExp(int userId, int id) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", id);
    return mPersonalServices.deleteWorkExp(ZPRequestBody);
  }

  /**
   * 获取教育背景列表
   */
  public Observable<BaseResponse<List<EduBackgroundItemData>>> getEduBackgroundList(int userId,
      int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    return mPersonalServices.getEduBackgroundList(ZPRequestBody);
  }

  /**
   * 获取教育背景详情
   */
  public Observable<BaseResponse<EduBackgroundData>> getEduBackgroundDetails(int userId, int id) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", id);
    return mPersonalServices.getEduBackgroundDetails(ZPRequestBody);
  }

  /**
   * 添加教育背景
   */
  public Observable<BaseResponse> addEduBackground(int userId, int resumeId, String startDate,
      String endDate, String school, String schoolAddress, int proId, String proDetails, int quaId,
      String proDesc) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("date1", startDate);
    ZPRequestBody.put("date2", endDate);
    ZPRequestBody.put("school", school);
    ZPRequestBody.put("schoolAddress", schoolAddress);
    ZPRequestBody.put("proid", proId);
    ZPRequestBody.put("proName2", proDetails);
    ZPRequestBody.put("quaid", quaId);
    ZPRequestBody.put("remark", proDesc);
    return mPersonalServices.addEduBackground(ZPRequestBody);
  }

  /**
   * 修改教育背景
   */
  public Observable<BaseResponse> updateEduBackground(int userId, int eduId, String startDate,
      String endDate, String school, String schoolAddress, int proId, String proDetails, int quaId,
      String proDesc) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", eduId);
    ZPRequestBody.put("date1", startDate);
    ZPRequestBody.put("date2", endDate);
    ZPRequestBody.put("school", school);
    ZPRequestBody.put("schoolAddress", schoolAddress);
    ZPRequestBody.put("proid", proId);
    ZPRequestBody.put("proName2", proDetails);
    ZPRequestBody.put("quaid", quaId);
    ZPRequestBody.put("remark", proDesc);
    return mPersonalServices.updateEduBackground(ZPRequestBody);
  }

  /**
   * 删除教育经历
   */
  public Observable<BaseResponse> deleteEduBackground(int userId, int eduId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", eduId);
    return mPersonalServices.deleteEduBackground(ZPRequestBody);
  }

  /**
   * 修改自我评价
   */
  public Observable<BaseResponse> updateSelfEvaluation(int userId, int resumeId,
      String selfEvaluationText) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    ZPRequestBody.put("goalScore", selfEvaluationText);
    return mPersonalServices.updateSelfEvaluation(ZPRequestBody);
  }

  /**
   * 获取专业技能列表
   */
  public Observable<BaseResponse<List<ProfessionalSkillItemData>>> getProfessionalSkillList(
      int userId, int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    return mPersonalServices.getProfessionalSkillList(ZPRequestBody);
  }

  /**
   * 获取专业技能详情
   */
  public Observable<BaseResponse<ProfessionalSkillItemData>> getProfessionalSkillDetails(int userId,
      int skillId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", skillId);
    return mPersonalServices.getProfessionalSkillDetails(ZPRequestBody);
  }

  /**
   * 添加专业技能
   */
  public Observable<BaseResponse> addProfessionalSkill(int userId, int resumeId, String skillName,
      String skillLevel, String skillDesc) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("name", skillName);
    ZPRequestBody.put("degree", skillLevel);
    ZPRequestBody.put("remark", skillDesc);
    return mPersonalServices.addProfessionalSkill(ZPRequestBody);
  }

  /**
   * 修改专业技能
   */
  public Observable<BaseResponse> updateProfessionalSkill(int userId, int skillId, String skillName,
      String skillLevel, String skillDesc) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", skillId);
    ZPRequestBody.put("name", skillName);
    ZPRequestBody.put("degree", skillLevel);
    ZPRequestBody.put("remark", skillDesc);
    return mPersonalServices.updateProfessionalSkill(ZPRequestBody);
  }

  /**
   * 删除专业技能
   */
  public Observable<BaseResponse> deleteProfessionalSkill(int uid, int skillId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", uid);
    ZPRequestBody.put("id", skillId);
    return mPersonalServices.deleteProfessionalSkill(ZPRequestBody);
  }

  /**
   * 获取语言能力列表
   */
  public Observable<BaseResponse<List<LanguageSkillsItemData>>> getLanguageSkillsList(int userId,
      int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    return mPersonalServices.getLanguageSkillsList(ZPRequestBody);
  }

  /**
   * 获取语言能力详情
   */
  public Observable<BaseResponse<LanguageSkillsItemData>> getLanguageSkillsDetails(int userId,
      int skillId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", skillId);
    return mPersonalServices.getLanguageSkillsDetails(ZPRequestBody);
  }

  /**
   * 添加语言能力
   */
  public Observable<BaseResponse> addLanguageSkills(int userId, int resumeId, int languageId,
      int levelId, String desc) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("lanid", languageId);
    ZPRequestBody.put("quaid", levelId);
    ZPRequestBody.put("remark", desc);
    return mPersonalServices.addLanguageSkills(ZPRequestBody);
  }

  /**
   * 修改语言能力
   */
  public Observable<BaseResponse> updateLanguageSkills(int userId, int skillId, int languageId,
      int levelId, String desc) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", skillId);
    ZPRequestBody.put("lanid", languageId);
    ZPRequestBody.put("quaid", levelId);
    ZPRequestBody.put("remark", desc);
    return mPersonalServices.updateLanguageSkills(ZPRequestBody);
  }

  /**
   * 删除语言能力
   */
  public Observable<BaseResponse> deleteLanguageSkills(int userId, int skillId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", skillId);
    return mPersonalServices.deleteLanguageSkills(ZPRequestBody);
  }

  /**
   * 获取在校情况列表
   */
  public Observable<BaseResponse<List<SchoolSituationItemData>>> getSchoolSituationList(int userId,
      int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resId", resumeId);
    return mPersonalServices.getSchoolSituationList(ZPRequestBody);
  }

  /**
   * 获取在校情况详情
   */
  public Observable<BaseResponse<SchoolSituationItemData>> getSchoolSituationDetails(int userId,
      int detailsId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", detailsId);
    return mPersonalServices.getSchoolSituationDetails(ZPRequestBody);
  }

  /**
   * 添加在校情况
   */
  public Observable<BaseResponse> addSchoolSituation(int userId, int resumeId, String type,
      String name, String remark) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("remark", remark);
    return mPersonalServices.addSchoolSituation(ZPRequestBody);
  }

  /**
   * 修改在校情况
   */
  public Observable<BaseResponse> updateSchoolSituation(int userId, int detailsId, String type,
      String name, String remark) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", detailsId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("remark", remark);
    return mPersonalServices.updateSchoolSituation(ZPRequestBody);
  }

  /**
   * 删除在校情况
   */
  public Observable<BaseResponse> deleteSchoolSituation(int userId, int id) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", id);
    return mPersonalServices.deleteSchoolSituation(ZPRequestBody);
  }

  /**
   * 获取证书列表
   */
  public Observable<BaseResponse<List<CertificateItemData>>> getCertificateList(int userId,
      int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    return mPersonalServices.getCertificateList(ZPRequestBody);
  }

  /**
   * 获取证书详情
   */
  public Observable<BaseResponse<CertificateItemData>> getCertificateDetails(int userId,
      int certificateId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", certificateId);
    return mPersonalServices.getCertificateDetails(ZPRequestBody);
  }

  /**
   * 添加证书
   */
  public Observable<BaseResponse> addCertificate(int userId, int resumeId, String type, String name,
      String remark) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("remark", remark);
    return mPersonalServices.addCertificate(ZPRequestBody);
  }

  /**
   * 修改证书
   */
  public Observable<BaseResponse> updateCertificate(int userId, int certificateId, String type,
      String name, String remark) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", certificateId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("remark", remark);
    return mPersonalServices.updateCertificate(ZPRequestBody);
  }

  /**
   * 删除证书
   */
  public Observable<BaseResponse> deleteCertificate(int userId, int certificateId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", certificateId);
    return mPersonalServices.deleteCertificate(ZPRequestBody);
  }

  /**
   * 获取期望行业
   */
  public Observable<BaseResponse<List<PickerOptionsData>>> getIndustryList() {
    return mPersonalServices.getIndustryList();
  }

  /**
   * 获取专业列表
   */
  public Observable<BaseResponse<List<PickerOptionsData>>> getProfessionalList() {
    return mPersonalServices.getProfessionalList();
  }

  /**
   * 提交意见反馈
   */
  public Observable<BaseResponse> submitFeedback(int type, String content, String email,
      String phone) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("content", content);
    ZPRequestBody.put("email", email);
    ZPRequestBody.put("phone", phone);
    return mPersonalServices.submitFeedback(ZPRequestBody);
  }

  /**
   * 获取关于我们信息
   */
  public Observable<BaseResponse<AboutUsData>> getAboutUsInfo() {
    return mPersonalServices.getAboutUsInfo();
  }

  /**
   * 获取资讯分类2
   */
  public Observable<BaseResponse<List<FilterOptionData>>> getNewsTypeTwo() {
    return mPersonalServices.getNewsTypeTwo();
  }

  /**
   * 获取订阅消息列表
   */
  public Observable<BaseResponse<List<MessageItemData>>> getSubscriptionMessageList(int userId,
      int subId, int type, int status, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("dyid", subId);
    ZPRequestBody.put("ntid", type);
    ZPRequestBody.put("look", status);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getSubscriptionMessageList(ZPRequestBody);
  }

  /**
   * 检查版本信息
   */
  public Observable<BaseResponse<VersionData>> checkVersionInfo() {
    return mPersonalServices.checkVersionInfo();
  }

  /**
   * 获取用户订阅总数
   */
  public Observable<BaseResponse> getUserSubscriptionCount(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getUserSubscriptionCount(ZPRequestBody);
  }

  /**
   * 获取用户订阅消息总数
   */
  public Observable<BaseResponse> getSubscriptionMessageCount(int userId, int subId, int typeId,
      int status) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("dyid", subId);
    ZPRequestBody.put("ntid", typeId);
    ZPRequestBody.put("look", status);
    return mPersonalServices.getSubscriptionMessageCount(ZPRequestBody);
  }

  /**
   * 改变消息查看状态
   */
  public Observable<BaseResponse> changeMessageStatus(int id) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", id);
    return mPersonalServices.changeMessageStatus(ZPRequestBody);
  }

  /**
   * 修改消息查看状态
   */
  public Observable<BaseResponse> updateFeedbackMsgState(int msgId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", msgId);
    return mPersonalServices.updateFeedbackMsgState(ZPRequestBody);
  }

  /**
   * 同意邀请投递
   *
   * @param userId 用户id
   */
  public Observable<BaseResponse> acceptDeliveryInvitation(int userId, int otherId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", otherId);
    ZPRequestBody.put(CommonApiConstants.SOURCE_PARAMS_NAME, CommonApiConstants.SOURCE_ANDROID);
    return mPersonalServices.acceptDeliveryInvitation(ZPRequestBody);
  }

  /**
   * 拒绝邀请投递
   */
  public Observable<BaseResponse> refuseDeliveryInvitation(int userId, int otherId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", otherId);
    return mPersonalServices.refuseDeliveryInvitation(ZPRequestBody);
  }

  /**
   * 接受面試邀請
   */
  public Observable<BaseResponse> acceptInterviewInvitation(int userId, int otherId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", otherId);
    return mPersonalServices.acceptInterviewInvitation(ZPRequestBody);
  }

  /**
   * 拒絕面試邀請
   */
  public Observable<BaseResponse> refuseInterviewInvitation(int userId, int otherId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", otherId);
    return mPersonalServices.refuseInterviewInvitation(ZPRequestBody);
  }

  /**
   * 获取投递邀请列表
   */
  public Observable<BaseResponse<List<InvitationsToDeliveryItemData>>> getInvitationsToDeliveryList(
      int userId, int state, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("state", state);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getInvitationsToDeliveryList(ZPRequestBody);
  }

  /**
   * 获取投递邀请数量
   */
  public Observable<BaseResponse> getInvitationsToDeliveryCount(int userId, int state) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("state", state);
    return mPersonalServices.getInvitationsToDeliveryCount(ZPRequestBody);
  }

  /**
   * 获取投递反馈消息列表
   */
  public Observable<BaseResponse<List<FeedbackMsgItemData>>> getFeedbackNoticeList(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getFeedbackNoticeList(ZPRequestBody);
  }

  /**
   * 根据公司信息获取反馈消息列表
   */
  public Observable<BaseResponse<List<FeedbackMsgContentItemData>>> getFeedbackMsgContentByCompany(
      int userId, int companyId, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("cuid", companyId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getFeedbackMsgContentByCompany(ZPRequestBody);
  }

  /**
   * 获取查看过我的公司列表
   */
  public Observable<BaseResponse<List<SeenMeBusinessData>>> getSewMeCompanyList(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getSewMeCompanyList(ZPRequestBody);
  }

  /**
   * 获取谁看过我总数
   */
  public Observable<BaseResponse> getSewMeCompanyCount(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getSewMeCompanyCount(ZPRequestBody);
  }

  /**
   * 获取消息通知
   */
  public Observable<BaseResponse<List<FeedbackMsgItemData>>> getMsgNotificationList(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("look", -1);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getMsgNotificationList(ZPRequestBody);
  }

  /**
   * 获取消息通知内容列表（公司下）
   */
  public Observable<BaseResponse<List<FeedbackMsgContentItemData>>> getMsgNotificationContentListByCompany(
      int userId, int companyId, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("cuid", companyId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getMsgNotificationContentListByCompany(ZPRequestBody);
  }

  /**
   * 获取系统消息
   */
  public Observable<BaseResponse<List<SystemMsgData>>> getSystemMsgList(int userId, int pageIndex,
      int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getSystemMsgList(ZPRequestBody);
  }

  /**
   * 更新个人基础信息
   *
   * @param sex      性别 0男1女
   * @param province 居住地省
   * @param city     居住地市
   */
  public Observable<BaseResponse> updatePersonalBasicInfo(int uid, String name, int sex,
      String birthday
      , int province, int city, int education
      //            , String phone, int bFirstType, int bSecondType, int nFirstType, int nSecondType
  ) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", uid);
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("sex", sex);
    ZPRequestBody.put("birthday", birthday);
    ZPRequestBody.put("provinceJZ", province);
    ZPRequestBody.put("cityJZ", city);
    ZPRequestBody.put("quaId", education);
    //        mRequestBody.put("phone", phone);
    //        mRequestBody.put("jobType1", bFirstType);
    //        mRequestBody.put("jobType2", bSecondType);
    //        mRequestBody.put("jobTypeWish1", nFirstType);
    //        mRequestBody.put("jobTypeWish2", nSecondType);
    return mPersonalServices.updatePersonalBasicInformation(ZPRequestBody);
  }

  /**
   * 获取民族信息
   */
  public Observable<BaseResponse<List<PickerOptionsData>>> getNationList() {
    return mPersonalServices.getNationList();
  }

  /**
   * 更新用户详细资料
   *
   * @param marryState     婚姻状况
   * @param height         身高
   * @param country        国籍
   * @param mzId           民族
   * @param provinceHK     户口所在省
   * @param cityHK         户口所在市
   * @param cerId          证件类型
   * @param certificate    证件号码
   * @param polId          政治面貌
   * @param graduationTime 毕业时间
   * @param address        通信地址
   * @param postcode       邮编
   */
  public Observable<BaseResponse> updatePersonalDetailsInformation(int userId, int marryState,
      String height, String country
      , int mzId, int provinceHK, int cityHK, int cerId, String certificate, int polId,
      String graduationTime, String qq, String address, String postcode) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("marryState", marryState);
    ZPRequestBody.put("hight", height);
    ZPRequestBody.put("country", country);
    ZPRequestBody.put("mzId", mzId);
    ZPRequestBody.put("provinceHK", provinceHK);
    ZPRequestBody.put("cityHK", cityHK);
    ZPRequestBody.put("cerId", cerId);
    ZPRequestBody.put("certificate", certificate);
    ZPRequestBody.put("polId", polId);
    ZPRequestBody.put("graduationTime", graduationTime);
    ZPRequestBody.put("qq", qq);
    ZPRequestBody.put("address", address);
    ZPRequestBody.put("postcode", postcode);
    return mPersonalServices.updatePersonalDetailsInformation(ZPRequestBody);
  }

  /**
   * 获取简历个人信息
   */
  public Observable<BaseResponse<ResumePersonalData>> getResumePersonalData(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getResumePersonalData(ZPRequestBody);
  }

  /**
   * 更新简历个人信息
   */
  public Observable<BaseResponse> createResumeStepOne(int userId,
      ResumePersonalData resumePersonalData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("ubname", resumePersonalData.getName());
    ZPRequestBody.put("sex", resumePersonalData.getSex());
    ZPRequestBody.put("birthday", resumePersonalData.getBirthday());
    ZPRequestBody.put("province", resumePersonalData.getProvinceJZ());
    ZPRequestBody.put("city", resumePersonalData.getCityJZ());
    ZPRequestBody.put("county", resumePersonalData.getCountyJZ());
    ZPRequestBody.put("town", resumePersonalData.getTownJZ());
    ZPRequestBody.put("quaId", resumePersonalData.getQuaId());
    ZPRequestBody.put("gaoxiaoId", resumePersonalData.getGaoxiaoid());
    ZPRequestBody.put("proId", resumePersonalData.getProid());
    ZPRequestBody.put("banjiId", resumePersonalData.getBanji());
    ZPRequestBody.put("xuehaoId", resumePersonalData.getXuehao());
    ZPRequestBody.put("graduationTime", resumePersonalData.getGraduationTime());
    ZPRequestBody.put("jobType1", resumePersonalData.getJobType1());
    ZPRequestBody.put("jobType2", resumePersonalData.getJobType2());
    ZPRequestBody.put("detailsName", resumePersonalData.getDetailsName());
    return mPersonalServices.createResumeOneStep(ZPRequestBody);
  }

  /**
   * 获取简历求职意向
   */
  public Observable<BaseResponse<CreateResumeData>> getResumeCareerObjective(int userId,
      int resumeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    return mPersonalServices.getResumeCareerObjective(ZPRequestBody);
  }

  /**
   * 更新简历求职意向(新建简历时,换了接口)
   */
  public Observable<BaseResponse> updateResumeCareerObjective(int userId, int resumeId,
      CreateResumeData createResumeData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    ZPRequestBody.put("name", createResumeData.getName());   //简历名称
    ZPRequestBody.put("jnid", createResumeData.getJnid()); //工作性质id
    ZPRequestBody.put("wtid", createResumeData.getWtid());
    ZPRequestBody.put("willMoney", createResumeData.getWillMoney());
    ZPRequestBody.put("daogang", createResumeData.getDaogang());
    ZPRequestBody.put("sheng", createResumeData.getSheng());
    ZPRequestBody.put("shi", createResumeData.getShi());
    ZPRequestBody.put("zhen", createResumeData.getZhen());
    ZPRequestBody.put("xiang", createResumeData.getXiang());
    return mPersonalServices.updateResumeCareerObjective(ZPRequestBody);
  }

  /**
   * 添加简历工作经验
   */
  public Observable<BaseResponse> addResumeWorkExp(int userId, int resumeId, String startDate,
      String endDate, String companyName, int industryId, String positionName, String workDes) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("date1", startDate);
    ZPRequestBody.put("date2", endDate);
    ZPRequestBody.put("coname", companyName);
    ZPRequestBody.put("tradeid", industryId);
    ZPRequestBody.put("job", positionName);
    ZPRequestBody.put("des", workDes);
    return mPersonalServices.addResumeWorkExp(ZPRequestBody);
  }

  /**
   * 添加简历教育背景（新）
   */
  public Observable<BaseResponse> addResumeEduBg(int userId, int resumeId, String startDate,
      String endDate, String school, int proId, String proDetails, int quaId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("date1", startDate);
    ZPRequestBody.put("date2", endDate);
    ZPRequestBody.put("school", school);
    ZPRequestBody.put("proid", proId);
    ZPRequestBody.put("proName2", proDetails);
    ZPRequestBody.put("quaid", quaId);
    return mPersonalServices.addResumeEduBg(ZPRequestBody);
  }

  /**
   * 获取用户收藏职位总数
   */
  public Observable<BaseResponse> getUserCollectionCompanyCount(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getUserCollectionCompanyCount(ZPRequestBody);
  }

  /**
   * 修改消息查看状态
   */
  public Observable<BaseResponse> setupMsgReaded(int userId, int msgId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("cuid", msgId);
    return mPersonalServices.updateMessageState(ZPRequestBody);
  }

  /**
   * 判断是否收藏公司
   */
  public Observable<BaseResponse> checkCollectTheCompany(int userId, int companyId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("comid", companyId);
    return mPersonalServices.checkCollectTheCompany(ZPRequestBody);
  }

  /**
   * 收藏公司
   */
  public Observable<BaseResponse> collectTheCompany(int userId, int companyId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("comid", companyId);
    return mPersonalServices.collectTheCompany(ZPRequestBody);
  }

  /**
   * 取消收藏公司
   */
  public Observable<BaseResponse> cancelCollectCompany(int userId, int companyId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("comid", companyId);
    return mPersonalServices.cancelCollectTheCompany(ZPRequestBody);
  }

  /**
   * 获取收藏公司列表
   */
  public Observable<BaseResponse<List<CollectCompanyItemData>>> getCollectCompanyList(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getCollectCompanyList(ZPRequestBody);
  }

  /**
   * 上传附件简历
   */
  public Observable<BaseResponse> uploadAttachedResume(int userId, int resumeId, String filePath,
      String fileType) {
    File file = new File(filePath);
    RequestBody fileBody = RequestBody.create(MediaType.parse("multipart/form-data"), file);
    MultipartBody mRequestBody = new MultipartBody.Builder()
        .setType(MultipartBody.FORM)
        .addFormDataPart("imgFile", CheckUtils.encode(file.getName()), fileBody)
        .addFormDataPart("action", "uploadFile")
        .addFormDataPart("para", AESOperator.safeEncrypt(userId + "|" + fileType + "|" + resumeId))
        .build();
    return mPersonalServices.uploadAttachmentResume(
        PersonalApiConstants.I_OPERATION_ATTACHMENT_RESUME, mRequestBody);
  }

  /**
   * 删除附件简历
   */
  public Observable<BaseResponse> deleteAttachmentResume(int userId, int resumeId) {
    MultipartBody multipartBody = new MultipartBody.Builder()
        .setType(MultipartBody.FORM)
        .addFormDataPart("action", "deleteFile")
        .addFormDataPart("para", AESOperator.safeEncrypt(userId + "|" + resumeId))
        .build();
    return mPersonalServices.deleteAttachmentResume(
        PersonalApiConstants.I_OPERATION_ATTACHMENT_RESUME, multipartBody);
  }

  /**
   * 修改簡歷個人信息
   */
  public Observable<BaseResponse> updateResumePersonalData(int userId,
      ResumePersonalData resumePersonalData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("name", resumePersonalData.getName());
    ZPRequestBody.put("sex", resumePersonalData.getSex());
    ZPRequestBody.put("birthday", resumePersonalData.getBirthday());
    ZPRequestBody.put("provinceJZ", resumePersonalData.getProvinceJZ());
    ZPRequestBody.put("cityJZ", resumePersonalData.getCityJZ());
    ZPRequestBody.put("quaid", resumePersonalData.getQuaId());
    ZPRequestBody.put("gaoxiaoId", resumePersonalData.getGaoxiaoid());
    ZPRequestBody.put("proId", resumePersonalData.getProid());
    ZPRequestBody.put("banjiId", resumePersonalData.getBanji());
    ZPRequestBody.put("xuehaoId", resumePersonalData.getXuehao());
    ZPRequestBody.put("graduationTime", resumePersonalData.getGraduationTime());
    return mPersonalServices.updateResumePersonalData(ZPRequestBody);
  }

  /**
   * 修改创建简历个人信息中的求职意向
   */
  public Observable<BaseResponse> updateResumePersonalDataCareerObject(int userId, int resumeId,
      ResumePersonalData resumePersonalData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", resumeId);
    ZPRequestBody.put("jobid1", resumePersonalData.getJobType1());
    ZPRequestBody.put("jobid2", resumePersonalData.getJobType2());
    ZPRequestBody.put("detailsName", resumePersonalData.getDetailsName());
    ZPRequestBody.put("sheng", resumePersonalData.getProvinceJZ());
    ZPRequestBody.put("shi", resumePersonalData.getCityJZ());
    ZPRequestBody.put("zhen", resumePersonalData.getCountyJZ());
    ZPRequestBody.put("xiang", resumePersonalData.getTownJZ());
    return mPersonalServices.updateResumePersonalDatCareerObject(ZPRequestBody);
  }

  /**
   * 获取用户未读消息通知总数
   */
  public Observable<BaseResponse> getUnreadMsgNoticeCount(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getUnreadMsgNoticeCount(ZPRequestBody);
  }

  /**
   * 获取用户未读系统消息总数
   */
  public Observable<BaseResponse> getUnreadSystemMsgCount(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getUnreadSystemMsgCount(ZPRequestBody);
  }

  /**
   * 修改系统消息查看状态
   */
  public Observable<BaseResponse> updateSystemMsgReadState(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.updateSystemMsgReadState(ZPRequestBody);
  }

  /**
   * 获取未读订阅消息数量
   */
  public Observable<BaseResponse> getUnreadSubMsgCount(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getUnreadSubMsgCount(ZPRequestBody);
  }

  /**
   * 获取未读查看过我的数量
   */
  public Observable<BaseResponse> getUnreadSewMeCount(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getUnreadSewMeCount(ZPRequestBody);
  }

  /**
   * 修改谁看过我的查看状态
   */
  public Observable<BaseResponse> updateSewMeReadState(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.updateSewMeReadState(ZPRequestBody);
  }

  /**
   * 扫码登录
   */
  public Observable<BaseResponse> scanQrCodeLogin(String channel, String content) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("channel", channel);
    ZPRequestBody.put("content", content);
    return mPersonalServices.scanQrCodeLogin(ZPRequestBody);
  }

  /**
   * 获取客服列表
   */
  public Observable<BaseResponse<List<ServiceItemData>>> getServiceList() {
    return mPersonalServices.getServiceList();
  }

  /**
   * 检查系统状态
   */
  public Observable<BaseResponse> checkSystemStatus() {
    return mPersonalServices.checkSystemStatus();
  }

  /**
   * 检查用户信息是否完善
   */
  public Observable<BaseResponse> checkInfoIsPerfected(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.checkInfoIsPerfected(ZPRequestBody);
  }

  /**
   * 修改企业信息
   */
  public Observable<BaseResponse> editEnterpriseInfo(EnterpriseInfoData enterpriseInfoData) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", enterpriseInfoData.getUid());
    ZPRequestBody.put("lxr", enterpriseInfoData.getLxr());
    ZPRequestBody.put("phone", enterpriseInfoData.getPhone());
    ZPRequestBody.put("name", enterpriseInfoData.getName());
    ZPRequestBody.put("name2", enterpriseInfoData.getName2());
    ZPRequestBody.put("province", enterpriseInfoData.getProvince());
    ZPRequestBody.put("city", enterpriseInfoData.getCity());
    ZPRequestBody.put("county", enterpriseInfoData.getCounty());
    ZPRequestBody.put("town", enterpriseInfoData.getTown());
    ZPRequestBody.put("address", enterpriseInfoData.getAddress());
    ZPRequestBody.put("Info", enterpriseInfoData.getInfo());
    ZPRequestBody.put("tradeid", enterpriseInfoData.getTradeid());
    ZPRequestBody.put("proid", enterpriseInfoData.getProid());
    ZPRequestBody.put("sizeid", enterpriseInfoData.getSizeid());
    ZPRequestBody.put("coordinate", enterpriseInfoData.getCoordinate());
    return mPersonalServices.editCompanyInfo(ZPRequestBody);
  }

  /**
   * 检查用户公司信息是否完善
   */
  public Observable<BaseResponse> checkCompanyInfoCompleted(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.checkCompanyInfoPerfected(ZPRequestBody);
  }

  /**
   * 获取教职工招聘详情
   */
  public Observable<BaseResponse<FacultyRecruitDetailsData>> getFacultyRecruitmentDetails(
      int facultyRecruitId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", facultyRecruitId);
    return mPersonalServices.getFacultyRecruitDetails(ZPRequestBody);
  }

  /**
   * 获取校园招聘会详情
   */
  public Observable<BaseResponse<SchoolRecruitDetailsData>> getSchoolRecruitDetails(int userId,
      int schoolRecruitId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", schoolRecruitId);
    return mPersonalServices.getSchoolRecruitDetails(ZPRequestBody);
  }

  /**
   * 检查招聘会是否关注
   */
  public Observable<BaseResponse> checkJobFairIsFollow(int userId, int jobFairId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("glID", jobFairId);
    ZPRequestBody.put("gzType", PersonalApiConstants.FOLLOW_JOB_FAIR_TYPE);
    return mPersonalServices.checkIsFollow(ZPRequestBody);
  }

  /**
   * 获取高校评论列表
   */
  public Observable<BaseResponse<List<CommentItemData>>> getCommendList(int userId, int newsId,
      int newsType, int parentId, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("nid", newsId);
    ZPRequestBody.put("type", newsType);
    ZPRequestBody.put("pid", parentId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getCommentList(ZPRequestBody);
  }

  /**
   * 喜欢或取消喜欢评论
   */
  public Observable<BaseResponse> likeOrUnlikeTheComment(int userId, int commentId, int newsType,
      int newsId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("plid", commentId);
    ZPRequestBody.put("type", newsType);
    ZPRequestBody.put("nid", newsId);
    return mPersonalServices.likeOrUnlikeTheComment(ZPRequestBody);
  }

  /**
   * 添加评论
   *
   * @param userId      用户id
   * @param newsId      资讯id
   * @param typeId      资讯类型id 1、资讯评论 2、高校评论
   * @param parentId    顶级评论id
   * @param replyUserId 回复用户id
   * @param content     评论内容
   */
  public Observable<BaseResponse<CommentSuccessResultData>> addComment(int userId, int newsId,
      int typeId, int parentId, int replyUserId, String content) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("nid", newsId);
    ZPRequestBody.put("type", typeId);
    ZPRequestBody.put("pid", parentId);
    ZPRequestBody.put("puid", replyUserId);
    ZPRequestBody.put("content", content);
    return mPersonalServices.addComment(ZPRequestBody);
  }

  /**
   * 查询院校列表
   */
  public Observable<BaseResponse<List<UniversityItemData>>> getUniversityList(int city, String name,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("city", city);
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getUniversityList(ZPRequestBody);
  }

  /**
   * 获取高校id
   */
  public Observable<BaseResponse<UniversityIdData>> getUniversityId(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", userId);
    return mPersonalServices.getUniversityId(ZPRequestBody);
  }

  /**
   * 获取院系列表
   */
  public Observable<BaseResponse<List<DepartmentItemData>>> getDepartmentList(String name) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("name", name);
    return mPersonalServices.getDepartmentList(ZPRequestBody);
  }

  /**
   * 获取积分充值优惠列表
   */
  public Observable<BaseResponse<List<RechargeDiscountItemData>>> getRechargeDiscountList() {
    return mPersonalServices.getRechargeDiscountList();
  }

  /**
   * 发布资讯
   *
   * @param userId   用户id
   * @param cityId   城市编号
   * @param address  地址
   * @param lng      经度
   * @param lat      纬度
   * @param newsType 类型 1、兼职 2、活动
   * @param price    价格
   * @param content  内容
   * @return 请求结果
   */
  public Observable<BaseResponse> postNews(int userId, int cityId, String address, String lng,
      String lat, int newsType, int price, String content, List<UploadFileItem> picList) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("sjcity", cityId);
    ZPRequestBody.put("address", address);
    ZPRequestBody.put("sj_lng", lng);
    ZPRequestBody.put("sj_lat", lat);
    ZPRequestBody.put("sjtype", newsType);
    ZPRequestBody.put("sjprice", price);
    ZPRequestBody.put("content", content);
    ZPRequestBody.put("pic", new Gson().toJson(picList));
    return mPersonalServices.postNews(ZPRequestBody);
  }

  /**
   * 上传资讯文件
   */
  public Observable<BaseResponse> uploadFile(String filePath,
      UploadFileRequestParams uploadFileRequestParams) {
    File file = new File(filePath);
    RequestBody fileBody = RequestBody.create(MediaType.parse("multipart/form-data"), file);
    MultipartBody mRequestBody = new MultipartBody.Builder()
        .setType(MultipartBody.FORM)
        .addFormDataPart("imgFile", CheckUtils.encode(file.getName()), fileBody)
        .addFormDataPart("action", "uploadFile2")
        .addFormDataPart("para",
            AESOperator.safeEncrypt(new Gson().toJson(uploadFileRequestParams)))
        .build();
    return mPersonalServices.uploadAttachmentResume(
        PersonalApiConstants.I_OPERATION_ATTACHMENT_RESUME, mRequestBody);
  }

  /**
   * 收藏/取消收藏
   *
   * @param type   1、资讯 2、高校 3、社交 4、问答
   * @param newsId 对应id
   */
  public Observable<BaseResponse> addOrRemoveCollection(int userId, int type, int newsId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("infoId", newsId);
    return mPersonalServices.addOrRemoveCollection(ZPRequestBody);
  }

  /**
   * 添加/取消关注
   *
   * @param userId     用户id
   * @param followType 关注类型1、公司 2、高校 3、事业单位 4、招聘会 5、用户
   * @param followId   关注id
   */
  public Observable<BaseResponse> addOrRemoveFollow(int userId, int followType, int followId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("gzType", followType);
    ZPRequestBody.put("glID", followId);
    return mPersonalServices.addOrRemoveFollow(ZPRequestBody);
  }

  /**
   * 获取支付宝订单信息
   */
  public Observable<BaseResponse> getAlipayOrderInfo(int userId, String orderId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("para", TextUtils.stringToHex(userId + "|" + orderId));
    return mPersonalServices.getAlipayOrderInfo(ZPRequestBody);
  }

  /**
   * 创建支付订单
   *
   * @param userId     用户id
   * @param orderType  订单类型3、购买积分4、短信充值
   * @param orderPrice 金额
   */
  public Observable<BaseResponse> createPaymentOrder(int userId, int orderType, int orderPrice) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", orderType);
    ZPRequestBody.put("price", orderPrice);
    return mPersonalServices.createPaymentOrder(ZPRequestBody);
  }

  /**
   * 获取校友列表
   *
   * @param lng       经度
   * @param lat       纬度
   * @param pageIndex 页码
   * @param pageSize  大小
   */
  public Observable<BaseResponse<List<SchoolMateItemData>>> getSchoolMateList(int userId, String lng,
      String lat, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("lng", lng);
    ZPRequestBody.put("lat", lat);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getSchoolMateList(ZPRequestBody);
  }

  /**
   * 获取支付结果
   */
  public Observable<BaseResponse<OrderItemData>> getPaymentResult(int userId, String orderId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", orderId);
    return mPersonalServices.getPaymentResult(ZPRequestBody);
  }

  /**
   * 获取专业列表
   *
   * @param departmentId 院系id
   */
  public Observable<BaseResponse<List<DepartmentItemData>>> getProfessionList(int departmentId,
      String name) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("proId", departmentId);
    ZPRequestBody.put("name", name);
    return mPersonalServices.getProfessionList(ZPRequestBody);
  }

  /**
   * 获取班级列表
   *
   * @param professionId 专业编号
   */
  public Observable<BaseResponse<List<DepartmentItemData>>> getClassList(int professionId,
      String name) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("zyId", professionId);
    ZPRequestBody.put("name", name);
    return mPersonalServices.getClassList(ZPRequestBody);
  }

  /**
   * 修改用户基本资料
   */
  public Observable<BaseResponse> updateUserBasicInfo(UserBasicInfoData userInfoData) {
    userInfoData.setWstype(CommonApiConstants.SOURCE_ANDROID);
    return mPersonalServices.updateUserBasicInfo(EncryptReqParams.encryptObject(userInfoData));
  }

  /**
   *
   */
  public Observable<BaseResponse> checkUserInfoIsComplete(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    return mPersonalServices.checkUserInfoIsComplete(ZPRequestBody);
  }

  /**
   * 创建简历
   */
  public Observable<BaseResponse> createResume(CreateResumeData createResumeData) {
    createResumeData.setWstype(CommonApiConstants.SOURCE_ANDROID);
    return mPersonalServices.createResume(createResumeData);
  }

  /**
   * 获取创建简历默认信息
   *
   * @param userId 用户id
   */
  public Observable<BaseResponse<ResumeDefaultData>> getCreateResumeDefaultData(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getCreateResumeDefaultInfo(ZPRequestBody);
  }

  /**
   * 获取关注双选会列表
   */
  public Observable<BaseResponse<List<FollowedJobFairItemData>>> getFollowedJobFairList(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getFollowedJobFairList(ZPRequestBody);
  }

  /**
   * 获取已支付用户列表
   *
   * @param userId   用户id
   * @param momentId 动态id
   */
  public Observable<BaseResponse<List<PaidUserItemData>>> getPaidUserList(int userId, int momentId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("sjid", momentId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getPaidUserList(ZPRequestBody);
  }

  /**
   * 获取打赏记录
   */
  public Observable<BaseResponse<List<UserMomentsItemData>>> getRewardHistoryList(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getRewardHistoryList(ZPRequestBody);
  }

  /**
   * 删除社交信息
   */
  public Observable<BaseResponse> deleteMoment(int userId, int momentId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("sjid", momentId);
    return mPersonalServices.deleteMoment(ZPRequestBody);
  }

  /**
   * 删除评论
   */
  public Observable<BaseResponse> deleteComment(int userId, int commentId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", commentId);
    return mPersonalServices.deleteComment(ZPRequestBody);
  }

  /**
   * 微信登录
   */
  public Observable<BaseResponse<WechatLoginResultData>> loginByWechat(String code) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("code", code);
    ZPRequestBody.put(CommonApiConstants.SOURCE_PARAMS_NAME, CommonApiConstants.SOURCE_ANDROID);
    return mPersonalServices.loginByWechat(ZPRequestBody);
  }

  /**
   * 判断头像是否已上传
   */
  public Observable<BaseResponse> checkAvatarIsUpload(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.checkAvatarIsUpload(ZPRequestBody);
  }

  /**
   * 获取用户昵称和头像
   */
  public Observable<BaseResponse<UserInfoData>> getUserAvatarAndNikename(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getUserAvatarAndNickname(ZPRequestBody);
  }

  /**
   * 获取默认头像列表
   */
  public Observable<BaseResponse<List<DefaultAvatarItemData>>> getDefaultAvatarList(int pageIndex,
      int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getDefaultAvatarList(ZPRequestBody);
  }

  /**
   * 修改头像和昵称
   */
  public Observable<BaseResponse> updateAvatarAndNickname(UserInfoData userInfoData) {
    return mPersonalServices.updateAvatarAndNickname(userInfoData);
  }

  /**
   * 检查是否已绑定手机号
   */
  public Observable<BaseResponse> checkIsBindMobile(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.putAndAes("id2en", userId);
    return mPersonalServices.checkIsBindMobile(ZPRequestBody);
  }

  /**
   * 绑定手机号码
   */
  public Observable<BaseResponse> bindMobileNumber(int userId, String mobileNumber, String smsCode) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.putAndAes("id2en", userId);
    ZPRequestBody.putAndAes("mobile", mobileNumber);
    ZPRequestBody.put("smsCode", smsCode);
    return mPersonalServices.bindMobileNumber(ZPRequestBody);
  }

  /**
   * 发送短信验证码
   */
  public Observable<BaseResponse> sendSmsVerificationCode(String mobileNumber, int sendType) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("mobile", AESOperator.safeEncrypt(mobileNumber + "|-6"));
    ZPRequestBody.put("type", sendType);
    return mCommonServices.basePost(
        CommonApiConstants.BASE_URL + PersonalApiConstants.I_SEND_SMS_VERIFICATION_CODE,
            ZPRequestBody);
  }

  /**
   * 账号合并
   *
   * @param mergeType 1、保留微信2、保留手机
   */
  public Observable<BaseResponse<String>> accountMerge(int userId, String newUserId, int mergeType) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.putAndAes("id2en", userId);
    ZPRequestBody.putAndAes("iden", newUserId);
    ZPRequestBody.put("hbType", mergeType);
    return mPersonalServices.accountMerge(ZPRequestBody);
  }

  /**
   * 获取首页资讯分类
   */
  public Observable<BaseResponse<List<NewsTypeItemData>>> getHomeNewsTypeList() {
    return mPersonalServices.getHomeNewsTypeList();
  }

  /**
   * 获取资讯详情
   */
  public Observable<BaseResponse<GzNewsDetailsData>> getGzNewsDetails(int userId, int newsId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("id", newsId);
    return mPersonalServices.getGzNewsDetails(ZPRequestBody);
  }

  /**
   * 获取推荐资讯列表
   */
  public Observable<BaseResponse<List<RecommendNewsItemData>>> getLinkRecommendNewsList(
      int newsSubTypeId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type2", newsSubTypeId);
    return mPersonalServices.getLinkRecommendNewsList(ZPRequestBody);
  }

  /**
   * 获取视频列表
   *
   * @param videoType 视频类型（1、公招公考，2、企业视频）
   */
  public Observable<BaseResponse<List<VideoItemData>>> getVideoList(int userId, int videoType,
      int pageIndex, int pageSize, String title) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", videoType);
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    ZPRequestBody.put("title", title);
    return mPersonalServices.getVideoList(ZPRequestBody);
  }

  /**
   * 获取视频详情
   */
  public Observable<BaseResponse<VideoItemData>> getVideoDetails(int videoId, int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", videoId);
    ZPRequestBody.put("userID", userId);
    return mPersonalServices.getVideoDetails(ZPRequestBody);
  }

  /**
   * 检查企业是否认证
   *
   * @param userId 用户id
   */
  public Observable<BaseResponse> checkEnterpriseIsCertification(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    return mPersonalServices.checkEnterpriseIsCertification(ZPRequestBody);
  }

  /**
   * 保存用户资讯类别排序
   */
  public Observable<BaseResponse> saveNewsTypeSort(int userId, String ids) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("listID", ids);
    return mPersonalServices.saveNewsTypeSort(ZPRequestBody);
  }

  /**
   * 获取已排序的资讯分类
   */
  public Observable<BaseResponse<List<NewsTypeItemData>>> getSortedNewsTypeList(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    return mPersonalServices.getSortedNewsTypeList(ZPRequestBody);
  }

  /**
   * 提交企业认证
   */
  public Observable<BaseResponse> submitEnterpriseCertification(int userId, String name, String pic) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("pic", pic);
    return mPersonalServices.submitEnterpriseCertification(
        EncryptReqParams.encryptMap(ZPRequestBody));
  }

  /**
   * 根据用户id获取公司名
   */
  public Observable<BaseResponse> getCompanyNameById(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getCompanyNameById(ZPRequestBody);
  }

  /**
   * 获取用户企业认证信息
   */
  public Observable<BaseResponse<CertificationData>> getCertificationInfo(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    return mPersonalServices.getCertificationInfo(ZPRequestBody);
  }

  /**
   * 获取公司规模信息
   */
  public Observable<BaseResponse<List<PickerOptionsData>>> getCompanySizeList() {
    return mPersonalServices.getCompanySizeList();
  }

  /**
   * 发布公告
   */
  public Observable<BaseResponse> postNotice(PostNoticeRequest postNoticeRequest) {
    return mPersonalServices.postNotice(postNoticeRequest);
  }

  /**
   * 获取用户基本信息
   */
  public Observable<BaseResponse<UserBasicInfoData>> getUserBasicInfo1(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    return mPersonalServices.getUserBasicInfo1(ZPRequestBody);
  }

  /**
   * 获取用户主页信息
   */
  public Observable<BaseResponse<UserHomeData>> getUserHomeInfo(int queryUserId, int queryCompanyId,
      int myUserId, boolean isOrg) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", queryUserId);
    ZPRequestBody.put("dwID", queryCompanyId);
    ZPRequestBody.put("myUserID", myUserId);
    if (isOrg) {
      ZPRequestBody.put("type", 1);
    }
    return mPersonalServices.getUserHomeInfo(ZPRequestBody);
  }

  /**
   * 获取用户发布的公告列表
   */
  public Observable<BaseResponse<List<UserNoticeItemData>>> getUserNoticeList(int queryCompanyId,
      int myUserId, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("dwID", queryCompanyId);
    ZPRequestBody.put("myUserID", myUserId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getUserNoticeList(ZPRequestBody);
  }

  /**
   * 获取用户发布的视频
   */
  public Observable<BaseResponse<List<VideoItemData>>> getUserVideoList(int queryUserId, int myUserId,
      int type, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", queryUserId);
    ZPRequestBody.put("myUserID", myUserId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getUserVideoList(ZPRequestBody);
  }

  /**
   * 提交回答
   *
   * @param userId          用户id
   * @param replyUserId     回复用户id
   * @param replyQuestionId 回复问题id
   * @param photos          图片列表
   */
  public Observable<BaseResponse<AnswerItemData>> submitAnswer(int userId, int replyUserId,
      int replyQuestionId, String content, String photos, boolean isClassicAnswer) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", 2);
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("toUserID", replyUserId);
    ZPRequestBody.put("toWDID", replyQuestionId);
    ZPRequestBody.put("title", content);
    ZPRequestBody.put("content", content);
    ZPRequestBody.put("listMedia", photos);
    ZPRequestBody.put("isJD", isClassicAnswer ? 1 : 0);
    return mPersonalServices.submitAnswer(ZPRequestBody);
  }

  /**
   * 提交问题
   */
  public Observable<BaseResponse> submitQuestion(int userId, int provinceID, int cityId, String title,
      String content) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", 1);
    ZPRequestBody.put("provinceID", provinceID);
    ZPRequestBody.put("cityID", cityId);
    ZPRequestBody.put("title", title);
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("content", content);
    return mPersonalServices.submitQuestion(ZPRequestBody);
  }

  /**
   * 获取推荐视频
   */
  public Observable<BaseResponse<List<VideoItemData>>> getRecommendVideo() {
    return mPersonalServices.getRecommendVideo();
  }

  /**
   * 获取用户问答列表
   *
   * @param queryUserId 查询用户id
   * @param myUserId    当前用户id
   */
  public Observable<BaseResponse<List<UserQuestionItemData>>> getUserQuestionList(int queryUserId,
      int myUserId, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", queryUserId);
    ZPRequestBody.put("myUserID", myUserId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getUserQuestionList(ZPRequestBody);
  }

  /**
   * 获取回答详情
   */
  public Observable<BaseResponse<AnswerDetailsData>> getAnswerDetails(int userId, int answerId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("WDID", answerId);
    return mPersonalServices.getAnswerDetails(ZPRequestBody);
  }

  /**
   * 获取粉丝列表
   *
   * @param currentUserId 登录的用户id
   */
  public Observable<BaseResponse<List<FansItemData>>> getUserFansList(int queryType, int queryId,
      int currentUserId, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", queryType);
    ZPRequestBody.put("glID", queryId);
    ZPRequestBody.put("myUserID", currentUserId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getUserFansList(ZPRequestBody);
  }

  /**
   * 获取用户关注列表
   */
  public Observable<BaseResponse<List<FollowItemData>>> getFollowList(int queryUserId,
      boolean isEnterprise, int myUserId, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", isEnterprise ? 0 : queryUserId);
    ZPRequestBody.put("dwID", isEnterprise ? queryUserId : 0);
    ZPRequestBody.put("myUserID", myUserId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getFollowList(ZPRequestBody);
  }

  /**
   * 获取我的收藏列表
   */
  public Observable<BaseResponse<List<UserHistoryItemData>>> getMyCollectionList(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getMyCollectionList(ZPRequestBody);
  }

  /**
   * 获取评论历史列表
   */
  public Observable<BaseResponse<List<UserHistoryItemData>>> getMyCommentList(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getMyCommentList(ZPRequestBody);
  }

  /**
   * 获取我的点赞列表
   */
  public Observable<BaseResponse<List<UserHistoryItemData>>> getMyLikeList(int userId, int pageIndex,
      int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getMyLikeList(ZPRequestBody);
  }

  /**
   * 删除我的发布
   *
   * @param deleteType 1、公告2、评论3、问答4、视频
   * @param subType    type为2时 1、资讯评论4、问答评论5、视频评论
   * @param deleteId   信息id
   */
  public Observable<BaseResponse> deleteMyPublish(int userId, int deleteType, int subType,
      int deleteId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("type", deleteType);
    ZPRequestBody.put("subType", subType);
    ZPRequestBody.put("glID", deleteId);
    return mPersonalServices.deleteMyPublish(ZPRequestBody);
  }

  /**
   * 清空我的浏览历史
   *
   * @param clearType 清空类型1、收藏2、点赞3、评论
   */
  public Observable<BaseResponse> clearMyHistory(int userId, int clearType) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("type", clearType);
    return mPersonalServices.clearMyHistory(ZPRequestBody);
  }

  /**
   * 根据坐标范围获取职位列表
   *
   * @param swlng 左上lng
   * @param swlat 左上lat
   * @param nelng 右下lng
   * @param nelat 右下lat
   */
  public Observable<BaseResponse<List<JobData>>> getJobListByLocation(double centerLng,
      double centerLat, double swlng, double swlat, double nelng, double nelat, String name
      , int jobFirstClassId, int jobSecondClassId, int moneyId, int workExpId, int workNatureId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("lng", centerLng);
    ZPRequestBody.put("lat", centerLat);
    ZPRequestBody.put("lng1", swlng);
    ZPRequestBody.put("lat1", swlat);
    ZPRequestBody.put("lng2", nelng);
    ZPRequestBody.put("lat2", nelat);
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("jid1", jobFirstClassId);
    ZPRequestBody.put("jid2", jobSecondClassId);
    ZPRequestBody.put("moneyid", moneyId);
    ZPRequestBody.put("expid", workExpId);
    ZPRequestBody.put("jnid", workNatureId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getJobListByLocation(ZPRequestBody);
  }

  /**
   * 发布视频
   *
   * @param type 1、公招公考2、企业视频3、个人视频
   */
  public Observable<BaseResponse> publishVideo(int type, int userId, String title, String content,
      String coverUrl, String videoUrl, int cityID, int moneyID, String address) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("cityID", cityID);
    ZPRequestBody.put("moneyID", moneyID);
    ZPRequestBody.put("address", address);
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("title", title);
    ZPRequestBody.put("content", content);
    ZPRequestBody.put("pic", coverUrl);
    ZPRequestBody.put("video", videoUrl);
    return mPersonalServices.publishVideo(ZPRequestBody);
  }

  /**
   * 获取简历置顶折扣
   */
  public Observable<BaseResponse<List<ResumeTopDiscountItemData>>> getResumeTopDiscount(int userId,
      int type) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("lanmu", type);
    return mPersonalServices.getResumeTopDiscount(ZPRequestBody);
  }

  /**
   * 创建简历置顶订单
   */
  public Observable<BaseResponse> createResumeTopOrder(int userId, int resumeId, int topDays) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("resid", resumeId);
    ZPRequestBody.put("days", topDays);
    return mPersonalServices.createResumeTopOrder(ZPRequestBody);
  }

  /**
   * 获取用户订单列表
   */
  public Observable<BaseResponse<List<OrderItemData>>> getOrderList(int userId, int type, int isPay,
      int isEffect, int invoiceID, int pageSize, int pageIndex) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("ispay", isPay);
    ZPRequestBody.put("iseffect", isEffect);
    ZPRequestBody.put("invoiceID", invoiceID);
    ZPRequestBody.put("pageSize", pageSize);
    ZPRequestBody.put("pageIndex", pageIndex);
    return mPersonalServices.getOrderList(ZPRequestBody);
  }

  /**
   * 生效订单
   */
  public Observable<BaseResponse> activateOrder(int userId, int orderId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("id", orderId);
    return mPersonalServices.activateOrder(ZPRequestBody);
  }

  /**
   * 删除订单
   */
  public Observable<BaseResponse> deleteOrder(int userId, int orderId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("uid", userId);
    ZPRequestBody.put("ids", orderId);
    return mPersonalServices.deleteOrder(ZPRequestBody);
  }

  /**
   * 获取问题详情
   *
   * @param questionId 问题id
   */
  public Observable<BaseResponse<QuestionData>> getQuestionDetails(int userId, int questionId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("WDID", questionId);
    return mPersonalServices.getQuestionDetails(ZPRequestBody);
  }

  /**
   * 获取回答列表
   *
   * @param questionId 问题id
   */
  public Observable<BaseResponse<List<AnswerItemData>>> getAnswerList(int userId, int questionId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("WDID", questionId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getAnswerList(ZPRequestBody);
  }

  /**
   * 获取问题可邀请回答用户列表
   */
  public Observable<BaseResponse<List<InviteUserItemData>>> getInviteUserList(int questionId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("wdID", questionId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getInviteUserList(ZPRequestBody);
  }

  /**
   * 邀请用户回答问题
   */
  public Observable<BaseResponse> inviteUserToAnswer(int userId, int questionId, String inviteIds) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("wdID", questionId);
    ZPRequestBody.put("userIDs", inviteIds);
    return mPersonalServices.inviteUserToAnswer(ZPRequestBody);
  }

  /**
   * 搜索用户
   */
  public Observable<BaseResponse<List<InviteUserItemData>>> searchUserByName(String name,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("nickName", name);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.searchUserByName(ZPRequestBody);
  }

  /**
   * 获取邀请问题列表
   */
  public Observable<BaseResponse<List<QuestionItemData>>> getInviteQuestionList(int userId,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getInviteQuestionList(ZPRequestBody);
  }

  /**
   * 获取推荐问题列表
   */
  public Observable<BaseResponse<List<QuestionItemData>>> getRecommendQuestionList(String linkIds,
      String title, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("lastWDIDs", linkIds);
    ZPRequestBody.put("title", title);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getRecommendQuestionList(ZPRequestBody);
  }

  /**
   * 获取问题列表
   */
  public Observable<BaseResponse<List<QuestionItemData>>> getQuestionList(String linkIds,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cNewIDs", linkIds);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getQuestionList(ZPRequestBody);
  }

  /**
   * 搜索问题
   *
   * @param keyword 关键字
   */
  public Observable<BaseResponse<List<QuestionItemData>>> searchQuestionByKeyword(String keyword) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("title", keyword);
    return mPersonalServices.searchQuestionByKeyword(ZPRequestBody);
  }

  /**
   * 上传用户搜索行为
   */
  public Observable<BaseResponse> uploadUserSearchAction(int userId, int type, String keyword) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("key", keyword);
    return mPersonalServices.uploadUserSearchAction(ZPRequestBody);
  }

  /**
   * 上传用户离线搜索行为
   */
  public Observable<BaseResponse> uploadUserOfflineSearchAction(int userId, String types,
      String keywords) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("types", types);
    ZPRequestBody.put("keys", keywords);
    return mPersonalServices.uploadUserOfflineSearchAction(ZPRequestBody);
  }

  /**
   * 获取推荐资讯列表
   */
  public Observable<NewsPageResponse> getRecommendNewsList(
      int userId, int countryId, int cityId, int provinceId, int attachCityId
      , String searchKeyword, int pageIndex, int pageSize,
      HomeNewsRequestOptions homeNewsRequestOptions) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("countyID", countryId);
    ZPRequestBody.put("cityID", cityId);
    ZPRequestBody.put("provinceID", provinceId);
    ZPRequestBody.put("tjCity", attachCityId);
    ZPRequestBody.put("type2", 0);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    ZPRequestBody.put("px", 0);
    ZPRequestBody.put("title", searchKeyword);
    if (homeNewsRequestOptions != null) {
      ZPRequestBody.put("wdID", homeNewsRequestOptions.getWdID());
      ZPRequestBody.put("videoID", homeNewsRequestOptions.getVideoID());
      ZPRequestBody.put("companyID", homeNewsRequestOptions.getCompanyID());
      ZPRequestBody.put("minID", homeNewsRequestOptions.getMinID());
      ZPRequestBody.put("minIDOther", homeNewsRequestOptions.getMinIDOther());
      ZPRequestBody.put("currentIDs", homeNewsRequestOptions.getCurrentIDs());
      ZPRequestBody.put("uaTypes", homeNewsRequestOptions.getUaTypes());
      ZPRequestBody.put("uaKeys", homeNewsRequestOptions.getUaKeys());
      ZPRequestBody.put("isCNews", homeNewsRequestOptions.isCNews());
      ZPRequestBody.put("otherId", homeNewsRequestOptions.getOtherId());
      ZPRequestBody.put("shenfen", homeNewsRequestOptions.getShenfen());
      ZPRequestBody.put("gxId", homeNewsRequestOptions.getGxId());
      if (homeNewsRequestOptions.getJobPageIndex() != 0) {
        ZPRequestBody.put("jobPageIndex", homeNewsRequestOptions.getJobPageIndex());
      }
      ZPRequestBody.put("resPageIndex", homeNewsRequestOptions.getResPageIndex());
      ZPRequestBody.put("flag", homeNewsRequestOptions.getFlag());
      ZPRequestBody.put("resProvince", homeNewsRequestOptions.getResProvince());
      ZPRequestBody.put("resCity", homeNewsRequestOptions.getResCity());
    }
    return mPersonalServices.getRecommendNewsList(ZPRequestBody);
  }

  /**
   * 获取资讯列表
   *
   * @param desc          排序（0）默认 （1）浏览量 （2）更新时间
   * @param searchKeyword 搜索关键词
   */
  public Observable<NewsPageResponse> getNewNewsList(int userId, int countryId, int cityId,
      int provinceId, int attachCityId, int newsType
      , int desc, String searchKeyword, int pageIndex, int pageSize,
      HomeNewsRequestOptions homeNewsRequestOptions, boolean isFollow, String wdIds) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    if (isFollow) {
      ZPRequestBody.put("subType", 1);
    }
    ZPRequestBody.put("userID", userId);
    ZPRequestBody.put("countyID", countryId);
    ZPRequestBody.put("cityID", cityId);
    ZPRequestBody.put("provinceID", provinceId);
    ZPRequestBody.put("tjCity", attachCityId);
    ZPRequestBody.put("type2", newsType);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    ZPRequestBody.put("px", desc);
    ZPRequestBody.put("title", searchKeyword);
    ZPRequestBody.put("lastWDIDs", wdIds);
    if (homeNewsRequestOptions != null) {
      ZPRequestBody.put("wdID", homeNewsRequestOptions.getWdID());
      ZPRequestBody.put("videoID", homeNewsRequestOptions.getVideoID());
      ZPRequestBody.put("companyID", homeNewsRequestOptions.getCompanyID());
      ZPRequestBody.put("minID", homeNewsRequestOptions.getMinID());
      ZPRequestBody.put("minIDOther", homeNewsRequestOptions.getMinIDOther());
      ZPRequestBody.put("currentIDs", homeNewsRequestOptions.getCurrentIDs());
    }
    return mPersonalServices.getNewNewsList(ZPRequestBody);
  }

  public Observable<BaseResponse<List<SearchHotKeyItemData>>> getSearchHotKey(int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("top", pageSize);
    return mPersonalServices.getSearchHotKey(ZPRequestBody);
  }

  /**
   * 检查隐私条款版本
   */
  public Observable<BaseResponse<VersionData>> checkAgreementVersion() {
    return mPersonalServices.checkAgreementVersion();
  }

  /**
   * 获取发布视频分类
   */
  public Observable<BaseResponse<List<VideoTypeItemData>>> getVideoTypeList() {
    return mPersonalServices.getPostVideoTypeList();
  }

  /**
   * 验证视频发布权限
   */
  public Observable<BaseResponse> verifyPublishVideo(int userID, int type) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userID);
    ZPRequestBody.put("type", type);
    return mPersonalServices.verifyPublishVideo(ZPRequestBody);
  }

  /**
   * 更新视频播放量
   */
  public Observable<BaseResponse> updateVideoCount(int videoId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", videoId);
    return mPersonalServices.updateVideoPlayCount(ZPRequestBody);
  }

  /**
   * 视频招聘报名
   *
   * @param videoId 视频id
   * @param name    报名人
   * @param phone   报名电话
   */
  public Observable<BaseResponse> videoRecruitSignUp(int videoId, String name, String phone) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("name", name);
    ZPRequestBody.put("id", videoId);
    ZPRequestBody.put("phone", phone);
    return mPersonalServices.videoRecruitSignUp(ZPRequestBody);
  }

  /**
   * 获取用户报名信息
   */
  public Observable<BaseResponse<UserSignInData>> getUserSignInInfo(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    return mPersonalServices.getUserSignInData(ZPRequestBody);
  }

  /**
   * 获取视频报名列表
   *
   * @param videoId  视频id
   * @param userType 报名状态
   */
  public Observable<BaseResponse<List<SignUpUserItemData>>> getSignUpUserList(int videoId,
      int userType, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", videoId);
    ZPRequestBody.put("type", userType);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getSignUpUserList(ZPRequestBody);
  }

  /**
   * 更新视频报名状态
   */
  public Observable<BaseResponse> updateSignUpUserStatus(int recordId, int status) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", recordId);
    ZPRequestBody.put("state", status);
    return mPersonalServices.updateSignUpUserStatus(ZPRequestBody);
  }

  /**
   * 检查是否存在未读视频消息
   */
  public Observable<BaseResponse> checkHasUnreadVideoMsg(int userId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userId);
    return mPersonalServices.checkHasUnreadVideoMsg(ZPRequestBody);
  }

  public Observable<BaseResponse<List<VideoUnreadMsgItemData>>> getVideoUnreadMsgList(int userID,
      int type, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userID);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getVideoUnreadMsgList(ZPRequestBody);
  }

  /**
   * 设置消息已读（v2）
   */
  public Observable<BaseResponse> setupMsgReadV2(int msgId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("id", msgId);
    return mPersonalServices.setupMsgReadV2(ZPRequestBody);
  }

  /**
   * 无数据后获取区县列表
   */
  public Observable<BaseResponse<List<NewsItemData>>> getDistrictNewsList(String newsIDs,
      int distractId, String keyword, int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("cNewIDs", newsIDs);
    ZPRequestBody.put("countyID", distractId);
    ZPRequestBody.put("title", keyword);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getDistrictNewsList(ZPRequestBody);
  }

  /**
   * 获取问答榜单列表
   */
  public Observable<BaseResponse<List<QARankItemData>>> getQARankList(int userID, int pageIndex,
      int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("myUserID", userID);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.getQARankList(ZPRequestBody);
  }

  /**
   * 获取公考资料列表
   */
  public Observable<BaseResponse<List<StudyNewsItemData>>> getStudyNewsList(int pageIndex,
      int pageSize, int type, String title) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    ZPRequestBody.put("type", type);
    ZPRequestBody.put("title", title);
    return mPersonalServices.getStudyNewsList(ZPRequestBody);
  }

  /**
   * 获取问答用户信息
   */
  public Observable<BaseResponse<QAUserData>> getQAUserInfo(int userID) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userID);
    return mPersonalServices.getQAUserInfo(ZPRequestBody);
  }

  /**
   * 获取学习资料详情
   */
  public Observable<BaseResponse<StudyNewsDetailsData>> getStudyNewsDetails(int userId, int newsId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("myUserID", userId);
    ZPRequestBody.put("id", newsId);
    return mPersonalServices.getStudyNewsDetails(ZPRequestBody);
  }

  /**
   * 上传用户本地行为
   */
  public Observable<BaseResponse> uploadUserLocalAction(int userID,
      List<UserActionRecord> userLocalActions) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userID);
    ZPRequestBody.put("list", userLocalActions);
    return mPersonalServices.uploadUserLocalAction(ZPRequestBody);
  }

  /**
   * 获取未回答问题列表
   */
  public Observable<BaseResponse<List<QuestionItemData>>> getUnansweredQuestionList() {
    return mPersonalServices.getUnansweredQuestionList();
  }

  /**
   * 搜索公考资料
   */
  public Observable<BaseResponse<List<StudyNewsItemData>>> searchStudyNews(String keyword,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("title", keyword);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.searchStudyNews(ZPRequestBody);
  }

  /**
   * 搜索职位
   */
  public Observable<BaseResponse<List<SearchJobResultItemData>>> searchJob(String keyword,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("title", keyword);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.searchJob(ZPRequestBody);
  }

  /**
   * 搜索公司
   */
  public Observable<BaseResponse<List<CompanyDetailsData>>> searchCompany(int userID, String keyword,
      int pageIndex, int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("userID", userID);
    ZPRequestBody.put("title", keyword);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.searchCompany(ZPRequestBody);
  }

  /**
   * 搜索视频
   */
  public Observable<BaseResponse<List<VideoItemData>>> searchVideo(String keyword, int pageIndex,
      int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("title", keyword);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.searchVideo(ZPRequestBody);
  }

  /**
   * 搜索问答资讯
   */
  public Observable<BaseResponse<List<QuestionItemData>>> searchQaNews(String keyword, int pageIndex,
      int pageSize) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("title", keyword);
    ZPRequestBody.put("pageIndex", pageIndex);
    ZPRequestBody.put("pageSize", pageSize);
    return mPersonalServices.searchQaNews(ZPRequestBody);
  }

  /**
   * 获取用户动态详情
   *
   * @param momentId 用户动态id
   */
  public Observable<BaseResponse<MomentDetailsData>> getMomentDetails(int userId, int momentId) {
    ZPRequestBody ZPRequestBody = new ZPRequestBody();
    ZPRequestBody.put("curUid", userId);
    ZPRequestBody.put("sjid", momentId);
    return mPersonalServices.getMomentDetails(ZPRequestBody);
  }
}
