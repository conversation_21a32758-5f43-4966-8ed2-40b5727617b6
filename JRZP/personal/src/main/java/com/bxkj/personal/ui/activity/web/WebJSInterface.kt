package com.bxkj.personal.ui.activity.web

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.webkit.JavascriptInterface
import com.bxkj.common.util.AESOperator
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.router.RouterNavigation.LoginActivity
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.web.BaseJSInterface.OnJSCallListener
import com.bxkj.jrzp.live.audience.ui.audience.LiveAudienceNavigation
import com.bxkj.jrzp.live.room.data.LiveRoomData
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsNavigation
import com.bxkj.personal.ui.activity.selectresume.SendResumeNavigation
import com.elvishew.xlog.XLog
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import kotlinx.coroutines.*

sealed class JSMethod(val methodName: String) {
  object Login : JSMethod("login")
  object Back : JSMethod("back")
  object Share : JSMethod("share")
  object ToUserHome : JSMethod("toUserHome")
  object ToLivePlayer : JSMethod("toLivePlayer")
  object ToJobDetails : JSMethod("toJobDetails")
  object ToSendResume : JSMethod("toSendResume")

  companion object {
    fun fromString(method: String): JSMethod? = when (method) {
      Login.methodName -> Login
      Back.methodName -> Back
      Share.methodName -> Share
      ToUserHome.methodName -> ToUserHome
      ToLivePlayer.methodName -> ToLivePlayer
      ToJobDetails.methodName -> ToJobDetails
      ToSendResume.methodName -> ToSendResume
      else -> null
    }
  }
}

/**
 */
class WebJSInterface(
  private val activity: Activity,
  private val callBack: (par: String) -> Unit,
  private val shareCallBack: (infoId: Int, shareUrl: String) -> Unit,
  private var mOnJSCallListener: OnJSCallListener? = null
) {

  private var mHandler = Handler(Looper.getMainLooper())
  private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

  @JavascriptInterface
  fun callHandler(method: String?) {
    callHandler(method, null, null)
  }

  @JavascriptInterface
  fun callHandler(method: String?, params: String?) {
    callHandler(method, params, null)
  }

  @JavascriptInterface
  fun callHandler(method: String?, params: String?, callback: Any?) {
    try {
      XLog.d("method:$method---params:$params")

      if (method == null) {
        XLog.e("Method is null")
        return
      }

      val jsMethod = JSMethod.fromString(method)
      if (jsMethod == null) {
        invokeMethodOnMainHandler {
          mOnJSCallListener?.onJSCall(method, convertParams(params))
        }
        return
      }

      val jsonObj = parseParams(params)
      handleJSMethod(jsMethod, jsonObj)
    } catch (e: Exception) {
      XLog.e("Error handling JS method: ${e.message}")
      e.printStackTrace()
    }
  }

  private fun parseParams(params: String?): JsonObject? {
    return params?.takeIf { it != "undefined" }?.let {
      try {
        JsonParser.parseString(it).asJsonObject
      } catch (e: Exception) {
        XLog.e("Error parsing params: ${e.message}")
        null
      }
    }
  }

  private fun handleJSMethod(method: JSMethod, params: JsonObject?) {
    coroutineScope.launch(Dispatchers.Main + CoroutineExceptionHandler { _, throwable ->
      XLog.e("Error in handleJSMethod: ${throwable.message}")
      throwable.printStackTrace()
    }) {
      when (method) {
        is JSMethod.Login -> handleLogin(params)
        is JSMethod.Back -> activity.finish()
        is JSMethod.Share -> handleShare(params)
        is JSMethod.ToUserHome -> handleToUserHome(params)
        is JSMethod.ToLivePlayer -> handleToLivePlayer(params)
        is JSMethod.ToJobDetails -> handleToJobDetails(params)
        is JSMethod.ToSendResume -> handleToSendResume(params)
      }
    }
  }

  private fun handleLogin(params: JsonObject?) {
    params?.let {
      val url = it.get("url")
      val urlString = if (url.isJsonNull) "" else url.asString
      this.callBack(urlString)
    }
    invokeMethodOnMainHandler {
      Router.getInstance().to(LoginActivity.URL)
        .startForResult(activity, TO_LOGIN_CODE)
    }
  }

  private fun handleShare(params: JsonObject?) {
    params?.let {
      val infoId = it.get("courseid").asInt
      val shareUrl = it.get("url").asString
      invokeMethodOnMainHandler {
        shareCallBack.invoke(infoId, shareUrl)
      }
    }
  }

  private fun handleToUserHome(params: JsonObject?) {
    params?.let {
      val userID = it.get("userID").asInt
      invokeMethodOnMainHandler {
        UserHomeNavigation.navigate(userID)
          .startForResult(activity, WebActivity.TO_USER_HOME_CODE)
      }
    }
  }

  private fun handleToLivePlayer(params: JsonObject?) {
    params?.let {
      val liveID = it.get("liveID").asInt
      val roomID = it.get("roomID").asString
      invokeMethodOnMainHandler {
        if (UserUtils.logged()) {
          LiveAudienceNavigation.navigate(LiveRoomData.get(liveID, roomID))
            .start()
        } else {
          Router.getInstance().to(LoginActivity.URL).start()
        }
      }
    }
  }

  private fun handleToJobDetails(params: JsonObject?) {
    params?.let {
      val jobID = it.get("jobID").asInt
      invokeMethodOnMainHandler {
        JobDetailsNavigation.navigate(jobID).start()
      }
    }
  }

  private fun handleToSendResume(params: JsonObject?) {
    params?.let {
      val jobID = it.get("jobID").asInt
      val companyUserID = it.get("companyUserID").asInt
      SendResumeNavigation.navigate(jobID, companyUserID).start()
    }
  }

  private fun convertParams(params: String?): JsonObject? {
    var paramsObject: JsonObject? = null
    if (!params.isNullOrEmpty()) {
      if (params != "undefined") {
        paramsObject = JsonParser.parseString(params).asJsonObject
      }
    }
    return paramsObject
  }

  @JavascriptInterface
  fun getUserToken(): String = try {
    if (UserUtils.logged()) {
      AESOperator.safeEncrypt(UserUtils.getUserId().toString())
    } else ""
  } catch (e: Exception) {
    XLog.e("Error in getUserToken: ${e.message}")
    ""
  }

  @JavascriptInterface
  fun getUserId(): String {
    if (UserUtils.logged()) {
      return UserUtils.getUserId().toString()
    } else {
      return ""
    }
  }

  @JavascriptInterface
  fun getUserOriginToken(): String = try {
    if (UserUtils.logged()) {
      UserUtils.getUserToken()
    } else ""
  } catch (e: Exception) {
    XLog.e("Error in getUserOriginToken: ${e.message}")
    ""
  }

  private fun invokeMethodOnMainHandler(method: () -> Unit) {
    mHandler.post {
      method.invoke()
    }
  }

  fun onDestroy() {
    coroutineScope.cancel()
    mOnJSCallListener = null
  }

  companion object {
    const val TO_LOGIN_CODE = 100
  }
}