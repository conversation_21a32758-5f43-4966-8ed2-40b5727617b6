package com.bxkj.personal.ui.fragment.study

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.databinding.IncludeMvvmRefreshLayoutBinding
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.personal.data.StudyNewsItemData
import com.bxkj.personal.ui.activity.studynewsdetails.StudyNewsDetailsActivity
import com.bxkj.personal.ui.activity.typenews.TypeNewsActivity

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/4/1
 * @version: V1.0
 */
class StudyFragment : BaseDBFragment<IncludeMvvmRefreshLayoutBinding, StudyViewModel>() {

    companion object {
        fun newInstance(): Fragment {
            return StudyFragment()
        }
    }

    override fun getViewModelClass(): Class<StudyViewModel> = StudyViewModel::class.java

    override fun getLayoutId(): Int = R.layout.include_mvvm_refresh_layout

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.listViewModel = viewModel.studyNewsListViewModel

        setupLearnNewsListAdapter()
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        viewModel.start()
    }

    private fun setupLearnNewsListAdapter() {
        val studyNewsListAdapter = object : SimpleDBListAdapter<StudyNewsItemData>(parentActivity, R.layout.personal_recycler_study_news_item) {
            private var lastPosition = 0
            override fun convert(holder: SuperViewHolder, viewType: Int, item: StudyNewsItemData, position: Int) {
                super.convert(holder, viewType, item, position)
                if (position % 15 == 10) {
                    if (lastPosition < position)
                        viewModel.loadMore()
                }
                lastPosition = position
            }
        }.apply {
            setOnItemClickListener(object :
              SuperItemClickListener {
                override fun onClick(v: View, position: Int) {
                    val item = data[position]
                    if (v.id == R.id.tv_type) {
                        startActivity(TypeNewsActivity.newIntent(parentActivity, item.typeId, item.typeName, true))
                    } else {
                        startActivity(StudyNewsDetailsActivity.newIntent(parentActivity, data[position].id))
                    }
                }
            }, R.id.tv_type)
        }
        viewBinding.recyclerContent.layoutManager = LinearLayoutManager(parentActivity)
        viewModel.studyNewsListViewModel.setAdapter(studyNewsListAdapter)
    }
}