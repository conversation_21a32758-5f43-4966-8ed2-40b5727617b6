package com.bxkj.personal.api;

import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.data.EncryptReqParams;
import com.bxkj.common.data.PickerOptionsData;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.ZPRequestBody;
import com.bxkj.common.widget.filterpopup.FilterOptionData;
import com.bxkj.jrzp.support.comment.data.CommentItemData;
import com.bxkj.jrzp.support.comment.data.CommentSuccessResultData;
import com.bxkj.jrzp.user.data.JobData;
import com.bxkj.jrzp.user.mine.data.ServiceItemData;
import com.bxkj.jrzp.user.mine.data.UserHomeData;
import com.bxkj.jrzp.userhome.data.UserQuestionItemData;
import com.bxkj.personal.data.AboutUsData;
import com.bxkj.personal.data.AnswerDetailsData;
import com.bxkj.personal.data.AnswerItemData;
import com.bxkj.personal.data.CertificateItemData;
import com.bxkj.personal.data.CertificationData;
import com.bxkj.personal.data.CollectCompanyItemData;
import com.bxkj.personal.data.CompanyDetailsData;
import com.bxkj.personal.data.CreateResumeData;
import com.bxkj.personal.data.DefaultAvatarItemData;
import com.bxkj.personal.data.DepartmentItemData;
import com.bxkj.personal.data.EduBackgroundData;
import com.bxkj.personal.data.EduBackgroundItemData;
import com.bxkj.personal.data.FacultyRecruitDetailsData;
import com.bxkj.personal.data.FansItemData;
import com.bxkj.personal.data.FeedbackMsgContentItemData;
import com.bxkj.personal.data.FeedbackMsgItemData;
import com.bxkj.personal.data.FollowItemData;
import com.bxkj.personal.data.FollowedJobFairItemData;
import com.bxkj.personal.data.GzNewsDetailsData;
import com.bxkj.personal.data.InvitationsToDeliveryItemData;
import com.bxkj.personal.data.InviteUserItemData;
import com.bxkj.personal.data.JobDetailsData;
import com.bxkj.personal.data.LanguageSkillsItemData;
import com.bxkj.personal.data.MessageItemData;
import com.bxkj.personal.data.MomentDetailsData;
import com.bxkj.personal.data.MyCollectionJobData;
import com.bxkj.personal.data.NewsData;
import com.bxkj.personal.data.NewsDetailsData;
import com.bxkj.personal.data.NewsItemData;
import com.bxkj.personal.data.NewsPageResponse;
import com.bxkj.personal.data.NewsTypeItemData;
import com.bxkj.personal.data.OrderItemData;
import com.bxkj.personal.data.PaidUserItemData;
import com.bxkj.personal.data.ProfessionalSkillItemData;
import com.bxkj.personal.data.QARankItemData;
import com.bxkj.personal.data.QAUserData;
import com.bxkj.personal.data.QuestionData;
import com.bxkj.personal.data.QuestionItemData;
import com.bxkj.personal.data.QuickRecruitmentData;
import com.bxkj.personal.data.QuickRecruitmentPublishData;
import com.bxkj.personal.data.RechargeDiscountItemData;
import com.bxkj.personal.data.RecommendNewsItemData;
import com.bxkj.personal.data.ResumeBasicData;
import com.bxkj.personal.data.ResumeDefaultData;
import com.bxkj.personal.data.ResumeDeliveryRecordBean;
import com.bxkj.personal.data.ResumeItemData;
import com.bxkj.personal.data.ResumePersonalData;
import com.bxkj.personal.data.ResumeTopDiscountItemData;
import com.bxkj.personal.data.SchoolMateItemData;
import com.bxkj.personal.data.SchoolRecruitDetailsData;
import com.bxkj.personal.data.SchoolSituationItemData;
import com.bxkj.personal.data.SearchHotKeyItemData;
import com.bxkj.personal.data.SearchJobResultItemData;
import com.bxkj.personal.data.SeenMeBusinessData;
import com.bxkj.personal.data.SignUpUserItemData;
import com.bxkj.personal.data.StudyNewsDetailsData;
import com.bxkj.personal.data.StudyNewsItemData;
import com.bxkj.personal.data.SubscriptionItemData;
import com.bxkj.personal.data.SystemMsgData;
import com.bxkj.personal.data.UniversityIdData;
import com.bxkj.personal.data.UniversityItemData;
import com.bxkj.personal.data.UserBasicInfoData;
import com.bxkj.personal.data.UserCenterPersonalData;
import com.bxkj.personal.data.UserHistoryItemData;
import com.bxkj.personal.data.UserInfoData;
import com.bxkj.personal.data.UserMomentsItemData;
import com.bxkj.personal.data.UserNoticeItemData;
import com.bxkj.personal.data.UserResumeData;
import com.bxkj.personal.data.UserSignInData;
import com.bxkj.personal.data.VersionData;
import com.bxkj.personal.data.VideoItemData;
import com.bxkj.personal.data.VideoTypeItemData;
import com.bxkj.personal.data.VideoUnreadMsgItemData;
import com.bxkj.personal.data.WechatLoginResultData;
import com.bxkj.personal.data.WorkExpData;
import com.bxkj.personal.data.WorkExpItemData;
import com.bxkj.personal.ui.activity.personalbasicinformation.PersonalBasicInfoData;
import com.bxkj.personal.ui.activity.personaldetailsinformation.PersonalDetailsInfoData;
import com.bxkj.personal.ui.activity.postnotice.PostNoticeRequest;
import com.bxkj.video.data.VideoData;
import com.bxkj.video.openapi.OpenVideoApiConstants;
import io.reactivex.Observable;
import java.util.List;
import okhttp3.MultipartBody;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.Url;

/**
 * @date 2018/3/30
 */

public interface PersonalServices {

  @POST(OpenVideoApiConstants.I_GET_INFO_ATTACH_VIDEOS)
  Observable<BaseResponse<List<VideoData>>> getResumeAttachVideo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_JOB_LIST)
  Observable<BaseResponse<List<JobData>>> getHomeJobList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_NATURE_OF_COMPANY)
  Observable<BaseResponse<List<FilterOptionData>>> getNaturesOfCompany();

  @POST(PersonalApiConstants.I_GET_WORKING_EXP)
  Observable<BaseResponse<List<FilterOptionData>>> getWorkingExp();

  @POST(PersonalApiConstants.I_GET_EDUCATION)
  Observable<BaseResponse<List<FilterOptionData>>> getEducation();

  @POST(PersonalApiConstants.I_GET_SALARY_RANGE)
  Observable<BaseResponse<List<FilterOptionData>>> getSalaryRange();

  @POST(PersonalApiConstants.I_GET_JOB_LIST)
  Observable<BaseResponse<List<JobData>>> getJobList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_JOBS_COUNT)
  Observable<BaseResponse> getJobsCountByTitle(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_JOB_DETAILS_INFO)
  Observable<BaseResponse<JobDetailsData>> getJobDetailsInfo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CHECK_JOB_COLLECTION)
  Observable<BaseResponse> checkJobCollection(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_COLLECTION_JOB)
  Observable<BaseResponse> collectionJob(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UNCOLLECTION_JOB)
  Observable<BaseResponse> uncollectionJob(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_AVAILABLE_RESUME_LIST)
  Observable<BaseResponse<List<ResumeItemData>>> getAvailableResumeList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SUBMIT_RESUME)
  Observable<BaseResponse> submitResume(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_COMPANY_DETAILS_INFO)
  Observable<BaseResponse<CompanyDetailsData>> getCompanyDetailsData(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_NEWS_LIST)
  Observable<BaseResponse<List<NewsData>>> getNewsList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_NEWS_DETAILS)
  Observable<BaseResponse<NewsDetailsData>> getNewsDetails(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SUBSCRIPTION_LIST)
  Observable<BaseResponse<List<SubscriptionItemData>>> getUserSubscriptionList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_QUICK_RECRUITMENT)
  Observable<BaseResponse<List<QuickRecruitmentData>>> getQuickRecruitmentList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_QUICK_RECRUITMENT)
  Observable<BaseResponse> addQuickRecruitment(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_QUICK_RECRUITMENT)
  Observable<BaseResponse> updateQuickRecruitment(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_QUICK_RECRUITMENT)
  Observable<BaseResponse> deleteQuickRecruitment(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_ADDRESS_INFO_BY_ADDRESS_NAME)
  Observable<BaseResponse<AreaOptionsData>> getAddressInfoByAddressName(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_QUICK_RECRUITMENT_DETAILS)
  Observable<BaseResponse<QuickRecruitmentPublishData>> getQuickRecruitmentDetails(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_QUICK_RECRUITMENT_LOGIN)
  Observable<BaseResponse> quickRecruitmentLogin(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_CENTER_PERSONAL_DATA)
  Observable<BaseResponse<UserCenterPersonalData>> getUserCenterPersonalData(
      @Body ZPRequestBody ZPRequestBody);

  @POST(OpenVideoApiConstants.I_GET_INFO_ATTACH_VIDEOS)
  Observable<BaseResponse<List<VideoData>>> getInfoLinkVideos(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_COLLECTION_JOB_COUNT)
  Observable<BaseResponse> getUserCollectionJobCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_SUBMIT_RECORD_COUNT)
  Observable<BaseResponse> getUserSubmitRecordCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_APPLY_RECORD)
  Observable<BaseResponse<List<ResumeDeliveryRecordBean>>> getUserApplyRecord(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_BATCH_DELETION_SUBMIT_RECORD)
  Observable<BaseResponse> batchDeletionSubmitRecord(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_COLLECTION_JOBS)
  Observable<BaseResponse<List<MyCollectionJobData>>> getUserCollectionJobs(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_RESUME_LIST)
  Observable<BaseResponse<List<UserResumeData>>> getUserResumeList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_RESUME)
  Observable<BaseResponse> deleteResume(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_RESUME_INFO)
  Observable<BaseResponse<ResumeBasicData>> getResumeInfo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_REFRESH_RESUME)
  Observable<BaseResponse> refreshResume(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_RESUME_NAME)
  Observable<BaseResponse> updateResumeName(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_RESUME_OPEN_STATE)
  Observable<BaseResponse> updateResumeOpenState(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SET_UP_RESUME_IS_DEFAULT)
  Observable<BaseResponse> setUpResumeIsDefault(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SETUP_RESUME_AUTO_REFRESH)
  Observable<BaseResponse> setupResumeAutoRefresh(@Body EncryptReqParams encryptReqParams);

  @POST(PersonalApiConstants.I_GET_WORK_EXP_LIST)
  Observable<BaseResponse<List<WorkExpItemData>>> getWorkExpList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_EDU_BACKGROUND_LIST)
  Observable<BaseResponse<List<EduBackgroundItemData>>> getEduBackgroundList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_RESUME_CAREER_OBJECTIVE)
  Observable<BaseResponse> updateCareerObjective(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_INDUSTRY_LIST)
  Observable<BaseResponse<List<PickerOptionsData>>> getIndustryList();

  @POST(PersonalApiConstants.I_GET_WORK_EXP_DETAILS)
  Observable<BaseResponse<WorkExpData>> getWorkExpDetails(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_WORK_EXP)
  Observable<BaseResponse> addWorkExp(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_WORK_EXP)
  Observable<BaseResponse> updateWorkExp(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_WORK_EXP)
  Observable<BaseResponse> deleteWorkExp(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_EDU_BACKGROUND_DETAILS)
  Observable<BaseResponse<EduBackgroundData>> getEduBackgroundDetails(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_EDU_BACKGROUND)
  Observable<BaseResponse> addEduBackground(@Body ZPRequestBody requestBody);

  @POST(PersonalApiConstants.I_UPDATE_EDU_BACKGROUND)
  Observable<BaseResponse> updateEduBackground(@Body ZPRequestBody requestBody);

  @POST(PersonalApiConstants.I_DELETE_EDU_BACKGROUND)
  Observable<BaseResponse> deleteEduBackground(@Body ZPRequestBody requestBody);

  @POST(PersonalApiConstants.I_UPDATE_SELF_EVALUATION)
  Observable<BaseResponse> updateSelfEvaluation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_PROFESSIONAL_SKILL_LIST)
  Observable<BaseResponse<List<ProfessionalSkillItemData>>> getProfessionalSkillList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_PROFESSIONAL_SKILL_DETAILS)
  Observable<BaseResponse<ProfessionalSkillItemData>> getProfessionalSkillDetails(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_PROFESSIONAL_SKILL)
  Observable<BaseResponse> addProfessionalSkill(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_PROFESSIONAL_SKILL)
  Observable<BaseResponse> updateProfessionalSkill(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_LANGUAGE_SKILLS_LIST)
  Observable<BaseResponse<List<LanguageSkillsItemData>>> getLanguageSkillsList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_LANGUAGE_SKILLS_DETAILS)
  Observable<BaseResponse<LanguageSkillsItemData>> getLanguageSkillsDetails(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_LANGUAGE_SKILLS)
  Observable<BaseResponse> addLanguageSkills(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_LANGUAGE_SKILLS)
  Observable<BaseResponse> updateLanguageSkills(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_LANGUAGE_SKILLS)
  Observable<BaseResponse> deleteLanguageSkills(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SCHOOL_SITUATION_LIST)
  Observable<BaseResponse<List<SchoolSituationItemData>>> getSchoolSituationList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SCHOOL_SITUATION_DETAILS)
  Observable<BaseResponse<SchoolSituationItemData>> getSchoolSituationDetails(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_SCHOOL_SITUATION)
  Observable<BaseResponse> addSchoolSituation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_SCHOOL_SITUATION)
  Observable<BaseResponse> updateSchoolSituation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_SCHOOL_SITUATION)
  Observable<BaseResponse> deleteSchoolSituation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_CERTIFICATE_LIST)
  Observable<BaseResponse<List<CertificateItemData>>> getCertificateList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_CERTIFICATE_DETAILS)
  Observable<BaseResponse<CertificateItemData>> getCertificateDetails(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_CERTIFICATE)
  Observable<BaseResponse> addCertificate(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_CERTIFICATE)
  Observable<BaseResponse> updateCertificate(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_CERTIFICATE)
  Observable<BaseResponse> deleteCertificate(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_PROFESSIONAL_SKILL)
  Observable<BaseResponse> deleteProfessionalSkill(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_PROFESSIONAL_LIST)
  Observable<BaseResponse<List<PickerOptionsData>>> getProfessionalList();

  @POST(PersonalApiConstants.I_SUBMIT_FEEDBACK)
  Observable<BaseResponse> submitFeedback(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_ABOUT_US_INFO)
  Observable<BaseResponse<AboutUsData>> getAboutUsInfo();

  @POST(PersonalApiConstants.I_GET_NEWS_TYPE_TWO)
  Observable<BaseResponse<List<FilterOptionData>>> getNewsTypeTwo();

  @POST(PersonalApiConstants.I_GET_SUBSCRIPTION_MESSAGE_LIST)
  Observable<BaseResponse<List<MessageItemData>>> getSubscriptionMessageList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CHECK_VERSION_INFO)
  Observable<BaseResponse<VersionData>> checkVersionInfo();

  @POST(PersonalApiConstants.I_GET_USER_SUBSCRIPTION_COUNT)
  Observable<BaseResponse> getUserSubscriptionCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SUBSCRIPTION_MESSAGE_COUNT)
  Observable<BaseResponse> getSubscriptionMessageCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CHANGE_MESSAGE_STATUS)
  Observable<BaseResponse> changeMessageStatus(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_FEEDBACK_MSG_STATE)
  Observable<BaseResponse> updateFeedbackMsgState(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ACCEPT_DELIVERY_INVITATION)
  Observable<BaseResponse> acceptDeliveryInvitation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_REFUSE_DELIVERY_INVITATION)
  Observable<BaseResponse> refuseDeliveryInvitation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ACCEPT_INTERVIEW_INVITATION)
  Observable<BaseResponse> acceptInterviewInvitation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_REFUSE_INTERVIEW_INVITATION)
  Observable<BaseResponse> refuseInterviewInvitation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_INVITATIONS_TO_DELIVERY_LIST)
  Observable<BaseResponse<List<InvitationsToDeliveryItemData>>> getInvitationsToDeliveryList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_INVITAIONS_TO_DELIVERY_COUNT)
  Observable<BaseResponse> getInvitationsToDeliveryCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_FEEDBACK_NOTICE_LIST)
  Observable<BaseResponse<List<FeedbackMsgItemData>>> getFeedbackNoticeList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_FEEDBACK_MSG_CONTENT_BY_COMPANY)
  Observable<BaseResponse<List<FeedbackMsgContentItemData>>> getFeedbackMsgContentByCompany(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SEEN_MY_BUSINESS_LIST)
  Observable<BaseResponse<List<SeenMeBusinessData>>> getSewMeCompanyList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SEW_ME_COMPANY_COUNT)
  Observable<BaseResponse> getSewMeCompanyCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_MSG_NOTIFICATION_LIST)
  Observable<BaseResponse<List<FeedbackMsgItemData>>> getMsgNotificationList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_MSG_NOTIFICATION_CONTENT_LIST_BY_COMPANY)
  Observable<BaseResponse<List<FeedbackMsgContentItemData>>> getMsgNotificationContentListByCompany(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SYSTEM_MSG_LIST)
  Observable<BaseResponse<List<SystemMsgData>>> getSystemMsgList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_PERSONAL_BASIC_INFORMATION)
  Observable<BaseResponse> updatePersonalBasicInformation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_NATION_LIST)
  Observable<BaseResponse<List<PickerOptionsData>>> getNationList();

  @POST(PersonalApiConstants.I_UPDATE_PERSONAL_DETAILS_INFORMATION)
  Observable<BaseResponse> updatePersonalDetailsInformation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_PERSONAL_DETAILS_INFORMATION)
  Observable<BaseResponse<PersonalDetailsInfoData>> getPersonalDetailsInfo(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_RESUME_PERSONAL_DATA)
  Observable<BaseResponse<ResumePersonalData>> getResumePersonalData(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_RESUME_CAREER_OBJECTIVE)
  Observable<BaseResponse> createResumeOneStep(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_RESUME_CAREER_OBJECTIVE)
  Observable<BaseResponse<CreateResumeData>> getResumeCareerObjective(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_RESUME_CAREER_OBJECTIVE_NEW)
  Observable<BaseResponse> updateResumeCareerObjective(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_RESUME_WORK_EXP)
  Observable<BaseResponse> addResumeWorkExp(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_RESUME_EDU_BG)
  Observable<BaseResponse> addResumeEduBg(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_COLLECTION_COMPANY_COUNT)
  Observable<BaseResponse> getUserCollectionCompanyCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_MASSAGE_STATUS)
  Observable<BaseResponse> updateMessageState(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CHECK_COLLECT_THE_COMPANY)
  Observable<BaseResponse> checkCollectTheCompany(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_COLLECT_THE_COMPANY)
  Observable<BaseResponse> collectTheCompany(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CANCEL_COLLECT_THE_COMPANY)
  Observable<BaseResponse> cancelCollectTheCompany(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_COLLECT_COMPANY_LIST)
  Observable<BaseResponse<List<CollectCompanyItemData>>> getCollectCompanyList(
      @Body ZPRequestBody ZPRequestBody);

  @POST
  Observable<BaseResponse> uploadAttachmentResume(@Url String url, @Body MultipartBody requestBody);

  @POST
  Observable<BaseResponse> deleteAttachmentResume(@Url String url, @Body MultipartBody multipartBody);

  @POST(PersonalApiConstants.I_UPDATE_RESUME_PERSONAL_DATA)
  Observable<BaseResponse> updateResumePersonalData(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_RESUME_PERSONAL_DATA_CAREER_OBJECT)
  Observable<BaseResponse> updateResumePersonalDatCareerObject(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_UNREAD_MSG_NOTICE_COUNT)
  Observable<BaseResponse> getUnreadMsgNoticeCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_UNREAD_SYSTEM_MSG_COUNT)
  Observable<BaseResponse> getUnreadSystemMsgCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_SYSTEM_MSG_READ_STATE)
  Observable<BaseResponse> updateSystemMsgReadState(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_UNREAD_SUB_MSG_COUNT)
  Observable<BaseResponse> getUnreadSubMsgCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_UNREAD_VIEW_ME_COUNT)
  Observable<BaseResponse> getUnreadSewMeCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_SEW_ME_READ_STATE)
  Observable<BaseResponse> updateSewMeReadState(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SCAN_QR_CODE_LOGIN)
  Observable<BaseResponse> scanQrCodeLogin(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SERVICE_LIST)
  Observable<BaseResponse<List<ServiceItemData>>> getServiceList();

  @POST(PersonalApiConstants.I_CHECK_SYSTEM_STATUS)
  Observable<BaseResponse> checkSystemStatus();

  @POST(PersonalApiConstants.I_CHECK_INFO_IS_PERFECTED)
  Observable<BaseResponse> checkInfoIsPerfected(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_EDIT_COMPANY_INFO)
  Observable<BaseResponse> editCompanyInfo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CHECK_COMPANY_INFO_PERFECTED)
  Observable<BaseResponse> checkCompanyInfoPerfected(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_COMMENT_LIST)
  Observable<BaseResponse<List<CommentItemData>>> getCommentList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_FACULTY_RECRUIT_DETAILS)
  Observable<BaseResponse<FacultyRecruitDetailsData>> getFacultyRecruitDetails(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SCHOOL_RECRUIT_DETAILS)
  Observable<BaseResponse<SchoolRecruitDetailsData>> getSchoolRecruitDetails(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CHECK_IS_FOLLOW)
  Observable<BaseResponse> checkIsFollow(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_LIKE_OR_UNLIKE_THE_COMMENT)
  Observable<BaseResponse> likeOrUnlikeTheComment(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_COMMENT)
  Observable<BaseResponse<CommentSuccessResultData>> addComment(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_UNIVERSITY_LIST)
  Observable<BaseResponse<List<UniversityItemData>>> getUniversityList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_UNIVERSITY_ID)
  Observable<BaseResponse<UniversityIdData>> getUniversityId(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_DEPARTMENT_LIST)
  Observable<BaseResponse<List<DepartmentItemData>>> getDepartmentList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_RECHARGE_DISCOUNT_LIST)
  Observable<BaseResponse<List<RechargeDiscountItemData>>> getRechargeDiscountList();

  @POST
  Observable<BaseResponse> uploadMomentPhoto(@Url String url, @Body MultipartBody body);

  @POST(PersonalApiConstants.I_POST_NEWS)
  Observable<BaseResponse> postNews(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_OR_REMOVE_COLLECTION)
  Observable<BaseResponse> addOrRemoveCollection(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ADD_OR_REMOVE_FOLLOW)
  Observable<BaseResponse> addOrRemoveFollow(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_ALIPAY_ORDER_INFO)
  Observable<BaseResponse> getAlipayOrderInfo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CREATE_PAYMENT_ORDER)
  Observable<BaseResponse> createPaymentOrder(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SCHOOLMATE_LIST)
  Observable<BaseResponse<List<SchoolMateItemData>>> getSchoolMateList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_PAYMENT_RESULT)
  Observable<BaseResponse<OrderItemData>> getPaymentResult(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_PROFESSION_LIST)
  Observable<BaseResponse<List<DepartmentItemData>>> getProfessionList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_CLASS_LIST)
  Observable<BaseResponse<List<DepartmentItemData>>> getClassList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_USER_BASIC_INFO)
  Observable<BaseResponse> updateUserBasicInfo(@Body EncryptReqParams encryptReqParams);

  @POST(PersonalApiConstants.I_CHECK_USER_INFO_IS_COMPLETE)
  Observable<BaseResponse> checkUserInfoIsComplete(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CREATE_RESUME)
  Observable<BaseResponse> createResume(@Body CreateResumeData resumeInfo);

  @POST(PersonalApiConstants.I_GET_CREATE_RESUME_DEFAULT_INFO)
  Observable<BaseResponse<ResumeDefaultData>> getCreateResumeDefaultInfo(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_FOLLOWED_JOB_FAIR_LIST)
  Observable<BaseResponse<List<FollowedJobFairItemData>>> getFollowedJobFairList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_PAID_USER_LIST)
  Observable<BaseResponse<List<PaidUserItemData>>> getPaidUserList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_REWARD_HISTORY_LIST)
  Observable<BaseResponse<List<UserMomentsItemData>>> getRewardHistoryList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_MOMENT)
  Observable<BaseResponse> deleteMoment(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_COMMENT)
  Observable<BaseResponse> deleteComment(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_LOGIN_BY_WECHAT)
  Observable<BaseResponse<WechatLoginResultData>> loginByWechat(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CHECK_AVATAR_IS_UPLOAD)
  Observable<BaseResponse> checkAvatarIsUpload(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_AVATAR_AND_NICKNAME)
  Observable<BaseResponse<UserInfoData>> getUserAvatarAndNickname(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_DEFAULT_AVATAR_LIST)
  Observable<BaseResponse<List<DefaultAvatarItemData>>> getDefaultAvatarList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_AVATAR_AND_NICKNAME)
  Observable<BaseResponse> updateAvatarAndNickname(@Body UserInfoData userInfoData);

  @POST(PersonalApiConstants.I_CHECK_IS_BIND_MOBILE)
  Observable<BaseResponse> checkIsBindMobile(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_BIND_MOBILE_NUMBER)
  Observable<BaseResponse> bindMobileNumber(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ACCOUNT_MERGE)
  Observable<BaseResponse<String>> accountMerge(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_NEWS_TYPE_LIST)
  Observable<BaseResponse<List<NewsTypeItemData>>> getHomeNewsTypeList();

  @POST(PersonalApiConstants.I_GET_NEW_NEWS_LIST)
  Observable<NewsPageResponse> getNewNewsList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_GZ_NEWS_DETAILS)
  Observable<BaseResponse<GzNewsDetailsData>> getGzNewsDetails(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_LINK_RECOMMEND_NEWS_LIST)
  Observable<BaseResponse<List<RecommendNewsItemData>>> getLinkRecommendNewsList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_VIDEO_LIST)
  Observable<BaseResponse<List<VideoItemData>>> getVideoList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_VIDEO_DETAILS)
  Observable<BaseResponse<VideoItemData>> getVideoDetails(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CHECK_ENTERPRISE_IS_CERTIFICATION)
  Observable<BaseResponse> checkEnterpriseIsCertification(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SAVE_NEWS_TYPE_SORT)
  Observable<BaseResponse> saveNewsTypeSort(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SORTED_NEW_TYPE_LIST)
  Observable<BaseResponse<List<NewsTypeItemData>>> getSortedNewsTypeList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SUBMIT_BUSINESS_CERTIFICATION)
  Observable<BaseResponse> submitEnterpriseCertification(@Body
  EncryptReqParams encryptReqParams);

  @POST(PersonalApiConstants.I_GET_COMPANY_NAME_BY_ID)
  Observable<BaseResponse> getCompanyNameById(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_CERTIFICATION_INFO)
  Observable<BaseResponse<CertificationData>> getCertificationInfo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_COMPANY_SIZE_LIST)
  Observable<BaseResponse<List<PickerOptionsData>>> getCompanySizeList();

  @POST(PersonalApiConstants.I_POST_NOTICE)
  Observable<BaseResponse> postNotice(@Body PostNoticeRequest postNoticeRequest);

  @POST(PersonalApiConstants.I_GET_USER_BASIC_INFO_1)
  Observable<BaseResponse<UserBasicInfoData>> getUserBasicInfo1(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_HOME_INFO)
  Observable<BaseResponse<UserHomeData>> getUserHomeInfo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_NOTICE_LIST)
  Observable<BaseResponse<List<UserNoticeItemData>>> getUserNoticeList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_VIDEO_LIST)
  Observable<BaseResponse<List<VideoItemData>>> getUserVideoList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_POST_ANSWER_OR_QUESTION)
  Observable<BaseResponse<AnswerItemData>> submitAnswer(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_POST_ANSWER_OR_QUESTION)
  Observable<BaseResponse> submitQuestion(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_RECOMMEND_VIDEO)
  Observable<BaseResponse<List<VideoItemData>>> getRecommendVideo();

  @POST(PersonalApiConstants.I_GET_USER_QUESTION_LIST)
  Observable<BaseResponse<List<UserQuestionItemData>>> getUserQuestionList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_ANSWER_DETAILS)
  Observable<BaseResponse<AnswerDetailsData>> getAnswerDetails(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_FANS_LIST)
  Observable<BaseResponse<List<FansItemData>>> getUserFansList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_FOLLOW_LIST)
  Observable<BaseResponse<List<FollowItemData>>> getFollowList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_MY_COLLECTION_LIST)
  Observable<BaseResponse<List<UserHistoryItemData>>> getMyCollectionList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_MY_COMMENT_LIST)
  Observable<BaseResponse<List<UserHistoryItemData>>> getMyCommentList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_MY_LIKE_LIST)
  Observable<BaseResponse<List<UserHistoryItemData>>> getMyLikeList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_MY_PUBLISH)
  Observable<BaseResponse> deleteMyPublish(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CLEAR_MY_HISTORY)
  Observable<BaseResponse> clearMyHistory(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_JOB_LIST_BY_LOCATION)
  Observable<BaseResponse<List<JobData>>> getJobListByLocation(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_PUBLISH_VIDEO)
  Observable<BaseResponse> publishVideo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_POSITION_TOP_DISCOUNT)
  Observable<BaseResponse<List<ResumeTopDiscountItemData>>> getResumeTopDiscount(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CREATE_RESUME_TOP_ORDER)
  Observable<BaseResponse> createResumeTopOrder(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_ORDER_LIST)
  Observable<BaseResponse<List<OrderItemData>>> getOrderList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_ACTIVATE_ORDER)
  Observable<BaseResponse> activateOrder(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_DELETE_ORDER)
  Observable<BaseResponse> deleteOrder(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_QUESTION_DETAILS)
  Observable<BaseResponse<QuestionData>> getQuestionDetails(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_ANSWER_LIST)
  Observable<BaseResponse<List<AnswerItemData>>> getAnswerList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_INVITE_USER_LIST)
  Observable<BaseResponse<List<InviteUserItemData>>> getInviteUserList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_INVITE_USER_TO_ANSWER)
  Observable<BaseResponse> inviteUserToAnswer(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SEARCH_USER)
  Observable<BaseResponse<List<InviteUserItemData>>> searchUserByName(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_INVITE_QUESTION_LIST)
  Observable<BaseResponse<List<QuestionItemData>>> getInviteQuestionList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_RECOMMEND_QUESTION_LIST)
  Observable<BaseResponse<List<QuestionItemData>>> getRecommendQuestionList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_QUESTION_LIST)
  Observable<BaseResponse<List<QuestionItemData>>> getQuestionList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SEARCH_QUESTION_LIST_BY_KEYWORD)
  Observable<BaseResponse<List<QuestionItemData>>> searchQuestionByKeyword(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPLOAD_USER_SEARCH_ACTION)
  Observable<BaseResponse> uploadUserSearchAction(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPLOAD_USER_OFFLINE_SEARCH_ACTION)
  Observable<BaseResponse> uploadUserOfflineSearchAction(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_RECOMMEND_NEWS_LIST)
  Observable<NewsPageResponse> getRecommendNewsList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SEARCH_HOT_KEY)
  Observable<BaseResponse<List<SearchHotKeyItemData>>> getSearchHotKey(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CHECK_AGREEMENT_VERSION)
  Observable<BaseResponse<VersionData>> checkAgreementVersion();

  @POST(PersonalApiConstants.I_GET_POST_VIDEO_TYPE_LIST)
  Observable<BaseResponse<List<VideoTypeItemData>>> getPostVideoTypeList();

  @POST(PersonalApiConstants.I_VERIFY_PUBLISH_VIDEO)
  Observable<BaseResponse> verifyPublishVideo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_VIDEO_PLAY_COUNT)
  Observable<BaseResponse> updateVideoPlayCount(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_VIDEO_RECRUIT_SIGN_UP)
  Observable<BaseResponse> videoRecruitSignUp(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_USER_SIGN_IN_INFO)
  Observable<BaseResponse<UserSignInData>> getUserSignInData(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_SIGN_UP_USER_LIST)
  Observable<BaseResponse<List<SignUpUserItemData>>> getSignUpUserList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPDATE_SIGN_UP_USER_STATUS)
  Observable<BaseResponse> updateSignUpUserStatus(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_CHECK_HAS_UNREAD_VIDEO_MSG)
  Observable<BaseResponse> checkHasUnreadVideoMsg(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_VIDEO_UNREAD_MSG_LIST)
  Observable<BaseResponse<List<VideoUnreadMsgItemData>>> getVideoUnreadMsgList(
      @Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SETUP_MSG_READ_V2)
  Observable<BaseResponse> setupMsgReadV2(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_DISTRACT_NEWS_LIST)
  Observable<BaseResponse<List<NewsItemData>>> getDistrictNewsList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_QA_RANK_LIST)
  Observable<BaseResponse<List<QARankItemData>>> getQARankList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_STUDY_NEWS_LIST)
  Observable<BaseResponse<List<StudyNewsItemData>>> getStudyNewsList(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_QA_USER_INFO)
  Observable<BaseResponse<QAUserData>> getQAUserInfo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_STUDY_NEWS_DETAILS)
  Observable<BaseResponse<StudyNewsDetailsData>> getStudyNewsDetails(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_UPLOAD_USER_LOCAL_ACTION)
  Observable<BaseResponse> uploadUserLocalAction(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_TOP_QUESTION_LIST)
  Observable<BaseResponse<List<QuestionItemData>>> getUnansweredQuestionList();

  @POST(PersonalApiConstants.I_SEARCH_STUDY_NEWS)
  Observable<BaseResponse<List<StudyNewsItemData>>> searchStudyNews(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SEARCH_JOB)
  Observable<BaseResponse<List<SearchJobResultItemData>>> searchJob(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SEARCH_COMPANY)
  Observable<BaseResponse<List<CompanyDetailsData>>> searchCompany(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SEARCH_VIDEO_NEWS)
  Observable<BaseResponse<List<VideoItemData>>> searchVideo(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_SEARCH_QA_NEWS)
  Observable<BaseResponse<List<QuestionItemData>>> searchQaNews(@Body ZPRequestBody ZPRequestBody);

  @POST(PersonalApiConstants.I_GET_MOMENT_DETAILS)
  Observable<BaseResponse<MomentDetailsData>> getMomentDetails(@Body ZPRequestBody ZPRequestBody);
}
