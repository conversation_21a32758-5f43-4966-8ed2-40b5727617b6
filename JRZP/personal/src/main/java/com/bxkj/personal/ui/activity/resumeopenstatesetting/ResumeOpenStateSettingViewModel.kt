package com.bxkj.personal.ui.activity.resumeopenstatesetting

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.personal.data.source.MyResumeRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description: 简历公开状态设置
 * Author:Sanjin
 * Date:2024/3/29
 **/
class ResumeOpenStateSettingViewModel @Inject constructor(
  private val myResumeRepo: MyResumeRepo
) : BaseViewModel() {
  val resumeOpenStateList = MutableLiveData<List<ResumeOpenState>>()
  val updateSuccessEvent = MutableLiveData<VMEvent<Unit>>()

  private var _selectStateIndex = 0

  fun start() {
    resumeOpenStateList.value = listOf(
      ResumeOpenState("简历及联系方式", "企业可以看到简历，并通过预留联系方式联系你"),
      ResumeOpenState("仅简历", "只能通过站内消息找到你")
    )

    getResumeOpenState()
  }

  private fun getResumeOpenState() {
    viewModelScope.launch {
      showLoading()
      myResumeRepo.getResumeOpenState()
        .handleResult({
          _selectStateIndex = it.getOrDefault()
          updateSuccessEvent.value = VMEvent(Unit)
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  fun isSelected(index: Int): Boolean {
    return _selectStateIndex == index
  }

  fun updateSelectedIndex(index: Int) {
    viewModelScope.launch {
      showLoading()
      myResumeRepo.updateResumeOpenState(index)
        .handleResult({
          _selectStateIndex = index
          updateSuccessEvent.value = VMEvent(Unit)
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }
}