package com.bxkj.personal.ui.activity.createresumesteptwo

import com.bxkj.common.constants.RouterConstants
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.util.router.RouterNavigator

class CreateResumeNavigation {
    companion object {

        const val PATH = RouterNavigation.CreateResumeStepTwoActivity.URL

        fun navigate(createResumeOrigin: Int): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withInt(RouterConstants.CREATE_RESUME_FROM, createResumeOrigin)
        }
    }
}