package com.bxkj.personal.ui.activity.personalbasicinformation;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.ui.personalbasicinformation
 * @Description: PersonalBasicInformation
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface PersonalBasicInformationContract {
    interface View extends BaseView {
        void updateSuccess();

        void onGetInfoSuccess(PersonalBasicInfoData personalBasicInfoData);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void updateInfo(int uid, PersonalBasicInfoData personalBasicInfoData);

        public abstract void getInfo(int userId);
    }
}
