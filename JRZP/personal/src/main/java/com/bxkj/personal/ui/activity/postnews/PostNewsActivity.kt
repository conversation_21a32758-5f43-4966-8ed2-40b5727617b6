package com.bxkj.personal.ui.activity.postnews

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.PermissionUtils.OnRequestResultListener
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.location.LocationManager
import com.bxkj.common.util.location.LocationStatus
import com.bxkj.common.util.location.ZPLocation
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.widget.popup.menupopup.MenuPopup.Builder
import com.bxkj.enterprise.ui.activity.selectrealtejob.SelectRelateJobNavigation
import com.bxkj.personal.R
import com.bxkj.personal.R.array
import com.bxkj.personal.databinding.PersonalActivityPostNewsBinding
import com.bxkj.support.upload.data.FileItem
import com.bxkj.videorecord.ui.videorecord.VideoRecordNavigation
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.manager.PictureCacheManager
import com.tencent.qcloud.ugckit.basic.OnUpdateUIListener
import com.tencent.qcloud.ugckit.basic.UGCKitResult
import com.tencent.qcloud.ugckit.module.VideoGenerateKit
import com.tencent.qcloud.ugckit.module.effect.VideoEditerSDK
import com.tencent.ugc.TXVideoInfoReader

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.releasenews
 * @Description: 发布活动
 * <AUTHOR>
 * @date 2019/11/21
 * @version V1.0
 */
class PostNewsActivity : BaseDBActivity<PersonalActivityPostNewsBinding, PostNewsViewModel>(),
  View.OnClickListener {
  companion object {
    const val RESULT_POST_SUCCESS = Activity.RESULT_FIRST_USER + 1

    const val TO_SELECT_RELATE_JOBS_CODE = 1
    const val TO_TAKE_VIDEO_CODE = 2

    fun newIntent(
      context: Context
    ): Intent {
      return Intent(context, PostNewsActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<PostNewsViewModel> = PostNewsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_post_news

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    setupPhotoListAdapter()
    viewModel.start()
    getLocation()

    setupVideoCompressSdk()
    subscribeViewModelComment()
    viewBinding.clTitleBar.setRightOptionClickListener {
      viewModel.post(viewBinding.etContent.text.toString())
    }
  }

  private fun setupVideoCompressSdk() {
    VideoEditerSDK.getInstance().releaseSDK()
    VideoEditerSDK.getInstance().clear()
    VideoEditerSDK.getInstance().initSDK()

    VideoGenerateKit.getInstance().setOnUpdateUIListener(object : OnUpdateUIListener {
      override fun onUICancel() {
      }

      override fun onUIProgress(progress: Float) {
      }

      override fun onUIComplete(retCode: Int, descMsg: String?) {
        val videoInfo = TXVideoInfoReader.getInstance(this@PostNewsActivity)
          .getVideoFileInfo(VideoGenerateKit.getInstance().videoOutputPath)
        val ugcKitResult = UGCKitResult().apply {
          coverPath = VideoGenerateKit.getInstance().coverPath
          outputPath = VideoGenerateKit.getInstance().videoOutputPath
          width = videoInfo.width
          height = videoInfo.height
        }
        viewModel.uploadVideo(ugcKitResult)
      }
    })
  }

  private fun subscribeViewModelComment() {
    viewModel.locationComment.observe(this, Observer {
      PermissionUtils.requestPermission(
        this,
        getString(R.string.post_news_location_permission_tips_title),
        getString(R.string.post_news_location_permission_tips_content),
        object : OnRequestResultListener {
          override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          }

          override fun onRequestSuccess() {
            getLocation()
          }
        },
        Permission.ACCESS_FINE_LOCATION
      )
    })

    viewModel.compressVideoCommand.observe(this, Observer {
      showLoading()
      VideoEditerSDK.getInstance().apply {
        if (editer == null) {
          initSDK()
        }
        setVideoPath(it)
      }
      VideoGenerateKit.getInstance().startGenerate()
    })

    viewModel.postSuccessEvent.observe(this, Observer {
      if (XXPermissions.isGranted(
          this,
          Permission.WRITE_EXTERNAL_STORAGE,
          Permission.READ_EXTERNAL_STORAGE
        )
      ) {
        PictureCacheManager.deleteCacheDirFile(this, SelectMimeType.ofImage())
      }
      finish()
    })
  }

  private fun getLocation() {
    viewModel.setLocationStatus(LocationStatus.LOCATING, getString(R.string.post_news_locating))
    LocationManager.requestLocation(this, object : LocationManager.OnLocationListener {
      override fun onSuccess(location: ZPLocation) {
        viewModel.setLocation(location)
        viewModel.setLocationStatus(
          LocationStatus.SUCCESS,
          getString(
            R.string.post_news_location_format,
            location.city + location.district + location.street
          )
        )
      }

      override fun onFailed() {
        viewModel.setLocationStatus(
          LocationStatus.FAILED,
          getString(R.string.post_news_location_failed)
        )
      }
    })
  }

  override fun onClick(v: View?) {
    v?.let {
      if (it.id == R.id.tv_relate_jobs) {
        SelectRelateJobNavigation.navigate()
          .startForResult(this, TO_SELECT_RELATE_JOBS_CODE)
      }
    }
  }

  private fun setupPhotoListAdapter() {
    val photoListAdapter =
      SimpleDBListAdapter<FileItem>(this, R.layout.upload_recycler_photo_item)
    photoListAdapter.setOnItemClickListener(object :
      SuperItemClickListener {
      override fun onClick(v: View, position: Int) {
        if (v.id == R.id.iv_delete) {
          viewModel.deletePhoto(position)
        } else {
          photoListAdapter.data.let {
            val photoItem = it[position]
            if (photoItem.isAddItem && it.size == 1) {
              showSelectFileTypeMenu()
            } else {
              toSelectMomentPic()
            }
          }
        }
      }
    }, R.id.iv_delete)
    viewBinding.recyclerPhoto.layoutManager = GridLayoutManager(this, 3)
    viewBinding.recyclerPhoto.addItemDecoration(
      GridItemDecoration(
        ContextCompat.getDrawable(
          this,
          R.drawable.divider_ffffff_4
        )
      )
    )
    viewModel.setPhotoAdapter(photoListAdapter)
  }

  private fun showSelectFileTypeMenu() {
    Builder(this@PostNewsActivity)
      .setData(resources.getStringArray(array.personal_post_news_select_file_type))
      .setOnItemClickListener { view, position ->
        if (position == 0) {
          toSelectMomentPic()
        } else {
          VideoRecordNavigation.navigate(VideoRecordNavigation.NEXT_BACK).startForResult(
            this,
            TO_TAKE_VIDEO_CODE
          )
        }
      }.build().show()
  }

  private fun toSelectMomentPic() {
    PermissionUtils.requestPermission(
      this@PostNewsActivity,
      getString(R.string.permission_tips_title),
      getString(R.string.permission_select_img_tips),
      object : PermissionUtils.OnRequestResultListener {
        override fun onRequestSuccess() {
          PictureSelector.create(this@PostNewsActivity)
            .openGallery(SelectMimeType.ofImage())
            .setImageEngine(GlideEngine.getInstance())
            .setSandboxFileEngine(SandboxFileEngine.getInstance())
            .setCompressEngine(ImageCompressEngine.getInstance())
            .setMaxSelectNum(viewModel.getAvailableImgNum())
            .setImageSpanCount(4)
            .forResult(PictureConfig.CHOOSE_REQUEST)
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          showToast(getString(R.string.cancel_operation))
        }
      },
      Permission.WRITE_EXTERNAL_STORAGE,
      Permission.READ_EXTERNAL_STORAGE
    )
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

  override fun onDestroy() {
    super.onDestroy()
    VideoEditerSDK.getInstance().clear()
    VideoEditerSDK.getInstance().releaseSDK()
  }

}
