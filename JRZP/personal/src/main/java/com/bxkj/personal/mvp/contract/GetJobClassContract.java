package com.bxkj.personal.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.common.data.JobTypeData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.mvp.contract
 * @Description: GetJobClass
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface GetJobClassContract {
    interface View extends BaseView {
        void getJobFirstClassListSuccess(List<JobTypeData> firstClassList);

        void getJobSecondClassListSuccess(List<JobTypeData> secondClassList);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getJobClassList(int type, int firstClassId);
    }
}
