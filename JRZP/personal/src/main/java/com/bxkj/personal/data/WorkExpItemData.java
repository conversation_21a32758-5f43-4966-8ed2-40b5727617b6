package com.bxkj.personal.data;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description:
 * @TODO: TODO
 * @date 2018/5/9
 */

public class WorkExpItemData {

  private int id;
  private int resumeId;
  private String date1;
  private String date2;
  private String coname;
  private String job;
  private String des;
  private List<WorkExpItemData> workExpItemDataList;

  public WorkExpItemData(int resumeId, List<WorkExpItemData> workExpItemDataList) {
    this.resumeId = resumeId;
    this.workExpItemDataList = workExpItemDataList;
  }

  public int getResumeId() {
    return resumeId;
  }

  public void setResumeId(int resumeId) {
    this.resumeId = resumeId;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getDate1() {
    return date1;
  }

  public void setDate1(String date1) {
    this.date1 = date1;
  }

  public String getDate2() {
    return date2;
  }

  public void setDate2(String date2) {
    this.date2 = date2;
  }

  public String getConame() {
    return coname;
  }

  public void setConame(String coname) {
    this.coname = coname;
  }

  public String getJob() {
    return job;
  }

  public void setJob(String job) {
    this.job = job;
  }

  public String getDes() {
    return des;
  }

  public void setDes(String des) {
    this.des = des;
  }

  public List<WorkExpItemData> getWorkExpItemDataList() {
    return workExpItemDataList;
  }

  public void setWorkExpItemDataList(List<WorkExpItemData> workExpItemDataList) {
    this.workExpItemDataList = workExpItemDataList;
  }

  public String getDateRangeText() {
    return date1 + "至" + date2;
  }

  public String getCompanyAndJobText() {
    return coname + " | " + job;
  }

  public static class DiffCallback extends DiffUtil.ItemCallback<WorkExpItemData> {

    @Override
    public boolean areItemsTheSame(@NonNull WorkExpItemData oldItem,
        @NonNull WorkExpItemData newItem) {
      return oldItem == newItem;
    }

    @Override
    public boolean areContentsTheSame(@NonNull WorkExpItemData oldItem,
        @NonNull WorkExpItemData newItem) {
      return oldItem.id == newItem.id;
    }
  }
}
