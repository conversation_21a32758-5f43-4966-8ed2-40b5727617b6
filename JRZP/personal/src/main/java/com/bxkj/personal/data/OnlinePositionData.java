package com.bxkj.personal.data;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.widget.popup.IWheelOptions;
import com.contrarywind.interfaces.IPickerViewData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description:
 * @TODO: TODO
 * @date 2018/8/17
 */
public class OnlinePositionData implements IPickerViewData, IWheelOptions {
  private int id;
  private String name;
  private int type2;
  private int shi;
  private String shiName;
  private String quName;
  private String jieName;
  private String address;
  private String hrLxr;
  private String hrMobile;

  public String getHrLxr() {
    return CheckUtils.isNullOrEmpty(hrLxr) ? "" : hrLxr;
  }

  public void setHrLxr(String hrLxr) {
    this.hrLxr = hrLxr;
  }

  public String getHrMobile() {
    return CheckUtils.isNullOrEmpty(hrMobile) ? "" : hrMobile;
  }

  public void setHrMobile(String hrMobile) {
    this.hrMobile = hrMobile;
  }

  public OnlinePositionData() {
  }

  public OnlinePositionData(int id, String name) {
    this.id = id;
    this.name = name;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public int getType2() {
    return type2;
  }

  public void setType2(int type2) {
    this.type2 = type2;
  }

  public int getShi() {
    return shi;
  }

  public void setShi(int shi) {
    this.shi = shi;
  }

  public String getShiName() {
    return shiName;
  }

  public void setShiName(String shiName) {
    this.shiName = shiName;
  }

  public String getQuName() {
    return quName;
  }

  public void setQuName(String quName) {
    this.quName = quName;
  }

  public String getJieName() {
    return jieName;
  }

  public void setJieName(String jieName) {
    this.jieName = jieName;
  }

  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  @Override
  public String getPickerViewText() {
    return name;
  }

  @Override
  public String getItemOption() {
    return name;
  }
}
