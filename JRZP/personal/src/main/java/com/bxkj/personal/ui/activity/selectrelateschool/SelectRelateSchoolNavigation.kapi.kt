package com.bxkj.personal.ui.activity.selectrelateschool

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants
import com.bxkj.personal.data.CompanyItemData

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/27
 * @version: V1.0
 */
class SelectRelateSchoolNavigation {

  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/selectrelateschool"

    const val EXTRA_SELECTED_SCHOOL = "SELECTED_SCHOOL"

    fun navigate(selectedSchool: List<CompanyItemData>? = null): RouterNavigator {
      return Router.getInstance().to(PATH).apply {
        if (!selectedSchool.isNullOrEmpty()) {
          withParcelableArrayList(EXTRA_SELECTED_SCHOOL, ArrayList(selectedSchool))
        }
      }
    }
  }

}