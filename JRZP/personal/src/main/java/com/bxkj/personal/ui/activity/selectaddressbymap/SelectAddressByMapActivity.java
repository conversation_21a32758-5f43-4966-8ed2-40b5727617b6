package com.bxkj.personal.ui.activity.selectaddressbymap;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.therouter.router.Route;
import com.baidu.mapapi.map.BaiduMap;
import com.baidu.mapapi.map.MapStatus;
import com.baidu.mapapi.map.MapStatusUpdateFactory;
import com.baidu.mapapi.map.MapView;
import com.baidu.mapapi.model.LatLng;
import com.baidu.mapapi.search.core.SearchResult;
import com.baidu.mapapi.search.geocode.GeoCodeOption;
import com.baidu.mapapi.search.geocode.GeoCodeResult;
import com.baidu.mapapi.search.geocode.GeoCoder;
import com.baidu.mapapi.search.geocode.OnGetGeoCoderResultListener;
import com.baidu.mapapi.search.geocode.ReverseGeoCodeResult;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.data.AreaOptionsData;
import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.util.AnimationUtils;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.util.location.LocationUtils;
import com.bxkj.common.util.map.LngLat;
import com.bxkj.common.widget.adresspickerdialog.AddressData;
import com.bxkj.common.widget.adresspickerdialog.AddressPickerDialogFragment;
import com.bxkj.personal.R;
import com.bxkj.personal.mvp.contract.AddressInfoContract;
import com.bxkj.personal.mvp.presenter.AddressInfoPresenter;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;

import com.jakewharton.rxbinding2.widget.RxTextView;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.ui.selectaddress
 * @Description: 选择地址
 * @TODO: TODO
 * @date 2018/6/23
 */
@Route(path = SelectAddressNavigation.PATH)
public class SelectAddressByMapActivity extends BaseDaggerActivity
  implements AddressInfoContract.View {

  public static final String ADDRESS_DATA = "address_data";
  public static final String DETAILS_ADDRESS = "details_address";
  public static final String LNGLAT = "lnglat";
  public static final String EXTRA_SELECT_LEVEL = "SELECT_LEVEL";

  @Inject
  AddressInfoPresenter mAddressInfoPresenter;

  private TextView tvRegion;
  private EditText etDetails;
  private MapView mapAddress;
  private ImageView ivPosition;
  private ImageView ivPositionShadow;
  private LinearLayout llDetailsAddress;

  private BaiduMap mBaiduMap;
  private GeoCoder mGeoCoder;

  private AddressPickerDialogFragment mAddressPickerDialog;
  private AddressData mSelectedAddressData;

  private Disposable mTextChangeDisposable;

  public static Intent newIntent(Context context, int selectLevel) {
    return newIntent(context, new AddressData(), null, null, selectLevel);
  }

  public static Intent newIntent(Context context, AddressData addressData, String detailsAddress,
    LngLat lngLat) {
    return newIntent(context, addressData, detailsAddress, lngLat, 2);
  }

  public static Intent newIntent(Context context, AddressData addressData, String detailsAddress,
    LngLat lngLat, int selectLevel) {
    Intent starter = new Intent(context, SelectAddressByMapActivity.class);
    Bundle bundle = new Bundle();
    bundle.putParcelable(ADDRESS_DATA, addressData);
    bundle.putString(DETAILS_ADDRESS, detailsAddress);
    bundle.putParcelable(LNGLAT, lngLat);
    bundle.putInt(EXTRA_SELECT_LEVEL, selectLevel);
    starter.putExtras(bundle);
    return starter;
  }

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_select_map_address;
  }

  @Override
  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    presenters.add(mAddressInfoPresenter);
    return presenters;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.select_address_title))
      .setRightText(getString(R.string.common_save))
      .setRightOptionClickListener(view -> {
        if (mSelectedAddressData.checkAddressEmpty(
          getIntent().getIntExtra(SelectAddressNavigation.EXTRA_SELECT_LEVEL, 2))) {
          showToast(getString(R.string.select_address_no_selected));
          return;
        }
        String detailsAddress = etDetails.getText().toString();
        if (CheckUtils.isNullOrEmpty(detailsAddress)) {
          showToast(getString(R.string.select_address_details_not_be_null));
          return;
        }
        Intent intent = new Intent();
        Bundle bundle = new Bundle();
        bundle.putParcelable(ADDRESS_DATA, mSelectedAddressData);
        bundle.putString(DETAILS_ADDRESS, detailsAddress);
        if (getIntent().getBooleanExtra(SelectAddressNavigation.EXTRA_SHOW_MAP, true)) {
          LatLng latLng = mBaiduMap.getMapStatus().target;
          LngLat lngLat = new LngLat(latLng.longitude, latLng.latitude);
          bundle.putParcelable(LNGLAT, lngLat);
        }
        intent.putExtras(bundle);
        setResult(RESULT_OK, intent);
        finish();
      });
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    setupAddressPickerDialog();
    if (getIntent().getBooleanExtra(SelectAddressNavigation.EXTRA_SHOW_MAP, true)) {
      setupMap();
      handleTextChange();
    } else {
      findViewById(R.id.cl_map_group).setVisibility(View.GONE);
    }
  }

  private void checkHasAddressInfo() {
    if (mSelectedAddressData == null || mSelectedAddressData.provinceAndCityIsNull()) {
      XXPermissions.with(this)
        .permission(Permission.ACCESS_COARSE_LOCATION)
        .request((permissions, all) -> getLocation());
    }
  }

  private void getLocation() {
    new LocationUtils(this.getApplicationContext(),
      (LocationUtils.OnCityResultListener) (city, bdLocation) -> {
        mBaiduMap.animateMapStatus(MapStatusUpdateFactory.newLatLng(
          new LatLng(bdLocation.getLatitude(), bdLocation.getLongitude())));
        mAddressInfoPresenter.getAddressInfoByCityName(CommonApiConstants.GET_CITY_TYPE, city);
      }).startLocation();
  }

  private void handleTextChange() {
    mTextChangeDisposable = RxTextView.textChanges(etDetails)
      .debounce(1000, TimeUnit.MILLISECONDS)
      .subscribe(charSequence -> {
          if (mSelectedAddressData == null
            || CheckUtils.isNullOrEmpty(mSelectedAddressData.getCityName())
            || charSequence.length() == 0) {
            return;
          } else {
            mGeoCoder.geocode(new GeoCodeOption()
              .city(mSelectedAddressData.getCityName())
              .address(mSelectedAddressData.getAreaAndStreet() + charSequence.toString()));
          }
        }
      );
  }

  private void setupMap() {
    LngLat lngLat = getIntent().getParcelableExtra(LNGLAT);
    mBaiduMap = mapAddress.getMap();
    mBaiduMap.setCompassEnable(false);
    if (lngLat != null) {
      mBaiduMap.animateMapStatus(MapStatusUpdateFactory.newLatLng(
        new LatLng(lngLat.getLantitude(), lngLat.getLongitude())));
    }
    mGeoCoder = GeoCoder.newInstance();
    mGeoCoder.setOnGetGeoCodeResultListener(new OnGetGeoCoderResultListener() {
      @Override
      public void onGetGeoCodeResult(GeoCodeResult geoCodeResult) {
        if (geoCodeResult == null || geoCodeResult.error != SearchResult.ERRORNO.NO_ERROR) {
          //没有检索到结果
          showToast(getString(R.string.select_address_not_search_position));
          return;
        }
        mBaiduMap.animateMapStatus(MapStatusUpdateFactory.newLatLng(geoCodeResult.getLocation()));
      }

      @Override
      public void onGetReverseGeoCodeResult(ReverseGeoCodeResult reverseGeoCodeResult) {

      }
    });

    mBaiduMap.setOnMapStatusChangeListener(new BaiduMap.OnMapStatusChangeListener() {
      @Override
      public void onMapStatusChangeStart(MapStatus mapStatus) {
        ivPosition.animate().translationY(-15);
        ivPositionShadow.animate().scaleX(2.0f);
        ivPositionShadow.animate().scaleY(2.0f);
      }

      @Override
      public void onMapStatusChangeStart(MapStatus mapStatus, int i) {
      }

      @Override
      public void onMapStatusChange(MapStatus mapStatus) {

      }

      @Override
      public void onMapStatusChangeFinish(MapStatus mapStatus) {
        ivPosition.animate().translationY(0);
        ivPositionShadow.animate().scaleX(1.0f);
        ivPositionShadow.animate().scaleY(1.0f);
      }
    });

    mBaiduMap.setOnMapLoadedCallback(() -> checkHasAddressInfo());
  }

  private void setupAddressPickerDialog() {
    mAddressPickerDialog = new AddressPickerDialogFragment();
    int convertLevel = AddressPickerDialogFragment.STREET_LEVEL;
    switch (getIntent().getIntExtra(EXTRA_SELECT_LEVEL, 4)) {
      case 1: {
        convertLevel = AddressPickerDialogFragment.PROVINCE_LEVEL;
        break;
      }
      case 2: {
        convertLevel = AddressPickerDialogFragment.CITY_LEVEL;
        break;
      }
      case 3: {
        convertLevel = AddressPickerDialogFragment.AREA_LEVEL;
        break;
      }
    }
    mAddressPickerDialog.setEndLevel(convertLevel);
    mAddressPickerDialog.setOnSelectedListener((province, city, area, street) -> {
      mSelectedAddressData = new AddressData(province, city, area, street);
      tvRegion.setText(mSelectedAddressData.getAddress());
      if (llDetailsAddress.getVisibility() == View.GONE) {
        AnimationUtils.alphaToVisibility(llDetailsAddress, 500);
      }
      if (!CheckUtils.isNullOrEmpty(mSelectedAddressData.getCityName())) {
        if (getIntentShowMap()) {
          mGeoCoder.geocode(new GeoCodeOption()
            .city(mSelectedAddressData.getCityName())
            .address(
              CheckUtils.isNullOrEmpty(mSelectedAddressData.getAreaAndStreet()) ? getString(
                R.string.select_address_city_center)
                : mSelectedAddressData.getAreaAndStreet()));
        }
      }
    });
  }

  private Boolean getIntentShowMap() {
    return getIntent().getBooleanExtra(SelectAddressNavigation.EXTRA_SHOW_MAP, true);
  }

  private void onViewClicked() {
    if (mSelectedAddressData != null) {
      mAddressPickerDialog.setSelected(mSelectedAddressData.getProvinceName(),
        mSelectedAddressData.getCityName(), mSelectedAddressData.getAreaName(),
        mSelectedAddressData.getStreetName());
    }
    if (getSupportFragmentManager().findFragmentByTag(
      AddressPickerDialogFragment.class.getSimpleName()) == null) {
      mAddressPickerDialog.show(getSupportFragmentManager(),
        AddressPickerDialogFragment.class.getSimpleName());
    }
  }

  @Override
  public void getAddressInfoSuccess(AreaOptionsData areaItemData) {
    if (getIntent().getIntExtra(EXTRA_SELECT_LEVEL, 4) == 2) {
      AnimationUtils.alphaToVisibility(llDetailsAddress, 500);
    }
    mSelectedAddressData.setProvinceName(areaItemData.getPName());
    mSelectedAddressData.setProvinceId(areaItemData.getPid());
    mSelectedAddressData.setCityName(areaItemData.getName());
    mSelectedAddressData.setCityId(areaItemData.getId());
    tvRegion.setText(mSelectedAddressData.getAddress());
  }

  @Override
  protected void onPause() {
    super.onPause();
    mapAddress.onPause();
  }

  @Override
  protected void onResume() {
    super.onResume();
    mapAddress.onResume();
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    if (getIntentShowMap() && mGeoCoder != null) {
      mGeoCoder.destroy();
    }
    mapAddress.onDestroy();
    if (mTextChangeDisposable != null && !mTextChangeDisposable.isDisposed()) {
      mTextChangeDisposable.dispose();
    }
  }

  private void bindView(View bindSource) {
    tvRegion = bindSource.findViewById(R.id.tv_region);
    etDetails = bindSource.findViewById(R.id.et_details);
    mapAddress = bindSource.findViewById(R.id.map_address);
    ivPosition = bindSource.findViewById(R.id.iv_position);
    ivPositionShadow = bindSource.findViewById(R.id.iv_position_shadow);
    llDetailsAddress = bindSource.findViewById(R.id.ll_details_address);
    bindSource.findViewById(R.id.tv_region).setOnClickListener(v -> onViewClicked());
  }
}
