package com.bxkj.personal.ui.fragment.homenews

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.jzvd.Jzvd
import cn.jzvd.JzvdStd
import com.bxkj.business.ui.courseinfogroup.CourseInfoType
import com.bxkj.business.ui.goodcourselist.GoodCourseListNavigation
import com.bxkj.business.ui.industrialservicedetails.IndustrialServiceDetailsNavigation
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.enterprise.ui.activity.conversation.BusinessChatContentNavigation
import com.bxkj.enterprise.ui.activity.invitingdelivery.InvitationDeliveryNavigation
import com.bxkj.enterprise.ui.activity.postjob.PostJobNavigation
import com.bxkj.enterprise.ui.activity.resumedetails.ApplicantResumeNavigation
import com.bxkj.enterprise.ui.activity.resumedownload.ResumeDownloadNavigation
import com.bxkj.enterprise.ui.activity.selectsayhellojob.SelectSayHelloJobNavigation
import com.bxkj.jrzp.support.feature.data.CheckInfoTag
import com.bxkj.jrzp.support.feature.data.CheckResultMsg
import com.bxkj.jrzp.support.feature.data.InfoCheckItem
import com.bxkj.jrzp.support.feature.ui.infocheck.CheckInfoCallBackAdapter
import com.bxkj.jrzp.support.feature.ui.infocheck.InfoCompletedCheck
import com.bxkj.jrzp.user.mine.ui.membercenter.MemberCenterWebNavigation
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.learning.data.CourseData
import com.bxkj.learning.data.IndustrialServicesData
import com.bxkj.learning.data.SchoolItemData
import com.bxkj.learning.ui.web.LearningWebNavigation
import com.bxkj.personal.R
import com.bxkj.personal.R.layout
import com.bxkj.personal.R.string
import com.bxkj.personal.data.ADItemData
import com.bxkj.personal.data.NewsItemData
import com.bxkj.personal.data.NewsItemData.PhotoListBean
import com.bxkj.personal.data.QuestionItemData
import com.bxkj.personal.databinding.PersonalFragmentRecruitmentNewsBinding
import com.bxkj.personal.enums.NewsType
import com.bxkj.personal.ui.activity.gznews.GzNewsDetailsActivity
import com.bxkj.personal.ui.activity.gznews.NewsDetailsNavigation
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsNavigation
import com.bxkj.personal.ui.activity.main.MainActivity
import com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsActivity
import com.bxkj.personal.ui.activity.typenews.TypeNewsActivity
import com.bxkj.personal.ui.activity.web.WebNavigation
import com.bxkj.personal.ui.fragment.home.GovRecruitmentFragment
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.ADItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.CoursesItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.CoursesItemViewBinder.OnCourseItemClickListener
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.EmploymentItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.JobFairItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.NewVideoItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.NewVideoItemViewBinder.OnVideoItemClickListener
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.NewsItemOnePhotoViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.NewsItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.ProEmploymentViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.ProEmploymentViewBinder.OnProEmploymentItemClickListener
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.QuestionsItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.ResumeItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.SchoolItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.SchoolItemViewBinder.OnSchoolItemClickListener
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.SeminarItemViewBinder
import com.bxkj.personal.ui.fragment.homenews.itemviewbinder.SingleJobItemViewBinder
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.message.VideoListMassage
import com.bxkj.video.ui.galllery.VideoGalleryNavigation
import com.bxkj.video.ui.galllery.VideoGalleryType
import com.youth.banner.listener.OnBannerListener

/**
 * @Description: 首页资讯
 * @date 2019/12/23
 * @version V1.0
 */
class RecruitmentNewsFragment :
  BaseDBFragment<PersonalFragmentRecruitmentNewsBinding, RecruitmentNewsViewModel>() {

  companion object {
    const val EXTRA_NEWS_TYPE = "NEWS_TYPE"

    const val TO_NEWS_DETAILS_CODE = 1
    const val TO_NEWS_AUTHOR_HOME_CODE = 2
    const val TO_SAY_HELLO_CODE = 3
    const val TO_DOWNLOAD_RESUME_CODE = 4

    fun newInstance(newsType: Int): RecruitmentNewsFragment {
      return RecruitmentNewsFragment().apply {
        arguments = Bundle()
          .apply {
            putInt(EXTRA_NEWS_TYPE, newsType)
          }
      }
    }
  }

  override fun getViewModelClass(): Class<RecruitmentNewsViewModel> = RecruitmentNewsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_fragment_recruitment_news

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    if (arguments?.getInt(EXTRA_NEWS_TYPE) == NewsType.NEWS_TYPE_QUESTION) {
      setupRecommendQuestionListAdapter()
    } else {
      setupNewListAdapter()
    }
    subscribeParentChangeEvent()
    subscribeRxMsg()
    subscribeViewModelEvent()
  }

  override fun onResume() {
    super.onResume()
    if (arguments?.getInt(EXTRA_NEWS_TYPE) == NewsType.NEWS_TYPE_FOLLOW) {
      viewModel.refreshFollowData()
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.noJobTipsEvent.observe(this, Observer {
      ActionDialog.Builder()
        .setContent(getString(R.string.personal_no_job_tips_content))
        .setConfirmText(getString(R.string.personal_no_job_tips_confirm_text))
        .setOnConfirmClickListener {
          PostJobNavigation.navigate(PostJobNavigation.TYPE_FULL_TIME, PostJobNavigation.NEXT_BACK)
            .start()
        }
        .build().show(childFragmentManager)
    })

    viewModel.noGreetBalanceEvent.observe(this, Observer {
      ActionDialog.Builder()
        .setTitle(getString(string.common_tips))
        .setContent(getString(string.resume_details_no_greet_balance_tips))
        .setConfirmText(getString(string.resume_details_member_upgrade_text))
        .setOnConfirmClickListener {
          MemberCenterWebNavigation.create().start()
        }.build().show(childFragmentManager)
    })

    viewModel.showInviteCommand.observe(this, Observer {
      ActionDialog.Builder()
        .setTitle(getString(string.common_tips))
        .setContent(getString(string.resume_details_no_greet_balance_tips))
        .setCenterText("邀请投递")
        .setOnCenterOptionsClickListener {
          InvitationDeliveryNavigation.create(
            intArrayOf(it.id),
            resumeUserId = it.ubInfo!!.id
          ).start()
        }
        .setConfirmText(getString(string.resume_details_member_upgrade_text))
        .setOnConfirmClickListener {
          MemberCenterWebNavigation.create().start()
        }.build().show(childFragmentManager)
    })
  }

  private fun subscribeRxMsg() {
    addDisposable(RxBus.get().toObservable(RxBus.Message::class.java)
      .subscribe {
        if (it.code == RxMsgCode.ACTION_USER_ROLE_CHANGE) {
          viewModel.refreshNews()
        }
      })
  }

  private fun setupRecommendQuestionListAdapter() {
    val recommendQuestionListAdapter = object : SimpleDBListAdapter<QuestionItemData>(
      parentActivity,
      R.layout.personal_recycler_home_recommend_question
    ) {
      var lastPosition: Int = 0
      override fun convert(
        holder: SuperViewHolder,
        viewType: Int,
        item: QuestionItemData,
        position: Int,
      ) {
        super.convert(holder, viewType, item, position)
        if (!CheckUtils.isNullOrEmpty(item.photoList)) {
          val showList =
            if (item.photoList.size > 3) item.photoList.subList(0, 3) else item.photoList
          val recyclerAnswererList =
            holder.findViewById<RecyclerView>(R.id.recycler_answerer_list)
          recyclerAnswererList.isNestedScrollingEnabled = false
          recyclerAnswererList.layoutManager =
            LinearLayoutManager(
              holder.itemView.context,
              LinearLayoutManager.HORIZONTAL,
              false
            )
          val paidUserListAdapter =
            SimpleDBListAdapter<PhotoListBean>(
              holder.itemView.context,
              layout.personal_recycler_answerer_avatar_item
            )
          recyclerAnswererList.adapter = paidUserListAdapter
          paidUserListAdapter.reset(showList)
        }
        if (position % 15 == 10) {
          if (lastPosition < position)
            viewModel.loadMore()
        }
        lastPosition = position
      }
    }.apply {
      setOnItemClickListener(object :
        SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          startActivity(
            QuestionDetailsActivity.newIntent(
              parentActivity,
              data[position].id
            )
          )
        }
      })
    }
    val recyclerNewsList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    val layoutManager = LinearLayoutManager(parentActivity)
    recyclerNewsList.layoutManager = layoutManager
    viewModel.listViewModel.setAdapter(recommendQuestionListAdapter)
  }

  private fun subscribeParentChangeEvent() {
    when (arguments?.getInt(EXTRA_NEWS_TYPE)) {
      NewsType.NEWS_TYPE_CITY, NewsType.NEWS_TYPE_RECOMMEND -> {
        (parentFragment as GovRecruitmentFragment).viewModel.refreshCityPageCommand.observe(
          this,
          Observer {
            refreshPage()
          })
      }

      NewsType.NEWS_TYPE_DISTRICT -> {
        (parentFragment as GovRecruitmentFragment).viewModel.refreshDistrictPageCommand.observe(
          this,
          Observer {
            refreshPage()
          })
      }
    }
  }

  private fun refreshPage() {
    viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).scrollToPosition(0)
    viewModel.refreshNews()
  }

  override fun lazyLoadData() {
    viewModel.start(arguments)
  }

  private fun setupNewListAdapter() {
    val newListAdapter = object : MultiTypeAdapter(parentActivity) {
      var lastPosition: Int = 0
      override fun convert(holder: SuperViewHolder, viewType: Int, item: Any, position: Int) {
        super.convert(holder, viewType, item, position)
        if (position % 13 == 10) {
          if (lastPosition < position)
            viewModel.loadMore()
        }
        lastPosition = position
      }
    }
    newListAdapter.register(NewsItemData::class.java)
      .to(
        getNewsItemViewBinder(),
        getNewsItemOnePhotoViewBinder(),
        getQuestionsItemViewBinder(),
        getNewVideoItemViewBinder(),
        getSingleJobItemViewBinder(),
        getCoursesItemViewBinder(),
        getSchoolItemViewBinder(),
        getADItemViewBinder(),
        getJobFairItemViewBinder(),
        getSeminarItemViewBinder(),
        getEmploymentItemViewBinder(),
        getResumeItemViewBinder(),
        getProEmploymentViewBinder()
      ).withClassLinker { _, item ->
        when (item.itemType) {
          0 -> {  //普通资讯
            if (!CheckUtils.isNullOrEmpty(item.listMedia) && item.listMedia.size == 1) NewsItemOnePhotoViewBinder::class.java
            else NewsItemViewBinder::class.java
          }

          1 -> {  //问答
            QuestionsItemViewBinder::class.java
          }

          2 -> {  //视频
            NewVideoItemViewBinder::class.java
          }

          3 -> {  //招聘
            SingleJobItemViewBinder::class.java
          }

          4 -> {  //技能
            CoursesItemViewBinder::class.java
          }

          5 -> {//学校
            SchoolItemViewBinder::class.java
          }

          6 -> {  //广告
            ADItemViewBinder::class.java
          }

          7, 8 -> {
            JobFairItemViewBinder::class.java
          }

          9 -> {  //宣讲会
            SeminarItemViewBinder::class.java
          }

          10 -> { //就业安置
            EmploymentItemViewBinder::class.java
          }

          11 -> { //简历
            ResumeItemViewBinder::class.java
          }

          12 -> { //专业用工
            ProEmploymentViewBinder::class.java
          }

          else -> { //普通资讯
            if (!CheckUtils.isNullOrEmpty(item.listMedia) && item.listMedia.size == 1) NewsItemOnePhotoViewBinder::class.java
            else NewsItemViewBinder::class.java
          }
        }
      }
    val recyclerNewsList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerNewsList.layoutManager = LinearLayoutManager(parentActivity)
    recyclerNewsList.addItemDecoration(HomeNewsItemDecoration())
    recyclerNewsList.addOnChildAttachStateChangeListener(object :
      RecyclerView.OnChildAttachStateChangeListener {
      override fun onChildViewDetachedFromWindow(view: View) {
        val jzVideoPlayer = view.findViewById<JzvdStd>(R.id.video_player)
        if (jzVideoPlayer != null && Jzvd.CURRENT_JZVD != null && jzVideoPlayer.jzDataSource != null && jzVideoPlayer.jzDataSource.containsTheUrl(
            Jzvd.CURRENT_JZVD.jzDataSource.currentUrl
          )
        ) {
          if (Jzvd.CURRENT_JZVD != null && Jzvd.CURRENT_JZVD.screen != Jzvd.SCREEN_FULLSCREEN) {
            Jzvd.releaseAllVideos()
          }
        }
      }

      override fun onChildViewAttachedToWindow(view: View) {
      }
    })
    viewModel.listViewModel.setAdapter(newListAdapter)
  }

  private fun getProEmploymentViewBinder(): ProEmploymentViewBinder =
    ProEmploymentViewBinder(object : OnProEmploymentItemClickListener {
      override fun onProEmploymentItemClick(proEmploymentItem: IndustrialServicesData) {
        IndustrialServiceDetailsNavigation.create(
          proEmploymentItem.productId,
          proEmploymentItem.encryptBusinessUserId,
          proEmploymentItem.encryptProductId
        ).start()
      }
    }).apply {
      setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
        override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
          GoodCourseListNavigation.create(CourseInfoType.TYPE_PRO_EMPLOYMENT).start()
        }
      }, R.id.tv_title, R.id.tv_more)
    }

  private fun getResumeItemViewBinder(): ResumeItemViewBinder {
    return ResumeItemViewBinder().apply {
      setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
        override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
          item.resume?.let {
            when (v.id) {
              R.id.tv_collect -> {
                viewModel.collectResume(it)
              }

              R.id.tv_download -> {
                InfoCompletedCheck.with(parentActivity)
                  .checkItem(
                    InfoCheckItem.Builder()
                      .checkInfoTag(CheckInfoTag.CHECK_COMPANY_AUTH_NEW)
                      .onlyCheck(false)
                      .build()
                  ).setCheckInfoCallBack(object : CheckInfoCallBackAdapter() {
                    override fun onAllCheckSuccess(result: CheckResultMsg) {
                      viewModel.checkHasOnlinePosition {
                        ResumeDownloadNavigation.create(it.id)
                          .startForResult(
                            this@RecruitmentNewsFragment,
                            TO_DOWNLOAD_RESUME_CODE
                          )
                      }
                    }
                  }).start()
              }

              R.id.tv_application -> {
                if (it.IsHuihua == 1) {
                  BusinessChatContentNavigation.create(
                    ChatRole.BUSINESS,
                    it.ubInfo!!.uid
                  )
                    .start()
                } else {
                  viewModel.checkHasOnlinePosition {
                    viewModel.checkHasGreetBalance(it) {
                      SelectSayHelloJobNavigation.create(
                        it.ubInfo!!.uid,
                        it.id
                      ).startForResult(
                        this@RecruitmentNewsFragment,
                        TO_SAY_HELLO_CODE
                      )
                    }
                  }
                }
              }

              else -> {
                it.ubInfo?.let { applicantUserInfo ->
                  ApplicantResumeNavigation.navigate(applicantUserInfo.uid, it.id)
                    .start()
                }
              }
            }
          }
        }
      }, R.id.tv_collect, R.id.tv_download, R.id.tv_application)
    }
  }

  private fun getEmploymentItemViewBinder(): EmploymentItemViewBinder =
    EmploymentItemViewBinder().apply {
      setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
        override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
          item.jiuye?.let {
            UserHomeNavigation.navigate(
              it.uid,
              targetTab = UserInfoNavigationData.NAVIGATION_EMPLOYMENT_FACILITATION
            ).start()
          }
        }
      })
    }

  private fun getSeminarItemViewBinder(): SeminarItemViewBinder = SeminarItemViewBinder()

  private fun getJobFairItemViewBinder(): JobFairItemViewBinder = JobFairItemViewBinder().apply {
    setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
      override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
        item.zhaopinhui?.let {
          NewsDetailsNavigation.navigation(it.id).start()
          it.addView()
        }
      }
    })
  }

  private fun getADItemViewBinder() = ADItemViewBinder(object : OnBannerListener<ADItemData> {
    override fun OnBannerClick(data: ADItemData?, position: Int) {
      data?.let { adItem ->
        when (adItem.type) {
          0 -> {
            return
          }

          1 -> {
            WebNavigation.navigate("${adItem.url}?ly=android").start()
          }

          2 -> {
            UserHomeNavigation.navigate(adItem.url.toInt()).start()
          }

          3 -> {
            JobDetailsNavigation.navigate(adItem.url.toInt()).start()
          }

          4 -> {
            NewsDetailsNavigation.navigation(adItem.url.toInt()).start()
          }

          5 -> {
            if (parentFragment is GovRecruitmentFragment) {
              (parentFragment as GovRecruitmentFragment).switchToJobFragment()
            }
          }

          else -> {
            return
          }
        }
      }
    }
  })

  private fun getSchoolItemViewBinder() =
    SchoolItemViewBinder(object : OnSchoolItemClickListener {
      override fun onSchoolClick(schoolItemData: SchoolItemData) {
        LearningWebNavigation.navigate(
          LearningWebNavigation.PAGE_TYPE_EDU,
          schoolItemData.id,
          localUserId
        ).start()
      }
    }).apply {
      setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
        override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
          GoodCourseListNavigation.create(CourseInfoType.TYPE_EDU).start()
        }
      }, R.id.tv_title, R.id.tv_more)
    }

  private fun getCoursesItemViewBinder() =
    CoursesItemViewBinder(object : OnCourseItemClickListener {
      override fun onCourseClick(courseData: CourseData) {
        LearningWebNavigation.navigate(
          LearningWebNavigation.PAGE_TYPE_COURSE,
          courseData.id,
          localUserId
        ).start()
      }
    }).apply {
      setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
        override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
          GoodCourseListNavigation.create(CourseInfoType.TYPE_SKILL).start()
        }
      }, R.id.tv_title, R.id.tv_more)
    }

  private fun getSingleJobItemViewBinder(): SingleJobItemViewBinder {
    return SingleJobItemViewBinder().apply {
      setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
        override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
          when (v.id) {
            R.id.tv_follow -> {
              afterLogin {
                viewModel.addOrRemoveFollowCompany(item.job)
              }
            }

            R.id.iv_hr_avatar, R.id.tv_hr_info, R.id.tv_com_name -> {
              UserHomeNavigation.navigate(item.job.uid)
                .startForResult(this@RecruitmentNewsFragment, TO_NEWS_AUTHOR_HOME_CODE)
            }

            else -> {
              JobDetailsNavigation.navigate(item.job.id).start()
            }
          }
        }
      }, R.id.iv_hr_avatar, R.id.tv_hr_info, R.id.tv_com_name, R.id.tv_follow)
    }
  }

  private fun getNewVideoItemViewBinder(): NewVideoItemViewBinder {
    return NewVideoItemViewBinder(object : OnVideoItemClickListener {
      override fun onVideoClick(
        position: Int,
        onlineVideoData: OnlineVideoData?,
        videos: List<OnlineVideoData>,
      ) {
        if (onlineVideoData == null) {
          if (parentActivity is MainActivity) {
            (parentActivity as MainActivity).switchToTabTwo()
          }
        } else {
          VideoGalleryNavigation.navigate(
            VideoGalleryType.MULTIPLE_VIDEO,
            VideoListMassage.from(position, videos, VideoGalleryType.MULTIPLE_VIDEO)
          ).start()
        }
      }
    }).apply {
      setOnItemClickListener(object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
        override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
          if (parentActivity is MainActivity) {
            (parentActivity as MainActivity).selectTabTwo()
          }
        }
      }, R.id.tv_title)
    }
  }

  private fun getQuestionsItemViewBinder(): QuestionsItemViewBinder {
    return QuestionsItemViewBinder()
      .apply {
        setOnItemClickListener(object :
          DefaultViewBinder.OnItemClickListener<NewsItemData> {
          override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
            startActivity(QuestionDetailsActivity.newIntent(parentActivity, item.id))
          }
        })
      }
  }

  private fun getNewsItemOnePhotoViewBinder(): NewsItemOnePhotoViewBinder {
    return NewsItemOnePhotoViewBinder()
      .apply {
        setOnItemClickListener(
          object :
            DefaultViewBinder.OnItemClickListener<NewsItemData> {
            override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
              when (v.id) {
                R.id.iv_logo, R.id.tv_name, R.id.tv_author -> {
                  toNewsAuthorHomePage(item)
                }

                R.id.tv_type -> startActivity(
                  TypeNewsActivity.newIntent(
                    parentActivity,
                    item.subType,
                    item.subTypeName
                  )
                )

                R.id.tv_comment -> {
                  startActivityForResult(
                    GzNewsDetailsActivity.newIntent(parentActivity, item, true),
                    TO_NEWS_DETAILS_CODE
                  )
                }

                R.id.tv_like -> {
                  afterLogin {
                    viewModel.addOrRemoveLike(item)
                  }
                }

                R.id.tv_follow -> {
                  afterLogin {
                    viewModel.addOrRemoveFollow(item)
                  }
                }

                else -> {
                  startActivityForResult(
                    GzNewsDetailsActivity.newIntent(parentActivity, item),
                    TO_NEWS_DETAILS_CODE
                  )
                }
              }
            }
          },
          R.id.iv_logo,
          R.id.tv_name,
          R.id.tv_author,
          R.id.tv_type,
          R.id.tv_comment,
          R.id.tv_like,
          R.id.tv_follow
        )
      }
  }

  private fun getNewsItemViewBinder(): NewsItemViewBinder {
    return NewsItemViewBinder().apply {
      setOnItemClickListener(
        object : DefaultViewBinder.OnItemClickListener<NewsItemData> {
          override fun onItemClicked(v: View, position: Int, item: NewsItemData) {
            when (v.id) {
              R.id.iv_logo, R.id.tv_name, R.id.tv_author -> {
                toNewsAuthorHomePage(item)
              }

              R.id.tv_type -> {
                startActivity(
                  TypeNewsActivity.newIntent(
                    parentActivity,
                    item.subType,
                    item.subTypeName
                  )
                )
              }

              R.id.tv_comment -> {
                startActivityForResult(
                  GzNewsDetailsActivity.newIntent(parentActivity, item, true),
                  TO_NEWS_DETAILS_CODE
                )
              }

              R.id.tv_like -> {
                afterLogin {
                  viewModel.addOrRemoveLike(item)
                }
              }

              R.id.tv_follow -> {
                afterLogin { viewModel.addOrRemoveFollow(item) }
              }

              else -> {
                startActivityForResult(
                  GzNewsDetailsActivity.newIntent(parentActivity, item),
                  TO_NEWS_DETAILS_CODE
                )
              }
            }
          }
        },
        R.id.iv_logo,
        R.id.tv_name,
        R.id.tv_author,
        R.id.tv_type,
        R.id.tv_comment,
        R.id.tv_like,
        R.id.tv_follow
      )
    }
  }

  private fun toNewsAuthorHomePage(item: NewsItemData) {
    if (AuthenticationType.isInstitutions(item.rzType) || (item.dwID > 0)) {
      UserHomeNavigation.navigate(
        item.userID,
        item.getAuthType(AuthenticationType.INSTITUTIONS),
        item.dwID
      ).startForResult(this@RecruitmentNewsFragment, TO_NEWS_AUTHOR_HOME_CODE)
    } else if (item.authorHigherEnterpriseAuth()) {
      UserHomeNavigation.navigate(
        item.userID,
        item.getAuthType(AuthenticationType.ENTERPRISE)
      ).startForResult(this@RecruitmentNewsFragment, TO_NEWS_AUTHOR_HOME_CODE)
    } else {
      UserHomeNavigation.navigate(
        item.userID
      ).startForResult(this@RecruitmentNewsFragment, TO_NEWS_AUTHOR_HOME_CODE)
    }
  }

  override fun onHiddenChanged(hidden: Boolean) {
    super.onHiddenChanged(hidden)
    if (hidden) {
      Jzvd.releaseAllVideos()
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

  override fun onDestroyView() {
    super.onDestroyView()
    Jzvd.releaseAllVideos()
  }

  override fun onPause() {
    super.onPause()
    Jzvd.releaseAllVideos()
  }
}