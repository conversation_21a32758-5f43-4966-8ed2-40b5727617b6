package com.bxkj.personal.ui.fragment.question

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.databinding.IncludeMvvmRefreshLayoutBinding
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.personal.R
import com.bxkj.jrzp.userhome.data.UserQuestionItemData
import com.bxkj.personal.ui.activity.addanswer.AddAnswerActivity
import com.bxkj.personal.ui.activity.answerdetails.AnswerDetailsActivity
import com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsActivity
import com.bxkj.personal.ui.fragment.question.itemviewbinder.AnswerViewBinder
import com.bxkj.personal.ui.fragment.question.itemviewbinder.QuestionViewBinder

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.question
 * @Description:  问题
 * <AUTHOR>
 * @date 2020/1/14
 * @version V1.0
 */
class QuestionFragment : BaseDBFragment<IncludeMvvmRefreshLayoutBinding, QuestionViewModel>() {

    companion object {
        const val EXTRA_USER_ID = "USER_ID"
        private const val TO_ADD_ANSWER_CODE = 1

        fun newInstance(userId: Int): QuestionFragment {
            val instance = QuestionFragment()
            instance.arguments = Bundle()
                .apply {
                    putInt(EXTRA_USER_ID, userId)
                }
            return instance
        }
    }

    override fun getViewModelClass(): Class<QuestionViewModel> = QuestionViewModel::class.java

    override fun getLayoutId(): Int = R.layout.include_mvvm_refresh_layout

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.listViewModel = viewModel.listViewModel
        setupListAdapter()
        viewModel.start(arguments)
    }

    private fun setupListAdapter() {
        val multiTypeAdapter = MultiTypeAdapter(parentActivity)
        multiTypeAdapter.register(UserQuestionItemData::class.java)
            .to(AnswerViewBinder()
                .apply {
                    setOnItemClickListener(object :
                        DefaultViewBinder.OnItemClickListener<UserQuestionItemData> {
                        override fun onItemClicked(
                            v: View,
                            position: Int,
                            item: UserQuestionItemData
                        ) {
                            when (v.id) {
                                R.id.tv_comment -> startActivity(
                                    AnswerDetailsActivity.newIntent(
                                        parentActivity,
                                        item.toWendaTitle,
                                        item.id,
                                        true
                                    )
                                )
                                R.id.tv_like -> viewModel.addOrRemoveLike(item)
                                R.id.iv_option -> {
                                    MenuPopup.Builder(parentActivity)
                                        .setData(resources.getStringArray(R.array.moment_details_delete_menu))
                                        .setOnItemClickListener { _, menuPosition ->
                                            if (menuPosition == 0) {
                                                showDeleteQuestionConfirmDialog(position, item)
                                            }
                                        }.build().show()
                                }
                                else -> startActivity(
                                    AnswerDetailsActivity.newIntent(
                                        parentActivity,
                                        item.toWendaTitle,
                                        item.id
                                    )
                                )
                            }
                        }
                    }, R.id.tv_comment, R.id.tv_like, R.id.iv_option)
                }, QuestionViewBinder()
                .apply {
                    setOnItemClickListener(object :
                        DefaultViewBinder.OnItemClickListener<UserQuestionItemData> {
                        override fun onItemClicked(
                            v: View,
                            position: Int,
                            item: UserQuestionItemData
                        ) {
                            when (v.id) {
                                R.id.tv_add_answer -> startActivityForResult(
                                    AddAnswerActivity.newIntent(
                                        parentActivity,
                                        item.userID,
                                        item.id,
                                        item.title,
                                        true
                                    ), TO_ADD_ANSWER_CODE
                                )
                                R.id.iv_option -> {
                                    MenuPopup.Builder(parentActivity)
                                        .setData(resources.getStringArray(R.array.moment_details_delete_menu))
                                        .setOnItemClickListener { _, menuPosition ->
                                            if (menuPosition == 0) {
                                                showDeleteQuestionConfirmDialog(position, item)
                                            }
                                        }.build().show()
                                }
                                else -> startActivity(
                                    QuestionDetailsActivity.newIntent(
                                        parentActivity,
                                        item.id
                                    )
                                )
                            }
                        }
                    }, R.id.tv_add_answer, R.id.iv_option)
                }
            ).withClassLinker { _, item ->
                if (item.type == 1) {
                    return@withClassLinker QuestionViewBinder::class.java
                } else {
                    return@withClassLinker AnswerViewBinder::class.java
                }
            }
        val recyclerContentList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
        recyclerContentList.layoutManager = LinearLayoutManager(parentActivity)
        viewModel.listViewModel.setAdapter(multiTypeAdapter)
    }

    private fun showDeleteQuestionConfirmDialog(
        position: Int,
        item: UserQuestionItemData
    ) {
        ActionDialog.Builder()
            .setTitle(getString(R.string.tips))
            .setContent(getString(R.string.common_delete_confirm_tips))
            .setOnConfirmClickListener {
                viewModel.deleteQuestionOrAnswer(position, item)
            }.build().show(childFragmentManager)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == TO_ADD_ANSWER_CODE && resultCode == AddAnswerActivity.RESULT_ADD_SUCCESS) {
            viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).scrollToPosition(0)
            viewModel.listViewModel.refresh()
        }
    }

}