package com.bxkj.personal.ui.fragment.homenews.itemviewbinder

import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.util.HtmlUtils
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.NewsItemData
import com.bxkj.personal.data.NewsItemData.MedialistBean

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.homenews.itemviewbinder
 * @Description:
 * <AUTHOR>
 * @date 2019/12/24
 * @version V1.0
 */
class NewsItemViewBinder : DefaultViewBinder<NewsItemData>(
    R.layout.personal_recycler_news_list_news,
    BR.data,
    true
) {

    override fun onBindViewHolder(
        holder: SuperViewHolder,
        item: NewsItemData,
        position: Int
    ) {
        super.onBindViewHolder(holder, item, position)
        holder.findViewById<TextView>(R.id.tv_title).text = HtmlUtils.fromHtml(item.title)
        if (!CheckUtils.isNullOrEmpty(item.listMedia)) {
            val photoListAdapter =
                SimpleDBListAdapter<MedialistBean>(
                    holder.itemView.context,
                    layout.personal_recycler_news_img_item
                )
            val recyclerPhotoList = holder.findViewById<RecyclerView>(R.id.recycler_photo)
            recyclerPhotoList.layoutManager =
                GridLayoutManager(holder.itemView.context, item.listMedia.size)
            recyclerPhotoList.addItemDecoration(
                GridItemDecoration(
                    ContextCompat.getDrawable(
                        holder.itemView.context,
                        R.drawable.divider_ffffff_4
                    )
                )
            )
            recyclerPhotoList.adapter = photoListAdapter
            photoListAdapter.reset(item.listMedia)
        }
    }
}