package com.bxkj.personal.data;

import com.bxkj.common.network.BaseResponse;

import java.util.List;

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/4/20
 * @version: V1.0
 */
public class SearchMultipleResponseData extends BaseResponse<List<SearchNewsResultItemData>> {

    private String minIDs;

    public String getMinIDs() {
        return minIDs;
    }

    public void setMinIDs(String minIDs) {
        this.minIDs = minIDs;
    }

    public SearchMultipleResponseData(int status, String msg) {
        super(status, msg);
    }
}
