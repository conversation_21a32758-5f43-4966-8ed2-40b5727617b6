package com.bxkj.personal.ui.activity.quickanswer

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.AnswerItemData
import com.bxkj.personal.databinding.PersonalActivityQuickAnswerBinding
import com.bxkj.personal.ui.activity.editinfo.EditInfoActivity
import com.bxkj.personal.ui.activity.editinfo.EditInfoNavigation

/**
 * @Project: gzgk
 * @Description: 快捷回答
 * @author:45457
 * @date: 2020/4/17
 * @version: V1.0
 */
class QuickAnswerActivity :
  BaseDBActivity<PersonalActivityQuickAnswerBinding, QuickAnswerViewModel>() {

  companion object {

    const val RESULT_SELECTED = Activity.RESULT_FIRST_USER + 1
    const val EXTRA_RESULT_SELECTED_ITEM = "RESULT_SELECTED_ITEM"

    const val TO_ADD_QUICK_ANSWER_CODE = 1

    fun newIntent(context: Context): Intent {
      return Intent(context, QuickAnswerActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<QuickAnswerViewModel> = QuickAnswerViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_quick_answer

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupAnswerHistoryList()

    subscribeSearchKeywordChange()
    subscribeViewModelEvent()

    viewModel.start()
  }

  private fun subscribeSearchKeywordChange() {
    viewModel.searchKeyword.observe(this, Observer {
      (viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content).layoutManager as LinearLayoutManager).scrollToPositionWithOffset(
        0,
        0
      )
      viewModel.startSearch(it)
    })
  }

  private fun subscribeViewModelEvent() {
    viewModel.toAddQuickAnswerCommand.observe(this, Observer {
      startActivityForResult(
        EditInfoActivity.newIntent(
          this,
          getString(R.string.quick_answer_add_page_title),
          getString(R.string.quick_answer_add_page_hint),
          CommonApiConstants.NO_TEXT,
          2000
        ), TO_ADD_QUICK_ANSWER_CODE
      )
    })
  }

  private fun setupAnswerHistoryList() {
    val answerHistoryListAdapter = SimpleDBListAdapter<AnswerItemData>(
      this,
      layout.personal_recycler_quick_history_item
    )
      .apply {
        setOnItemClickListener(object :
          SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            backAndResultData(data[position])
          }
        })
      }
    val recyclerAnswerHistoryList =
      viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerAnswerHistoryList.layoutManager = LinearLayoutManager(this)
    recyclerAnswerHistoryList.addItemDecoration(
      LineItemDecoration(
        ContextCompat.getDrawable(
          this,
          R.drawable.divider_f4f4f4_8
        ), LinearLayoutManager.VERTICAL
      )
    )
    viewModel.answerHistoryListViewModel.setAdapter(answerHistoryListAdapter)
  }

  private fun backAndResultData(answerItemData: AnswerItemData) {
    val resultIntent = Intent()
    resultIntent.putExtra(EXTRA_RESULT_SELECTED_ITEM, answerItemData.content)
    setResult(RESULT_SELECTED, resultIntent)
    finish()
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == TO_ADD_QUICK_ANSWER_CODE && resultCode == RESULT_OK && data != null) {
      viewModel.addQuickAnswer(data.getStringExtra(EditInfoNavigation.EXTRA_RESULT_TEXT).getOrDefault())
    }
  }
}