package com.bxkj.personal.ui.activity.videosignupmsg

import android.app.Application
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.data.VideoUnreadMsgItemData
import com.bxkj.personal.data.source.AccountRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/3/27
 * @version: V1.0
 */
class VideoSignUpMsgViewModel @Inject constructor(application: Application
                                                  , private val mAccountRepo: AccountRepo) : BaseViewModel() {

    val videoUnReadMsgListViewModel = RefreshListViewModel()

    init {
        videoUnReadMsgListViewModel.setOnLoadDataListener { currentPage ->
            mAccountRepo.getVideoUnreadMsgList(getSelfUserID(), AppConstants.PERSONAL_TYPE, currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE
                    , object : ResultDataCallBack<List<VideoUnreadMsgItemData>> {
                override fun onSuccess(data: List<VideoUnreadMsgItemData>?) {
                    videoUnReadMsgListViewModel.autoAddAll(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30002 || respondThrowable.errCode == 30005) {
                        videoUnReadMsgListViewModel.noMoreData()
                    } else {
                        videoUnReadMsgListViewModel.loadError()
                    }
                }
            })
        }
    }

    fun start() {
        videoUnReadMsgListViewModel.refresh()
    }

    fun setupMsgRead(item: VideoUnreadMsgItemData) {
        mAccountRepo.setupMsgReadV2( item.id
                , object : ResultCallBack {
            override fun onSuccess() {
            }

            override fun onError(respondThrowable: RespondThrowable) {
            }
        })
    }
}