package com.bxkj.personal.ui.activity.service;

import android.app.Application;

import com.bxkj.common.base.mvvm.callback.ResultDataCallBack;
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.jrzp.user.mine.data.ServiceItemData;
import com.bxkj.personal.data.source.AccountRepo;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.service
 * @Description:
 * @TODO: TODO
 * @date 2019/6/3
 */
public class ServiceViewModel extends BaseViewModel {

    private AccountRepo mAccountRepo;
    private ServiceListAdapter mServiceListAdapter;

    @Inject
    public ServiceViewModel(AccountRepo accountRepo) {
        super();
        mAccountRepo = accountRepo;
    }

    public void getServiceList() {
        mAccountRepo.getServiceList(new ResultDataCallBack<List<ServiceItemData>>() {
            @Override
            public void onSuccess(List<ServiceItemData> data) {
                mServiceListAdapter.reset(data);
            }

            @Override
            public void onError(RespondThrowable respondThrowable) {
                showToast(respondThrowable.getErrMsg());
            }
        });
    }

    public void setServiceListAdapter(ServiceListAdapter mServiceListAdapter) {
        this.mServiceListAdapter = mServiceListAdapter;
    }

    public ServiceListAdapter getServiceListAdapter() {
        return mServiceListAdapter;
    }
}
