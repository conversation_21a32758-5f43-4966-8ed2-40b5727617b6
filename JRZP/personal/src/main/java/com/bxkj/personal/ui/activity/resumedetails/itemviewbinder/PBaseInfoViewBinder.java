package com.bxkj.personal.ui.activity.resumedetails.itemviewbinder;

import android.app.Activity;
import android.view.View;
import android.widget.ImageView;

import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.imageloder.base.ImageLoader;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.HtmlUtils;
import com.bxkj.common.util.imageloader.GlideLoadConfig;
import com.bxkj.personal.R;
import com.bxkj.personal.data.UserCenterPersonalData;
import com.bxkj.personal.ui.activity.myresume.VideoBannerAdapter;
import com.bxkj.video.data.VideoData;
import com.bxkj.video.message.VideoListMassage;
import com.bxkj.video.ui.galllery.VideoGalleryNavigation;
import com.bxkj.video.ui.galllery.VideoGalleryType;
import com.youth.banner.Banner;
import com.youth.banner.indicator.CircleIndicator;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.ui.activity.resumedetails.itemviewbinder
 * @Description: 简历基本信息
 * @TODO: TODO
 * @date 2018/8/15
 */
public class PBaseInfoViewBinder implements ItemViewBinder<UserCenterPersonalData> {

  private Activity mActivity;

  public PBaseInfoViewBinder(Activity activity) {
    mActivity = activity;
  }

  @Override
  public void onBindViewHolder(SuperViewHolder holder, UserCenterPersonalData item, int position) {
    ImageView ivHeader = holder.findViewById(R.id.iv_header);
    ImageLoader.loadImage(mActivity, new GlideLoadConfig.Builder().url(item.getRealAvatar())
        .error(item.getSexTag() == 0 ? R.drawable.ic_default_avatar_man
            : R.drawable.ic_default_avatar_woman)
        .circle()
        .into(ivHeader)
        .build());
    holder.setText(R.id.tv_name, item.getName());
//    holder.setText(R.id.tv_about,
//        mActivity.getString(R.string.resume_details_about_format, item.getSex(), item.getQuaName(),
//            item.getWorkExp()));
    holder.setText(R.id.tv_basic_info, item.getPersonalBasicInfoText());

    holder.setText(R.id.tv_about, item.getPersonalOtherInfoText());
    holder.setText(R.id.tv_position, HtmlUtils.fromHtml(
        mActivity.getString(R.string.resume_details_position_format, item.getPosition())));
    holder.setText(R.id.tv_salary, HtmlUtils.fromHtml(
        mActivity.getString(R.string.resume_details_salary_format,
            item.getSalary().equals("0.00") ? "面议" : item.getSalary())));
//    holder.setText(R.id.tv_age,
//        HtmlUtils.fromHtml(mActivity.getString(R.string.resume_details_age_format, TimeUtils
//            .getAge(item.getBirthday()))));
//    holder.setText(R.id.tv_address, HtmlUtils.fromHtml(
//        mActivity.getString(R.string.resume_details_address_format, item.getProvinceJZName(),
//            item.getCityJZName())));
//    holder.setText(R.id.tv_registered_residence, HtmlUtils.fromHtml(
//        mActivity.getString(R.string.resume_details_registered_residence_format,
//            item.getProvinceHKName(), item.getCityHKName())));
    Banner<VideoData, VideoBannerAdapter> videoBanner = holder
        .findViewById(R.id.vp_resume_attach_video);
    if (videoBanner != null) {
      if (CheckUtils.isNullOrEmpty(item.getAttachVideos())) {
        videoBanner.setVisibility(View.GONE);
      } else {
        videoBanner.setVisibility(View.VISIBLE);
        videoBanner.setIndicator(new CircleIndicator(holder.itemView.getContext()));
        videoBanner.setAdapter(new VideoBannerAdapter(item.getAttachVideos()));
        videoBanner.setLoopTime(5000);
        videoBanner.setOnBannerListener((data, videoPosition) ->
            VideoGalleryNavigation.Companion.navigate(VideoGalleryType.NORMAL_GALLERY,
                VideoListMassage.from(videoPosition, item.getAttachVideos(),
                    VideoGalleryType.NORMAL_GALLERY), item.getId(),
                CommonApiConstants.NO_TEXT
            ).start());
      }
    }
  }

  @Override
  public int getLayoutId() {
    return R.layout.personal_recycler_resume_base_details_info;
  }
}
