package com.bxkj.personal.ui.activity.selectrelateschool

import android.content.Context
import android.view.View
import android.widget.ImageView
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.data.CompanyItemData
import java.util.ArrayList

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/27
 * @version: V1.0
 */
class SchoolListAdapter constructor(
  context: Context,
  private val viewModel: SelectRelateSchoolViewModel
) :
  SimpleDBListAdapter<CompanyItemData>(
    context,
    R.layout.personal_recycler_school_item, BR.data
  ) {

  override fun openMultiSelect(): <PERSON><PERSON>an {
    return true
  }

  override fun convert(holder: SuperViewHolder, viewType: Int, item: CompanyItemData, position: Int) {
    super.convert(holder, viewType, item, position)
    val checkbox = holder.findViewById<ImageView>(R.id.iv_select)
    checkbox.isSelected = selectedItems.contains(item)
    checkbox.visibility = View.VISIBLE
    holder.itemView.setOnClickListener {
      if (checkbox.isSelected) {
        selectedItems.remove(item)
      } else {
        selectedItems.add(item)
      }
      notifySelectedChange()
    }
  }

  fun removeSelected(company: CompanyItemData) {
    selectedItems.remove(company)
    notifySelectedChange()
  }

  override fun setSelectedItems(selectedItems: MutableList<CompanyItemData>?) {
    super.setSelectedItems(selectedItems)
    notifySelectedChange()
  }

  private fun notifySelectedChange() {
    notifyDataSetChanged()
    viewModel.setSelectedList(selectedItems as ArrayList<CompanyItemData>)
  }

}