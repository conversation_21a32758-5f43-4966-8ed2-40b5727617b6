package com.bxkj.personal.ui.activity.applicationrecord

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description: 申请记录
 * @author:45457
 * @date: 2020/9/16
 * @version: V1.0
 */
class ResumeDeliveryRecordNavigation {

  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/applicationrecord"

    const val EXTRA_TARGET_INDEX = "current_index"

    fun navigate(targetIndex: Int): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_TARGET_INDEX, targetIndex)
    }
  }
}