package com.bxkj.personal.ui.activity.resumesetting;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.resumesetting
 * @Description: ResumeSetting
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface ResumeSettingContract {
  interface View extends BaseView {
    void updateResumeOpenStateSuccess(int nowState);

    void setUpResumeIsDefaultSuccess();

    void updateResumeAutoRefreshSuccess(int nowState);
  }

  abstract class Presenter extends BaseMvpPresenter<View> {
    abstract void updateResumeOpenState(int state);

    abstract void setUpResumeIsDefault();

    abstract void updateResumeAutoRefresh(int state);
  }
}
