package com.bxkj.personal.ui.activity.seenmybusiness

import android.os.Bundle
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivitySeenMeBusinessBinding

/**
 * @Description:谁看过我
 * @TODO: TODO
 * <AUTHOR>
 */
@Route(path = ViewedMeCompanyNavigation.PATH)
class ViewedMeCompanyAcitivity :
    BaseDBActivity<PersonalActivitySeenMeBusinessBinding, BaseViewModel>() {

    override fun getLayoutId(): Int = R.layout.personal_activity_seen_me_business

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun initPage(savedInstanceState: Bundle?) {
        supportFragmentManager.beginTransaction()
            .add(R.id.fl_container, ViewedMeCompanyFragment.newInstance())
            .commit()
    }

}