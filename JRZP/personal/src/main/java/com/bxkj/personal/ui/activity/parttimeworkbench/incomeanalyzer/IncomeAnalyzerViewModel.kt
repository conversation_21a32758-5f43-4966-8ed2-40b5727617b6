package com.bxkj.personal.ui.activity.parttimeworkbench.incomeanalyzer

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.personal.data.IncomeAnalyzerRecord
import com.bxkj.personal.data.source.SalesServiceRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * author:Sanjin
 * date:2025/2/26
 **/
class IncomeAnalyzerViewModel @Inject constructor(
  private val salesServiceRepo: SalesServiceRepo
) : BaseViewModel() {

  val incomeList = MutableLiveData<List<IncomeAnalyzerRecord>>()

  fun start() {
    viewModelScope.launch {
      salesServiceRepo.getPartTimeIncomeItem()
        .handleResult({
          incomeList.value = it
        }, {
          showToast(it.errMsg)
        })
    }
  }
}