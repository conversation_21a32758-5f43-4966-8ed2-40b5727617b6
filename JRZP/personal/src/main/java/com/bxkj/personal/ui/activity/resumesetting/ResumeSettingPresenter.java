package com.bxkj.personal.ui.activity.resumesetting;

import androidx.annotation.NonNull;

import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.di.module.ApplicationModule;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.api.PersonalApi;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.resumesetting
 * @Description: ResumeSetting
 * @TODO: TODO
 * @date 2018/3/27
 */

public class ResumeSettingPresenter extends ResumeSettingContract.Presenter {

  private static final String TAG = ResumeSettingPresenter.class.getSimpleName();
  private PersonalApi mPersonalApi;
  private int mUserId;
  private int mResumeId;

  @Inject
  public ResumeSettingPresenter(@Named(ApplicationModule.USER_ID) int userId,
      @Named(ResumeSettingActivity.EXTRA_RESUME_ID) int resumeId, PersonalApi personalApi) {
    mUserId = userId;
    mResumeId = resumeId;
    mPersonalApi = personalApi;
  }

  @Override
  void updateResumeOpenState(int state) {
    mPersonalApi.updateResumeOpenState(mUserId, mResumeId, state)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.updateResumeOpenStateSuccess(state);
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            mView.onError(respondThrowable.getErrMsg());
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }

  @Override
  void setUpResumeIsDefault() {
    mPersonalApi.setUpResumeIsDefault(mUserId, mResumeId)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.setUpResumeIsDefaultSuccess();
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            mView.onError(respondThrowable.getErrMsg());
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }

  @Override void updateResumeAutoRefresh(int state) {
    showLoading();
    mPersonalApi.setupResumeAutoRefresh(mResumeId, state)
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override protected void onSuccess(@NonNull BaseResponse baseResponse) {
            hiddenLoading();
            mView.updateResumeAutoRefreshSuccess(state);
          }

          @Override protected void onError(@NonNull RespondThrowable respondThrowable) {
            hiddenLoading();
            mView.onError(respondThrowable.getErrMsg());
          }

          @Override public void onSubscribe(@NonNull Disposable d) {
            mCompositeDisposable.add(d);
          }
        });
  }
}
