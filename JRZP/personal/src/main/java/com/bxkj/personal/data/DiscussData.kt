package com.bxkj.personal.data

import android.os.Parcelable
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.recyclerview.widget.DiffUtil
import com.bxkj.personal.BR
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 *
 * @author: sanjin
 * @date: 2022/8/31
 */
@Parcelize
data class DiscussData(
    var no: Int,
    @SerializedName("wdid", alternate = ["id"])
    val wdid: Int = 0,
    val userID: Int,
    val title: String? = "",
    var content: String? = "",
    @get:Bindable
    var view: Int,
    val cityName: String? = "",
    @SerializedName("nickName", alternate = ["userName"])
    val nickName: String? = "",
    val photo: String? = "",
    @get:Bindable
    var plCount: Int? = 0,
    @get:Bindable
    @SerializedName("isZan")
    var liked: Int? = 0,
    @get:Bindable
    var zanCount: Int,
) : BaseObservable(), Parcelable {

    fun updateLikeState(like: Boolean) {
        liked = if (like) 1 else 0
        if (like) {
            zanCount += 1
        } else {
            zanCount -= 1
        }
        notifyPropertyChanged(BR.liked)
        notifyPropertyChanged(BR.zanCount)
    }

    fun addViewCount(count: Int = 1) {
        this.view += count
        notifyPropertyChanged(BR.view)
    }

    fun updateViewCount(count: Int) {
        this.view = count
        notifyPropertyChanged(BR.view)
    }

    fun updateCommentCount(count: Int) {
        this.plCount = count
        notifyPropertyChanged(BR.plCount)
    }

    fun updateLikeCount(likeCount: Int) {
        zanCount = likeCount
        notifyPropertyChanged(BR.zanCount)
    }

    class DiffCallback : DiffUtil.ItemCallback<DiscussData>() {
        override fun areItemsTheSame(oldItem: DiscussData, newItem: DiscussData): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: DiscussData, newItem: DiscussData): Boolean {
            return oldItem.wdid == newItem.wdid && oldItem.no == newItem.no
        }

    }
}