package com.bxkj.personal.ui.fragment.usernotice

import android.os.Bundle
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.UserNoticeItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.CommentRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.usernotice
 * @Description:
 * <AUTHOR>
 * @date 2020/1/9
 * @version V1.0
 */
class UserNoticeViewModel @Inject constructor(
    private val mAccountRepo: AccountRepo
    , private val mCommentRepo: CommentRepo
) : BaseViewModel() {
    val listViewModel = RefreshListViewModel()

    fun start(bundle: Bundle?) {
        bundle?.let {
            val companyId = bundle.getInt(UserNoticeFragment.EXTRA_ENTERPRISE_ID, CommonApiConstants.NO_ID)
            listViewModel.setOnLoadDataListener { currentPage ->
                mAccountRepo.getUserNoticeList(companyId, getSelfUserID(), currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE
                        , object : ResultDataCallBack<List<UserNoticeItemData>> {
                    override fun onSuccess(data: List<UserNoticeItemData>?) {
                        listViewModel.autoAddAll(data)
                    }

                    override fun onError(respondThrowable: RespondThrowable) {
                        if (respondThrowable.errCode == 30001) {
                            listViewModel.noMoreData()
                        } else {
                            listViewModel.loadError()
                        }
                    }
                })
            }

            listViewModel.refresh()
        }
    }

    fun addOrRemoveLike(item: UserNoticeItemData) {
        mCommentRepo.likeOrUnlikeTheComment(getSelfUserID(), CommonApiConstants.NO_ID, PersonalApiConstants.NEWS_TYPE_NEWS, item.id
                , object : ResultCallBack {
            override fun onSuccess() {
                item.addLike()
            }

            override fun onError(respondThrowable: RespondThrowable) {
                if (respondThrowable.errCode == 10002) {
                    item.removeLike()
                } else {
                    showToast(respondThrowable.errMsg)
                }
            }
        })
    }

    fun getShareItem(): UserNoticeItemData? {
        return if (listViewModel.data.isNullOrEmpty()) {
            null
        } else {
            listViewModel.data[0] as UserNoticeItemData?
        }
    }

}