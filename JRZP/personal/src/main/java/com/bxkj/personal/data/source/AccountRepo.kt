package com.bxkj.personal.data.source

import com.alipay.sdk.app.PayTask
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.ListDTO
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.ZPCommonApi
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.exception.ExceptionCode
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.AESOperator
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.HtmlUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.objectEncrypt
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.enterprise.data.GreetBalanceData
import com.bxkj.jrzp.user.mine.data.ServiceItemData
import com.bxkj.jrzp.user.mine.data.UserHomeData
import com.bxkj.personal.api.CoroutinesApi
import com.bxkj.personal.api.PersonalApi
import com.bxkj.personal.api.PersonalApiConstants.UserVideoType
import com.bxkj.personal.data.AliPayResultData
import com.bxkj.personal.data.AnswerItemData
import com.bxkj.personal.data.CreateOrderResultData
import com.bxkj.personal.data.DefaultAvatarItemData
import com.bxkj.personal.data.FansItemData
import com.bxkj.personal.data.FeedbackMsgContentItemData
import com.bxkj.personal.data.FeedbackMsgItemData
import com.bxkj.personal.data.FollowItemData
import com.bxkj.personal.data.FollowedJobFairItemData
import com.bxkj.personal.data.GrayListState
import com.bxkj.personal.data.InviteUserItemData
import com.bxkj.personal.data.OnlinePositionData
import com.bxkj.personal.data.OrderItemData
import com.bxkj.personal.data.PersonalMemberBean
import com.bxkj.personal.data.QAUserData
import com.bxkj.personal.data.ResumeTopDiscountItemData
import com.bxkj.personal.data.ResumeTopPageBean
import com.bxkj.personal.data.SchoolRecruitRecordData
import com.bxkj.personal.data.SignUpUserItemData
import com.bxkj.personal.data.UserBasicInfoData
import com.bxkj.personal.data.UserHistoryItemData
import com.bxkj.personal.data.UserInfoData
import com.bxkj.personal.data.UserNoticeItemData
import com.bxkj.personal.data.UserSignInData
import com.bxkj.personal.data.UserStatusData
import com.bxkj.personal.data.VideoItemData
import com.bxkj.personal.data.VideoTypeItemData
import com.bxkj.personal.data.VideoUnreadMsgItemData
import com.bxkj.personal.data.WechatLoginResultData
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import javax.inject.Inject

/**
 * <AUTHOR>
 * @date 2019/7/16
 * @version V1.0
 */
class AccountRepo @Inject constructor(
  personalApi: PersonalApi,
  private val mCommonApi: ZPCommonApi,
  private val coroutinesApi: CoroutinesApi,
) : BaseRepo() {

  private val mPersonalApi = personalApi

  suspend fun getInvitationCode(): ReqResponse<String> {
    return httpRequest {
      coroutinesApi.getInvitationQRCode()
    }
  }

  /**
   * 记录用户最后登录时间
   */
  suspend fun recordUserLastLoginTime(): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.recordUserLastLoginTime()
    }
  }

  /**
   * 检查用户
   */
  suspend fun checkUserGeekVipStatus(): ReqResponse<Int> {
    return httpRequest {
      coroutinesApi.checkUserGeekVipStatus()
    }
  }

  /**
   * 删除用户
   */
  suspend fun deleteUser(): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.deleteUser()
    }
  }

  /**
   * 创建个人vip订单
   */
  suspend fun createPersonalVipOrder(id: String): ReqResponse<String> {
    return httpRequest {
      coroutinesApi.createPersonalVipOrder(ZPRequestBody().apply {
        put("id", id)
      })
    }
  }

  /**
   * 获取个人会员套餐列表
   */
  suspend fun getMemberPackageList(): ReqResponse<PersonalMemberBean> {
    return httpRequest {
      coroutinesApi.getMemberPackageList()
    }
  }

  /**
   * 创建简历置顶订单
   * [id]:服务id
   */
  suspend fun createTopResumeOrderV2(id: Int): ReqResponse<String> {
    return httpRequest {
      coroutinesApi.createTopResumeOrder(ZPRequestBody().apply {
        put("id", id)
      })
    }
  }

  /**
   * 获取简历置顶页面数据
   */
  suspend fun getResumeTopPageData(): ReqResponse<ResumeTopPageBean> {
    return httpRequest {
      coroutinesApi.getResumeTopPageData()
    }
  }

  /**
   * 检查企业灰名单状态
   */
  suspend fun checkAccountGrayListState(): ReqResponse<GrayListState> {
    return httpRequest {
      coroutinesApi.checkAccountGrayListState()
    }
  }

  /**
   * 删除校招投递记录
   */
  suspend fun deleteSchoolRecruitDeliveryRecord(recordIds: String): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.deleteSchoolRecruitDeliveryRecord(
        mapOf("ids" to recordIds).paramsEncrypt()
      )
    }
  }

  /**
   * 获取校招投递记录
   */
  suspend fun getSchoolRecruitDeliveryRecord(
    type: Int,
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<ListDTO<SchoolRecruitRecordData>> {
    return httpRequest {
      coroutinesApi.getSchoolRecruitDeliveryRecord(
        mapOf(
          "type" to type,
          "pageIndex" to pageIndex,
          "pageSize" to pageSize
        ).paramsEncrypt()
      )
    }
  }

  /**
   * 根据
   * [channel]:频道，登录组件需要
   * 登录到电脑
   */
  fun loginToComputer(
    channel: String,
    userId: Int,
    userNameOrPhone: String,
    userType: Int,
    resultCallBack: ResultCallBack,
  ) {
    mPersonalApi.scanQrCodeLogin(
      channel,
      AESOperator.safeEncrypt("$userId|$userNameOrPhone|$userType")
    ).compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          resultCallBack.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          resultCallBack.onError(respondThrowable)
        }
      })
  }

  fun getServiceList(callBack: ResultDataCallBack<List<ServiceItemData>>) {
    mPersonalApi.serviceList
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onError(respondThrowable)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }
      })
  }

  fun checkCompanyInfoCompleted(userId: Int, callback: ResultCallBack) {
    mPersonalApi.checkCompanyInfoCompleted(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun checkUserInfoIsComplete(userId: Int, callBack: ResultCallBack) {
    mPersonalApi.checkUserInfoIsComplete(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onError(respondThrowable)
        }
      })
  }

  fun addOrRemoveCollection(
    userId: Int,
    type: Int,
    infoId: Int,
    callBack: ResultDataCallBack<BaseResponse<*>>,
  ) {
    if (!UserUtils.logged()) {
      callBack.onError(RespondThrowable.fromUnLogin())
      return
    }
    mPersonalApi.addOrRemoveCollection(userId, type, infoId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess(baseResponse)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onError(respondThrowable)
        }
      })
  }

  fun addOrRemoveFollow(myUserId: Int, followType: Int, followId: Int, callback: ResultCallBack) {
    if (!UserUtils.logged()) {
      callback.onError(RespondThrowable.fromUnLogin())
      return
    }
    mPersonalApi.addOrRemoveFollow(myUserId, followType, followId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun updateUserBasicInfo(userInfoData: UserBasicInfoData, callback: ResultCallBack) {
    mPersonalApi.updateUserBasicInfo(userInfoData)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getUserFansList(
    queryType: Int,
    queryId: Int,
    currentUserId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<FansItemData>>,
  ) {
    mPersonalApi.getUserFansList(queryType, queryId, currentUserId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getUserFollowedJobFairList(
    userId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<FollowedJobFairItemData>>,
  ) {
    mPersonalApi.getFollowedJobFairList(userId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun loginByWechat(code: String, callback: ResultDataCallBack<WechatLoginResultData>) {
    mPersonalApi.loginByWechat(code)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse.data as WechatLoginResultData?)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun checkAvatarIsUpload(userId: Int, callback: ResultCallBack) {
    mPersonalApi.checkAvatarIsUpload(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getUserAvatarAndNickname(userId: Int, callBack: ResultDataCallBack<UserInfoData>) {
    mPersonalApi.getUserAvatarAndNikename(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess(baseResponse.data as UserInfoData?)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onError(respondThrowable)
        }
      })
  }

  fun getDefaultAvatarList(
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<DefaultAvatarItemData>>,
  ) {
    mPersonalApi.getDefaultAvatarList(pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun updateAvatarAndNickname(userInfo: UserInfoData, callback: ResultCallBack) {
    mPersonalApi.updateAvatarAndNickname(userInfo)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun checkIsBindMobile(userId: Int, callback: ResultCallBack) {
    mCommonApi.checkIsBindMobile(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun bindMobileNumber(
    userId: Int,
    mobileNumber: String,
    smsCode: String,
    callback: ResultCallBack,
  ) {
    mPersonalApi.bindMobileNumber(userId, mobileNumber, smsCode)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun sendSmsCode(
    mobileNumber: String,
    sendType: Int,
    callback: ResultDataCallBack<BaseResponse<*>>,
  ) {
    mPersonalApi.sendSmsVerificationCode(mobileNumber, sendType)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun accountMerge(
    userId: Int,
    mobileNumber: String,
    mergeType: Int,
    callback: ResultDataCallBack<String>,
  ) {
    mPersonalApi.accountMerge(userId, mobileNumber, mergeType)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          if (baseResponse.data != null) {
            callback.onSuccess(baseResponse.data.toString())
          }
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun checkEnterpriseIsCertification(userId: Int, callback: ResultCallBack) {
    mPersonalApi.checkEnterpriseIsCertification(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getUserBasicInfo1(userId: Int, callback: ResultDataCallBack<UserBasicInfoData>) {
    mPersonalApi.getUserBasicInfo1(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse.data as UserBasicInfoData?)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getUserHomeInfo(
    queryUserId: Int,
    queryCompanyId: Int,
    myUserId: Int,
    callback: ResultDataCallBack<UserHomeData>,
    isOrg: Boolean = false,
  ) {
    mPersonalApi.getUserHomeInfo(queryUserId, queryCompanyId, myUserId, isOrg)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse.data as UserHomeData)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getUserNoticeList(
    queryCompanyId: Int,
    userId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<UserNoticeItemData>>,
  ) {
    mPersonalApi.getUserNoticeList(queryCompanyId, userId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getUserVideoList(
    queryUserId: Int,
    myUserId: Int,
    @UserVideoType type: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<VideoItemData>>,
  ) {
    mPersonalApi.getUserVideoList(queryUserId, myUserId, type, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun submitAnswer(
    userId: Int,
    replyUserId: Int,
    replyQuestionId: Int,
    content: String,
    photos: String,
    isClassicAnswer: Boolean,
    callback: ResultDataCallBack<AnswerItemData>,
  ) {
    mPersonalApi.submitAnswer(
      userId,
      replyUserId,
      replyQuestionId,
      content,
      photos,
      isClassicAnswer
    )
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse.data as AnswerItemData?)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun submitQuestion(
    userId: Int,
    provinceID: Int,
    cityId: Int,
    questionTitle: String,
    questionContent: String,
    callback: ResultCallBack,
  ) {
    mPersonalApi.submitQuestion(userId, provinceID, cityId, questionTitle, questionContent)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getUserQuestionList(
    queryUserId: Int,
    myUserId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<com.bxkj.jrzp.userhome.data.UserQuestionItemData>>,
  ) {
    mPersonalApi.getUserQuestionList(queryUserId, myUserId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getFollowList(
    queryUserId: Int,
    isEnterprise: Boolean,
    myUserId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<FollowItemData>>,
  ) {
    mPersonalApi.getFollowList(queryUserId, isEnterprise, myUserId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getMyCollectionList(
    userId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<UserHistoryItemData>>,
  ) {
    mPersonalApi.getMyCollectionList(userId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getMyCommentList(
    userId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<UserHistoryItemData>>,
  ) {
    mPersonalApi.getMyCommentList(userId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getMyLikeList(
    userId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<UserHistoryItemData>>,
  ) {
    mPersonalApi.getMyLikeList(userId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun deleteMyPublish(
    userId: Int,
    deleteType: Int,
    subType: Int,
    deleteId: Int,
    callback: ResultCallBack,
  ) {
    mPersonalApi.deleteMyPublish(userId, deleteType, subType, deleteId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun clearMyHistory(userId: Int, clearType: Int, callback: ResultCallBack) {
    mPersonalApi.clearMyHistory(userId, clearType)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun publishVideo(
    type: Int,
    userId: Int,
    title: String,
    content: String,
    coverPath: String,
    videoPath: String,
    cityId: Int = CommonApiConstants.NO_ID,
    moneyID: Int = CommonApiConstants.NO_ID,
    address: String = CommonApiConstants.NO_TEXT,
    callback: ResultCallBack,
  ) {
    var coverUrl: String? = null
    mPersonalApi.uploadFile(
      coverPath,
      UploadFileRequestParams.fromFileType(userId, UploadFileRequestParams.TYPE_IMG)
    ).flatMap {
      if (it.status == 10001) {
        coverUrl = it.msg
        mPersonalApi.uploadFile(
          videoPath,
          UploadFileRequestParams.fromFileType(userId, UploadFileRequestParams.TYPE_VIDEO)
        )
      } else {
        Observable.error(RespondThrowable(null, it.status, it.msg))
      }
    }.flatMap {
      if (it.status == 10001) {
        val videoUrl = it.msg
        mPersonalApi.publishVideo(
          type,
          userId,
          title,
          content,
          coverUrl,
          videoUrl,
          cityId,
          moneyID,
          address
        )
      } else {
        Observable.error(RespondThrowable(null, it.status, it.msg))
      }
    }.compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getResumeTopDiscount(
    userId: Int,
    type: Int,
    callback: ResultDataCallBack<List<ResumeTopDiscountItemData>>,
  ) {
    mPersonalApi.getResumeTopDiscount(userId, type)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun createResumeTopOrder(
    userId: Int,
    resumeId: Int,
    topDays: Int,
    callback: ResultDataCallBack<String>,
  ) {
    mPersonalApi.createResumeTopOrder(userId, resumeId, topDays)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse.msg)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getAlipayOrderInfo(
    userId: Int,
    orderId: String,
    callBack: ResultDataCallBack<CreateOrderResultData>,
  ) {
    mPersonalApi.getAlipayOrderInfo(userId, orderId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess(CreateOrderResultData(baseResponse.msg, orderId))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onError(respondThrowable)
        }
      })
  }

  fun payOfAlipay(
    payTask: PayTask,
    createOrderResultData: CreateOrderResultData,
    callBack: ResultDataCallBack<AliPayResultData>,
  ) {
    mCompositeDisposable.add(
      Observable.create({ emitter ->
        val resultMap =
          payTask.payV2(
            HtmlUtils.fromHtml(createOrderResultData.alipayOrderInfo).toString(), true
          )
        emitter.onNext(AliPayResultData(resultMap))
      }).subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .subscribe({
          it.orderId = createOrderResultData.orderId
          callBack.onSuccess(it)
        }, {
          callBack.onError(RespondThrowable(it, ExceptionCode.UNKNOWN, "支付失败，请重试"))
        })
    )
  }

  fun getPaymentResult(
    userId: Int,
    orderId: String,
    callBack: ResultDataCallBack<OrderItemData>
  ) {
    mPersonalApi.getPaymentResult(userId, orderId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callBack.onSuccess(baseResponse.data as OrderItemData?)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callBack.onError(respondThrowable)
        }
      })
  }

  fun getOrderHistory(
    userId: Int,
    type: Int,
    isPay: Int,
    isEffect: Int,
    invoiceId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<OrderItemData>>,
  ) {
    mPersonalApi.getOrderList(userId, type, isPay, isEffect, invoiceId, pageSize, pageIndex)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun activateOrder(userId: Int, orderId: Int, callback: ResultCallBack) {
    mPersonalApi.activateOrder(userId, orderId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun deleteOrder(userId: Int, orderId: Int, callback: ResultCallBack) {
    mPersonalApi.deleteOrder(userId, orderId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun searchUserByName(
    name: String,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<InviteUserItemData>>,
  ) {
    mPersonalApi.searchUserByName(name, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getMsgNotificationList(
    userId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<FeedbackMsgItemData>>,
  ) {
    mPersonalApi.getMsgNotificationList(userId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getMsgNotificationContent(
    userId: Int,
    senderId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<FeedbackMsgContentItemData>>,
  ) {
    mPersonalApi.getMsgNotificationContentListByCompany(userId, senderId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun setupMsgReaded(userId: Int, msgId: Int, callback: ResultCallBack) {
    mPersonalApi.setupMsgReaded(userId, msgId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun uploadUserSearchAction(userId: Int, keyword: String, callback: ResultCallBack) {
    mPersonalApi.uploadUserSearchAction(userId, CommonApiConstants.NO_ID, keyword)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun uploadUserOfflineSearchAction(userId: Int, keywords: String, callback: ResultCallBack) {
    mPersonalApi.uploadUserOfflineSearchAction(userId, CommonApiConstants.NO_TEXT, keywords)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getPostVideoTypeList(callback: ResultDataCallBack<List<VideoTypeItemData>>) {
    mPersonalApi.videoTypeList
      .subscribeOn(Schedulers.io())
      .observeOn(AndroidSchedulers.mainThread())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun verifyPublishVideo(userId: Int, type: Int, callback: ResultCallBack) {
    mPersonalApi.verifyPublishVideo(userId, type)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun videoRecruitSignUp(
    videoId: Int,
    name: String,
    phone: String,
    callback: ResultDataCallBack<BaseResponse<*>>,
  ) {
    mPersonalApi.videoRecruitSignUp(videoId, name, phone)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getUserSignInInfo(userId: Int, callback: ResultDataCallBack<UserSignInData>) {
    mPersonalApi.getUserSignInInfo(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse.data as UserSignInData?)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getSignUpUserList(
    videoId: Int,
    status: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<SignUpUserItemData>>,
  ) {
    mPersonalApi.getSignUpUserList(videoId, status, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun updateSignUpUserStatus(recordId: Int, status: Int, callback: ResultCallBack) {
    mPersonalApi.updateSignUpUserStatus(recordId, status)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getVideoUnreadMsgList(
    userId: Int,
    type: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<VideoUnreadMsgItemData>>,
  ) {
    mPersonalApi.getVideoUnreadMsgList(userId, type, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun setupMsgReadV2(msgId: Int, callback: ResultCallBack) {
    mPersonalApi.setupMsgReadV2(msgId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getQAUserInfo(userId: Int, callback: ResultDataCallBack<QAUserData>) {
    mPersonalApi.getQAUserInfo(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse.data as QAUserData)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  suspend fun getAnswerHistory(
    userID: Int,
    title: String,
    pageIndex: Int,
    pageSize: Int,
  ): ReqResponse<List<AnswerItemData>> {
    return httpRequest {
      coroutinesApi.getAnswerHistory(
        ZPRequestBody()
          .apply {
            put("userID", userID)
            put("title", title)
            put("pageIndex", pageIndex)
            put("pageSize", pageSize)
          }
      )
    }
  }

  /**
   * 添加屏蔽公司
   */
  suspend fun addShieldCompany(
    userId: Int,
    companyIds: String,
    companyNames: String,
  ): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.addShieldCompany(
        ZPRequestBody().apply {
          put("userID", userId)
          put("companyIDs", companyIds)
          put("companyNames", companyNames)
        }
      )
    }
  }

  /**
   * 移除屏蔽公司
   */
  suspend fun removeShieldCompany(userId: Int, companyIds: String): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.removeShieldCompany(
        ZPRequestBody().apply {
          put("userID", userId)
          put("ids", companyIds)
        }
      )
    }
  }

  /**
   * 获取用户状态
   */
  suspend fun getUserStatus(userId: Int): ReqResponse<UserStatusData> {
    return httpRequest {
      coroutinesApi.getUserStatus(ZPRequestBody().apply {
        put("userID", userId)
      }.paramsEncrypt())
    }
  }

  /**
   * 更新用户状态
   */
  suspend fun updateUserStatus(userID: Int, stateID: Int): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.updateUserStatus(
        ZPRequestBody().apply {
          put("userID", userID)
          put("stateID", stateID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 检查是否开启首页推荐
   */
  suspend fun checkOpenPersonalizedPush(userId: Int): ReqResponse<Boolean> {
    return httpRequest {
      coroutinesApi.checkOpenHomePush(
        ZPRequestBody().apply {
          put("userID", userId)
        }.paramsEncrypt()
      ).apply {
        if (status == 10001) {
          data = (msg == "1")
        }
      }
    }
  }

  /**
   * 修改首页推荐状态
   */
  suspend fun switchHomePushStatus(userID: Int, open: Boolean): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.switchHomePushStatus(
        ZPRequestBody().apply {
          put("userID", userID)
          put("tuijian", if (open) 1 else 0)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 接受面试邀请
   */
  suspend fun acceptInviteSendResume(userId: Int, otherId: Int): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.acceptInviteSendResume(
        ZPRequestBody().apply {
          put("uid", userId)
          put("id", otherId)
        }
      )
    }
  }

  /**
   * 拒绝投递简历
   */
  suspend fun rejectInviteSendResume(userId: Int, otherId: Int): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.rejectInviteSendResume(
        ZPRequestBody().apply {
          put("uid", userId)
          put("id", otherId)
        }
      )
    }
  }

  /**
   * 接受面试
   */
  suspend fun acceptInviteInterview(userId: Int, otherId: Int): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.acceptInviteInterview(
        ZPRequestBody().apply {
          put("uid", userId)
          put("id", otherId)
        }
      )
    }
  }

  /**
   * 拒绝面试
   */
  suspend fun rejectInviteInterview(userId: Int, otherId: Int): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.rejectInviteInterview(
        ZPRequestBody().apply {
          put("uid", userId)
          put("id", otherId)
        }
      )
    }
  }

  /**
   * 绑定用户推送Token
   */
  suspend fun bindUserPushToken(userId: Int, token: String): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.bindUserPushToken(
        ZPRequestBody().apply {
          put("uid", userId)
          put("type", 1)
          put("userToken", token)
        }.paramsEncrypt()
      )
    }
  }

  fun checkAvatarIsUplaod(userId: Int, callback: ResultCallBack) {
    mPersonalApi.checkAvatarIsUpload(userId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  /**
   *  获取邀请余额
   */
  suspend fun getGreetBalance(userId: Int): ReqResponse<GreetBalanceData> {
    return httpRequest {
      coroutinesApi.getGreetBalance(
        ZPRequestBody().apply {
          put("uid", userId)
        }.objectEncrypt()
      )
    }
  }

  /**
   * 获取在线职位
   */
  suspend fun getOnlineJobList(userID: Int): ReqResponse<List<OnlinePositionData>> {
    return httpRequest {
      coroutinesApi.getOnlineJobList(ZPRequestBody().apply {
        put("uid", userID)
      })
    }
  }

  /**
   * 收藏简历
   */
  suspend fun collectionResume(userID: Int, resumeID: Int): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.collectionResume(ZPRequestBody().apply {
        put("cuid", userID)
        put("resid", resumeID)
      })
    }
  }
}