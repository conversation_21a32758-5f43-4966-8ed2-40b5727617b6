package com.bxkj.personal.ui.activity.paiduser

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.PaidUserItemData
import com.bxkj.personal.databinding.PersonalActivityPaidUserBinding

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.paiduser
 * @Description: 已支付用户
 * <AUTHOR>
 * @date 2019/12/11
 * @version V1.0
 */
class PaidUserActivity : BaseDBActivity<PersonalActivityPaidUserBinding, PaidUserViewModel>() {

    companion object {
        const val EXTRA_MOMENT_ID = "MOMENT_ID"
        const val EXTEA_QUERY_ALL = "QUERY_ALL"

        fun newIntent(context: Context): Intent {
            return newIntent(context, true, CommonApiConstants.NO_DATA)
        }

        fun newIntent(context: Context, momentId: Int): Intent {
            return newIntent(context, false, momentId)
        }

        fun newIntent(context: Context, queryAll: Boolean, momentId: Int): Intent {
            val intent = Intent(context, PaidUserActivity::class.java)
            intent.putExtra(EXTEA_QUERY_ALL, queryAll)
            intent.putExtra(EXTRA_MOMENT_ID, momentId)
            return intent
        }

    }

    override fun getViewModelClass(): Class<PaidUserViewModel> = PaidUserViewModel::class.java

    override fun getLayoutId(): Int = R.layout.personal_activity_paid_user

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        setupPaidUserListAdapter()
        viewModel.start(intent)
    }

    private fun setupPaidUserListAdapter() {
        val recyclerPaidUserList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
        recyclerPaidUserList.layoutManager = LinearLayoutManager(this)
        val paidUserListAdapter =
          SimpleDBListAdapter<PaidUserItemData>(
            this,
            layout.personal_recycler_paid_user_item
          )
        viewModel.listViewModel.setAdapter(paidUserListAdapter)
    }

}