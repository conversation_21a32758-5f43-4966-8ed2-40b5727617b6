package com.bxkj.personal.ui.activity.qaranklist

import android.app.Application
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.QARankItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.QuestionRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/4/1
 * @version: V1.0
 */
class QARankListViewModel @Inject constructor(application: Application
                                              , private val mQuestionRepo: QuestionRepo
                                              , private val mAccountRepo: AccountRepo) : BaseViewModel() {

    val qaRankListViewModel = RefreshListViewModel()

    init {
        qaRankListViewModel.setOnLoadDataListener { currentPage ->
            mQuestionRepo.getQARankList(getSelfUserID(), currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE
                    , object : ResultDataCallBack<List<QARankItemData>> {
                override fun onSuccess(data: List<QARankItemData>?) {
                    qaRankListViewModel.autoAddAll(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30001) {
                        qaRankListViewModel.noMoreData()
                    } else {
                        qaRankListViewModel.loadError()
                    }
                }
            })
        }
    }

    fun start() {
        qaRankListViewModel.refresh()
    }

    fun followOrUnFollow(qaRankItemData: QARankItemData) {
        if (checkLoginStateAndToLogin()) {
            mAccountRepo.addOrRemoveFollow(getSelfUserID(), PersonalApiConstants.FOLLOW_USER_TYPE, qaRankItemData.userID
                    , object : ResultCallBack {
                override fun onSuccess() {
                    qaRankItemData.isFollowUser = true
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 10002) {
                        qaRankItemData.isFollowUser = false
                    } else {
                        showToast(respondThrowable.errMsg)
                    }
                }
            })
        }
    }
}