package com.bxkj.personal.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.util.TimeUtils;
import com.bxkj.common.util.UserUtils;
import com.bxkj.personal.BR;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2019/12/9
 */
public class UserHomeHeaderData extends BaseObservable {

    /**
     * name2 : 大龙猫
     * sex : 0
     * userSex : 男
     * tx : http://img.jrzp.com/images_server/Personal/402847/8P8F404314_4745030.jpg
     * birthday : 1989-12-19
     * provinceJZ : 28009
     * cityJZ : 28590
     * quaId : 6
     * graduationTime : 2013-07
     * provinceJZName : 浙江
     * cityJZName : 杭州
     * quaName : 大专
     * jieshao :
     * integral : 136
     * dongtaiCount : 3
     * guanzhuCount : 0
     * fensiCount : 0
     * yaoqingCount : 0
     */

    private int uid;
    private String name2;
    private int sex;
    private String userSex;
    private String tx;
    private String birthday;
    private int provinceJZ;
    private int cityJZ;
    private int quaId;
    private String graduationTime;
    private String provinceJZName;
    private String cityJZName;
    private String quaName;
    private String jieshao;
    private int integral;
    private int dongtaiCount;
    private int guanzhuCount;
    private int fensiCount;
    private int yaoqingCount;
    private int stowCount;
    private int isHasGuanzhu;
    private int companyID;

    public int getCompanyID() {
        return companyID;
    }

    public void setCompanyID(int companyID) {
        this.companyID = companyID;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public String getName2() {
        return name2;
    }

    public void setName2(String name2) {
        this.name2 = name2;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public String getUserSex() {
        return userSex;
    }

    public void setUserSex(String userSex) {
        this.userSex = userSex;
    }

    public String getTx() {
        return tx;
    }

    public void setTx(String tx) {
        this.tx = tx;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public int getProvinceJZ() {
        return provinceJZ;
    }

    public void setProvinceJZ(int provinceJZ) {
        this.provinceJZ = provinceJZ;
    }

    public int getCityJZ() {
        return cityJZ;
    }

    public void setCityJZ(int cityJZ) {
        this.cityJZ = cityJZ;
    }

    public int getQuaId() {
        return quaId;
    }

    public void setQuaId(int quaId) {
        this.quaId = quaId;
    }

    public String getGraduationTime() {
        return graduationTime;
    }

    public void setGraduationTime(String graduationTime) {
        this.graduationTime = graduationTime;
    }

    public String getProvinceJZName() {
        return provinceJZName;
    }

    public void setProvinceJZName(String provinceJZName) {
        this.provinceJZName = provinceJZName;
    }

    public String getCityJZName() {
        return cityJZName;
    }

    public void setCityJZName(String cityJZName) {
        this.cityJZName = cityJZName;
    }

    public String getQuaName() {
        return quaName;
    }

    public void setQuaName(String quaName) {
        this.quaName = quaName;
    }

    public String getJieshao() {
        return jieshao;
    }

    public void setJieshao(String jieshao) {
        this.jieshao = jieshao;
    }

    public int getIntegral() {
        return integral;
    }

    public void setIntegral(int integral) {
        this.integral = integral;
    }

    public int getDongtaiCount() {
        return dongtaiCount;
    }

    public void setDongtaiCount(int dongtaiCount) {
        this.dongtaiCount = dongtaiCount;
    }

    @Bindable
    public int getGuanzhuCount() {
        return guanzhuCount;
    }

    public void setGuanzhuCount(int guanzhuCount) {
        this.guanzhuCount = guanzhuCount;
        notifyPropertyChanged(BR.guanzhuCount);
    }

    @Bindable
    public int getFensiCount() {
        return fensiCount;
    }

    public void setFensiCount(int fensiCount) {
        this.fensiCount = fensiCount;
        notifyPropertyChanged(BR.fensiCount);
    }

    public int getYaoqingCount() {
        return yaoqingCount;
    }

    public void setYaoqingCount(int yaoqingCount) {
        this.yaoqingCount = yaoqingCount;
    }

    public int getStowCount() {
        return stowCount;
    }

    public void setStowCount(int stowCount) {
        this.stowCount = stowCount;
    }

    public boolean isSelf() {
        return UserUtils.logged() && UserUtils.getUserId() == uid;
    }

    @Bindable
    public int getIsHasGuanzhu() {
        return isHasGuanzhu;
    }

    public void setIsHasGuanzhu(int isHasGuanzhu) {
        this.isHasGuanzhu = isHasGuanzhu;
        notifyPropertyChanged(com.bxkj.personal.BR.isHasGuanzhu);
    }

    /**
     * 添加关注
     */
    public void addFollow() {
        setIsHasGuanzhu(1);
        setFensiCount(fensiCount + 1);
    }

    /**
     * 取消关注
     */
    public void removeFollow() {
        setIsHasGuanzhu(0);
        setFensiCount(fensiCount - 1);
    }

    public String getSexText() {
        if (sex == 0) {
            return "男";
        } else {
            return "女";
        }
    }

    public int getAge() {
        return TimeUtils.getAge(birthday, "yyyy-MM-dd");
    }
}
