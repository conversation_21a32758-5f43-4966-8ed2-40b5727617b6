package com.bxkj.personal.ui.activity.resumedetails.itemviewbinder;

import android.app.Activity;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.R;
import com.bxkj.personal.data.LanguageSkillsItemData;
import com.bxkj.personal.ui.activity.resumedetails.adapter.LanguageSkillsAdapter;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.itemviewbinder
 * @Description: 专业技能视图绑定
 * @TODO: TODO
 * @date 2018/5/9
 */

public class LanguageViewBinder implements ItemViewBinder<LanguageSkillsItemData> {

    private Activity mActivity;

    public LanguageViewBinder(Activity activity) {
        mActivity = activity;
    }

    @Override
    public void onBindViewHolder(SuperViewHolder holder, LanguageSkillsItemData item, int position) {
        holder.setText(R.id.tv_tag,mActivity.getString(R.string.resume_details_language_proficiency));
        LanguageSkillsAdapter languageSkillsAdapter = new LanguageSkillsAdapter(mActivity, item.getLanguageSkillsItemDataList(), R.layout.enterprise_recycler_resume_skill_item);
        RecyclerView recyclerView = holder.findViewById(R.id.recycler);
        recyclerView.setVisibility(
            CheckUtils.isNullOrEmpty(item.getLanguageSkillsItemDataList()) ? View.GONE : View.VISIBLE);
        recyclerView.setLayoutManager(new LinearLayoutManager(mActivity));
        recyclerView.setAdapter(languageSkillsAdapter);
    }

    @Override
    public int getLayoutId() {
        return R.layout.enterprise_recycler_resume_item;
    }
}
