package com.bxkj.personal.data.source

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.network.*
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.personal.api.CoroutinesApi
import com.bxkj.personal.api.PersonalApi
import com.bxkj.personal.data.*
import com.bxkj.personal.ui.activity.postnotice.PostNoticeRequest
import com.bxkj.personal.ui.activity.postnotice.SchoolNoticeData
import com.bxkj.personal.ui.fragment.homenews.HomeNewsRequestOptions
import io.reactivex.android.schedulers.AndroidSchedulers

import javax.inject.Inject

import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

/**
 * @date 2019/4/30
 */
class NewsRepo @Inject constructor(
    private val mPersonalApi: PersonalApi,
    private val mCoroutinesApi: CoroutinesApi
) : BaseRepo() {

    /**
     * 获取热议评论列表
     */
    suspend fun getHotDiscussComments(
        discussId: Int,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<List<CommentItemData>> {
        return httpRequest {
            mCoroutinesApi.getHotDiscussComments(
                ZPRequestBody().apply {
                    put("nid", discussId)
                    put("type", 4)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }
            )
        }
    }

    /**
     * 获取热议详情
     */
    suspend fun getHotDiscussDetails(id: Int): ReqResponse<List<DiscussData>> {
        return httpRequest {
            mCoroutinesApi.getHotDiscussDetails(
                ZPRequestBody().apply {
                    put("userID", UserUtils.getUserId())
                    put("wdID", id)
                    put("type", 4)
                }
            )
        }
    }

    /**
     * 获取好文评论
     */
    suspend fun getArticleComment(
        articleId: Int,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<List<CommentItemData>> {
        return httpRequest {
            mCoroutinesApi.getArticleComment(
                ZPRequestBody().apply {
                    put("nid", articleId)
                    put("type", 1)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }
            )
        }
    }

    /**
     * 获取好文详情
     */
    suspend fun getFineArticleDetails(articleId: Int): ReqResponse<FineArticleData> {
        return httpRequest {
            mCoroutinesApi.getFineArticleDetails(
                ZPRequestBody().apply {
                    put("userID", UserUtils.getUserId())
                    put("id", articleId)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 获取好文列表
     */
    suspend fun getFineArticleList(
        title: String,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<ListDTO<FineArticleData>> {
        return httpRequest {
            mCoroutinesApi.getFineArticleList(
                ZPRequestBody().apply {
                    put("title", title)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }
            )
        }
    }

    suspend fun updateSchoolNotice(
        userID: Int,
        noticeID: Int,
        title: String,
        content: String
    ): ReqResponse<Nothing> {
        return httpRequest {
            mCoroutinesApi.updateSchoolNotice(
                ZPRequestBody().apply {
                    put("id", noticeID)
                    put("uid", userID)
                    put("title", title)
                    put("content", content)
                }.paramsEncrypt()
            )
        }
    }

    suspend fun getSchoolNotice(noticeID: Int): ReqResponse<SchoolNoticeData> {
        return httpRequest {
            mCoroutinesApi.getSchoolNotice(
                ZPRequestBody().apply {
                    put("id", noticeID)
                }.paramsEncrypt()
            )
        }
    }

    fun getFacultyRecruitDetails(
        newsId: Int,
        callBack: ResultDataCallBack<FacultyRecruitDetailsData>
    ) {
        mPersonalApi.getFacultyRecruitmentDetails(newsId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callBack.onSuccess(baseResponse.data as FacultyRecruitDetailsData)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callBack.onError(respondThrowable)
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }
            })
    }

    fun getHomeNewsTypeList(userId: Int, callback: ResultDataCallBack<List<NewsTypeItemData>>) {
        if (UserUtils.logged()) {
            mPersonalApi.getSortedNewsTypeList(userId)
        } else {
            mPersonalApi.homeNewsTypeList
        }
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    suspend fun getSchoolNoticeType(): ReqResponse<List<PickerOptionsData>> {
        return httpRequest {
            mCoroutinesApi.getSchoolNoticeType()
        }
    }

    fun getNewsList(
        userId: Int,
        countyId: Int,
        cityId: Int,
        provinceId: Int,
        attachCityId: Int,
        newsType: Int,
        desc: Int,
        searchKeyword: String,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<NewsPageResponse>,
        homeNewsRequestOptions: HomeNewsRequestOptions? = null,
        isFollow: Boolean = false,
        wdIds: String = ""
    ) {
        mPersonalApi.getNewNewsList(
            userId,
            countyId,
            cityId,
            provinceId,
            attachCityId,
            newsType,
            desc,
            searchKeyword,
            pageIndex,
            pageSize,
            homeNewsRequestOptions,
            isFollow,
            wdIds
        ).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : CustomTObserver<NewsPageResponse>() {
                override fun onSuccess(result: NewsPageResponse) {
                    callback.onSuccess(result)
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getRecommendNewsList(
        userId: Int,
        countyId: Int,
        cityId: Int,
        provinceId: Int,
        attachCityId: Int,
        searchKeyword: String,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<NewsPageResponse>,
        homeNewsRequestOptions: HomeNewsRequestOptions? = null
    ) {
        mPersonalApi.getRecommendNewsList(
            userId,
            countyId,
            cityId,
            provinceId,
            attachCityId,
            searchKeyword,
            pageIndex,
            pageSize,
            homeNewsRequestOptions
        ).subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : CustomTObserver<NewsPageResponse>() {
                override fun onSuccess(result: NewsPageResponse) {
                    callback.onSuccess(result)
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getNewsDetails(userId: Int, newsId: Int, callback: ResultDataCallBack<GzNewsDetailsData>) {
        mPersonalApi.getGzNewsDetails(userId, newsId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(baseResponse.data as GzNewsDetailsData?)
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getLinkRecommendNewsList(
        subNewsTypeId: Int,
        callback: ResultDataCallBack<List<RecommendNewsItemData>>
    ) {
        mPersonalApi.getLinkRecommendNewsList(subNewsTypeId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getVideoList(
        userId: Int,
        videoType: Int,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<VideoItemData>>,
        title: String? = ""
    ) {
        mPersonalApi.getVideoList(userId, videoType, pageIndex, pageSize, title)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getVideoDetails(userId: Int, videoId: Int, callBack: ResultDataCallBack<VideoItemData>) {
        mPersonalApi.getVideoDetails(videoId, userId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callBack.onSuccess(baseResponse.data as VideoItemData?)
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callBack.onError(respondThrowable)
                }
            })
    }

    fun postNotice(request: PostNoticeRequest, callback: ResultCallBack) {
        mPersonalApi.postNotice(request)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess()
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getRecommendVideo(callback: ResultDataCallBack<List<VideoItemData>>) {
        mPersonalApi.recommendVideo
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun updateVideoPlayCount(videoId: Int, callBack: ResultCallBack? = null) {
        mPersonalApi.updateVideoCount(videoId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callBack?.onSuccess()
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callBack?.onError(respondThrowable)
                }
            })
    }

    fun getDistrictNewsList(
        newsIDs: String,
        distractId: Int,
        keyword: String,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<NewsItemData>>
    ) {
        mPersonalApi.getDistrictNewsList(newsIDs, distractId, keyword, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getStudyNewsList(
        pageIndex: Int,
        pageSize: Int,
        type: Int,
        title: String,
        callback: ResultDataCallBack<List<StudyNewsItemData>>
    ) {
        mPersonalApi.getStudyNewsList(pageIndex, pageSize, type, title)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    fun getStudyNewsDetails(
        userId: Int,
        newId: Int,
        callback: ResultDataCallBack<StudyNewsDetailsData>
    ) {
        mPersonalApi.getStudyNewsDetails(userId, newId)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(baseResponse.data as StudyNewsDetailsData)
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }

    /**
     * 根据资讯子分类[newsSubType]和资讯标题[newsTitle]返回给定广告信息
     */
    suspend fun getStudyNewsAdInfo(
        newsSubType: Int,
        newsTitle: String
    ): ReqResponse<StudyNewsAdData> {
        return httpRequest {
            mCoroutinesApi.getStudyNewsAdInfo(
                ZPRequestBody().apply {
                    put("ntid2", newsSubType)
                    put("keyword", newsTitle)
                }.paramsEncrypt()
            )
        }
    }

    suspend fun recordNewsAdClickAction(
        tag: String,
        type: Int,
        adID: Int,
        pageTag: String
    ): ReqResponse<Nothing> {
        return httpRequest {
            mCoroutinesApi.recordNewsAdClickAction(
                ZPRequestBody().apply {
                    put("identity", tag)
                    put("type", type)
                    put("tgid", adID)
                    put("webUrl", pageTag)
                }.paramsEncrypt()
            )
        }
    }

    fun searchVideo(
        keyword: String,
        pageIndex: Int,
        pageSize: Int,
        callback: ResultDataCallBack<List<VideoItemData>>
    ) {
        mPersonalApi.searchVideo(keyword, pageIndex, pageSize)
            .compose(RxHelper.applyThreadSwitch())
            .subscribe(object : CustomObserver() {
                override fun onSuccess(baseResponse: BaseResponse<*>) {
                    callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
                }

                override fun onSubscribe(d: Disposable) {
                    mCompositeDisposable.add(d)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    callback.onError(respondThrowable)
                }
            })
    }
}
