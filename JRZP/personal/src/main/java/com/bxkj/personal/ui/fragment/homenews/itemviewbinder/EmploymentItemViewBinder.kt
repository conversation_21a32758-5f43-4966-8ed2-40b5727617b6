package com.bxkj.personal.ui.fragment.homenews.itemviewbinder

import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.ecommon.BR
import com.bxkj.personal.R
import com.bxkj.personal.data.NewsItemData

/**
 * @Description:
 * @author: Yang<PERSON><PERSON>
 * @date: 2020/12/25
 * @version: V1.0
 */
class EmploymentItemViewBinder :
  DefaultViewBinder<NewsItemData>(R.layout.personal_recycler_news_list_employment, BR.data, false) {

  override fun onBindViewHolder(holder: SuperViewHolder, item: NewsItemData, position: Int) {
    super.onBindViewHolder(holder, item, position)
    holder.bind(BR.data, item.jiuye)
  }
}