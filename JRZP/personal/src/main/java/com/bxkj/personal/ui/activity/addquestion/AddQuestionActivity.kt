package com.bxkj.personal.ui.activity.addquestion

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.Observer
import com.baidu.location.BDLocation
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.location.LocationManager
import com.bxkj.common.util.qmui.AndroidBug5497Workaround
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalActivityAddQuestionBinding

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.addquestion
 * @Description: 提问
 * <AUTHOR>
 * @date 2020/1/13
 * @version V1.0
 */
class AddQuestionActivity :
  BaseDBActivity<PersonalActivityAddQuestionBinding, AddQuestionViewModel>() {

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, AddQuestionActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<AddQuestionViewModel> = AddQuestionViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_add_question

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    AndroidBug5497Workaround.assistActivity(this)

    viewBinding.titleBar.setRightOptionClickListener {
      viewModel.submitQuestion()
    }

    getLocation()

    subscribeViewModelEvent()
  }

  private fun getLocation() {
    LocationManager.requestLocation(this, object : LocationManager.OnLocationListener {
      override fun onSuccess(location: BDLocation) {
        viewModel.setupLocation(location)
      }

      override fun onFailed() {
      }
    })
  }

  private fun subscribeViewModelEvent() {
    viewModel.addQuestionSuccessEvent.observe(this, Observer {
      UserHomeNavigation.navigate(localUserId).start()
      finish()
    })
  }

}