package com.bxkj.personal.ui.activity.shieldcompany

import android.app.Application
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.CheckUtils
import com.bxkj.personal.data.ShieldCompanyData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.CompanyRepo
import kotlinx.coroutines.launch
import java.lang.StringBuilder
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/6/10
 * @version: V1.0
 */
class ShieldCompanyViewModel @Inject constructor(
  application: Application,
  private val mCompanyRepo: CompanyRepo,
  private val mAccountRepo: AccountRepo
) :
  BaseViewModel() {

  val shieldCompanyListViewModel = RefreshListViewModel()

  //屏蔽公司总数
  val shieldCompanyCount = MutableLiveData<Int>().apply { value = 0 }

  val editStatus = MutableLiveData<Boolean>().apply { value = false }

  val allSelected = MutableLiveData<Boolean>().apply { value = false }

  private val _selectItem = ArrayList<ShieldCompanyData>()

  val selectedItem = MutableLiveData<ArrayList<ShieldCompanyData>>()

  init {
    setupShieldCompanyListViewModel()
  }

  private fun setupShieldCompanyListViewModel() {
    shieldCompanyListViewModel.refreshLayoutViewModel.enableRefresh(false)
    shieldCompanyListViewModel.refreshLayoutViewModel.enableLoadMore(false)
    shieldCompanyListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        mCompanyRepo.getShieldCompanyList(getSelfUserID(), currentPage, 100)
          .handleResult({
            shieldCompanyCount.value = it?.size
            shieldCompanyListViewModel.reset(it)
          }, {
            if (it.isNoDataError) {
              shieldCompanyCount.value = 0
              shieldCompanyListViewModel.noMoreData()
            } else {
              shieldCompanyListViewModel.loadError()
            }
          })
      }
    }
  }

  fun start() {
    shieldCompanyListViewModel.refresh()
  }

  fun refreshData() {
    shieldCompanyListViewModel.refresh()
  }

  fun switchEditState() {
    editStatus.value?.let {
      val currentState = it.not()
      if (!currentState) {
        allSelected.value = false
        _selectItem.clear()
        notifySelectedChange()
      }
      editStatus.value = it.not()
    }
  }

  fun setSelectAll(selectAll: Boolean) {
    allSelected.value = selectAll
  }

  fun getSelectedCompany(): ArrayList<ShieldCompanyData> {
    return _selectItem
  }

  fun notifySelectedChange() {
    selectedItem.value = _selectItem
  }

  fun removeShieldCompany(shieldCompanyData: ShieldCompanyData) {
    viewModelScope.launch {
      showLoading()
      mAccountRepo.removeShieldCompany(getSelfUserID(), shieldCompanyData.id.toString())
        .handleResult({
          showToast("解除屏蔽成功")
          shieldCompanyCount.value?.let {
            shieldCompanyCount.value = it - 1
          }
          if (shieldCompanyListViewModel.remove(shieldCompanyData) == 0) {
            shieldCompanyListViewModel.refresh()
          }
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  fun confirmDelete() {
    if (CheckUtils.isNullOrEmpty(_selectItem)) {
      showToast("最少选择一家公司")
    } else {
      val idsBuilder = StringBuilder()
      _selectItem.forEach {
        idsBuilder.append(it.id).append(",")
      }
      viewModelScope.launch {
        showLoading()
        mAccountRepo.removeShieldCompany(
          getSelfUserID(),
          idsBuilder.substring(0, idsBuilder.lastIndexOf(","))
        ).handleResult({
          showToast("解除屏蔽成功")
          if (shieldCompanyListViewModel.removeAll(_selectItem) == 0) {
            shieldCompanyListViewModel.refresh()
            editStatus.value = false
          }
          shieldCompanyCount.value?.let {
            shieldCompanyCount.value = it - _selectItem.size
          }
          _selectItem.clear()
          notifySelectedChange()
          allSelected.value = false
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

}