package com.bxkj.personal.data;

import com.bxkj.personal.enums.NewsType;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: gzgk
 * @Package com.bxkj.personal.data
 * @Description: 首页资讯分类
 * @date 2019/12/23
 */
public class NewsTypeItemData {
    private int id;
    private String name;
    private int px;
    private boolean showExpand;

    public static NewsTypeItemData fromCustom(@NewsType.Type int id, String name) {
        return new NewsTypeItemData(id, name);
    }

    public static NewsTypeItemData fromCustom(@NewsType.Type int id, String name, boolean showExpand) {
        return new NewsTypeItemData(id, name, showExpand);
    }

    public NewsTypeItemData(@NewsType.Type int id, String name) {
        this.id = id;
        this.name = name;
    }

    public NewsTypeItemData(@NewsType.Type int id, String name, boolean showExpand) {
        this.id = id;
        this.name = name;
        this.showExpand = showExpand;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isShowExpand() {
        return showExpand;
    }
}
