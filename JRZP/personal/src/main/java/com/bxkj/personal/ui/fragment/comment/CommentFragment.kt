package com.bxkj.personal.ui.fragment.comment

import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.jzvd.Jzvd
import com.bxkj.common.adapter.HasCheckBoxListAdapter
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.UserHistoryItemData
import com.bxkj.personal.databinding.PersonalFragmentMyHistoryBinding
import com.bxkj.personal.ui.activity.answerdetails.AnswerDetailsActivity
import com.bxkj.personal.ui.activity.gznews.GzNewsDetailsActivity
import com.bxkj.personal.ui.activity.myhistory.MyHistoryActivity
import com.bxkj.personal.ui.activity.studynewsdetails.StudyNewsDetailsActivity
import com.bxkj.personal.ui.activity.videodetails.VideoDetailsActivity
import com.bxkj.personal.weight.videoplaylayout.VideoPlayLayout

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.comment
 * @Description: 评论
 * <AUTHOR>
 * @date 2020/2/13
 * @version V1.0
 */
class CommentFragment : BaseDBFragment<PersonalFragmentMyHistoryBinding, CommentViewModel>() {

  companion object {
    fun newInstance(): Fragment {
      return CommentFragment()
    }
  }

  private lateinit var mHasCheckBoxListAdapter: HasCheckBoxListAdapter

  override fun getViewModelClass(): Class<CommentViewModel> = CommentViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_fragment_my_history

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.listViewModel = viewModel.listViewModel

    setupCommentList()
    subscribeEditStatus()
    subscribeDeleteSuccess()
  }

  override fun lazyLoadData() {
    viewModel.start()
  }

  override fun onHiddenChanged(hidden: Boolean) {
    super.onHiddenChanged(hidden)
    if (hidden) {
      Jzvd.releaseAllVideos()
    }
  }

  private fun subscribeDeleteSuccess() {
    viewModel.deleteSuccessEvent.observe(this, Observer {
      mHasCheckBoxListAdapter.clearChecked()
    })

    viewModel.deleteToEmptyEvent.observe(this, Observer {
      (this.parentActivity as MyHistoryActivity).viewModel.closeEdit()
    })

    viewModel.showClearConfirmEvent.observe(this, Observer {
      ActionDialog.Builder()
        .setTitle(getString(R.string.tips))
        .setContent(getString(R.string.my_history_clear_tips))
        .setOnConfirmClickListener {
          (parentActivity as MyHistoryActivity).viewModel.closeEdit()
          viewModel.clearAllComment()
        }.build().show(childFragmentManager)
    })

    viewModel.enableEditCommand.observe(this, Observer {
      (parentActivity as MyHistoryActivity).viewModel.enableEdit(1, it)
    })
  }

  private fun subscribeEditStatus() {
    (parentActivity as MyHistoryActivity).viewModel.openEdit.observe(this, Observer {
      if (isVisible) {
        viewModel.openOrCloseDeleteBar(it)
        mHasCheckBoxListAdapter.setShowCheckbox(it)
      }
    })
  }

  private fun setupCommentList() {
    mHasCheckBoxListAdapter = HasCheckBoxListAdapter(
      parentActivity, R.id.iv_checked
    ).apply {
      setOnCheckedItemChangeListener(object : HasCheckBoxListAdapter.OnCheckedItemChangeListener {
        override fun onCheckedItemChange(items: List<Int>) {
          viewModel.setCheckedItem(items)
        }
      })
    }
    mHasCheckBoxListAdapter.register(UserHistoryItemData::class.java)
      .to(DefaultViewBinder<UserHistoryItemData>(
          R.layout.personal_recycler_comment_history_news,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object :
          DefaultViewBinder.OnItemClickListener<UserHistoryItemData> {
          override fun onItemClicked(v: View, position: Int, item: UserHistoryItemData) {
            if (item.type == PersonalApiConstants.NEWS_TYPE_STUDY) {
              when (v.id) {
                R.id.iv_avatar, R.id.tv_name -> {
//                  startActivity(
//                    GzUserHomeActivity.newIntent(
//                      parentActivity,
//                      mUserId
//                    )
//                  )
                }
                R.id.iv_author_avatar, R.id.tv_author_name -> {
                  startActivity(
                    StudyNewsDetailsActivity.newIntent(
                      parentActivity,
                      item.infoID
                    )
                  )
//                  startActivity(
//                    GzUserHomeActivity.newIntent(
//                      parentActivity,
//                      item.userID,
//                      item.dwID
//                    )
//                  )
                }
                else -> startActivity(
                  StudyNewsDetailsActivity.newIntent(
                    parentActivity,
                    item.infoID
                  )
                )
              }
            } else {
              when (v.id) {
                R.id.iv_avatar, R.id.tv_name -> {
//                  startActivity(
//                    GzUserHomeActivity.newIntent(
//                      parentActivity,
//                      mUserId
//                    )
//                  )
                }
                R.id.iv_author_avatar, R.id.tv_author_name -> {
//                  startActivity(
//                    GzUserHomeActivity.newIntent(
//                      parentActivity,
//                      item.userID,
//                      item.dwID,
//                      item.dwID > 0
//                    )
//                  )
                  GzNewsDetailsActivity.newIntent(
                    parentActivity,
                    item.infoID
                  )
                }
                else -> startActivity(
                  GzNewsDetailsActivity.newIntent(
                    parentActivity,
                    item.infoID
                  )
                )
              }
            }
          }
        }, R.id.iv_avatar, R.id.tv_name, R.id.iv_author_avatar, R.id.tv_author_name)
      }, DefaultViewBinder<UserHistoryItemData>(
          R.layout.personal_recycler_comment_history_answer,
          BR.data,
          true
      ).apply {
        setOnItemClickListener(object :
          DefaultViewBinder.OnItemClickListener<UserHistoryItemData> {
          override fun onItemClicked(v: View, position: Int, item: UserHistoryItemData) {
            when (v.id) {
              R.id.iv_avatar, R.id.tv_name -> {
//                startActivity(
//                  GzUserHomeActivity.newIntent(
//                    parentActivity,
//                    mUserId
//                  )
//                )
              }
              else -> startActivity(
                AnswerDetailsActivity.newIntent(
                  parentActivity,
                  item.questionTitle,
                  item.infoID
                )
              )
            }
          }
        }, R.id.iv_avatar, R.id.tv_name)
      }, object : DefaultViewBinder<UserHistoryItemData>(
          R.layout.personal_recycler_comment_history_video,
          BR.data,
          true
      ) {
        override fun onBindViewHolder(
          holder: SuperViewHolder,
          item: UserHistoryItemData,
          position: Int
        ) {
          super.onBindViewHolder(holder, item, position)
          holder.findViewById<VideoPlayLayout>(R.id.vpl_video)
            .getVideoPlayer()?.setOnFirstPlayerListener {
              viewModel.updateVideoPlayCount(item)
            }
        }
      }.apply {
        setOnItemClickListener(object :
          DefaultViewBinder.OnItemClickListener<UserHistoryItemData> {
          override fun onItemClicked(v: View, position: Int, item: UserHistoryItemData) {
            when (v.id) {
              R.id.iv_avatar, R.id.tv_name -> {
//                startActivity(
//                  GzUserHomeActivity.newIntent(
//                    parentActivity,
//                    mUserId
//                  )
//                )
              }
              R.id.iv_author_avatar, R.id.tv_author_name -> {
                startActivity(VideoDetailsActivity.newIntent(parentActivity, item.infoID))
//                startActivity(
//                GzUserHomeActivity.newIntent(
//                  parentActivity,
//                  item.userID,
//                  item.dwID,
//                  item.dwID > 0
//                )
//                )
              }
              else -> {
                startActivity(VideoDetailsActivity.newIntent(parentActivity, item.infoID))
              }
            }
          }
        }, R.id.iv_avatar, R.id.tv_name, R.id.iv_author_avatar, R.id.tv_author_name)
      })
      .withLinker { _, item ->
        when (item.type) {
          1, 6 -> {
            0
          }
          4 -> {
            1
          }
          5 -> {
            2
          }
          else -> {
            0
          }
        }
      }
    val recyclerCommentList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerCommentList.layoutManager = LinearLayoutManager(parentActivity)
    recyclerCommentList.addItemDecoration(
      LineItemDecoration(
        ContextCompat.getDrawable(
          parentActivity,
          R.drawable.divider_f4f4f4
        ), LinearLayoutManager.VERTICAL
      )
    )
    viewModel.listViewModel.setAdapter(mHasCheckBoxListAdapter)
  }

  override fun onPause() {
    super.onPause()
    Jzvd.releaseAllVideos()
  }

  override fun onDestroyView() {
    super.onDestroyView()
    Jzvd.releaseAllVideos()
  }
}