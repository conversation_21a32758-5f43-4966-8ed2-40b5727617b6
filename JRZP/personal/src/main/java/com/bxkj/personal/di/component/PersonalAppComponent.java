package com.bxkj.personal.di.component;

import android.app.Application;

import com.bxkj.common.di.module.ApplicationModule;
import com.bxkj.personal.PersonalApplication;
import com.bxkj.personal.di.module.PersonalProviderModule;
import com.bxkj.personal.di.module.PersonalUIModule;

import dagger.BindsInstance;
import dagger.Component;
import dagger.android.AndroidInjector;
import dagger.android.support.AndroidSupportInjectionModule;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.di.component
 * @Description:
 * @TODO: TODO
 * @date 2018/3/30
 */

@Component(modules = {ApplicationModule.class, PersonalUIModule.class, PersonalProviderModule.class, AndroidSupportInjectionModule.class})
public interface PersonalAppComponent extends AndroidInjector<PersonalApplication> {

    @Component.Builder
    interface Builder {
        @BindsInstance
        Builder application(Application application);

        Builder applicationModule(ApplicationModule applicationModule);

        PersonalAppComponent build();
    }
}
