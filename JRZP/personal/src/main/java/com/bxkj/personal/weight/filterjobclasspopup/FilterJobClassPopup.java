package com.bxkj.personal.weight.filterjobclasspopup;

import android.app.Activity;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

import com.bxkj.common.data.JobTypeData;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.recyclerutil.RecycleViewGridDivider;
import com.bxkj.personal.R;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.weight.FilterAreaPopup
 * @Description: 筛选职位分类popupwindow
 * @TODO: TODO
 * @date 2018/4/3
 */

public class FilterJobClassPopup extends PopupWindow {

    private JobFirstClassAdapter mJobFirstClassAdapter;
    private JobSecondClassAdapter mJobSecondClassAdapter;
    private Activity mActivity;
    private View rootView;

    public FilterJobClassPopup(Activity activity) {
        super(activity);
        mActivity = activity;
        initView();
    }

    private void initView() {
        rootView = LayoutInflater.from(mActivity).inflate(R.layout.personal_popup_filter_by_job_class, null);
        setContentView(rootView);

        setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        setBackgroundDrawable(new ColorDrawable());
        setOutsideTouchable(true);
        setFocusable(true);

        rootView.findViewById(R.id.v_other).setOnClickListener(view -> dismiss());

        mJobFirstClassAdapter = new JobFirstClassAdapter(mActivity, null, R.layout.personal_recycler_job_first_class_item);
        RecyclerView recyclerFirstClass = rootView.findViewById(R.id.recycler_first_class);
        recyclerFirstClass.setLayoutManager(new LinearLayoutManager(mActivity));
        recyclerFirstClass.setAdapter(mJobFirstClassAdapter);
        mJobFirstClassAdapter.setOnItemClickListener((view, position) -> {
            if (mAreaItemClickListener != null) {
                mJobSecondClassAdapter.resetSelectPosition();
                mAreaItemClickListener.onFirstClassItemClicked(position);
            }
        });

        mJobSecondClassAdapter = new JobSecondClassAdapter(mActivity, null, R.layout.personal_recycler_job_second_class_item);
        RecyclerView recyclerSecondClass = rootView.findViewById(R.id.recycler_second_class);
        recyclerSecondClass.setLayoutManager(new GridLayoutManager(mActivity, 2));
        recyclerSecondClass.addItemDecoration(new RecycleViewGridDivider(DensityUtils.dp2px(mActivity, 10), ContextCompat.getColor(mActivity, R.color.common_white), true));
        recyclerSecondClass.setAdapter(mJobSecondClassAdapter);
        mJobSecondClassAdapter.setOnItemClickListener((view, position) -> {
            if (mAreaItemClickListener != null) {
                dismiss();
                mAreaItemClickListener.onSecondClassClicked(position);
            }
        });
    }

    /**
     * 设置一级数据
     *
     * @param optionsDataList
     */
    public void setFirstClassData(List<JobTypeData> optionsDataList) {
        mJobFirstClassAdapter.setData(optionsDataList);
        mJobFirstClassAdapter.notifyDataSetChanged();
    }

    /**
     * 设置二级分类数据
     *
     * @param optionsDataList
     */
    public void setSecondClassData(List<JobTypeData> optionsDataList) {
        mJobSecondClassAdapter.getLayoutManager().scrollToPosition(0);
        mJobSecondClassAdapter.setData(optionsDataList);
        mJobSecondClassAdapter.notifyDataSetChanged();
    }

    @Override
    public void showAsDropDown(View anchor) {
        if (Build.VERSION.SDK_INT >= 24) {
            Rect visibleFrame = new Rect();
            anchor.getGlobalVisibleRect(visibleFrame);
            int height = anchor.getResources().getDisplayMetrics().heightPixels - visibleFrame.bottom;
            setHeight(height);
        }
        super.showAsDropDown(anchor);
    }

    public List<JobTypeData> getFirstClassData() {
        return mJobFirstClassAdapter.getData();
    }

    public List<JobTypeData> getSecondClassData() {
        return mJobSecondClassAdapter.getData();
    }

    private OnItemClickListener mAreaItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onAreaItemClickListener) {
        mAreaItemClickListener = onAreaItemClickListener;
    }

    public interface OnItemClickListener {
        void onFirstClassItemClicked(int position);

        void onSecondClassClicked(int positions);
    }
}
