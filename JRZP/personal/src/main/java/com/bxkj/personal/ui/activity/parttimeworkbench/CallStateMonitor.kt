package com.bxkj.personal.ui.activity.parttimeworkbench

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.telephony.PhoneStateListener
import android.telephony.TelephonyCallback
import android.telephony.TelephonyManager
import android.util.Log
import androidx.annotation.RequiresApi
import com.bxkj.jrzp.support.db.entry.CallLogInfo
import com.bxkj.personal.ui.activity.parttimeworkbench.CallLogUtils.getLastCallLogInfoByNumber
import com.elvishew.xlog.XLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.abs

class CallStateMonitor(private val context: Context) {

  private val telephonyManager =
    context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
  private var phoneStateListener: PhoneStateListener? = null
  private var telephonyCallback: TelephonyCallback? = null
  private var callStateListener: CallStateListener? = null

  private var monitorNumber = ""
  private var localStartTime: Long = 0

  // 回调接口
  interface CallStateListener {
    fun onCallStarted()
    fun onCallEnded(callInfo: CallLogInfo)
  }

  fun setMonitorNumber(number: String) {
    monitorNumber = number
    localStartTime = System.currentTimeMillis()
  }

  fun setCallStateListener(listener: CallStateListener?) {
    this.callStateListener = listener
  }

  @SuppressLint("MissingPermission")
  fun startMonitoring() {
    if (VERSION.SDK_INT >= VERSION_CODES.S) {
      // Android 12 及以上使用 TelephonyCallback
      XLog.d("Android 12以上")
      registerTelephonyCallback()
    } else {
      XLog.d("Android 12以下")
      // Android 12 以下使用 PhoneStateListener
      registerPhoneStateListener()
    }
  }

  fun stopMonitoring() {
    if (VERSION.SDK_INT >= VERSION_CODES.S) {
      unregisterTelephonyCallback()
    } else {
      unregisterPhoneStateListener()
    }
  }

  @RequiresApi(api = VERSION_CODES.S)
  private fun registerTelephonyCallback() {
    telephonyCallback = object : TelephonyCallback(), TelephonyCallback.CallStateListener {
      override fun onCallStateChanged(state: Int) {
        handleCallState(state);
      }
    }

    try {
      telephonyCallback?.let {
        telephonyManager.registerTelephonyCallback(
          context.mainExecutor,
          it
        )
      }
    } catch (e: SecurityException) {
      Log.e(TAG, "Permission denied: " + e.message)
    }
  }

  @RequiresApi(api = VERSION_CODES.S)
  private fun unregisterTelephonyCallback() {
    if (telephonyCallback != null) {
      try {
        telephonyManager.unregisterTelephonyCallback(telephonyCallback!!)
      } catch (e: Exception) {
        Log.e(TAG, "Error unregistering callback: " + e.message)
      }
    }
  }

  private fun registerPhoneStateListener() {
    phoneStateListener = object : PhoneStateListener() {
      override fun onCallStateChanged(state: Int, phoneNumber: String) {
        handleCallState(state)
      }
    }

    try {
      telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE)
    } catch (e: SecurityException) {
      Log.e(TAG, "Permission denied: " + e.message)
    }
  }

  private fun unregisterPhoneStateListener() {
    if (phoneStateListener != null) {
      try {
        telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_NONE)
      } catch (e: Exception) {
        Log.e(TAG, "Error unregistering listener: " + e.message)
      }
    }
  }

  private fun handleCallState(state: Int) {
    XLog.d("Call state: $state")

    if (callStateListener == null) return

    when (state) {
      TelephonyManager.CALL_STATE_OFFHOOK -> callStateListener!!.onCallStarted()
      TelephonyManager.CALL_STATE_IDLE -> {
        CoroutineScope(Dispatchers.Main).launch {
          delay(800)
          checkCallResult()
        }
      }
    }
  }

  private fun checkCallResult() {
    val callLogInfo = context.getLastCallLogInfoByNumber(monitorNumber)
    XLog.d("CallLogInfo: $callLogInfo")
    callLogInfo?.let { info ->
      val callTimeDiffSecond = abs((info.date - localStartTime) / 1000)
      XLog.d("Call time diff: $callTimeDiffSecond")
      // 如果是90秒内的呼出电话，认为是拨打电话
      if (callTimeDiffSecond <= 90) {
        callStateListener!!.onCallEnded(info)
      }
    }
  }

  companion object {
    private const val TAG = "CallStateMonitor"
  }
}