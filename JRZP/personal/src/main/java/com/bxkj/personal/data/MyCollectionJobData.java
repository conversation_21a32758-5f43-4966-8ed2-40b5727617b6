package com.bxkj.personal.data;

import com.bxkj.common.util.CheckUtils;
import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 收藏职位
 * @TODO: TODO
 * @date 2018/5/8
 */

public class MyCollectionJobData {

  /**
   * id : 3788
   * relid : 1260984
   * name : 模具工程师
   * coname : 青岛亿卓机械有限公司
   * edate1 : 2018/5/3 12:00:56
   * edate2 : 2020/7/1 0:00:00
   * moneyName : 面议
   * wtName : 3年以上
   * quaName : 大专
   * shiName : 青岛
   * xianName : 胶南市
   * jieName : 隐珠街道办事处
   */

  private int id;
  private int relid;
  private String name;
  private String coname;
  private String comName;
  private String edate1;
  private String edate2;
  private String moneyName;
  private String money;
  private String wtName;
  private String quaName;
  private String shiName;
  private String xianName;
  private String jieName;
  private int comid;
  private String quName;
  private String videoPic;
  private int type;

  private String jnName;

  private String moneyUnitName;
  private String moneyJiesuanName;

  @SerializedName("shenfenName")
  private String identityRequire;

  @SerializedName("jnName2")
  private String partnerNature;

  public String getJnName() {
    return jnName;
  }

  public void setJnName(String jnName) {
    this.jnName = jnName;
  }

  public String getMoneyUnitName() {
    return moneyUnitName;
  }

  public void setMoneyUnitName(String moneyUnitName) {
    this.moneyUnitName = moneyUnitName;
  }

  public String getMoneyJiesuanName() {
    return moneyJiesuanName;
  }

  public void setMoneyJiesuanName(String moneyJiesuanName) {
    this.moneyJiesuanName = moneyJiesuanName;
  }

  public String getIdentityRequire() {
    return identityRequire;
  }

  public void setIdentityRequire(String identityRequire) {
    this.identityRequire = identityRequire;
  }

  public String getPartnerNature() {
    return partnerNature;
  }

  public void setPartnerNature(String partnerNature) {
    this.partnerNature = partnerNature;
  }

  public String getQuName() {
    return quName;
  }

  public void setQuName(String quName) {
    this.quName = quName;
  }

  public String getComName() {
    return comName;
  }

  public void setComName(String comName) {
    this.comName = comName;
  }

  public String getMoney() {
    return money;
  }

  public String getRealMoney() {
    if (CheckUtils.isNullOrEmpty(money)) {
      return "面议";
    } else {
      return money;
    }
  }

  public void setMoney(String money) {
    this.money = money;
  }

  public int getComid() {
    return comid;
  }

  public void setComid(int comid) {
    this.comid = comid;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getRelid() {
    return relid;
  }

  public void setRelid(int relid) {
    this.relid = relid;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getConame() {
    return coname;
  }

  public void setConame(String coname) {
    this.coname = coname;
  }

  public String getEdate1() {
    return edate1;
  }

  public String getFormatPublishDate() {
    return CheckUtils.isNullOrEmpty(edate1) ? "未知" : edate1.split("\\s")[0];
  }

  public void setEdate1(String edate1) {
    this.edate1 = edate1;
  }

  public String getEdate2() {
    return edate2;
  }

  public void setEdate2(String edate2) {
    this.edate2 = edate2;
  }

  public String getMoneyName() {
    return moneyName;
  }

  public void setMoneyName(String moneyName) {
    this.moneyName = moneyName;
  }

  public String getWtName() {
    return wtName;
  }

  public String getFormatExpName() {
    return CheckUtils.isNullOrEmpty(wtName) ? "经验不限" : wtName;
  }

  public void setWtName(String wtName) {
    this.wtName = wtName;
  }

  public String getQuaName() {
    return quaName;
  }

  public String getFormatEduName() {
    return CheckUtils.isNullOrEmpty(quaName) ? "学历不限" : quaName;
  }

  public void setQuaName(String quaName) {
    this.quaName = quaName;
  }

  public String getShiName() {
    return shiName;
  }

  public void setShiName(String shiName) {
    this.shiName = shiName;
  }

  public String getXianName() {
    return xianName;
  }

  public void setXianName(String xianName) {
    this.xianName = xianName;
  }

  public String getJieName() {
    return jieName;
  }

  public void setJieName(String jieName) {
    this.jieName = jieName;
  }

  public String getVideoPic() {
    return videoPic;
  }

  public void setVideoPic(String videoPic) {
    this.videoPic = videoPic;
  }

  public String getTypeText() {
    if (type == 0) {
      return "社招";
    } else {
      return "校招";
    }
  }

  public Boolean isNormalRecruitment() {
    return type == 0;
  }

  public boolean emptyNatureName() {
    return CheckUtils.isNullOrEmpty(jnName);
  }

  public String getConvertSalary() {
    if (!CheckUtils.isNullOrEmpty(getMoney()) && getMoney().equals("面议")) {
      return getMoney();
    } else {
      final StringBuilder salaryBuilder = new StringBuilder();
      salaryBuilder.append(getMoney());
      if (!CheckUtils.isNullOrEmpty(moneyUnitName)) {
        salaryBuilder.append("/").append(moneyUnitName);
      }
      if (!CheckUtils.isNullOrEmpty(moneyJiesuanName)) {
        salaryBuilder.append("/").append(moneyJiesuanName);
      }
      return salaryBuilder.toString();
    }
  }
}
