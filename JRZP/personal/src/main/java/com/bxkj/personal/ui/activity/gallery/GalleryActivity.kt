package com.bxkj.personal.ui.activity.gallery

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.viewpager.widget.ViewPager
import com.therouter.router.Route
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseNActivity
import com.bxkj.common.data.GalleryItem
import com.bxkj.common.imageloder.base.ImageLoader
import com.bxkj.common.util.imageloader.GlideLoadConfig
import com.bxkj.personal.R
import com.github.chrisbanes.photoview.PhotoView

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.gallery
 * @Description:
 * <AUTHOR>
 * @date 2019/11/27
 * @version V1.0
 */
@Route(path = ImageGalleryNavigation.PATH)
class GalleryActivity : BaseNActivity() {

  companion object {

    fun newIntent(context: Context, imgList: ArrayList<in GalleryItem>, index: Int): Intent {
      val intent = Intent(context, GalleryActivity::class.java)
      intent.putExtra(ImageGalleryNavigation.EXTRA_TARGET_INDEX, index)
      intent.putExtras(Bundle().apply {
        putSerializable(ImageGalleryNavigation.EXTRA_IMG_LIST, imgList)
      })
      return intent
    }
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    setContentView(R.layout.personal_activity_gallery)

    findViewById<ImageView>(R.id.iv_back).setOnClickListener { finish() }

    statusBarManager.titleBar(findViewById(R.id.cl_title_bar)).init()
    setupViewPagerAdapter()
  }

  private fun setupViewPagerAdapter() {
    val imgList: ArrayList<GalleryItem> =
      intent.getSerializableExtra(ImageGalleryNavigation.EXTRA_IMG_LIST) as ArrayList<GalleryItem>
    findViewById<TextView>(R.id.tv_img_count).text =
      getString(R.string.gallery_img_count_format, 1, imgList.size)
    val imgViewList = ArrayList<PhotoView>()
    for (momentPhotoItemData in imgList) {
      val photoView = PhotoView(this)
      photoView.layoutParams = ViewGroup.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT
      )
      val imgUrl = if (momentPhotoItemData.getItemUrl()
          ?.contains("http") == true
      ) {
        momentPhotoItemData.getItemUrl()
      } else {
        CommonApiConstants.BASE_JRZP_IMG_URL + momentPhotoItemData.getItemUrl()
      }
      imgUrl?.let {
        val imgUrlSegment = imgUrl.split("/")
        if (imgUrlSegment[imgUrlSegment.size - 1].contains("_s")) {
          val hdUrl =
            imgUrl.removeRange(imgUrl.lastIndexOf(".") - 2, imgUrl.lastIndexOf("."))
          ImageLoader.loadImage(
            this,
            GlideLoadConfig.Builder().url(hdUrl).into(photoView).build()
          )
        } else {
          ImageLoader.loadImage(
            this,
            GlideLoadConfig.Builder().url(imgUrl).into(photoView).build()
          )
        }
      }
      imgViewList.add(photoView)
    }

    val vpGallery = findViewById<ViewPager>(R.id.vp_gallery)

    vpGallery.adapter = GalleryPagerAdapter(imgViewList)
    vpGallery.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
      override fun onPageScrollStateChanged(state: Int) {
      }

      override fun onPageScrolled(
        position: Int,
        positionOffset: Float,
        positionOffsetPixels: Int
      ) {
      }

      override fun onPageSelected(position: Int) {
        findViewById<TextView>(R.id.tv_img_count).text =
          getString(R.string.gallery_img_count_format, position + 1, imgList.size)
      }
    })

    val perIndex = intent.getIntExtra(ImageGalleryNavigation.EXTRA_TARGET_INDEX, 0)
    vpGallery.currentItem = perIndex
  }
}