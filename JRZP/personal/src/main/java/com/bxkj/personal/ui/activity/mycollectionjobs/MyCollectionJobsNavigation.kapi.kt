package com.bxkj.personal.ui.activity.mycollectionjobs

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/16
 * @version: V1.0
 */
class MyCollectionJobsNavigation {

  companion object {
    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/mycollectionjobs"

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}