package com.bxkj.personal.ui.activity.companydetails.companyabout;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;
import com.baidu.mapapi.map.BaiduMap;
import com.baidu.mapapi.map.BitmapDescriptor;
import com.baidu.mapapi.map.BitmapDescriptorFactory;
import com.baidu.mapapi.map.InfoWindow;
import com.baidu.mapapi.map.MapStatus;
import com.baidu.mapapi.map.MapStatusUpdateFactory;
import com.baidu.mapapi.map.MarkerOptions;
import com.baidu.mapapi.map.OverlayOptions;
import com.baidu.mapapi.map.TextureMapView;
import com.baidu.mapapi.model.LatLng;
import com.bxkj.common.base.BaseFragment;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.SystemUtil;
import com.bxkj.personal.R;
import com.bxkj.personal.data.CompanyDetailsData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.companydetails.companyabout
 * @Description: 公司详情fragment
 * @TODO: TODO
 * @date 2018/4/23
 */

public class CompanyAboutFragment extends BaseFragment {

  public static final String COMPANY_INFO = "companyInfo";

  private TextView tvDescOfContent;
  private TextView tvDescOfText;
  private FrameLayout flCompanyLocation;
  private TextView tvMapAddress;
  private TextView tvSeeDirections;

  private TextureMapView mapCompanyLocation;

  public static CompanyAboutFragment newInstance(CompanyDetailsData companyDetailsData) {
    CompanyAboutFragment companyAboutFragment = new CompanyAboutFragment();
    Bundle bundle = new Bundle();
    bundle.putParcelable(COMPANY_INFO, companyDetailsData);
    companyAboutFragment.setArguments(bundle);
    return companyAboutFragment;
  }

  @Override
  public int getLayoutId() {
    return R.layout.personal_fragment_company_about;
  }

  @Override
  public void initPage() {
    bindView(getView());

    CompanyDetailsData companyDetailsData = getArguments().getParcelable(COMPANY_INFO);
    tvDescOfContent.setText(getString(R.string.personal_company_about));
    tvDescOfText.setText(companyDetailsData.getInfo());

    initMap(companyDetailsData);
  }

  private void initMap(CompanyDetailsData companyDetailsData) {
    String[] latlngs = companyDetailsData.getCoordinate().split(",");
    if (!CheckUtils.hasEmptyOfItem(latlngs)) {
      flCompanyLocation.removeAllViews();
      mapCompanyLocation = new TextureMapView(getParentActivity());
      flCompanyLocation.addView(mapCompanyLocation,
        new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
          ViewGroup.LayoutParams.MATCH_PARENT));
      mapCompanyLocation.showScaleControl(false);
      mapCompanyLocation.showZoomControls(false);
      BaiduMap baiduMap = mapCompanyLocation.getMap();
      baiduMap.getUiSettings().setScrollGesturesEnabled(false);
      LatLng point = new LatLng(Double.parseDouble(latlngs[1]), Double.parseDouble(latlngs[0]));
      baiduMap.setMapStatus(MapStatusUpdateFactory.newMapStatus(new MapStatus.Builder()
        .target(point)
        .zoom(16)
        .build()));

      BitmapDescriptor bitmap =
        BitmapDescriptorFactory.fromResource(R.drawable.personal_ic_map_oval);

      OverlayOptions options = new MarkerOptions()
        .position(point)
        .icon(bitmap);
      baiduMap.addOverlay(options);

      TextView textView = (TextView) LayoutInflater.from(getContext())
        .inflate(R.layout.personal_layout_map_address, null);
      textView.setText(companyDetailsData.getAddress());
      baiduMap.showInfoWindow(
        new InfoWindow(textView, point, -DensityUtils.dp2px(getContext(), 15)));

      tvSeeDirections.setOnClickListener(view -> {
        if (!SystemUtil.openLocalMapShowLocation(getActivity(), latlngs[0], latlngs[1],
          companyDetailsData.getAddress())) {
          showToast(getString(R.string.personal_no_local_map));
        }
      });
    } else {
      tvSeeDirections.setVisibility(View.GONE);
      tvMapAddress.setText(companyDetailsData.getAddress());
    }
  }

  @Override
  public void onResume() {
    super.onResume();
    if (mapCompanyLocation != null) {
      mapCompanyLocation.onResume();
    }
  }

  @Override
  public void onPause() {
    super.onPause();
    if (mapCompanyLocation != null) {
      mapCompanyLocation.onPause();
    }
  }

  @Override
  public void onDestroy() {
    super.onDestroy();
    if (mapCompanyLocation != null) {
      mapCompanyLocation.onDestroy();
    }
  }

  private void bindView(View bindSource) {
    tvDescOfContent = bindSource.findViewById(R.id.tv_desc_of_content);
    tvDescOfText = bindSource.findViewById(R.id.tv_desc_of_text);
    flCompanyLocation = bindSource.findViewById(R.id.fl_company_location);
    tvMapAddress = bindSource.findViewById(R.id.tv_map_address);
    tvSeeDirections = bindSource.findViewById(R.id.tv_see_directions);
  }
}
