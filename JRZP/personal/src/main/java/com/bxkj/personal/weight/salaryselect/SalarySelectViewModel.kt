package com.bxkj.personal.weight.salaryselect

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.network.handleResult
import com.bxkj.personal.data.source.PickerOptionsRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
class SalarySelectViewModel @Inject constructor(
    private val _pickerOptionsRepo: PickerOptionsRepo
) : BaseViewModel() {

    val salaryOptionList = MutableLiveData<List<PickerOptionsData>>()

    val confirmCommand = MutableLiveData<VMEvent<Unit>>()

    var minSalary: Int = 0
    var maxSalary: Int = 0

    fun start() {
        viewModelScope.launch {
            _pickerOptionsRepo.getSalaryOptionList()
                .handleResult({
                    it?.dataList?.let { list ->
                        salaryOptionList.value = list
                    }
                }, {
                    showToast(it.errMsg)
                })
        }
    }

    fun setMinSalaryIndex(index: Int) {
        salaryOptionList.value?.let {
            minSalary = it[index].name.toInt()
        }
    }

    fun setMaxSalaryIndex(index: Int) {
        salaryOptionList.value?.let {
            maxSalary = it[index].name.toInt()
        }
    }

    fun confirmSelect() {
        salaryOptionList.value?.let {
            if (maxSalary < minSalary) {
                showToast("最高薪资不能小于最低薪资")
                return
            }
            confirmCommand.value = VMEvent(Unit)
        } ?: showToast("数据加载中，请稍后")
    }
}