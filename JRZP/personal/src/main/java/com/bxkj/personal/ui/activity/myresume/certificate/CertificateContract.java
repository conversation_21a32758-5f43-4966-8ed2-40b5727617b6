package com.bxkj.personal.ui.activity.myresume.certificate;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.personal.data.CertificateItemData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.certificate
 * @Description: Certificate
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface CertificateContract {
    interface View extends BaseView {
        void getCertificateDetailsSuccess(CertificateItemData certificateItemData);

        void addCertificateSuccess();

        void updateCertificateSuccess();

        void deleteCertificate();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getCertificateDetails(int userId, int certificateId);

        public abstract void addCertificate(int userId, int resumeId, CertificateItemData certificateItemData);

        public abstract void updateCertificate(int userId, int certificateId, CertificateItemData certificateItemData);

        public abstract void deleteCertificate(int userId, int certificateId);
    }
}
