package com.bxkj.personal.ui.activity.msgnotification

import android.app.Application
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.data.FeedbackMsgItemData
import com.bxkj.personal.data.source.AccountRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.msgnotification
 * @Description:
 * <AUTHOR>
 * @date 2020/3/2
 * @version V1.0
 */
class MsgNotificationViewModel @Inject constructor(application: Application
                                                   , private val mAccountRepo: AccountRepo) : BaseViewModel() {

    val listViewModel = RefreshListViewModel()

    init {
        setupListViewModel()
    }

    private fun setupListViewModel() {
        listViewModel.setOnLoadDataListener { currentPage ->
            mAccountRepo.getMsgNotificationList(getSelfUserID(), currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE
                    , object : ResultDataCallBack<List<FeedbackMsgItemData>> {
                override fun onSuccess(data: List<FeedbackMsgItemData>?) {
                    listViewModel.autoAddAll(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30002) {
                        listViewModel.noMoreData()
                    } else {
                        listViewModel.loadError()
                    }
                }
            })
        }
    }

    fun start() {
        listViewModel.refresh(true)
    }
}