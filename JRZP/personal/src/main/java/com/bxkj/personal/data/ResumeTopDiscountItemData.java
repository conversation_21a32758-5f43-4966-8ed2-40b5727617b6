package com.bxkj.personal.data;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jrzp
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2020/2/14
 */
public class ResumeTopDiscountItemData {

    /**
     * count : 3
     * zhekou : 100
     * totalIntegral : 30.0
     * totalIntegralZk : 30.0
     */

    private int count;
    private int zhekou;
    private double totalIntegral;
    private double totalIntegralZk;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getZhekou() {
        return zhekou;
    }

    public void setZhekou(int zhekou) {
        this.zhekou = zhekou;
    }

    public int getTotalIntegral() {
        return (int) totalIntegral;
    }

    public void setTotalIntegral(double totalIntegral) {
        this.totalIntegral = totalIntegral;
    }

    public int getTotalIntegralZk() {
        return (int) totalIntegralZk;
    }

    public void setTotalIntegralZk(double totalIntegralZk) {
        this.totalIntegralZk = totalIntegralZk;
    }

    public static class ItemDiffCallback extends DiffUtil.ItemCallback<ResumeTopDiscountItemData> {

        @Override
        public boolean areItemsTheSame(@NonNull ResumeTopDiscountItemData oldItem, @NonNull ResumeTopDiscountItemData newItem) {
            return oldItem.count == newItem.count;
        }

        @Override
        public boolean areContentsTheSame(@NonNull ResumeTopDiscountItemData oldItem, @NonNull ResumeTopDiscountItemData newItem) {
            return oldItem.count == newItem.count;
        }
    }
}
