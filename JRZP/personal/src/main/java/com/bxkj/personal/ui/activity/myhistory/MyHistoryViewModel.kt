package com.bxkj.personal.ui.activity.myhistory

import android.util.SparseArray
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.my
 * @Description: 我的
 * <AUTHOR>
 * @date 2020/2/12
 * @version V1.0
 */
class MyHistoryViewModel @Inject constructor() : BaseViewModel() {

  val openEdit = MutableLiveData<Boolean>().apply { value = false }
  val editEnableStatus = SparseArray<ObservableBoolean>(3).apply {
    put(0, ObservableBoolean(false))
    put(1, ObservableBoolean(false))
    put(2, ObservableBoolean(false))
//        put(3, ObservableBoolean(false))
  }
  val currentPage = ObservableInt(0)

  fun openOrCloseEdit() {
    openEdit.value?.let {
      openEdit.value = it.not()
    }
  }

  fun closeEdit() {
    openEdit.value = false
  }

  fun enableEdit(index: Int, enable: Boolean) {
    editEnableStatus.get(index).set(enable)
  }

  fun setCurrentPage(index: Int) {
    currentPage.set(index)
  }
}