package com.bxkj.personal.ui.activity.myresume.adapter;

import android.content.Context;
import android.view.View;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.R;
import com.bxkj.personal.data.ProfessionalSkillItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.itemviewbinder
 * @Description: 专业技能适配器
 * @TODO: TODO
 * @date 2018/5/11
 */

public class ProfessionalSkillAdapter extends SuperAdapter<ProfessionalSkillItemData> {
    public ProfessionalSkillAdapter(Context context, List<ProfessionalSkillItemData> list, int layoutResId) {
        super(context, layoutResId, list);
    }

    @Override
    protected void convert(SuperViewHolder holder, int viewType, ProfessionalSkillItemData professionalSkillItemData, int position) {
        holder.setText(R.id.tv_name_and_level, professionalSkillItemData.getName() + "|" + professionalSkillItemData.getDegree());
        holder.findViewById(R.id.tv_desc).setVisibility(CheckUtils.isNullOrEmpty(professionalSkillItemData.getRemark()) ? View.GONE : View.VISIBLE);
        holder.setText(R.id.tv_desc, professionalSkillItemData.getRemark());

        holder.findViewById(R.id.iv_edit).setOnClickListener(view -> {
            if (SuperItemClickListener != null) {
                SuperItemClickListener.onClick(view, position);
            }
        });
    }
}
