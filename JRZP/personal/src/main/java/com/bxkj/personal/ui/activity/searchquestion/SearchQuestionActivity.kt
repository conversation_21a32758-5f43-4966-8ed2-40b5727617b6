package com.bxkj.personal.ui.activity.searchquestion

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.recyclerutil.VerticalCenterSpan
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.personal.R
import com.bxkj.personal.data.QuestionItemData
import com.bxkj.personal.databinding.PersonalActivitySearchQuestionBinding
import com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsActivity

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.searchquestion
 * @Description: 搜索问题
 * <AUTHOR>
 * @date 2020/3/2
 * @version V1.0
 */
class SearchQuestionActivity :
  BaseDBActivity<PersonalActivitySearchQuestionBinding, SearchQuestionViewModel>(),
  View.OnClickListener {
  companion object {

    fun newIntent(context: Context): Intent {
      return Intent(context, SearchQuestionActivity::class.java)
    }
  }

  override fun getViewModelClass(): Class<SearchQuestionViewModel> =
    SearchQuestionViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_search_question

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this
    setupSearchResultListAdapter()
    subscribeViewModelEvent()
    setupSearchClickListener()
  }

  override fun onClick(v: View?) {
    v?.let {
      finish()
    }
  }

  private fun setupSearchClickListener() {
    viewBinding.etContent.setOnEditorActionListener { _, actionId, _ ->
      if (actionId == EditorInfo.IME_ACTION_SEARCH) {
        SystemUtil.hideSoftKeyboard(this)
        return@setOnEditorActionListener true
      }
      return@setOnEditorActionListener false
    }

  }

  private fun setupSearchResultListAdapter() {
    val searchResultListAdapter = object :
      SuperAdapter<QuestionItemData>(this, R.layout.personal_recycler_search_answerer_result_item) {
      override fun convert(
        holder: SuperViewHolder,
        viewType: Int,
        item: QuestionItemData,
        position: Int
      ) {
        val tvResultItem = holder.findViewById<TextView>(R.id.tv_title)
        val resultSpan = SpannableString(item.searchResult)
        resultSpan.setSpan(
          VerticalCenterSpan(
            DensityUtils.sp2px(this@SearchQuestionActivity, 12f).toFloat()
          ), item.title.length, item.searchResult.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE
        )
        tvResultItem.text = resultSpan
        setOnChildClickListener(position, holder.itemView)
      }
    }.apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          startActivity(
            QuestionDetailsActivity.newIntent(
              this@SearchQuestionActivity,
              data[position].id
            )
          )
        }
      })
    }
    val recyclerSearchResultList =
      viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerSearchResultList.layoutManager = LinearLayoutManager(this)
    recyclerSearchResultList.addItemDecoration(
      LineItemDecoration(
        ContextCompat.getDrawable(
          this,
          R.drawable.divider_f4f4f4
        ), LinearLayoutManager.VERTICAL
      )
    )
    viewModel.searchResultListViewModel.setAdapter(searchResultListAdapter)
  }

  private fun subscribeViewModelEvent() {
    viewModel.searchContent.observe(this, Observer {
      viewModel.startSearch(it)
    })
  }

}
