package com.bxkj.personal.ui.activity.feedbackmsgcontent

import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.R
import com.bxkj.personal.data.FeedbackMsgContentItemData
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2

/**
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.feedbackmsgcontent
 * @Description:
 * @TODO: TODO
 * <AUTHOR>
 * @date 2018/11/21
 * @version V1.0
 */
class FeedbackMsgContentAdapter(context: Context, list: MutableList<FeedbackMsgContentItemData>?, layoutId: Int) : SuperAdapter<FeedbackMsgContentItemData>(
    context,
    layoutId,
    list
) {

    override fun convert(holder: SuperViewHolder, viewType: Int, item: FeedbackMsgContentItemData?, position: Int) {
        holder.run {
            setText(R.id.tv_date, item!!.date)
            setText(R.id.tv_msg_type, item.typeName)
            val tvContent = findViewById<TextView>(R.id.tv_content)
            val builder = SpannableStringBuilder(item.content)
            builder.append(mContext.getString(R.string.personal_check_the_job_position))
            val clickableSpan = object : ClickableSpan() {
                override fun onClick(view: View) {
                    mContext.startActivity(JobDetailsActivityV2.newIntent(mContext, item.relId))
                }
            }
            builder.setSpan(clickableSpan, builder.length - 4, builder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            val foregroundColorSpan = ForegroundColorSpan(ContextCompat.getColor(mContext, R.color.cl_ff7405))
            builder.setSpan(foregroundColorSpan, builder.length - 4, builder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            tvContent.movementMethod = LinkMovementMethod.getInstance()
            tvContent.text = builder
            if (item.type == 5 && item.jInterview != null) {
                findViewById<LinearLayout>(R.id.ll_interview_content).visibility = View.VISIBLE
                setText(R.id.tv_time, mContext.getString(R.string.inviting_interview_time_format, item.jInterview.viewDate))
                setText(R.id.tv_address, mContext.getString(R.string.inviting_interview_address_format, item.jInterview.viewAddress.replace("\n", "")))
                setText(R.id.tv_contract, mContext.getString(R.string.inviting_interview_contract_format, item.jInterview.lxr))
                setText(R.id.tv_phone, mContext.getString(R.string.inviting_interview_phone_format, item.jInterview.phone))
            }

            val rlCancelOrAcceptBar = findViewById<RelativeLayout>(R.id.rl_cancel_or_accept_bar)
            val tvCancel = findViewById<TextView>(R.id.tv_cancel)
            val tvAccept = findViewById<TextView>(R.id.tv_accept)
            val rlConfirmBar = findViewById<RelativeLayout>(R.id.rl_confirm_bar)
            val tvConfirm = findViewById<TextView>(R.id.tv_confirm)

            //处理按钮状态
            if (item.type == 4) {         //邀請投遞
                if (item.state == 0) { //未处理
                    rlCancelOrAcceptBar.visibility = View.VISIBLE
                    tvCancel.text = mContext.getString(R.string.personal_refusing_to_deliver)
                    tvAccept.text = mContext.getString(R.string.personal_deliver_now)
                } else {  //已处理
                    rlConfirmBar.visibility = View.VISIBLE
                    tvConfirm.text = if (item.state == 1) mContext.getString(R.string.personal_refuse_to_deliver_tips) else mContext.getString(R.string.personal_accept_deliver_tips)
                }
            } else if (item.type == 5 && item.jInterview != null) {      //邀請面試
                if (item.jInterview.state == 0) {     //未处理
                    rlCancelOrAcceptBar.visibility = View.VISIBLE
                    tvCancel.text = mContext.getString(R.string.personal_refusing_an_interview)
                    tvAccept.text = mContext.getString(R.string.personal_accept_an_interview)
                } else {  //已处理
                    rlConfirmBar.visibility = View.VISIBLE
                    tvConfirm.text = if (item.jInterview.state == 1) mContext.getString(R.string.personal_refuse_interview_tips) else mContext.getString(R.string.personal_accept_interview_tips)
                }
            }
            setOnChildClickListener(position, tvCancel, tvAccept)
        }

    }

}