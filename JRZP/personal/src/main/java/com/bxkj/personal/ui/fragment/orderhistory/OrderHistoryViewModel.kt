package com.bxkj.personal.ui.fragment.orderhistory

import android.os.Bundle
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.data.OrderItemData
import com.bxkj.personal.data.source.AccountRepo
import javax.inject.Inject

/**
 * @Project: jrzp
 * @Package com.bxkj.personal.ui.fragment.orderhistory
 * @Description: 订单历史
 * <AUTHOR>
 * @date 2020/2/16
 * @version V1.0
 */
class OrderHistoryViewModel @Inject constructor(private val mAccountRepo: AccountRepo) : BaseViewModel() {

    val listViewModel = RefreshListViewModel()

    private var mPaymentStatus: Int = CommonApiConstants.NO_ID

    init {
        setupOrderHistoryListViewModel()
    }

    private fun setupOrderHistoryListViewModel() {
        listViewModel.setOnLoadDataListener { currentPage ->
            mAccountRepo.getOrderHistory(getSelfUserID(), CommonApiConstants.NO_DATA
                    , mPaymentStatus, CommonApiConstants.NO_DATA, CommonApiConstants.NO_DATA, currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE
                    , object : ResultDataCallBack<List<OrderItemData>> {
                override fun onSuccess(data: List<OrderItemData>?) {
                    listViewModel.autoAddAll(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30002) {
                        listViewModel.noMoreData()
                    } else {
                        listViewModel.loadError()
                    }
                }
            })
        }
    }

    fun start(arguments: Bundle?) {
        arguments?.let {
            mPaymentStatus = arguments.getInt(OrderHistoryFragment.EXTRA_PAYMENT_STATUS)
            listViewModel.refresh()
        }
    }

    fun activateOrder(orderItem: OrderItemData) {
        showLoading()
        mAccountRepo.activateOrder(getSelfUserID(), orderItem.id, object : ResultCallBack {
            override fun onSuccess() {
                hideLoading()
                orderItem.updateEffectStatus(1)
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                showToast(respondThrowable.errMsg)
            }
        })
    }

    fun deleteOrder(position: Int, orderItem: OrderItemData) {
        showLoading()
        mAccountRepo.deleteOrder(getSelfUserID(), orderItem.id
                , object : ResultCallBack {
            override fun onSuccess() {
                hideLoading()
                if (listViewModel.remove(position) == 0) {
                    listViewModel.refresh()
                } else {
                    listViewModel.remove(orderItem)
                }
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                showToast(respondThrowable.errMsg)
            }
        })
    }
}