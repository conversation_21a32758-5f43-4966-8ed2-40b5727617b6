package com.bxkj.personal.data.source

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.util.kotlin.format
import com.bxkj.common.util.kotlin.objectEncrypt
import com.bxkj.jrzp.support.db.entry.CallLogInfo
import com.bxkj.personal.api.CoroutinesApi
import com.bxkj.personal.data.IncomeAnalyzerRecord
import com.bxkj.personal.data.PartTimeIncomeDetails
import com.bxkj.personal.data.PartTimeIncomeRecord
import com.bxkj.personal.data.SalesPersonDetails
import com.bxkj.personal.data.XSCompanyData
import java.util.Date
import javax.inject.Inject

/**
 * author:Sanjin
 * date:2025/2/19
 **/
class SalesServiceRepo @Inject constructor(
  private val coroutinesApi: CoroutinesApi
) : BaseRepo() {

  /**
   * 获取兼职收入明细
   */
  suspend fun getPartTimeIncomeItem(): ReqResponse<List<IncomeAnalyzerRecord>> {
    return httpRequest {
      coroutinesApi.getPartTimeIncomeItem()
    }
  }

  /**
   * 获取销售人员详情
   */
  suspend fun getSalesPersonDetails(): ReqResponse<SalesPersonDetails> {
    return httpRequest {
      coroutinesApi.getSalespersonDetails()
    }
  }

  /**
   * 获取兼职收入明细
   */
  suspend fun getPartTimeIncomeDetails(): ReqResponse<PartTimeIncomeDetails> {
    return httpRequest {
      coroutinesApi.getPartTimeIncomeDetails()
    }
  }

  /**
   * 检查企业是否收藏
   */
  suspend fun checkIsCollect(id: Int): ReqResponse<String> {
    return httpRequest {
      coroutinesApi.checkIsCollect(id)
    }
  }

  /**
   * 获取兼职收入记录列表
   */
  suspend fun getPartTimeIncomeRecordList(
    pageIndex: Int,
    pageSize: Int,
    rzDate: String
  ): ReqResponse<List<PartTimeIncomeRecord>> {
    return httpRequest {
      coroutinesApi.getPartTimeIncomeRecordList(ZPRequestBody().apply {
        put("pageIndex", pageIndex)
        put("pageSize", pageSize)
        put("rzDate", rzDate)
      })
    }
  }

  /**
   * 获取企业列表
   */
  suspend fun getActiveCompanyList(
    type: Int,
    pageIndex: Int,
    pageSize: Int,
    searchKeyword: String = "",
    searchFlag: String = "1"
  ): ReqResponse<List<XSCompanyData>> {
    return httpRequest {
      coroutinesApi.getActiveCompanyList(ZPRequestBody().apply {
        put("type", type)
        put("pageIndex", pageIndex)
        put("pageSize", pageSize)
        put("coname", searchKeyword)
        put("lx", searchFlag)
      })
    }
  }

  /**
   * 添加通话记录
   */
  suspend fun addCallLog(companyId: Int, callLogInfo: CallLogInfo): ReqResponse<Nothing> {
    return httpRequest {
      coroutinesApi.addCallLog(ZPRequestBody().apply {
        put("id", companyId)
        put("calledNum", callLogInfo.number)
        put("ksdate", Date(callLogInfo.date).format())
        put("timeLength", callLogInfo.duration)
        put("rhthjl", callLogInfo.id)
      }.objectEncrypt())
    }
  }
}