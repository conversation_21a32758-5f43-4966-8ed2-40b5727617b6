package com.bxkj.personal.ui.activity.reportreason

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.ReportReasonItemData
import com.bxkj.common.data.repository.CommonRepository
import com.bxkj.common.network.handleResult
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/8
 * @version: V1.0
 */
class ReportReasonViewModel @Inject constructor(
  private val commonRepo: CommonRepository
) :
  BaseViewModel() {

  val reasonList = MutableLiveData<List<ReportReasonItemData>>()

  fun start() {
    viewModelScope.launch {
      showLoading()
      commonRepo.getReportReasonList()
        .handleResult({
          reasonList.value = it
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
    }
  }

  private fun setupReasonList() {
    // reasonList.value = arrayListOf(
    //   ReportReasonItem("散播违法/敏感言论", "发布的信息包含违法、政治敏感内容"),
    //   ReportReasonItem("人身攻击", "存在辱骂、骚扰等语言或肢体上的不当行为"),
    //   ReportReasonItem("色情骚扰", "发布的信息包含色情低俗内容或存在性骚扰行为"),
    //   ReportReasonItem(
    //     "职位虚假", "发布的职位信息与实际沟通职位不符，例如：薪资、工作\n" +
    //       "内容、工作地点不符"
    //   ),
    //   ReportReasonItem("身份虚假", "不是其认证公司的员工"),
    //   ReportReasonItem(
    //     "违法/欺诈行为", "存在引诱求职者从事不法活动或欺诈求职者，例如：网络\n" +
    //       "诈骗、套取简历"
    //   ),
    //   ReportReasonItem(
    //     "收取求职者费用", "以各种名义或变相收取求职者费用，例如：中介费、培训\n" +
    //       "费"
    //   ),
    //   ReportReasonItem("发布广告和招商信息", "变相发布广告或寻求投资、合作"),
    //   ReportReasonItem("其他违法行为", "招聘者或公司存在以上列举类型之外的违规行为")
    // )
  }
}