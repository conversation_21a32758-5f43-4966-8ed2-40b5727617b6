package com.bxkj.personal.ui.fragment.collection

import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.HasDeleteBarViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.UserHistoryItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.CommentRepo
import com.bxkj.personal.data.source.NewsRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.collection
 * @Description: 我的收藏
 * <AUTHOR>
 * @date 2020/2/12
 * @version V1.0
 */
class CollectionViewModel @Inject constructor(
    private val mNewsRepo: NewsRepo
    , private val mAccountRepo: AccountRepo
    , private val mCommentRepo: CommentRepo
) : HasDeleteBarViewModel() {
    val listViewModel = RefreshListViewModel()
    val enableEditCommand = LiveEvent<Boolean>()

    init {
        setupCollectionListViewModel()
    }

    private fun setupCollectionListViewModel() {
        listViewModel.setOnLoadDataListener { currentPage ->
            mAccountRepo.getMyCollectionList(getSelfUserID(), currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE
                    , object : ResultDataCallBack<List<UserHistoryItemData>> {
                override fun onSuccess(data: List<UserHistoryItemData>?) {
                    if (currentPage == 1) {
                        enableEditCommand.value = true
                    }
                    if (CheckUtils.isNullOrEmpty(data)) {
                        listViewModel.noMoreData()
                        return
                    }
                    listViewModel.autoAddAll(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (currentPage == 1) {
                        enableEditCommand.value = false
                    }
                    if (respondThrowable.errCode == 30001) {
                        listViewModel.noMoreData()
                    } else {
                        listViewModel.loadError()
                    }
                }
            })
        }
    }

    fun start() {
        listViewModel.refresh()
    }

    fun addOrRemoveLike(historyItemData: UserHistoryItemData, itemType: Int) {
        mCommentRepo.likeOrUnlikeTheComment(getSelfUserID(), CommonApiConstants.NO_ID, itemType, historyItemData.infoID
                , object : ResultCallBack {
            override fun onSuccess() {
                historyItemData.addLike()
            }

            override fun onError(respondThrowable: RespondThrowable) {
                if (respondThrowable.errCode == 10002) {
                    historyItemData.removeLike()
                } else {
                    showToast(respondThrowable.errMsg)
                }
            }
        })
    }

    fun clearAllCollection() {
        showLoading()
        mAccountRepo.clearMyHistory(getSelfUserID(), PersonalApiConstants.CLEAR_HISTORY_COLLECTION_TYPE
                , object : ResultCallBack {
            override fun onSuccess() {
                hideLoading()
                listViewModel.refresh()
            }

            override fun onError(respondThrowable: RespondThrowable) {
                hideLoading()
                showToast(respondThrowable.errMsg)
            }
        })
    }

    override fun confirmDelete(item: List<Int>?) {
        item?.let {
            showLoading()
            val needDeleteTempList = ArrayList<Any>()
            batchDelete(it, needDeleteTempList, 0)
        }
    }

    private fun batchDelete(items: List<Int>, needDeleteList: ArrayList<Any>, currentPosition: Int) {
        val item = listViewModel.data[items[currentPosition]]
        val realItem: UserHistoryItemData = item as UserHistoryItemData
        val followType = when (realItem.type) {
            1 -> PersonalApiConstants.NEWS_TYPE_NEWS
            4 -> PersonalApiConstants.NEWS_TYPE_QUESTIONS
            else -> PersonalApiConstants.NEWS_TYPE_VIDEO
        }
        mAccountRepo.addOrRemoveCollection(getSelfUserID(), followType, realItem.infoID
                , object : ResultDataCallBack<BaseResponse<*>> {
            override fun onSuccess(data: BaseResponse<*>?) {
                hideLoading()
            }

            override fun onError(respondThrowable: RespondThrowable) {
                if (respondThrowable.errCode == 10002) {
                    needDeleteList.add(item)
                    if (currentPosition < items.size - 1) {
                        batchDelete(items, needDeleteList, currentPosition + 1)
                    } else {
                        listViewModel.removeAll(needDeleteList)
                        if (listViewModel.childCount == 0) {
                            deleteToEmptyEvent.call()
                            listViewModel.refresh()
                        }
                        clearChecked()
                        hideLoading()
                    }
                } else {
                    hideLoading()
                    showToast(respondThrowable.errMsg)
                }
            }
        })
    }

    fun updateVideoPlayCount(item: UserHistoryItemData) {
        mNewsRepo.updateVideoPlayCount(item.infoID)
        item.addVideoPlay()
    }

}
