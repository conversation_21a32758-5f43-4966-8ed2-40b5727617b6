package com.bxkj.personal.ui.activity.jobdetails.itemviewbinder;

import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.personal.R;
import com.bxkj.personal.data.RecyclerGroupTitleData;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.jobdetails.itemviewbinder
 * @Description:
 * @TODO: TODO
 * @date 2018/5/8
 */

public class GroupTitleViewBinder implements ItemViewBinder<RecyclerGroupTitleData> {
    @Override
    public void onBindViewHolder(SuperViewHolder holder, RecyclerGroupTitleData item, int position) {
        holder.setText(R.id.tv_desc_of_content, item.getTitle());
    }

    @Override
    public int getLayoutId() {
        return R.layout.personal_recycler_job_details_group_title;
    }
}
