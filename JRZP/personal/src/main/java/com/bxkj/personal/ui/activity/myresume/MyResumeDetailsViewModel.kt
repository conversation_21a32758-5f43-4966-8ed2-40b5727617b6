package com.bxkj.personal.ui.activity.myresume

import android.app.Activity
import androidx.activity.result.ActivityResult
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.personal.data.EduBackgroundItemData
import com.bxkj.personal.data.MicroResumeData
import com.bxkj.personal.data.WorkExpItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.MyResumeRepo
import com.bxkj.personal.ui.activity.jobintention.JobIntentionActivity
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/9/8
 */
class MyResumeDetailsViewModel @Inject constructor(
  private val myResumeRepo: MyResumeRepo,
  private val accountRepo: AccountRepo
) : BaseViewModel() {

  val microResumeInfo = MutableLiveData<MicroResumeData>()
  val workExpList = MutableLiveData<List<WorkExpItemData>>()
  val eduExpList = MutableLiveData<List<EduBackgroundItemData>>()

  val showCompleteResume = MediatorLiveData<Boolean>().apply {
    addSource(microResumeInfo) {
      value = checkResumeCompleted()
    }
    addSource(workExpList) {
      value = checkResumeCompleted()
    }
    addSource(eduExpList) {
      value = checkResumeCompleted()
    }
    value = false
  }

  private val isGeekVip = MutableLiveData<Boolean>().apply { value = false }

  val showJoinVipBtn = MediatorLiveData<Boolean>().apply {
    value = false
    addSource(showCompleteResume) {
      value = showCompleteResume.value == false && isGeekVip.value == false
    }
    addSource(isGeekVip) {
      value = isGeekVip.value == false && showCompleteResume.value == false
    }
  }

  val toEditBasicInfoCommand = MutableLiveData<VMEvent<Unit>>()
  val toEditJobIntentionCommand = MutableLiveData<VMEvent<MicroResumeData>>()
  val toAddWorkExpCommand = MutableLiveData<VMEvent<Int>>()
  val toAddEduExpCommand = MutableLiveData<VMEvent<Int>>()
  val toEditSelfEvaluationCommand = MutableLiveData<VMEvent<MicroResumeData>>()
  val toEditAttachResumeCommand = MutableLiveData<VMEvent<MicroResumeData>>()

  fun start() {
    refreshMicroResumeInfo()
  }

  fun checkUserGeekVipStatus() {
    viewModelScope.launch {
      accountRepo.checkUserGeekVipStatus()
        .handleResult({
          isGeekVip.value = it == 1
        })
    }
  }

  private fun checkResumeCompleted(): Boolean {
    return (microResumeInfo.value?.attachResumeUrl.isNullOrBlank() ||
      microResumeInfo.value?.selfEvaluation.isNullOrBlank()) ||
      (workExpList.value.isNullOrEmpty()) ||
      (eduExpList.value.isNullOrEmpty())
  }

  private fun refreshMicroResumeInfo(refreshOtherResumeInfo: Boolean = true) {
    viewModelScope.launch {
      myResumeRepo.getMicroResumeInfo().handleResult({
        microResumeInfo.value = it
        if (refreshOtherResumeInfo) {
          it?.let {
            refreshUserWorkExp()
            refreshUserEduExp()
          }
        }
      }, {
        microResumeInfo.value = MicroResumeData()
      })
    }
  }

  fun toEditBasicInfo() {
    microResumeInfo.value?.let {
      toEditBasicInfoCommand.value = VMEvent(Unit)
    }
  }

  fun toEditJobIntention() {
    checkBasicInfoComplete {
      toEditJobIntentionCommand.value = VMEvent(it)
    }
  }

  fun toAddWorkExp() {
    checkBasicInfoComplete {
      toAddWorkExpCommand.value = VMEvent(it.resid.getOrDefault())
    }
  }

  fun toAddEduExp() {
    checkBasicInfoComplete {
      toAddEduExpCommand.value = VMEvent(it.resid.getOrDefault())
    }
  }

  fun toEditSelfEvaluation() {
    checkBasicInfoComplete {
      toEditSelfEvaluationCommand.value = VMEvent(it)
    }
  }

  fun toEditAttachResume() {
    checkBasicInfoComplete {
      toEditAttachResumeCommand.value = VMEvent(it)
    }
  }

  /**
   * 继续完善简历
   */
  fun continueCompleteResume() {
    when {
      workExpList.value.isNullOrEmpty() -> {
        toAddWorkExp()
      }

      eduExpList.value.isNullOrEmpty() -> {
        toAddEduExp()
      }

      microResumeInfo.value?.selfEvaluation.isNullOrBlank() -> {
        toEditSelfEvaluation()
      }

      microResumeInfo.value?.attachResumeUrl.isNullOrBlank() -> {
        toEditAttachResume()
      }
    }
  }

  private fun checkBasicInfoComplete(completeAction: (MicroResumeData) -> Unit) {
    microResumeInfo.value?.let {
      if (it.resid == 0) {
        showToast("请先完善基本信息")
        toEditBasicInfoCommand.value = VMEvent(Unit)
      } else {
        completeAction.invoke(it)
      }
    }
  }

  private fun refreshUserWorkExp() {
    microResumeInfo.value?.let {
      viewModelScope.launch {
        myResumeRepo.getUserWorkExp(getSelfUserID(), it.resid.getOrDefault())
          .handleResult({
            workExpList.value = it
          }, {
            workExpList.value = emptyList()
          })
      }
    }
  }

  private fun refreshUserEduExp() {
    microResumeInfo.value?.let {
      viewModelScope.launch {
        myResumeRepo.getUserEducationExp(getSelfUserID(), it.resid.getOrDefault())
          .handleResult({
            eduExpList.value = it
          }, {
            eduExpList.value = emptyList()
          })
      }
    }
  }

  fun handleEditBasicInfoResult(it: ActivityResult) {
    if (it.resultCode == Activity.RESULT_OK) {
      refreshMicroResumeInfo(false)
    }
  }

  fun handleEditJobIntentionResult(it: ActivityResult) {
    if (it.resultCode == Activity.RESULT_OK) {
      it.data?.getParcelableExtra<MicroResumeData>(JobIntentionActivity.EXTRA_MICRO_RESUME)
        ?.let { resultResumeInfo ->
          viewModelScope.launch {
            showLoading()
            myResumeRepo.updateMicroResumeInfo(resultResumeInfo)
              .handleResult({
                microResumeInfo.value = resultResumeInfo
              }, {
                showToast(it.errMsg)
              }, {
                hideLoading()
              })
          }
        }
    }
  }

  fun handleEditWorkExpResult(it: ActivityResult) {
    if (it.resultCode == Activity.RESULT_OK) {
      refreshUserWorkExp()
    }
  }

  fun handleEditEduExpResult(it: ActivityResult) {
    if (it.resultCode == Activity.RESULT_OK) {
      refreshUserEduExp()
    }
  }

  fun handleEditSelfEvaluationResult(it: ActivityResult) {
    if (it.resultCode == Activity.RESULT_OK) {
      refreshMicroResumeInfo(false)
    }
  }

  fun handleEditAttachResumeResult(it: ActivityResult) {
    refreshMicroResumeInfo(false)
  }
}