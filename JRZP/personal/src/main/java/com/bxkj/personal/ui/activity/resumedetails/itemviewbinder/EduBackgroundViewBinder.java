package com.bxkj.personal.ui.activity.resumedetails.itemviewbinder;

import android.app.Activity;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.common.adapter.multitypeadapter.ItemViewBinder;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.R;
import com.bxkj.personal.data.EduBackgroundItemData;
import com.bxkj.personal.ui.activity.resumedetails.adapter.EduBackgroundAdapter;

/**
 * @date 2018/5/9
 */

public class EduBackgroundViewBinder implements ItemViewBinder<EduBackgroundItemData> {

  private Activity mActivity;

  public EduBackgroundViewBinder(Activity activity) {
    mActivity = activity;
  }

  @Override
  public void onBindViewHolder(SuperViewHolder holder, EduBackgroundItemData item,
    int position) {
    holder.setText(R.id.tv_tag, mActivity.getString(R.string.resume_details_education_exp));
    RecyclerView recyclerView = holder.findViewById(R.id.recycler);
    recyclerView.setVisibility(
      CheckUtils.isNullOrEmpty(item.getEduBackgroundItemDataList()) ? View.GONE : View.VISIBLE);
    EduBackgroundAdapter eduBackgroundAdapter =
      new EduBackgroundAdapter(mActivity, item.getEduBackgroundItemDataList(),
        R.layout.enterprise_recycler_resume_education_item);
    recyclerView.setLayoutManager(new LinearLayoutManager(mActivity));
    recyclerView.setAdapter(eduBackgroundAdapter);
  }

  @Override
  public int getLayoutId() {
    return R.layout.enterprise_recycler_resume_item;
  }
}
