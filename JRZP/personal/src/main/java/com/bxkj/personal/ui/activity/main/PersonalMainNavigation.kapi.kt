package com.bxkj.personal.ui.activity.main

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.util.router.RouterNavigator

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/6
 * @version: V1.0
 */
class PersonalMainNavigation {
  companion object {

    const val PATH = RouterNavigation.PersonalMainActivity.URL

    fun navigate(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}