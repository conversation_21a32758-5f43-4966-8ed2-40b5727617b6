package com.bxkj.personal.ui.activity.mycollectionjobs;

import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.data.MyCollectionJobData;
import com.bxkj.personal.api.PersonalApi;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.mycollectionjobs
 * @Description: MyCollectionJobs
 * @TODO: TODO
 */

public class MyCollectionJobsPresenter extends MyCollectionJobsContract.Presenter {

    private static final String TAG = MyCollectionJobsPresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;

    @Inject
    public MyCollectionJobsPresenter(PersonalApi personalApi) {
        mPersonalApi = personalApi;
    }

    @Override
    public void getUserCollectionJobs(int userId, int pageIndex, int pageSize) {
        mPersonalApi.getUserCollectionJobs(userId, pageIndex, pageSize)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getUserCollectionJobsSuccess((List<MyCollectionJobData>) baseResponse.getDataList());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.getErrCode() == 30002) {
                            mView.onResultNoData();
                        } else {
                            mView.onRequestError(respondThrowable);
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    public void uncollectionJob(int userId, int jobId, int itemPosition) {
        mPersonalApi.uncollectionJob(userId, jobId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.uncollectionJobSuccess(itemPosition);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
