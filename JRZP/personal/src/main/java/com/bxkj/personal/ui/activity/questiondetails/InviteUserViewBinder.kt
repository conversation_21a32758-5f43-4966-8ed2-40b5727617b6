package com.bxkj.personal.ui.activity.questiondetails

import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.personal.R.layout
import com.bxkj.personal.data.InviteUserItemData
import com.bxkj.personal.data.InviteUserItemData.DiffCallback
import com.bxkj.personal.data.InviteUserItemData.Group

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.questiondetails
 * @Description:
 * <AUTHOR>
 * @date 2020/2/25
 * @version V1.0
 */
class InviteUserViewBinder constructor(private val viewModel: QuestionDetailsViewModel) : DefaultViewBinder<InviteUserItemData.Group>(
    R.layout.personal_recycler_question_invite_user_layout,
    BR.data,
    true
) {

    override fun onBindViewHolder(
      holder: SuperViewHolder,
      item: Group,
      position: Int
    ) {
        val inviteUserListAdapter = SimpleDiffListAdapter<InviteUserItemData>(
          layout.personal_recycler_invite_user_item,
          DiffCallback()
        )
                .apply {
                    setOnItemClickListener(object :
                      com.bxkj.common.adapter.superadapter.SuperItemClickListener {
                        override fun onClick(v: View, position: Int) {
                            getData()?.let {
                                if (v.id == R.id.tv_invite) {
                                    viewModel.inviteUserToAnswer(it[position])
                                } else {
                                    viewModel.toInviteUserHome(it[position])
                                }
                            }
                        }
                    }, R.id.tv_invite)
                }
        val recyclerInviteUser = holder.findViewById<RecyclerView>(R.id.recycler_invite_users)
        recyclerInviteUser.layoutManager = LinearLayoutManager(holder.itemView.context)
        recyclerInviteUser.adapter = inviteUserListAdapter
        recyclerInviteUser.addItemDecoration(LineItemDecoration(ContextCompat.getDrawable(holder.itemView.context, R.drawable.divider_f4f4f4), LinearLayoutManager.VERTICAL))
        super.onBindViewHolder(holder, item, position)
    }
}