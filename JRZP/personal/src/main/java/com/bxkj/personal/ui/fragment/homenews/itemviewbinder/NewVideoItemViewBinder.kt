package com.bxkj.personal.ui.fragment.homenews.itemviewbinder

import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.data.NewsItemData
import com.bxkj.personal.ui.fragment.homenews.RecommendNewsAdapter
import com.bxkj.personal.ui.fragment.homenews.RecommendNewsAdapter.OnMoreOptionClickListener
import com.bxkj.video.data.OnlineVideoData

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/30
 * @version: V1.0
 */
class NewVideoItemViewBinder constructor(private val mOnVideoItemClickListener: OnVideoItemClickListener) :
  DefaultViewBinder<NewsItemData>(
      R.layout.personal_recycler_home_recommend_video_item,
      BR.data,
      true
  ) {

  private var mVideoListAdapter: RecommendNewsAdapter? = null

  override fun onBindViewHolder(
    holder: SuperViewHolder,
    item: NewsItemData,
    position: Int
  ) {
    item.videoList?.let {
      val videoList = holder.findViewById<RecyclerView>(R.id.recycler_video_list)
      mVideoListAdapter = RecommendNewsAdapter(holder.itemView.context,
        object : OnMoreOptionClickListener {
          override fun onMoreClick() {
            mOnVideoItemClickListener.onVideoClick(0, null, emptyList())
          }
        }).apply {
        register(
          OnlineVideoData::class.java,
          DefaultViewBinder<OnlineVideoData>(
              R.layout.recycler_home_recommend_video_child_item,
              BR.data,
              true
          ).apply {
            setOnItemClickListener(object : OnItemClickListener<OnlineVideoData> {
              override fun onItemClicked(v: View, position: Int, item: OnlineVideoData) {
                mOnVideoItemClickListener.onVideoClick(position, item, it)
              }
            })
          }
        )
      }
      videoList.isNestedScrollingEnabled = false
      videoList.layoutManager =
        LinearLayoutManager(holder.itemView.context, LinearLayoutManager.HORIZONTAL, false)
      videoList.adapter = mVideoListAdapter
      mVideoListAdapter?.reset(it.toMutableList())
    }
    super.onBindViewHolder(holder, item, position)
  }

  interface OnVideoItemClickListener {
    fun onVideoClick(
      position: Int,
      onlineVideoData: OnlineVideoData?,
      videos: List<OnlineVideoData>
    )
  }
}