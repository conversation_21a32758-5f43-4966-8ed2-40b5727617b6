package com.bxkj.personal.data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 语言能力
 * @TODO: TODO
 * @date 2018/5/11
 */

public class LanguageSkillsItemData {

    private int id;
    private int resumeId;
    private int lanid;
    private String lanName;
    private int quaid;
    private String quaName;
    private String remark;

    public LanguageSkillsItemData() {
    }

    public LanguageSkillsItemData(int resumeId, List<LanguageSkillsItemData> languageSkillsItemDataList) {
        this.resumeId = resumeId;
        this.languageSkillsItemDataList = languageSkillsItemDataList;
    }

    private List<LanguageSkillsItemData> languageSkillsItemDataList;

    public List<LanguageSkillsItemData> getLanguageSkillsItemDataList() {
        return languageSkillsItemDataList;
    }

    public int getResumeId() {
        return resumeId;
    }

    public void setResumeId(int resumeId) {
        this.resumeId = resumeId;
    }

    public void setLanguageSkillsItemDataList(List<LanguageSkillsItemData> languageSkillsItemDataList) {
        this.languageSkillsItemDataList = languageSkillsItemDataList;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getLanid() {
        return lanid;
    }

    public void setLanid(int lanid) {
        this.lanid = lanid;
    }

    public String getLanName() {
        return lanName;
    }

    public void setLanName(String lanName) {
        this.lanName = lanName;
    }

    public int getQuaid() {
        return quaid;
    }

    public void setQuaid(int quaid) {
        this.quaid = quaid;
    }

    public String getQuaName() {
        return quaName;
    }

    public void setQuaName(String quaName) {
        this.quaName = quaName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
