package com.bxkj.personal.mvp.presenter;

import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.api.PersonalApi;
import com.bxkj.personal.mvp.contract.UpdateMessageStatusContract;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.presenter
 * @Description: UpdateMessageStatus
 * @TODO: TODO
 * @date 2018/3/27
 */

public class UpdateMessageStatusPresenter extends UpdateMessageStatusContract.Presenter {

    private static final String TAG = UpdateMessageStatusPresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;

    @Inject
    public UpdateMessageStatusPresenter(PersonalApi personalApi) {
        mPersonalApi = personalApi;
    }

    @Override
    public void updateMessageStatus(int userId, int companyId) {
        mPersonalApi.setupMsgReaded(userId, companyId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.updateMsgStatusSuccess();
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
