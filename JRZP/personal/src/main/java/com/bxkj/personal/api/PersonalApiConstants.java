package com.bxkj.personal.api;

import androidx.annotation.IntDef;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Description: 个人端接口常量
 */

public class PersonalApiConstants {

  //发布视频招聘类型
  public static final int I_POST_VIDEO_RECRUIT_TYPE = 3;

  //判断系统是否升级中
  public static final String I_CHECK_SYSTEM_STATUS = "/Comm/GetIsShowUpdatePersonal/";

  //获取公司性质列表
  public static final String I_GET_NATURE_OF_COMPANY = "/Company/GetCompanyProperty/";

  //获取工作经验列表
  public static final String I_GET_WORKING_EXP = "/WTime/GetWTimeList/";

  //获取学历列表
  public static final String I_GET_EDUCATION = "/Qua/GetQuaList/";

  //获取薪资范围列表
  public static final String I_GET_SALARY_RANGE = "/JMoney/GetJMoneyList/";

  //获取职位数量
  public static final String I_GET_JOBS_COUNT = "/ReleaseJob/GetJobCount/";

  //分页获取职位列表
  public static final String I_GET_JOB_LIST = "/ReleaseJob/GetJobListByPage/";

  //获取职位详情
  public static final String I_GET_JOB_DETAILS_INFO = "/ReleaseJob/GetJobInfo2/";

  //判断职位是否收藏
  public static final String I_CHECK_JOB_COLLECTION = "/JobStow/PersonalIsHasStowJob/";

  //个人用户收藏职位
  public static final String I_COLLECTION_JOB = "/JobStow/PersonalStowJob/";

  //取消收藏职位
  public static final String I_UNCOLLECTION_JOB = "/JobStow/PersonalStowJobCancel/";

  //获取简历列表（投简历处）
  public static final String I_GET_AVAILABLE_RESUME_LIST = "/Resume/GetResumeForJobApply/";

  //投递简历
  public static final String I_SUBMIT_RESUME = "/JobApply/AddJobApply/";

  //用户投递简历类型（1为用户手动投递2为自动推荐投递）
  public static final int USER_SUBMIT_RESUME_TYPE = 1;

  //获取公司详情
  public static final String I_GET_COMPANY_DETAILS_INFO = "/VCom/GetVComInfo/";

  //获取资讯列表
  public static final String I_GET_NEWS_LIST = "/News/GetNewsList/";

  //获取资讯详情
  public static final String I_GET_NEWS_DETAILS = "/VNews/GetNewsInfo/";

  //获取用户订阅列表
  public static final String I_GET_SUBSCRIPTION_LIST = "/VDingyue/GetDingyueList/";

  //添加订阅
  public static final String I_ADD_SUBSCRIPTION = "/DingYue/AddDingyue/";

  //取消订阅
  public static final String I_UNSUBSCRIBE = "/DingYue/DelDingyue/";

  //获取用户订阅总数
  public static final String I_GET_USER_SUBSCRIPTION_COUNT = "/DingYue/GetDingYueCount/";

  //获取速聘列表
  public static final String I_GET_QUICK_RECRUITMENT = "/ShuPin/GetShuPinListByPage/";

  //发布速聘
  public static final String I_ADD_QUICK_RECRUITMENT = "/ShuPin/AddShuPin/";

  //删除速聘
  public static final String I_DELETE_QUICK_RECRUITMENT = "/ShuPin/DelShuPin/";

  //修改速聘
  public static final String I_UPDATE_QUICK_RECRUITMENT = "/ShuPin/EditShuPin/";

  //根据地理位置获取位置编号
  public static final String I_GET_ADDRESS_INFO_BY_ADDRESS_NAME = "/Area/GetAreaByName/";

  //获取速聘详情
  public static final String I_GET_QUICK_RECRUITMENT_DETAILS = "/ShuPin/GetShuPinInfo/";

  //速聘登录
  public static final String I_QUICK_RECRUITMENT_LOGIN = "/ShuPin/ShuPinLogin/";

  //获取用户中心首页资料
  public static final String I_GET_USER_CENTER_PERSONAL_DATA =
    "/UserBasicInfo/GetUserCenterBasicInfo/";

  //获取用户收藏职位总数
  public static final String I_GET_USER_COLLECTION_JOB_COUNT = "/StowJob/GetStowCount/";

  //获取用户收藏公司总数
  public static final String I_GET_USER_COLLECTION_COMPANY_COUNT = "/StowJob/GetCompanyStowCount/";

  //获取用户职位申请总数
  public static final String I_GET_USER_SUBMIT_RECORD_COUNT = "/ApplyJob/GetApplyJobCount/";

  //获取用户职位申请记录
  public static final String I_GET_USER_APPLY_RECORD = "/ApplyJob/GetApplyJobListByPage/";

  //批量删除申请记录
  public static final String I_BATCH_DELETION_SUBMIT_RECORD = "/JobApply/DelJobApply/";

  //分页获取用户收藏的职位
  public static final String I_GET_USER_COLLECTION_JOBS = "/StowJob/GetStowJobListByPage/";

  //============================== 用户简历（Start） ==============================//

  //用户后台，获取用户简历列表
  public static final String I_GET_USER_RESUME_LIST = "/Resume/GetResumeList/";

  //用户后台，删除简历
  public static final String I_DELETE_RESUME = "/Resume/DelResume/";

  //获取简历信息
  public static final String I_GET_RESUME_INFO = "/VResume/GetResumeInfo/";

  //刷新简历
  public static final String I_REFRESH_RESUME = "/Resume/EditResumeEditTime/";

  //修改简历名称
  public static final String I_UPDATE_RESUME_NAME = "/Resume/EditResumeName/";

  //修改简历公开状态
  public static final String I_UPDATE_RESUME_OPEN_STATE = "/Resume/EditResumeState/";

  //设置简历为默认
  public static final String I_SET_UP_RESUME_IS_DEFAULT = "/Resume/EditResumeSetDefault/";

  //设置简历刷新状态
  public static final String I_SETUP_RESUME_AUTO_REFRESH = "/Resume/SetResumeRefreshState/";

  //更新简历求职意向
  public static final String I_UPDATE_RESUME_CAREER_OBJECTIVE = "/Resume/EditResume/";

  //获取工作经历列表
  public static final String I_GET_WORK_EXP_LIST = "/ResumeJobExp/GetResumeJobExpList/";

  //获取工作经验详情
  public static final String I_GET_WORK_EXP_DETAILS = "/ResumeJobExp/GetResumeJobExpInfo/";

  //添加工作经验
  public static final String I_ADD_WORK_EXP = "/ResumeJobExp/AddResumeJobExp/";

  //修改工作经验
  public static final String I_UPDATE_WORK_EXP = "/ResumeJobExp/UpdateResumeJobExp/";

  //删除工作经验
  public static final String I_DELETE_WORK_EXP = "/ResumeJobExp/DelResumeJobExp/";

  //获取简历教育背景
  public static final String I_GET_EDU_BACKGROUND_LIST = "/ResumeEdu/GetResumeEduList/";

  //获取教育背景详情
  public static final String I_GET_EDU_BACKGROUND_DETAILS = "/ResumeEdu/GetResumeEduInfo/";

  //添加教育背景
  public static final String I_ADD_EDU_BACKGROUND = "/ResumeEdu/AddResumeEdu/";

  //修改教育背景
  public static final String I_UPDATE_EDU_BACKGROUND = "/ResumeEdu/UpdateResumeEdu/";

  //删除教育背景
  public static final String I_DELETE_EDU_BACKGROUND = "/ResumeEdu/DelResumeEdu/";

  //修改自我评价
  public static final String I_UPDATE_SELF_EVALUATION = "/Resume/EditResumeGoalScore/";

  //获取专业技能
  public static final String I_GET_PROFESSIONAL_SKILL_LIST =
    "/ResumeProSkill/GetResumeProSkillList/";

  //获取专业技能详情
  public static final String I_GET_PROFESSIONAL_SKILL_DETAILS =
    "/ResumeProSkill/GetResumeProSkillInfo/";

  //添加专业技能
  public static final String I_ADD_PROFESSIONAL_SKILL = "/ResumeProSkill/AddResumeProSkill/";

  //修改专业技能
  public static final String I_UPDATE_PROFESSIONAL_SKILL = "/ResumeProSkill/UpdateResumeProSkill/";

  //删除专业技能
  public static final String I_DELETE_PROFESSIONAL_SKILL = "/ResumeProSkill/DelResumeProSkill/";

  //获取语言能力列表
  public static final String I_GET_LANGUAGE_SKILLS_LIST = "/ResumeLan/GetResumeLanList/";

  //获取语言能力详情
  public static final String I_GET_LANGUAGE_SKILLS_DETAILS = "/ResumeLan/GetResumeLanInfo/";

  //新增语言能力
  public static final String I_ADD_LANGUAGE_SKILLS = "/ResumeLan/AddResumeLan/";

  //修改语言能力
  public static final String I_UPDATE_LANGUAGE_SKILLS = "/ResumeLan/UpdateResumeLan/";

  //删除语言能力
  public static final String I_DELETE_LANGUAGE_SKILLS = "/ResumeLan/DelResumeLan/";

  //获取在校情况列表
  public static final String I_GET_SCHOOL_SITUATION_LIST = "/ResumeSchool/GetResumeSchoolList/";

  //获取在校情况详情
  public static final String I_GET_SCHOOL_SITUATION_DETAILS = "/ResumeSchool/GetResumeSchoolInfo/";

  //添加在校情况
  public static final String I_ADD_SCHOOL_SITUATION = "/ResumeSchool/AddResumeSchool/";

  //修改在校情况
  public static final String I_UPDATE_SCHOOL_SITUATION = "/ResumeSchool/UpdateResumeSchool/";

  //删除在校情况
  public static final String I_DELETE_SCHOOL_SITUATION = "/ResumeSchool/DelResumeSchool/";

  //获取证书情况列表
  public static final String I_GET_CERTIFICATE_LIST = "/ResumeCerti/GetResumeCertiList/";

  //获取证书详情
  public static final String I_GET_CERTIFICATE_DETAILS = "/ResumeCerti/GetResumeCertiInfo/";

  //新增证书详情
  public static final String I_ADD_CERTIFICATE = "/ResumeCerti/AddResumeCerti/";

  //修改证书
  public static final String I_UPDATE_CERTIFICATE = "/ResumeCerti/UpdateResumeCerti/";

  //删除证书
  public static final String I_DELETE_CERTIFICATE = "/ResumeCerti/DelResumeCerti/";

  //============================== 用户简历（End） ==============================//

  //获取行业列表
  public static final String I_GET_INDUSTRY_LIST = "/Company/GetCompanyTrade/";

  //获取专业类别
  public static final String I_GET_PROFESSIONAL_LIST = "/Professional/GetProfessionalList/";

  //提交意见反馈
  public static final String I_SUBMIT_FEEDBACK = "/Message/AddMessage/";

  //获取关于我们信息
  public static final String I_GET_ABOUT_US_INFO = "/About/About/";

  //获取资讯分类2
  public static final String I_GET_NEWS_TYPE_TWO = "/NewType/GetNewsType2List/";

  //分页获取订阅消息列表
  public static final String I_GET_SUBSCRIPTION_MESSAGE_LIST = "/DingYueNews/GetDingYueNewsList/";

  //改变消息查看状态
  public static final String I_CHANGE_MESSAGE_STATUS = "/DingYueNews/EditDingYueNewsLook/";

  //检查版本
  public static final String I_CHECK_VERSION_INFO = "/About/AndroidVersion/";

  //获取消息总数量
  public static final String I_GET_SUBSCRIPTION_MESSAGE_COUNT = "/DingYueNews/GetDingYueNewsCount/";

  //修改消息查看状态
  public static final String I_UPDATE_FEEDBACK_MSG_STATE = "/Notice/UpdateNoticeLook/";

  //同意邀请投递
  public static final String I_ACCEPT_DELIVERY_INVITATION = "/ApplyToudi/AcceptApplyToudi/";

  //拒绝邀请投递
  public static final String I_REFUSE_DELIVERY_INVITATION = "/ApplyToudi/RefuseApplyToudi/";

  //同意面试邀请
  public static final String I_ACCEPT_INTERVIEW_INVITATION = "/JobInterview/AcceptJobInterview/";

  //拒绝面试邀请
  public static final String I_REFUSE_INTERVIEW_INVITATION = "/JobInterview/RefuseJobInterview/";

  //获取投递邀请列表
  public static final String I_GET_INVITATIONS_TO_DELIVERY_LIST =
    "/ApplyToudi/GetApplyToudiListByPage/";

  //获取面试邀请总数
  public static final String I_GET_INVITAIONS_TO_DELIVERY_COUNT = "/ApplyToudi/GetApplyToudiCount/";

  //获取反馈消息列表(按公司分组)
  public static final String I_GET_FEEDBACK_NOTICE_LIST = "/Notice/GetGroupGoutongListByPage/";

  //获取反馈消息内容列表（公司下）
  public static final String I_GET_FEEDBACK_MSG_CONTENT_BY_COMPANY =
    "/Notice/GetSubGroupGoutongListByPage/";

  //获取谁看过我
  public static final String I_GET_SEEN_MY_BUSINESS_LIST = "/Notify/GetResumeLookedListByPageV2";

  //获取谁看过我总数
  public static final String I_GET_SEW_ME_COMPANY_COUNT = "/Notice/GetResumeLookComCountByPage/";

  //获取消息通知列表
  public static final String I_GET_MSG_NOTIFICATION_LIST = "/Notice/GetGroupXiaoxiListByPage/";

  //获取消息通知内容列表（公司下）
  public static final String I_GET_MSG_NOTIFICATION_CONTENT_LIST_BY_COMPANY =
    "/Notice/GetSubGroupXiaoxiListByPage/";

  //获取系统消息
  public static final String I_GET_SYSTEM_MSG_LIST = "/Notice/GetSystemXiaoxiListByPage/";

  //更新个人基础信息
  public static final String I_UPDATE_PERSONAL_BASIC_INFORMATION =
    "/UserBasicInfo/UpdateMustPersonalAccountBasicInfo2/";

  //获取民族信息
  public static final String I_GET_NATION_LIST = "/MinZu/GetMinZuList/";

  //更新个人详细信息
  public static final String I_UPDATE_PERSONAL_DETAILS_INFORMATION =
    "/UserBasicInfo/UpdatePersonalAccountBasicInfo2/";

  //获取个人详细信息
  public static final String I_GET_PERSONAL_DETAILS_INFORMATION =
    "/UserBasicInfo/GetPersonalAccountBasicInfo2/";

  //获取创建简历个人资料
  public static final String I_GET_RESUME_PERSONAL_DATA = "/UserBasicInfo/ByjGetBasicInfo/";

  //更新个人简历信息
  public static final String I_UPDATE_RESUME_PERSONAL_DATA = "/UserBasicInfo/ByjUpdateBasicInfo/";

  //创建简历第一步
  public static final String I_ADD_RESUME_CAREER_OBJECTIVE = "/Resume/ByjAddBasicInfo/";

  //获取简历求职意向（新）
  public static final String I_GET_RESUME_CAREER_OBJECTIVE = "/Resume/GetSelectJobIntention/";

  //修改简历求职意向
  public static final String I_UPDATE_RESUME_CAREER_OBJECTIVE_NEW = "/Resume/EditResume3/";

  //添加工作经历
  public static final String I_ADD_RESUME_WORK_EXP = "/ResumeJobExp/InsertWorkExperience/";

  //添加简历教育背景（新）
  public static final String I_ADD_RESUME_EDU_BG = "/ResumeEdu/AddResumeEdus/";

  //修改消息状态为已查看
  public static final String I_UPDATE_MASSAGE_STATUS = "/Notice/UpdateNoticeLook/";

  //判断是否收藏公司
  public static final String I_CHECK_COLLECT_THE_COMPANY = "/JobStow/GetWhetherConcernCompanyStow/";

  //收藏公司
  public static final String I_COLLECT_THE_COMPANY = "/JobStow/GetConcernCompanyStow/";

  //取消收藏公司
  public static final String I_CANCEL_COLLECT_THE_COMPANY = "/JobStow/CancelConcernCompanyStow/";

  //分页获取收藏公司列表
  public static final String I_GET_COLLECT_COMPANY_LIST = "/StowJob/GetConcernCompanyStowList/";

  //上传简历附件
  public static final String I_OPERATION_ATTACHMENT_RESUME =
    "http://img.jrzp.com/serviceInterface.ashx";

  //修改简历个人信息中的求职意向
  public static final String I_UPDATE_RESUME_PERSONAL_DATA_CAREER_OBJECT =
    "/Resume/UpdateResumeInformation/";

  //获取订阅消息的总数量
  public static final String I_GET_UNREAD_MSG_NOTICE_COUNT = "/Notice/GetGroupXiaoxiCount/";

  //获取未读系统消息总数
  public static final String I_GET_UNREAD_SYSTEM_MSG_COUNT = "/Notice/GetSystemNoticeCountByPage/";

  //修改系统消息查看状态
  public static final String I_UPDATE_SYSTEM_MSG_READ_STATE = "/Notice/UpdateSysteNoticemLook/";

  //获取未读订阅消息总数量
  public static final String I_GET_UNREAD_SUB_MSG_COUNT = "/DingYueNews/GetUnreadDingYueNewsCount/";

  //获取谁看过我未读消息数量
  public static final String I_GET_UNREAD_VIEW_ME_COUNT = "/Notice/GetResumeLookCountByPage/";

  //修改谁看过我的查看状态
  public static final String I_UPDATE_SEW_ME_READ_STATE = "/Notice/GetResumeLookCheckedByPage/";

  //扫码登录
  public static final String I_SCAN_QR_CODE_LOGIN = "/User/UserLoginByScan/";

  //获取客服列表
  public static final String I_GET_SERVICE_LIST = "/Comm/GetKefuInfo3/";

  //检查用户信息是否完善
  public static final String I_CHECK_INFO_IS_PERFECTED =
    "/UserBasicInfo/IsHasFinishedPersionalInfo/";

  //编辑公司信息
  public static final String I_EDIT_COMPANY_INFO = "/Company/EditComInfo/";

  //检查用户公司信息是否完善
  public static final String I_CHECK_COMPANY_INFO_PERFECTED =
    "/Company/GetCompanyBaseInfoISFinish/";

  //资讯评论类型
  public static final int NEWS_TYPE_NEWS = 1;

  //高校评论类型
  public static final int NEWS_TYPE_UNIVERSITY = 2;

  //动态评论类型
  public static final int NEWS_TYPE_MOMENT = 3;

  //问答评论类型
  public static final int NEWS_TYPE_QUESTIONS = 4;

  //视频评论类型
  public static final int NEWS_TYPE_VIDEO = 5;

  //公考资讯分类
  public static final int NEWS_TYPE_STUDY = 6;

  //获取我的大学评论列表
  public static final String I_GET_COMMENT_LIST = "/NewsPinglun/GetPinglunListByPage/";

  //获取教职工招聘详情
  public static final String I_GET_FACULTY_RECRUIT_DETAILS = "/News/GetJiaozhigongZhaopinInfo/";

  //获取校园招聘详情
  public static final String I_GET_SCHOOL_RECRUIT_DETAILS = "/NewsArea/GetXiaozhaoInfo/";

  //检查是否关注招聘会
  public static final String I_CHECK_IS_FOLLOW = "/Guanzhu/ExistZhaopinhui/";

  //点赞/取消点赞
  public static final String I_LIKE_OR_UNLIKE_THE_COMMENT = "/NewsPinglunZan/AddOrCancelZan/";

  //添加评论
  public static final String I_ADD_COMMENT = "/NewsPinglun/AddPinglunList/";

  //查询院校列表
  public static final String I_GET_UNIVERSITY_LIST = "/Gaoxiao/ByjGetGaoxiaoListByPage/";

  //根据用户id获取高校id
  public static final String I_GET_UNIVERSITY_ID = "/UserBasicInfo/GetUserGaoxiaoID/";

  //获取专业列表
  public static final String I_GET_DEPARTMENT_LIST = "/Professional/GetProfessionalListByPage/";

  //获取积分充值优惠列表
  public static final String I_GET_RECHARGE_DISCOUNT_LIST =
    "/UserOrder/GetIntegralRechargeshejiaoItemList/";

  //上传图片
  public static final String ACTION_UPLOAD_MOMENT_PHOTO = "uploadFileOfCms";

  //上传图片url
  public static final String URL_UPLOAD_MOMENT_PHOTO =
    "http://image.jrzp.com/serviceInterface.ashx";

  //发布资讯
  public static final String I_POST_NEWS = "/ShejiaoNews/AddShejiaoNew/";

  //收藏/取消收藏
  public static final String I_ADD_OR_REMOVE_COLLECTION = "/Stow/AddOrCancelStow/";

  //关注高校类型
  public static final int FOLLOW_UNIVERSITY_TYPE = 2;

  //关注事业单位类型
  public static final int FOLLOW_ORG_TYPE = 3;

  //关注招聘会类型
  public static final int FOLLOW_JOB_FAIR_TYPE = 4;

  //关注用户类型
  public static final int FOLLOW_USER_TYPE = 5;

  //关注企业
  public static final int FOLLOW_COMPANY_TYPE = 5;

  //添加/取消关注
  public static final String I_ADD_OR_REMOVE_FOLLOW = "/Guanzhu/AddOrCancelGuanzhu/";

  //获取校友列表
  public static final String I_GET_SCHOOLMATE_LIST = "/Fujin/GetXiaoyouListByPage/";

  //获取支付宝订单信息
  public static final String I_GET_ALIPAY_ORDER_INFO = "/UserOrder/GetZfbPayString/";

  public static final int RECHARGE_TYPE_INTEGRAL = 3;

  //创建支付订单
  public static final String I_CREATE_PAYMENT_ORDER = "/UserOrder/AddIntegralRecharge/";

  //获取支付结果
  public static final String I_GET_PAYMENT_RESULT = "/UserOrder/GetUserOrderInfo/";

  //获取专业列表
  public static final String I_GET_PROFESSION_LIST = "/Zhuanye/GetZhuanyeList/";

  //获取班级列表
  public static final String I_GET_CLASS_LIST = "/Banji/GetBanjiList/";

  //更新用用户基本信息
  public static final String I_UPDATE_USER_BASIC_INFO = "/UserBasicInfo/EditUserInfoEV2/";

  //检查用户信息是否完善
  public static final String I_CHECK_USER_INFO_IS_COMPLETE = "/UserBasicInfo/isInfoFinished/";

  //创建简历
  public static final String I_CREATE_RESUME = "/Resume/JrzpAddResume/";

  //获取创建简历默认初始信息
  public static final String I_GET_CREATE_RESUME_DEFAULT_INFO =
    "/UserBasicInfo/GetResumeDefaultInfo/";

  //获取关注双选会列表
  public static final String I_GET_FOLLOWED_JOB_FAIR_LIST =
    "/Guanzhu/GetUserShuangxuanhuiListByPage/";

  //获取已支付用户列表
  public static final String I_GET_PAID_USER_LIST =
    "/ShejiaoNewsOrder/GetShejiaoNewOrderListByPage/";

  //获取打赏记录
  public static final String I_GET_REWARD_HISTORY_LIST =
    "/ShejiaoNewsOrder/GetShejiaoNewOrderListByPage2/";

  //删除社交信息
  public static final String I_DELETE_MOMENT = "/ShejiaoNews/DelShejiao/";

  //删除评论
  public static final String I_DELETE_COMMENT = "/NewsPinglun/DelPinglun/";

  //微信登录
  public static final String I_LOGIN_BY_WECHAT = "/OtherUser/JrzpGrWxLogin/";

  //是否已完善头像和昵称
  public static final String I_CHECK_AVATAR_IS_UPLOAD = "/UserBasicInfo/IsHFNameAndTxInfo/";

  //获取用户头像和昵称
  public static final String I_GET_USER_AVATAR_AND_NICKNAME =
    "/UserBasicInfo/GetUserBasicNameAndTxInfo/";

  //获取默认头像列表
  public static final String I_GET_DEFAULT_AVATAR_LIST = "/Comm/GetMorenTouxiangList/";

  //修改头像和昵称
  public static final String I_UPDATE_AVATAR_AND_NICKNAME =
    "/UserBasicInfo/UpdateUserBasicNameAndTxInfo/";

  //检查是否已绑定手机号
  public static final String I_CHECK_IS_BIND_MOBILE = "/User/IsHasBindMobilePhone/";

  //绑定手机号
  public static final String I_BIND_MOBILE_NUMBER = "/User/BindMobilePhone/";

  //发送短信验证码
  public static final String I_SEND_SMS_VERIFICATION_CODE = "/User/SendPhoneCodeWithKeyIv/";

  //保留微信
  public static final int ACCOUNT_MERGE_KEEP_WECHAT = 1;

  //保留手机
  public static final int ACCOUNT_MERGE_KEEP_MOBILE = 2;

  //合并账号
  public static final String I_ACCOUNT_MERGE = "/User/BindMobilePhoneHebingGZGK/";

  //************公招公考

  //获取资讯分类
  public static final String I_GET_NEWS_TYPE_LIST = "/NewType2/GetList/";

  //获取已排序的资讯分类
  public static final String I_GET_SORTED_NEW_TYPE_LIST = "/NewType2/GetListByUID/";

  //获取资讯列表
  public static final String I_GET_NEW_NEWS_LIST = "/News/GetNewsListByPage10/";

  //获取资讯详情
  public static final String I_GET_GZ_NEWS_DETAILS = "/News/GetNewDetail/";

  //获取推荐资讯
  public static final String I_GET_LINK_RECOMMEND_NEWS_LIST = "/News/GetRecommandNews/";

  //分页获取视频列表
  public static final String I_GET_VIDEO_LIST = "/Video/GetVideoListByPage/";

  //保存用户分类排序
  public static final String I_SAVE_NEWS_TYPE_SORT = "/NewType2/SaveOrderedList/";

  //获取视频详情
  public static final String I_GET_VIDEO_DETAILS = "/Video/GetVideoDetail/";

  //检查企业是否已认证
  public static final String I_CHECK_ENTERPRISE_IS_CERTIFICATION = "/News/VerifyPublish/";

  //提交企业认证
  public static final String I_SUBMIT_BUSINESS_CERTIFICATION =
    "/BusinessLicense/AddOrEditBusinessLicenseE/";

  //根据id获取公司名
  public static final String I_GET_COMPANY_NAME_BY_ID = "/Company/GetCompanyNameByUID/";

  //获取营业执照认证信息
  public static final String I_GET_CERTIFICATION_INFO = "/BusinessLicense/GetBusinessLicenseInfo/";

  //获取公司规模
  public static final String I_GET_COMPANY_SIZE_LIST = "/Company/GetCompanySize/";

  //发布公告
  public static final String I_POST_NOTICE = "/News/PublishV2/";

  //获取用户基本信息
  public static final String I_GET_USER_BASIC_INFO_1 = "/UserBasicInfo/GetUserInfo/";

  //获取个人中心头部信息
  public static final String I_GET_USER_HOME_INFO = "/User/GetUserPageInfo/";

  //获取个人中心公告信息
  public static final String I_GET_USER_NOTICE_LIST = "/User/GetDWNewsByPage/";

  public static final int USER_VIDEO_ORG = 4;

  @IntDef({
    USER_VIDEO_ORG
  })
  @Retention(RetentionPolicy.SOURCE)
  @Target(ElementType.PARAMETER)
  public @interface UserVideoType {

  }

  //获取个人中心视频
  public static final String I_GET_USER_VIDEO_LIST = "/User/GetUserVideoByPage/";

  //发布问题或回答
  public static final String I_POST_ANSWER_OR_QUESTION = "/Wenda/Publish/";

  //获取推荐视频
  public static final String I_GET_RECOMMEND_VIDEO = "/Video/GetRecommendVideos/";

  //获取用户问答
  public static final String I_GET_USER_QUESTION_LIST = "/User/GetUserWendaByPage/";

  //获取回答详情
  public static final String I_GET_ANSWER_DETAILS = "/Wenda/GetAnswerDetail/";

  //获取用户粉丝列表
  public static final String I_GET_USER_FANS_LIST = "/User/GetFansListByPageV2/";

  //获取用户关注列表
  public static final String I_GET_FOLLOW_LIST = "/User/GetFollowListByPageV2/";

  //获取我的收藏列表
  public static final String I_GET_MY_COLLECTION_LIST = "/User/GetMyStowByPage/";

  //获取我的评论列表
  public static final String I_GET_MY_COMMENT_LIST = "/User/GetMyCommentsByPage/";

  //获取我的点赞列表
  public static final String I_GET_MY_LIKE_LIST = "/User/GetMyLikeByPage/";

  //我发布的评论类型
  public static final int MY_PUBLISH_COMMENT_TYPE = 2;

  //我发布的问答类型
  public static final int MY_PUBLISH_QUESTION_TYPE = 3;

  //我发布的视频类型
  public static final int MY_PUBLISH_VIDEO_TYPE = 4;

  public static final int MY_PUBLISH_NEWS_COMMENT_TYPE = 1;

  public static final int MY_PUBLISH_ANSWER_COMMENT_TYPE = 4;

  public static final int MY_PUBLISH_VIDEO_COMMENT_TYPE = 5;

  public static final int MY_PUBLISH_STUDY_COMMENT_TYPE = 6;

  //删除我的发布
  public static final String I_DELETE_MY_PUBLISH = "/User/DeleteMyPublish/";

  //清空历史记录类型
  public static final int CLEAR_HISTORY_COLLECTION_TYPE = 1;

  public static final int CLEAR_HISTORY_LIKE_TYPE = 2;

  public static final int CLEAR_HISTORY_COMMENT_TYPE = 3;

  //清空收藏、评论、点赞
  public static final String I_CLEAR_MY_HISTORY = "/User/ClearMyList/";

  //根据地理位置请求职位列表
  public static final String I_GET_JOB_LIST_BY_LOCATION = "/ReleaseJob/GetJobListByLocation/";

  //发布视频
  public static final String I_PUBLISH_VIDEO = "/Video/Publish/";

  //获取职位置顶折扣信息
  public static final String I_GET_POSITION_TOP_DISCOUNT = "/ResumeTopZhekou/GetZhekouDaysList/";

  //创建简历置顶订单
  public static final String I_CREATE_RESUME_TOP_ORDER = "/UserOrder/AddResumeTopOrder/";

  //分页获取企业订单信息
  public static final String I_GET_ORDER_LIST = "/UserOrder/GetUserOrderListByPageV2/";

  //生效订单
  public static final String I_ACTIVATE_ORDER = "/UserOrder/EffectOrder/";

  //删除订单
  public static final String I_DELETE_ORDER = "/UserOrder/DelMoreUserOrder/";

  //获取问题详情
  public static final String I_GET_QUESTION_DETAILS = "/Wenda/GetQuestionDetail/";

  //获取回答列表
  public static final String I_GET_ANSWER_LIST = "/Wenda/GetAnswerByPage/";

  //获取可邀请回答的用户列表
  public static final String I_GET_INVITE_USER_LIST = "/Wenda/GetInvitedUserListByPage/";

  //邀请用户回答
  public static final String I_INVITE_USER_TO_ANSWER = "/Wenda/InviteToAnswer/";

  //搜索用户
  public static final String I_SEARCH_USER = "/UserBasicInfo/Search/";

  //检查是否有未读消息
  public static final String I_CHECK_HAS_UNREAD_MSG = "/Notify/HasUnLookedNotice/";

  //获取邀请问题列表
  public static final String I_GET_INVITE_QUESTION_LIST = "/Wenda/GetInvitedQuestionByPage/";

  //获取推荐问题列表
  public static final String I_GET_RECOMMEND_QUESTION_LIST = "/Wenda/GetRecommendQuestionByPage/";

  //获取问题列表
  public static final String I_GET_QUESTION_LIST = "/Wenda/GetQuestionListByPage/";

  //搜索问题
  public static final String I_SEARCH_QUESTION_LIST_BY_KEYWORD = "/WenDa/SearchQuestion/";

  //上传用户搜索行为
  public static final String I_UPLOAD_USER_SEARCH_ACTION = "/UserAction/InsertAction/";

  //上传用户离线行为
  public static final String I_UPLOAD_USER_OFFLINE_SEARCH_ACTION =
    "/UserAction/InsertOffLineActions/";

  //获取推荐资讯
  public static final String I_GET_RECOMMEND_NEWS_LIST = "/News/GetRecommendNewsListByPage12/";

  //获取搜索热门关键词
  public static final String I_GET_SEARCH_HOT_KEY = "/UserAction/GetHotListAction/";

  //检查隐私条款是否有更新
  public static final String I_CHECK_AGREEMENT_VERSION = "/About/JrzpYinsiTiaokuanVersion/";

  //获取附近职位ID
  public static final String I_GET_NEARBY_JOB_ID_LIST = "/ReleaseJob/GetNearbyList/";

  //获取附近职位
  public static final String I_GET_NEARBY_JOB_LIST = "/ReleaseJob/GetJobByIDs/";

  //获取发布视频分类
  public static final String I_GET_POST_VIDEO_TYPE_LIST = "/Video/GetVideoType/";

  //验证发布视频权限
  public static final String I_VERIFY_PUBLISH_VIDEO = "/Video/VerifyPublish/";

  //更新视频浏览数
  public static final String I_UPDATE_VIDEO_PLAY_COUNT = "/Video/UpdateView1/";

  //视频招聘报名
  public static final String I_VIDEO_RECRUIT_SIGN_UP = "/Video/VideoSignUp/";

  //获取用户报名信息
  public static final String I_GET_USER_SIGN_IN_INFO = "/User/GetUserForVideo/";

  //获取视频报名列表
  public static final String I_GET_SIGN_UP_USER_LIST = "/Video/GetSignUpListByPage/";

  //更新视频报名状态
  public static final String I_UPDATE_SIGN_UP_USER_STATUS = "/Video/UpdateSignUpState/";

  //检查是否有未读视频消息
  public static final String I_CHECK_HAS_UNREAD_VIDEO_MSG = "/Notify/HasUnLookedSignUp/";

  //获取视频未读消息列表
  public static final String I_GET_VIDEO_UNREAD_MSG_LIST = "/Notify/GetSignUpByPage/";

  //设置消息已读
  public static final String I_SETUP_MSG_READ_V2 = "/Notify/GetNoticeRead/";

  //获取区资讯
  public static final String I_GET_DISTRACT_NEWS_LIST = "/News/GetCountyNewsListByPage/";

  //获取问答榜单
  public static final String I_GET_QA_RANK_LIST = "/Wenda/GetWDRankingByPage/";

  //获取公考资料
  public static final String I_GET_STUDY_NEWS_LIST = "/News/GetStudyByPage/";

  //获取问答用户信息
  public static final String I_GET_QA_USER_INFO = "/Wenda/GetWendaUserInfo/";

  //获取公考详情
  public static final String I_GET_STUDY_NEWS_DETAILS = "/News/GetStudyDetail/";

  //上传用户本地行为
  public static final String I_UPLOAD_USER_LOCAL_ACTION = "/UserAction/InsertOffLineActions2/";

  //获取顶部三条未回答的问题
  public static final String I_GET_TOP_QUESTION_LIST = "/Wenda/GetTopQuestionList/";

  //*****************************新搜索*****************************//
  //搜索公考资讯
  public static final String I_SEARCH_NEWS = "/Search/NewsSearch/";

  //搜索学习资料
  public static final String I_SEARCH_STUDY_NEWS = "/Search/StudySearch/";

  //搜索视频
  public static final String I_SEARCH_VIDEO_NEWS = "/Search/VideoSearch/";

  //搜索问答
  public static final String I_SEARCH_QA_NEWS = "/Search/WendaSearch/";

  //搜索职位
  public static final String I_SEARCH_JOB = "/Search/JobSearch/";

  //搜索企业
  public static final String I_SEARCH_COMPANY = "/Search/CompanySearch/";

  //搜索事业单位
  public static final String I_SEARCH_ENTERPRISE = "/Search/DanweiSearch/";

  //综合搜索
  public static final String I_SEARCH_MULTIPLE_NEWS = "/Search/MultipleSearch/";

  //搜索招聘会
  public static final String I_SEARCH_JOB_FAIR = "/Search/JobFairSearch/";

  //获取回答历史（快捷回答）
  public static final String I_GET_ANSWER_HISTORY = "/Wenda/GetMyAnswersByPage/";

  //添加快捷回答
  public static final String I_ADD_QUICK_ANSWER = "/Wenda/AddQuickAnswer/";

  //获取广告图
  public static final String I_GET_AD_IMG_URL = "/User/GetMHYSAdv/";

  //获取分享信息
  public static final String I_GET_SHARE_INFO = "/Share/GetShareMessage/";

  //获取屏蔽公司列表
  public static final String I_GET_SHIELD_COMPANY_LIST = "/Xianzhi/GetBlackListByPage/";

  //添加屏蔽公司
  public static final String I_ADD_SHIELD_COMPANY = "/Xianzhi/AddBlackList/";

  //删除屏蔽公司
  public static final String I_REMOVE_SHIELD_COMPANY = "/Xianzhi/RemoveBlackList/";

  //获取用户当前招工作状态
  public static final String I_GET_USER_STATUS = "/User/GetUserState/";

  //更新用户状态
  public static final String I_UPDATE_USER_STATUS = "/User/UpdateUserState/";

  //获取是否开始首页推荐
  public static final String I_CHECK_OPEN_HOME_PUSH = "/User/GetTuijian/";

  //修改首页推荐状态
  public static final String I_SWITCH_HOME_PUSH_STATUS = "/User/UpdateTuijian/";

  //绑定用户推送Token
  public static final String I_BIND_USER_PUSH_TOKEN = "/PushToken/AddPushToken/";

  //发送动态信息
  public static final String I_POST_MOMENT = "/ShejiaoNews/AddShuoshuoNew/";

  //获取用户动态详情
  public static final String I_GET_MOMENT_DETAILS = "/ShejiaoNews/GetShejiaoNewsInfo/";

  //获取用户动态详情V2
  public static final String I_GET_USER_MOMENT_DETAILS_V2 = "/ShejiaoNews/GetShuoshuoNewsInfo/";
}