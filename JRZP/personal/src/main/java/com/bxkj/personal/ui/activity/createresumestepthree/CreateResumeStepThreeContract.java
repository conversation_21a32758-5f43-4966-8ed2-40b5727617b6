package com.bxkj.personal.ui.activity.createresumestepthree;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.personal.data.WorkExpData;
import com.bxkj.personal.data.WorkExpItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.activity.createresumestepthree
 * @Description: CreateResumeStepThree
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface CreateResumeStepThreeContract {
    interface View extends BaseView {
        void deleteResumeWorkExpSuccess(int position);

        void getExpListSuccess(List<WorkExpItemData> workExpItemDataList);

        void addSuccess();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        abstract void getResumeWorkExpList(int userId, int resumeId);

        abstract void addResumeWorkExp(int userId, int resumeId, WorkExpData workExpData);

        abstract void deleteResumeWorkExp(int userId,int workExpId,int position);
    }
}
