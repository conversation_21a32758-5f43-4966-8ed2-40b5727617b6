package com.bxkj.personal.ui.fragment.onlinecampustalk

import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.personal.data.OnlineCampusTalkBean
import com.bxkj.personal.data.source.JobRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 **/
class OnlineCampusTalkViewModel @Inject constructor(
    private val _jobRepo: JobRepo
) : BaseViewModel() {

    val listViewModel = RefreshListViewModel()

    private var searchKey: String = ""

    private val _onlineCampusTalkHeaderData = OnlineCampusTalkHeaderData()

    init {
        listViewModel.setOnLoadDataListener { pageIndex ->
            viewModelScope.launch {
                if (pageIndex == 1) {
                    _jobRepo.getTopOnlineCampusTalkInfo()
                        .handleResult({
                            it?.dataList?.let { list ->
                                _onlineCampusTalkHeaderData.topCampusTalk = list.first()
                            }
                        }, final = {
                            _jobRepo.getRecentOnlineCampusTalkList().handleResult({
                                _onlineCampusTalkHeaderData.recentCampusTalkList = it?.dataList
                                listViewModel.autoAddAll(listOf(_onlineCampusTalkHeaderData))
                            }, final = {
                                getOnlineCampusTalkList(pageIndex)
                            })
                        })
                }
            }
        }
    }

    private fun getOnlineCampusTalkList(pageIndex: Int) {
        viewModelScope.launch {
            _jobRepo.getOnlineCampusTalkList(searchKey, pageIndex, 16)
                .handleResult({
                    listViewModel.addAll(it?.dataList ?: emptyList<OnlineCampusTalkBean>())
                }, {
                    if (it.isNoDataError) {
                        listViewModel.noMoreData()
                    }
                })
        }
    }

    fun start() {
        listViewModel.refresh()
    }

    fun startSearch(keyword: String) {
        searchKey = keyword
        listViewModel.refresh(true)
    }

    data class OnlineCampusTalkHeaderData(
        var topCampusTalk: OnlineCampusTalkBean? = null,
        var recentCampusTalkList: List<OnlineCampusTalkBean>? = null
    )
}