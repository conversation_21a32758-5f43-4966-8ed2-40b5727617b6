package com.bxkj.personal.ui.activity.videolist

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.personal.R
import com.bxkj.personal.databinding.VideoActivityVideoListBinding
import com.bxkj.personal.ui.fragment.videogroup.VideoContainerFragment

/**
 * Description:
 * Author:Sanjin
 **/
class VideoListActivity : BaseDBActivity<VideoActivityVideoListBinding, BaseViewModel>() {

    companion object {

        fun newIntent(context: Context) = Intent(context, VideoListActivity::class.java)
    }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.video_activity_video_list

    override fun initPage(savedInstanceState: Bundle?) {
        setupContentFragment()
    }

    private fun setupContentFragment() {
        supportFragmentManager.beginTransaction()
            .add(R.id.fl_content, VideoContainerFragment.newInstance(true))
            .commit()
    }
}