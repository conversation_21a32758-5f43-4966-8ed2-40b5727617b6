package com.bxkj.personal.ui.activity.service;

import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bxkj.common.base.mvvm.BaseDBActivity;
import com.bxkj.common.util.DensityUtils;
import com.bxkj.common.util.SystemUtil;
import com.bxkj.common.util.recyclerutil.RecycleViewDivider;
import com.bxkj.personal.R;
import com.bxkj.personal.databinding.PersonalActivityServiceBinding;

/**
 * @version V1.0
 * @Description:
 * @TODO: TODO
 */
public class ServiceActivity extends BaseDBActivity<PersonalActivityServiceBinding, ServiceViewModel> {

    @Override
    protected Class<ServiceViewModel> getViewModelClass() {
        return ServiceViewModel.class;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.personal_activity_service;
    }

    @Override
    protected void initPage(@Nullable Bundle savedInstanceState) {
        getViewBinding().setViewModel(getViewModel());

        getViewModel().getServiceList();
        setupServiceListAdapter();
    }

    private void setupServiceListAdapter() {
        final ServiceListAdapter serviceListAdapter = new ServiceListAdapter(this, null, R.layout.personal_recycler_service_list_item);
        serviceListAdapter.setOnItemClickListener((view, position) -> {
            if (view.getId() == R.id.tv_call_phone) {
                SystemUtil.callPhone(ServiceActivity.this, serviceListAdapter.getData().get(position).getKefuTel());
            } else {
                SystemUtil.toQqTalk(ServiceActivity.this, serviceListAdapter.getData().get(position).getKefuQq());
            }
        });
        final RecyclerView recyclerServiceList = getViewBinding().recyclerServiceList;
        recyclerServiceList.setLayoutManager(new LinearLayoutManager(this));
        recyclerServiceList.addItemDecoration(new RecycleViewDivider(this, LinearLayoutManager.HORIZONTAL, DensityUtils.dp2px(this, 8), getResColor(R.color.common_f4f4f4), true));
        getViewModel().setServiceListAdapter(serviceListAdapter);
    }
}
