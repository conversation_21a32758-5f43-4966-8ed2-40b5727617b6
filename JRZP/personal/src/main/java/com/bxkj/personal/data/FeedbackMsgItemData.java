package com.bxkj.personal.data;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data.db
 * @Description:
 * @TODO: TODO
 * @date 2018/11/23
 */
public class FeedbackMsgItemData extends BaseObservable implements Parcelable {

  private int id;
  private String typeName;
  private int otherid;
  private int resid;
  private int uid;
  private int cuid;
  private String content;
  private int look;
  private String date;
  private CompanyBean company;
  private int index;
  private int nolookCount;
  private int type;
  private String relName;
  private String cUserName;
  private String cUserPhoto;

  public static FeedbackMsgItemData fromPushData(int companyId, String companyName) {
    return new FeedbackMsgItemData(companyId, CompanyBean.fromName(companyName));
  }

  public FeedbackMsgItemData(int cuid, CompanyBean company) {
    this.cuid = cuid;
    this.company = company;
  }

  protected FeedbackMsgItemData(Parcel in) {
    id = in.readInt();
    typeName = in.readString();
    otherid = in.readInt();
    resid = in.readInt();
    uid = in.readInt();
    cuid = in.readInt();
    content = in.readString();
    look = in.readInt();
    date = in.readString();
    company = in.readParcelable(CompanyBean.class.getClassLoader());
    index = in.readInt();
    nolookCount = in.readInt();
    type = in.readInt();
    relName = in.readString();
    cUserName = in.readString();
    cUserPhoto = in.readString();
  }

  public static final Creator<FeedbackMsgItemData> CREATOR = new Creator<FeedbackMsgItemData>() {
    @Override
    public FeedbackMsgItemData createFromParcel(Parcel in) {
      return new FeedbackMsgItemData(in);
    }

    @Override
    public FeedbackMsgItemData[] newArray(int size) {
      return new FeedbackMsgItemData[size];
    }
  };

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getTypeName() {
    return typeName;
  }

  public void setTypeName(String typeName) {
    this.typeName = typeName;
  }

  public int getOtherid() {
    return otherid;
  }

  public void setOtherid(int otherid) {
    this.otherid = otherid;
  }

  public int getResid() {
    return resid;
  }

  public void setResid(int resid) {
    this.resid = resid;
  }

  public int getUid() {
    return uid;
  }

  public void setUid(int uid) {
    this.uid = uid;
  }

  public int getCuid() {
    return cuid;
  }

  public void setCuid(int cuid) {
    this.cuid = cuid;
  }

  public String getContent() {
    if (type == 40) {
      return relName;
    }
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public int getLook() {
    return look;
  }

  public void setLook(int look) {
    this.look = look;
  }

  public String getDate() {
    return date.split("\\s")[0];
  }

  public void setDate(String date) {
    this.date = date;
  }

  public CompanyBean getCompany() {
    return company;
  }

  public void setCompany(CompanyBean company) {
    this.company = company;
  }

  public int getIndex() {
    return index;
  }

  public void setIndex(int index) {
    this.index = index;
  }

  @Bindable
  public int getNolookCount() {
    return nolookCount;
  }

  public void setNolookCount(int nolookCount) {
    this.nolookCount = nolookCount;
    notifyPropertyChanged(com.bxkj.personal.BR.nolookCount);
  }

  public int getType() {
    return type;
  }

  public void setType(int type) {
    this.type = type;
  }

  public String getRelName() {
    return relName;
  }

  public void setRelName(String relName) {
    this.relName = relName;
  }

  public String getMsgType() {
    return "[" + typeName + "]";
  }

  public String getAuthor() {
    if (type == 40) {
      return cUserName;
    } else {
      return company == null ? "" : company.name;
    }
  }

  public String getAuthorAvatar() {
    if (type == 40) {
      return cUserPhoto;
    } else {
      return company == null ? "" : (company.domain + company.logo);
    }
  }

  @Override
  public int describeContents() {
    return 0;
  }

  @Override
  public void writeToParcel(Parcel dest, int flags) {
    dest.writeInt(id);
    dest.writeString(typeName);
    dest.writeInt(otherid);
    dest.writeInt(resid);
    dest.writeInt(uid);
    dest.writeInt(cuid);
    dest.writeString(content);
    dest.writeInt(look);
    dest.writeString(date);
    dest.writeParcelable(company, flags);
    dest.writeInt(index);
    dest.writeInt(nolookCount);
    dest.writeInt(type);
    dest.writeString(relName);
    dest.writeString(cUserName);
    dest.writeString(cUserPhoto);
  }

  public static class CompanyBean implements Parcelable {

    private String name = "";
    private String logo = "";
    private String domain = "";

    public static CompanyBean fromName(String companyName) {
      return new CompanyBean(companyName);
    }

    public CompanyBean(String name) {
      this.name = name;
    }

    protected CompanyBean(Parcel in) {
      name = in.readString();
      logo = in.readString();
      domain = in.readString();
    }

    public static final Creator<CompanyBean> CREATOR = new Creator<CompanyBean>() {
      @Override
      public CompanyBean createFromParcel(Parcel in) {
        return new CompanyBean(in);
      }

      @Override
      public CompanyBean[] newArray(int size) {
        return new CompanyBean[size];
      }
    };

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public String getLogo() {
      return logo;
    }

    public void setLogo(String logo) {
      this.logo = logo;
    }

    public String getDomain() {
      return domain;
    }

    public void setDomain(String domain) {
      this.domain = domain;
    }

    public String getIntegratedLogo() {
      return domain + "/" + logo;
    }

    @Override
    public int describeContents() {
      return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
      parcel.writeString(name);
      parcel.writeString(logo);
      parcel.writeString(domain);
    }
  }
}
