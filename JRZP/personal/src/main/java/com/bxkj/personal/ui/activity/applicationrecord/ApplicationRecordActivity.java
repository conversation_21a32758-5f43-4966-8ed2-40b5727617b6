package com.bxkj.personal.ui.activity.applicationrecord;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import androidx.viewpager.widget.ViewPager;
import com.bxkj.common.base.BaseDaggerActivity;
import com.bxkj.common.util.TitleBarManager;
import com.bxkj.common.widget.DropDownMenuView;
import com.bxkj.common.widget.dropdown.DropDownPopup;
import com.bxkj.personal.R;
import com.bxkj.personal.ui.fragment.applyrecordlist.ApplicationRecordListFragment;
import net.lucode.hackware.magicindicator.MagicIndicator;
import net.lucode.hackware.magicindicator.ViewPagerHelper;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;

/**
 * @version V1.0
 * @Description: 投递记录
 * @TODO: TODO
 */
//@Route(path = ResumeDeliveryRecordNavigation.PATH)
public class ApplicationRecordActivity extends BaseDaggerActivity {

  private MagicIndicator tabLayout;
  private ViewPager vpApplicationRecord;
  private View vLine;

  private CommonNavigator mCommonNavigator;
  private String[] mPageTitles;
  private ApplicationRecordPagerAdapter mApplicationRecordPagerAdapter;
  private ApplicationRecordListFragment mCurrentFragment;
  private DropDownPopup mDropDownPopup;

  public static void start(Context context, int currentIndex) {
    Intent starter = new Intent(context, ApplicationRecordActivity.class);
    starter.putExtra(ResumeDeliveryRecordNavigation.EXTRA_TARGET_INDEX, currentIndex);
    context.startActivity(starter);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.personal_activity_new_application_record;
  }

  @Override
  protected void initTitleBar(TitleBarManager titleBarManager) {
    titleBarManager.setTitle(getString(R.string.personal_application_record))
      .setRightText(getString(R.string.common_edit))
      .setRightOptionClickListener(view -> mCurrentFragment.switchCheckBoxShow());
  }

  @Override
  protected void initPage() {
    bindView(getWindow().getDecorView());

    mPageTitles = getResources().getStringArray(R.array.application_record_type);
    mApplicationRecordPagerAdapter =
      new ApplicationRecordPagerAdapter(getSupportFragmentManager(), mPageTitles);
    mCurrentFragment = ((ApplicationRecordListFragment) mApplicationRecordPagerAdapter.getItem(0));
    vpApplicationRecord.setAdapter(mApplicationRecordPagerAdapter);
    vpApplicationRecord.setOffscreenPageLimit(mApplicationRecordPagerAdapter.getCount());
    vpApplicationRecord.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
      @Override
      public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

      }

      @Override
      public void onPageSelected(int position) {
        mCurrentFragment =
          ((ApplicationRecordListFragment) mApplicationRecordPagerAdapter.getItem(position));
      }

      @Override
      public void onPageScrollStateChanged(int state) {

      }
    });
    setupIndicator();
    setupDropDownPopup();
    vpApplicationRecord.setCurrentItem(
      getIntent().getIntExtra(ResumeDeliveryRecordNavigation.EXTRA_TARGET_INDEX, 0));
  }

  private void setupDropDownPopup() {
    mDropDownPopup = new DropDownPopup(this, vLine);
    mDropDownPopup.addContentViews(getDropDownMenu());
  }

  private View getDropDownMenu() {
    String[] interviewStatus = getResources().getStringArray(R.array.interview_status);
    DropDownMenuView dropDownMenu = new DropDownMenuView(this);
    dropDownMenu.setData(interviewStatus);
    dropDownMenu.setOnItemClickListener((view, position) -> {
      mDropDownPopup.close();
      mPageTitles[3] = interviewStatus[position];
      mCommonNavigator.notifyDataSetChanged();
      mCurrentFragment.setPageType(
        getResources().getStringArray(R.array.interview_status_type)[position]);
    });
    return dropDownMenu;
  }

  private void setupIndicator() {
    mCommonNavigator = new CommonNavigator(this);
    mCommonNavigator.setAdjustMode(true);
    final ApplicationRecordNavigatorAdapter mApplicationRecordNavigatorAdapter =
      new ApplicationRecordNavigatorAdapter(vpApplicationRecord, mPageTitles);
    mApplicationRecordNavigatorAdapter.setOnTabItemClickListener((view, index) -> {
      if (index == 3 && mApplicationRecordNavigatorAdapter.getCurrentIndex() == 3) {
        mDropDownPopup.showItemAsDropDown(0);
      } else {
        mDropDownPopup.close();
      }
    });
    mCommonNavigator.setAdapter(mApplicationRecordNavigatorAdapter);
    tabLayout.setNavigator(mCommonNavigator);
    ViewPagerHelper.bind(tabLayout, vpApplicationRecord);
  }

  @Override
  public void onBackPressed() {
    if (mDropDownPopup.isShowing()) {
      mDropDownPopup.close();
    } else {
      super.onBackPressed();
    }
  }

  @Override
  public void finish() {
    if (mCurrentFragment != null && mCurrentFragment.getCheckBoxShow()) {
      mCurrentFragment.switchCheckBoxShow();
    } else {
      super.finish();
    }
  }

  private void bindView(View bindSource) {
    tabLayout = bindSource.findViewById(R.id.tab_layout);
    vpApplicationRecord = bindSource.findViewById(R.id.vp_application_record);
    vLine = bindSource.findViewById(R.id.v_line);
  }
}
