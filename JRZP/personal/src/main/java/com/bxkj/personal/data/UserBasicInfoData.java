package com.bxkj.personal.data;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.BR;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: gzgk
 * @Package com.bxkj.personal.data
 * @Description:
 * @date 2020/1/6
 */
public class UserBasicInfoData extends BaseObservable {

  /**
   * userID : 0
   * photo : http://img.jrzp.com/images_server/gaoxiao/img/zhongyangminzudaxuexiaomen.jpg
   * name : 余冰翰
   * nickName : 吸猫的鱼
   * sex : 0
   * birthDay : 1990.01.11 00:00:00
   * provinceID : 1
   * provinceName : 上海
   * cityID : 2
   * cityName : 上海
   * address : 浙江杭州
   * eduCode : 2
   * eduName : 硕士
   * graduationTime : 2015-06-1 00:00:00.000
   * info : 测试测试
   */

  private int userID;
  private String photo;
  private String name;
  private String nickName;
  private int sex;
  private String birthDay;
  private int provinceID;
  private String provinceName = "";
  private int cityID;
  private String cityName = "";
  private String address;
  private int eduCode;
  private String eduName;
  private String graduationTime;
  private String info;
  private int dwID;
  private String dwName;
  private String dwLogo;
  private int wstype;
  private int gxId;
  private String gxName;
  private int yxId;
  private String yxName;
  private int zyId;
  private String zyName;

  public int getWstype() {
    return wstype;
  }

  public void setWstype(int wstype) {
    this.wstype = wstype;
  }

  //    public UserBasicInfoData(UserBasicInfoData userBasicInfoData){
  //        this.photo=userBasicInfoData.photo;
  //    }

  public int getDwID() {
    return dwID;
  }

  public void setDwID(int dwID) {
    this.dwID = dwID;
  }

  public String getDwName() {
    return dwName;
  }

  public void setDwName(String dwName) {
    this.dwName = dwName;
  }

  public String getDwLogo() {
    return dwLogo;
  }

  public void setDwLogo(String dwLogo) {
    this.dwLogo = dwLogo;
  }

  public int getUserID() {
    return userID;
  }

  public void setUserID(int userID) {
    this.userID = userID;
  }

  public String getPhoto() {
    return photo;
  }

  public void setPhoto(String photo) {
    this.photo = photo;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  @Bindable
  public String getNickName() {
    return nickName;
  }

  public void setNickName(String nickName) {
    this.nickName = nickName;
    notifyPropertyChanged(BR.nickName);
  }

  @Bindable
  public int getSex() {
    return sex;
  }

  public void setSex(int sex) {
    this.sex = sex;
    notifyPropertyChanged(com.bxkj.personal.BR.sex);
  }

  @Bindable
  public String getBirthDay() {
    return birthDay;
  }

  public void setBirthDay(String birthDay) {
    this.birthDay = birthDay;
    notifyPropertyChanged(BR.birthDay);
  }

  public int getProvinceID() {
    return provinceID;
  }

  public void setProvinceID(int provinceID) {
    this.provinceID = provinceID;
  }

  @Bindable
  public String getProvinceName() {
    return provinceName;
  }

  public void setProvinceName(String provinceName) {
    this.provinceName = provinceName;
    notifyPropertyChanged(BR.provinceName);
  }

  public int getCityID() {
    return cityID;
  }

  public void setCityID(int cityID) {
    this.cityID = cityID;
  }

  @Bindable
  public String getCityName() {
    return cityName;
  }

  public void setCityName(String cityName) {
    this.cityName = cityName;
    notifyPropertyChanged(BR.cityName);
  }

  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public int getEduCode() {
    return eduCode;
  }

  public void setEduCode(int eduCode) {
    this.eduCode = eduCode;
  }

  /**
   * 大专往上
   */
  public boolean higherHVEdu() {
    return eduCode >= 6;
  }

  @Bindable
  public String getEduName() {
    return eduName;
  }

  public void setEduName(String eduName) {
    this.eduName = eduName;
    notifyPropertyChanged(BR.eduName);
  }

  @Bindable
  public String getGraduationTime() {
    return graduationTime;
  }

  public void setGraduationTime(String graduationTime) {
    this.graduationTime = graduationTime;
    notifyPropertyChanged(BR.graduationTime);
  }

  @Bindable
  public String getInfo() {
    return info;
  }

  public void setInfo(String info) {
    this.info = info;
    notifyPropertyChanged(BR.info);
  }

  public int getGxId() {
    return gxId;
  }

  public void setGxId(int gxId) {
    this.gxId = gxId;
  }

  @Bindable
  public String getGxName() {
    return gxName;
  }

  public void setGxName(String gxName) {
    this.gxName = gxName;
    notifyPropertyChanged(BR.gxName);
  }

  public boolean noSchoolInfo() {
    return gxId == 0 && CheckUtils.isNullOrEmpty(gxName);
  }

  public int getYxId() {
    return yxId;
  }

  public void setYxId(int yxId) {
    this.yxId = yxId;
  }

  @Bindable
  public String getYxName() {
    return yxName;
  }

  public void setYxName(String yxName) {
    this.yxName = yxName;
    notifyPropertyChanged(BR.yxName);
  }

  public boolean noDepartmentInfo() {
    return yxId == 0 && CheckUtils.isNullOrEmpty(yxName);
  }

  public int getZyId() {
    return zyId;
  }

  public void setZyId(int zyId) {
    this.zyId = zyId;
  }

  @Bindable
  public String getZyName() {
    return zyName;
  }

  public void setZyName(String zyName) {
    this.zyName = zyName;
    notifyPropertyChanged(BR.zyName);
  }

  public void resetSchoolInfo() {
    gxId = 0;
    setGxName("");
    yxId = 0;
    setYxName("");
    zyId = 0;
    setZyName("");
  }

  @Override public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    UserBasicInfoData that = (UserBasicInfoData) o;
    return userID == that.userID &&
        sex == that.sex &&
        provinceID == that.provinceID &&
        cityID == that.cityID &&
        eduCode == that.eduCode &&
        dwID == that.dwID &&
        wstype == that.wstype &&
        Objects.equals(photo, that.photo) &&
        Objects.equals(name, that.name) &&
        Objects.equals(nickName, that.nickName) &&
        Objects.equals(birthDay, that.birthDay) &&
        Objects.equals(provinceName, that.provinceName) &&
        Objects.equals(cityName, that.cityName) &&
        Objects.equals(address, that.address) &&
        Objects.equals(eduName, that.eduName) &&
        Objects.equals(graduationTime, that.graduationTime) &&
        Objects.equals(info, that.info) &&
        Objects.equals(dwName, that.dwName) &&
        Objects.equals(dwLogo, that.dwLogo);
  }

  @Override public int hashCode() {
    return Objects.hash(userID, photo, name, nickName, sex, birthDay, provinceID, provinceName,
        cityID, cityName, address, eduCode, eduName, graduationTime, info, dwID, dwName, dwLogo,
        wstype);
  }
}
