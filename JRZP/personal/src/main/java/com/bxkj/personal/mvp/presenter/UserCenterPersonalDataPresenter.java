package com.bxkj.personal.mvp.presenter;

import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.data.UserCenterPersonalData;
import com.bxkj.personal.mvp.contract.UserCenterPersonalDataContract;
import com.bxkj.personal.api.PersonalApi;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.presenter
 * @Description: UserCenterPersonalData
 * @TODO: TODO
 * @date 2018/3/27
 */

public class UserCenterPersonalDataPresenter extends UserCenterPersonalDataContract.Presenter {

    private static final String TAG = UserCenterPersonalDataPresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;

    @Inject
    public UserCenterPersonalDataPresenter(PersonalApi personalApi) {
        mPersonalApi = personalApi;
    }

    @Override
    public void getUserCenterPersonalData(int userId) {
        mPersonalApi.getUserCenterPersonalData(userId)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.getPersonalDataSuccess((UserCenterPersonalData) baseResponse.getData());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.noPersonalData();
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
