package com.bxkj.personal.ui.activity.selectdepartment

import android.app.Application
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshLayoutViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory
import com.bxkj.personal.R
import com.bxkj.personal.data.DepartmentItemData
import com.bxkj.personal.data.source.UniversityRepo
import javax.inject.Inject

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.selectdepartment
 * @Description:
 * <AUTHOR>
 * @date 2019/10/24
 * @version V1.0
 */
class SelectDepartmentViewModel @Inject constructor(
    private val mUniversityRepo: UniversityRepo,
    application: Application
) : BaseViewModel() {

    val listViewModel = RefreshListViewModel()
    val searchContent = MutableLiveData<String>()

    val addNewProfessionCommand = LiveEvent<Void>()
    val addNewClassCommand = LiveEvent<Void>()
    val hiddenKeyboardCommand = LiveEvent<Void>()

    private var mSearchKeyword: String = CommonApiConstants.NO_TEXT

    private var mSelectInfoType: UniversityInfoType = UniversityInfoType.DEPARTMENT

    init {
        listViewModel.refreshLayoutViewModel.enableRefresh(false)
        listViewModel.refreshLayoutViewModel.enableLoadMore(false)
    }

    fun start(intent: Intent?) {
        intent?.let {
            mSelectInfoType =
                intent.getSerializableExtra(SelectDepartmentActivity.EXTRA_SELECT_INFO_TYPE) as UniversityInfoType
            val parentId = intent.getIntExtra(
                SelectDepartmentActivity.EXTRA_PARENT_ID,
                CommonApiConstants.NO_DATA
            )
            setupListViewModelByInfoType(parentId, mSelectInfoType)
            listViewModel.refresh()
        }
    }

    private fun setupListViewModelByInfoType(parentId: Int, selectInfoType: UniversityInfoType) {
        listViewModel.setOnMultiLoadDataListener(object :
            RefreshLayoutViewModel.OnMultiLoadDataListener {
            override fun onNoData() {
                when (selectInfoType) {
                    UniversityInfoType.DEPARTMENT ->
                        listViewModel.showPageStatus(PageStatusConfigFactory.newEmptyConfig())
                    UniversityInfoType.PROFESSION -> {
                        hiddenKeyboardCommand.call()
                        listViewModel.showPageStatus(
                            PageStatusConfigFactory.newErrorConfig().setText("对不起，暂无符合条件的专业")
                                .setImg(R.drawable.common_ic_no_content).setBtnText("去添加")
                                .setOnButtonClickListener {
                                    addNewProfessionCommand.call()
                                })
                    }
                    UniversityInfoType.CLASS -> {
                        hiddenKeyboardCommand.call()
                        listViewModel.showPageStatus(
                            PageStatusConfigFactory.newErrorConfig().setText("对不起，暂无符合条件的班级")
                                .setImg(R.drawable.common_ic_no_content).setBtnText("去添加")
                                .setOnButtonClickListener {
                                    addNewClassCommand.call()
                                })
                    }
                    else -> {

                    }
                }
            }

            override fun onLoadData(currentPage: Int) {
                when (selectInfoType) {
                    UniversityInfoType.DEPARTMENT -> mUniversityRepo.getDepartmentList(
                        mSearchKeyword,
                        object : ResultDataCallBack<List<DepartmentItemData>> {
                            override fun onSuccess(data: List<DepartmentItemData>?) {
                                listViewModel.reset(data)
                            }

                            override fun onError(respondThrowable: RespondThrowable) {
                                if (respondThrowable.errCode != 30001) {
                                    listViewModel.loadError()
                                } else {
                                    listViewModel.noMoreData()
                                }
                            }
                        })
                    UniversityInfoType.PROFESSION -> mUniversityRepo.getProfessionList(
                        parentId,
                        mSearchKeyword,
                        object : ResultDataCallBack<List<DepartmentItemData>> {
                            override fun onSuccess(data: List<DepartmentItemData>?) {
                                listViewModel.reset(data)
                            }

                            override fun onError(respondThrowable: RespondThrowable) {
                                if (respondThrowable.errCode == 30002 || respondThrowable.errCode == 30001) {
                                    listViewModel.noMoreData()
                                } else {
                                    listViewModel.loadError()
                                }
                            }
                        })
                    UniversityInfoType.CLASS -> mUniversityRepo.getClassList(
                        parentId,
                        mSearchKeyword,
                        object : ResultDataCallBack<List<DepartmentItemData>> {
                            override fun onSuccess(data: List<DepartmentItemData>?) {
                                listViewModel.reset(data)
                            }

                            override fun onError(respondThrowable: RespondThrowable) {
                                if (respondThrowable.errCode == 30002 || respondThrowable.errCode == 30001) {
                                    listViewModel.noMoreData()
                                } else {
                                    listViewModel.loadError()
                                }
                            }
                        })
                    else -> {}
                }
            }

            override fun onPageError() {
                listViewModel.showPageStatus(
                    PageStatusConfigFactory.newErrorConfig().setOnButtonClickListener {
                        listViewModel.refresh()
                    })
            }
        })
    }

    fun refreshPageBySearchKeyword(keyword: String) {
        mSearchKeyword = keyword
        listViewModel.refresh()
    }
}