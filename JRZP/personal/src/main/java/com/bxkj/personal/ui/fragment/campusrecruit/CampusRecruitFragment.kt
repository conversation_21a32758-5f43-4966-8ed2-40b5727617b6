package com.bxkj.personal.ui.fragment.campusrecruit

import android.app.Activity.RESULT_OK
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.kotlin.attachIndicator
import com.bxkj.jrzp.support.feature.ui.commonsearch.CommonSearchActivity
import com.bxkj.jrzp.support.feature.ui.commonsearch.SearchType
import com.bxkj.personal.R
import com.bxkj.personal.databinding.FragmentCampusRecruitBinding
import com.bxkj.personal.ui.fragment.campustalk.CampusTalkFragment
import com.bxkj.personal.ui.fragment.onlinecampustalk.OnlineCampusTalkFragment
import com.bxkj.personal.ui.fragment.top500recruit.Top500RecruitFragment
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 * 校招
 */
class CampusRecruitFragment : BaseDBFragment<FragmentCampusRecruitBinding, BaseViewModel>() {

  companion object {

    fun newInstance(): Fragment {
      return CampusRecruitFragment()
    }
  }

  val searchKeyword = MutableLiveData<String>()

  private val _searchLauncher = registerForActivityResult(StartActivityForResult()) {
    if (it.resultCode == RESULT_OK) {
      val searchContent = it.data?.getStringExtra(CommonSearchActivity.EXTRA_SEARCH_CONTENT)
      searchContent?.let {
        viewBinding.tvSearchKeyword.text = searchContent
        searchKeyword.value = it
      }
    }
  }

  override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.fragment_campus_recruit

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    setupContent()

    viewBinding.tvSearchKeyword.setOnClickListener {
      _searchLauncher.launch(
        CommonSearchActivity.newIntent(
          requireContext(),
          SearchType.CAMPUS_RECRUITMENT
        )
      )
    }
  }

  private fun setupContent() {
    viewBinding.indicatorType.navigator = CommonNavigator(requireContext())
      .apply {
        adapter = MagicIndicatorAdapter(
          arrayOf("校园招聘", "500强网申", "宣讲会", "云宣讲"),
          MagicIndicatorAdapter.TabItemConfig(
            normalColor = R.color.cl_999999,
            selectedColor = R.color.cl_ff7405,
            scaleRatio = 0.2f,
            indicatorWidth = 0f
          )
        ).apply {
          setOnTabClickListener(object : OnTabClickListener {
            override fun onTabClicked(v: View, index: Int) {
              viewBinding.vpContent.currentItem = index
            }
          })
        }
      }

    viewBinding.vpContent.adapter = object : FragmentStateAdapter(this) {
      override fun getItemCount(): Int = 4

      override fun createFragment(position: Int): Fragment {
        return when (position) {
          0 -> CampusRecruitListFragment.newInstance()
          1 -> Top500RecruitFragment.newInstance()
          2 -> CampusTalkFragment.newInstance()
          3 -> OnlineCampusTalkFragment.newInstance()
          else -> CampusRecruitListFragment.newInstance()
        }
      }
    }

    viewBinding.vpContent.attachIndicator(viewBinding.indicatorType)
  }
}