package com.bxkj.personal.ui.activity.myresume.careerobjective;


import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.data.ResumeBasicData;
import com.bxkj.personal.api.PersonalApi;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.myresume.careerobjective
 * @Description: CareerObjective
 * @TODO: TODO
 * @date 2018/3/27
 */

public class CareerObjectivePresenter extends CareerObjectiveContract.Presenter {

  private static final String TAG = CareerObjectivePresenter.class.getSimpleName();
  private PersonalApi mPersonalApi;

  @Inject
  public CareerObjectivePresenter(PersonalApi personalApi) {
    mPersonalApi = personalApi;
  }

  @Override
  public void updateCareerObjective(int userId, ResumeBasicData editData) {
    if (CheckUtils.isNullOrEmpty(editData.getDetailsName2())) {
      mView.onError("请填写原从事职位");
      return;
    } else if (editData.getJobid2() == 0) {
      mView.onError("未选择期望职位");
      return;
    } else if (CheckUtils.isNullOrEmpty(editData.getDetailsName())) {
      mView.onError("未填写具体职位");
      return;
    } else if (editData.getJnid() == 0) {
      mView.onError("未选择工作性质");
      return;
    } else if (editData.getXiang() == CommonApiConstants.NO_DATA) {
      mView.onError("工作地点未完善");
      return;
    } else if (editData.getWtid() == 0) {
      mView.onError("未选择工作经验");
      return;
    }
    mView.showLoading();
    mPersonalApi.updateCareerObjective(userId, editData.getId(), editData.getDetailsName()
            , editData.getJnid(), editData.getTradeid(), editData.getJobid1(), editData.getJobid2()
            , editData.getWillMoney(), editData.getDaogang(), editData.getSheng(), editData.getShi(),
            editData.getZhen(), editData.getXiang(), editData.getWtid(), editData.getDetailsName2())
        .compose(RxHelper.applyThreadSwitch())
        .subscribe(new CustomObserver() {
          @Override
          protected void onSuccess(BaseResponse baseResponse) {
            mView.updateCareerObjectiveSuccess();
          }

          @Override
          protected void onError(RespondThrowable respondThrowable) {
            mView.hiddenLoading();
            mView.onError(respondThrowable.getErrMsg());
          }

          @Override
          public void onSubscribe(Disposable d) {
            mCompositeDisposable.add(d);
          }

          @Override
          public void onComplete() {
            super.onComplete();
            mView.hiddenLoading();
          }
        });
  }
}
