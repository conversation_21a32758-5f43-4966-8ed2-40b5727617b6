package com.bxkj.personal.ui.activity.gznews

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.widget.NestedScrollView
import androidx.databinding.Observable
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.therouter.router.Route
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.OnItemLongClickListener
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.InputDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.jrzp.support.comment.ui.replay.CommentReplyActivity
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.R.layout
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.GzNewsDetailsData
import com.bxkj.personal.data.NewsItemData
import com.bxkj.personal.data.RecommendNewsItemData
import com.bxkj.personal.data.RecommendNewsItemData.ItemDiffCallBack
import com.bxkj.personal.databinding.PersonalActivityGzNewsDetailsBinding
import com.bxkj.personal.ui.activity.searchnews.SearchNewsActivity
import com.bxkj.personal.ui.activity.typenews.TypeNewsActivity
import com.bxkj.personal.ui.activity.uploadavatar.UploadAvatarActivity
import com.bxkj.jrzp.support.comment.ui.CommentListAdapter
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.personal.ui.activity.web.WebNavigation
import com.bxkj.personal.weight.pagemoreoptions.PageMoreOptionsPopup
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.ViewSkeletonScreen
import me.wcy.htmltext.HtmlImageLoader
import me.wcy.htmltext.HtmlText

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.gznews
 * @Description: 资讯详情
 * <AUTHOR>
 * @date 2019/12/24
 * @version V1.0
 */
@Route(path = NewsDetailsNavigation.PATH)
class GzNewsDetailsActivity :
  BaseDBActivity<PersonalActivityGzNewsDetailsBinding, GzNewsDetailsViewModel>(),
  View.OnClickListener {
  companion object {

    const val EXTRA_NEWS_TYPE = "NEWS_TYPE"

    const val EXTRA_NEWS_ITEM = "NEWS_ITEM"

    const val TO_COMMENT_REPLY_CODE = 1
    const val TO_USER_HOME_CODE = 2

    const val RESULT_NEWS_STATE_CHANGE = Activity.RESULT_FIRST_USER + 1

    fun newIntent(context: Context, newsItem: NewsItemData, toComment: Boolean = false): Intent {
      return Intent(context, GzNewsDetailsActivity::class.java).apply {
        putExtra(EXTRA_NEWS_ITEM, newsItem)
        putExtra(NewsDetailsNavigation.EXTRA_JUMP_TO_COMMENT, toComment)
      }
    }

    fun newIntent(
      context: Context,
      newsId: Int,
      jumpToComment: Boolean? = false,
      newsType: String? = null
    ): Intent {
      return Intent(context, GzNewsDetailsActivity::class.java).apply {
        putExtra(NewsDetailsNavigation.EXTRA_NEWS_ID, newsId)
        putExtra(NewsDetailsNavigation.EXTRA_JUMP_TO_COMMENT, jumpToComment)
        putExtra(EXTRA_NEWS_TYPE, newsType)
      }
    }
  }

  private var mCommentDialog: InputDialog? = null
  private var skeletonScreen: ViewSkeletonScreen? = null
  private var mShareNewsPopup: PageMoreOptionsPopup? = null

  override fun getViewModelClass(): Class<GzNewsDetailsViewModel> =
    GzNewsDetailsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_gz_news_details

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeNewsDetailsChange()
    setupRecommendNewsListAdapter()
    setupCommentListAdapter()
    subscribeViewModelEvent()
    setupSkeletonScreen()
    subscribeShareBackAction()
    viewModel.start(intent)
  }

  private fun subscribeShareBackAction() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.ACTION_SHARE_BACK) {
            mShareNewsPopup?.dismiss()
          }
        })
  }

  override fun onClick(v: View?) {
    v?.let {
      when (it.id) {
        R.id.iv_left -> finish()
        R.id.iv_search -> startActivity(SearchNewsActivity.newIntent(this))
        R.id.iv_more -> viewModel.getShareInfo()
        R.id.tv_comment -> showCommentDialog()
        R.id.iv_share -> {
          mShareNewsPopup?.showBottom()
        }

        else -> {
          viewBinding.scrollContent.smoothScrollTo(0, viewBinding.llCommentLikeInfo.top)
        }
      }
    }
  }

  private fun setupSkeletonScreen() {
    skeletonScreen = Skeleton.bind(viewBinding.refreshContent)
      .load(R.layout.personal_activity_gz_news_details_skeleton)
      .color(R.color.common_f7f9fb)
      .duration(1500)
      .show()

    var loadRecommendNews = false

    viewBinding.webContent.webChromeClient = object : WebChromeClient() {
      override fun onProgressChanged(view: WebView?, newProgress: Int) {
        super.onProgressChanged(view, newProgress)
        if (newProgress >= 80 && !loadRecommendNews) {
          viewModel.getRecommendNewsBySubNewsTypeId()
          loadRecommendNews = true
        }
      }
    }
  }

  private fun subscribeNewsDetailsChange() {
    viewModel.newsDetails.observe(this, Observer {
      viewBinding.webContent.loadRichText(it.content)
      setupSharePopup(it)
    })
  }

  private fun setupSharePopup(newsDetails: GzNewsDetailsData) {
    val shareUrl = CommonApiConstants.SHARE_NEWS_URL_PREFIX + newsDetails.id
    mShareNewsPopup = PageMoreOptionsPopup(this, newsDetails.title, newsDetails.content, shareUrl)
      .apply {
        setOnItemClickListener(object :
          SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            if (position == PageMoreOptionsPopup.COLLECTION_POSITION) {
              viewModel.addOrRemoveCollection()
            }
          }
        })
      }
    mShareNewsPopup?.setCollection(newsDetails.isIsStow)
    newsDetails.addOnPropertyChangedCallback(object : Observable.OnPropertyChangedCallback() {
      override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
        if (propertyId == BR.isStow) {
          mShareNewsPopup?.setCollection(
            (sender as GzNewsDetailsData?)?.isIsStow
              ?: false
          )
        }
      }
    })
  }

  private fun subscribeViewModelEvent() {
    viewModel.addCommentSuccessEvent.observe(this, Observer {
      hideCommentDialog()
    })

    viewModel.toUploadAvatarCommand.observe(this, Observer {
      showToast(getString(R.string.please_upload_avatar))
      startActivity(UploadAvatarActivity.newIntent(this))
    })

    viewModel.toAuthorHomeCommand.observe(this, Observer {
      if (it.isInstitutions) {
        UserHomeNavigation.navigate(
          it.userID,
          it.getAuthType(AuthenticationType.INSTITUTIONS),
          it.dwID
        ).startForResult(this, TO_USER_HOME_CODE)
      } else {
        UserHomeNavigation.navigate(
          it.userID
        ).startForResult(this, TO_USER_HOME_CODE)
      }
    })

    viewModel.setupScrollListenerEvent.observe(this, Observer {
      setupContentScrollListener()
    })

    viewModel.dataLoadSuccess.observe(this, Observer {
      skeletonScreen?.hide()
      skeletonScreen = null
    })

    viewModel.scrollToCommentCommand.observe(this, Observer {
      if (it) {
        viewBinding.scrollContent.postDelayed({
          viewBinding.scrollContent.smoothScrollTo(
            0,
            viewBinding.clTopLayout.height + viewBinding.recyclerRecommendNews.height
          )
        }, 500)
      } else {
        viewBinding.scrollContent.smoothScrollTo(0, viewBinding.llCommentLikeInfo.top)
      }
    })

    viewModel.toTypeNewsCommand.observe(this, Observer {
      startActivity(TypeNewsActivity.newIntent(this, it.subType, it.subTypeName))
    })

    viewModel.showShareCommand.observe(this, Observer { shareInfo ->
      mShareNewsPopup?.let {
        it.setShareInfo(shareInfo.title, shareInfo.content)
        it.showBottom()
      }
    })

    viewModel.linkNewsItemStateChangeEvent.observe(this, Observer {
      it?.let {
        setResult(RESULT_NEWS_STATE_CHANGE, Intent().apply {
          putExtra(EXTRA_NEWS_ITEM, it)
        })
      }
    })

    viewModel.toAdUrlCommand.observe(this, Observer {
      WebNavigation.navigate(it).start()
    })
  }

  private fun hideCommentDialog() {
    mCommentDialog?.let {
      if (it.isShowing) {
        it.dismiss()
      }
    }
  }

  private fun setupContentScrollListener() {
    viewBinding.scrollContent.setOnScrollChangeListener { _: NestedScrollView?, _: Int, scrollY: Int, _: Int, _: Int ->
      if (scrollY < (viewBinding.clUserInfo.top + viewBinding.clUserInfo.height)) {
        viewBinding.clTitleBarUserInfo.visibility = View.GONE
        viewBinding.llTitleInfo.visibility = View.VISIBLE
      } else {
        viewBinding.clTitleBarUserInfo.visibility = View.VISIBLE
        viewBinding.llTitleInfo.visibility = View.GONE
      }
    }
  }

  private fun setupCommentListAdapter() {
    val commentListAdapter =
      object : CommentListAdapter(this, R.layout.personal_recycler_moment_comment_item) {
        override fun convert(
          holder: SuperViewHolder,
          viewType: Int,
          item: CommentItemData,
          position: Int
        ) {
          super.convert(holder, viewType, item, position)
          val tvContent = holder.findViewById<TextView>(R.id.tv_content)
          HtmlText.from(item.content)
            .setImageLoader(object : HtmlImageLoader {
              override fun getErrorDrawable(): Drawable? {
                return ContextCompat.getDrawable(
                  this@GzNewsDetailsActivity,
                  R.drawable.ic_no_photo_placeholder
                )
              }

              override fun getMaxWidth(): Int {
                return DensityUtils.dp2px(this@GzNewsDetailsActivity, 16f)
              }

              override fun loadImage(url: String?, callback: HtmlImageLoader.Callback?) {
                Glide.with(this@GzNewsDetailsActivity)
                  .asBitmap()
                  .load(url)
                  .into(object : SimpleTarget<Bitmap>() {
                    override fun onResourceReady(
                      resource: Bitmap,
                      transition: Transition<in Bitmap>?
                    ) {
                      callback?.onLoadComplete(resource)
                    }
                  })
              }

              override fun fitWidth(): Boolean {
                return true
              }

              override fun getDefaultDrawable(): Drawable? {
                return ContextCompat.getDrawable(
                  this@GzNewsDetailsActivity,
                  R.drawable.ic_no_photo_placeholder
                )
              }
            }).into(tvContent)
        }
      }.apply {
        setOnItemClickListener(object :
          SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            when (v.id) {
              R.id.iv_avatar, R.id.tv_nick_name -> {
                UserHomeNavigation.navigate(
                  data[position].uid,
                  AuthenticationType.QUERY_HIGHER_AUTH
                ).start()
              }

              R.id.tv_like -> {
                viewModel.commentLikeOrUnlike(data[position])
              }

              R.id.ll_reply -> {
                startActivityForResult(
                  CommentReplyActivity.newIntent(
                    this@GzNewsDetailsActivity,
                    PersonalApiConstants.NEWS_TYPE_NEWS,
                    viewModel.getNewsId(),
                    position,
                    data[position]
                  ), TO_COMMENT_REPLY_CODE
                )
              }

              else -> {
                showReplyDialog(position, data[position])
              }
            }
          }
        }, R.id.iv_avatar, R.id.tv_nick_name, R.id.tv_like, R.id.ll_reply)
        setOnItemLongClickListener(object : OnItemLongClickListener<CommentItemData> {
          override fun onLongClicked(v: View, position: Int, item: CommentItemData): Boolean {
            if (UserUtils.logged()) {
              if (item.uid == localUserId) {
                MenuPopup.Builder(this@GzNewsDetailsActivity)
                  .setData(resources.getStringArray(R.array.moment_details_delete_menu))
                  .setOnItemClickListener { _, menuPosition ->
                    if (menuPosition == 0) {
                      showDeleteCommentConfirmDialog(position, item)
                    }
                  }.build().show()
              }
            }
            return true
          }
        })
      }
    val recyclerCommentList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerCommentList.layoutManager = LinearLayoutManager(this)
    recyclerCommentList.isNestedScrollingEnabled = false
    viewModel.commentListViewModel.setAdapter(commentListAdapter)
  }

  private fun showDeleteCommentConfirmDialog(position: Int, item: CommentItemData) {
    ActionDialog.Builder()
      .setTitle(getString(R.string.tips))
      .setContent(getString(R.string.delete_comment_confirm_tips))
      .setOnConfirmClickListener {
        viewModel.deleteComment(position, item)
      }.build().show(supportFragmentManager)
  }

  private fun setupRecommendNewsListAdapter() {
    val recommendListAdapter =
      SimpleDiffListAdapter<RecommendNewsItemData>(
        layout.personal_recycler_recommend_news_item,
        ItemDiffCallBack()
      )
    recommendListAdapter.setOnItemClickListener(object :
      SuperItemClickListener {
      override fun onClick(v: View, position: Int) {
        recommendListAdapter.getData()?.let {
          startActivity(newIntent(this@GzNewsDetailsActivity, it[position].id))
          finish()
        }
      }
    })
    viewBinding.recyclerRecommendNews.layoutManager = LinearLayoutManager(this)
    viewBinding.recyclerRecommendNews.isNestedScrollingEnabled = false
    viewBinding.recyclerRecommendNews.adapter = recommendListAdapter
  }

  /**
   * 评论dialog
   */
  private fun showCommentDialog() {
    if (checkToLogin()) {
      viewModel.checkAvatarIsUpload {
        mCommentDialog = InputDialog(this).apply {
          onSendClickListener = object : InputDialog.OnSendClickListener {
            override fun onSendClicked(commentDialog: InputDialog, content: String) {
              viewModel.addComment(content)
            }
          }
        }
        mCommentDialog?.show()
      }
    }
  }

  /**
   * 回复dialog
   */
  private fun showReplyDialog(commentPosition: Int, commentItemData: CommentItemData) {
    if (checkToLogin()) {
      viewModel.checkAvatarIsUpload {
        mCommentDialog = InputDialog(this)
          .apply {
            hint =
              <EMAIL>(R.string.reply_format, commentItemData.nickName)
            onSendClickListener = object : InputDialog.OnSendClickListener {
              override fun onSendClicked(commentDialog: InputDialog, content: String) {
                viewModel.addReply(commentPosition, commentItemData, content)
              }
            }
          }
        mCommentDialog?.show()
      }
    }
  }

  override fun onPause() {
    super.onPause()
    viewBinding.webContent.onPause()
  }

  override fun onResume() {
    super.onResume()
    viewBinding.webContent.onResume()
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

  override fun onDestroy() {
    viewBinding.webContent.destroy()
    super.onDestroy()
  }
}