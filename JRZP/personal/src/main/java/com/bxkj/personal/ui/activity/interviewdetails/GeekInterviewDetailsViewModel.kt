package com.bxkj.personal.ui.activity.interviewdetails

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.support.chat.api.ChatRepo
import com.bxkj.jrzp.support.chat.data.InterviewInfoBean
import com.bxkj.personal.data.source.AccountRepo
import kotlinx.coroutines.launch
import javax.inject.Inject

class GeekInterviewDetailsViewModel @Inject constructor(
    private val _chatRepo: ChatRepo,
    private val _accountRepo: AccountRepo
) : BaseViewModel() {

    val interviewInfo = MutableLiveData<InterviewInfoBean>()

    val callHRCommand = MutableLiveData<VMEvent<String>>()

    val toChatCommand = MutableLiveData<VMEvent<InterviewInfoBean>>()

    val openLocalMapCommand = MutableLiveData<VMEvent<InterviewInfoBean>>()

    val interviewStateChangeEvent = MutableLiveData<VMEvent<Unit>>()

    private var _jobId: Int = 0
    private var _resumeId: Int = 0

    fun start(jobId: Int, resumeId: Int) {
        _jobId = jobId
        _resumeId = resumeId
        refreshInterviewInfo()
    }

    fun refuseInterview() {
        interviewInfo.value?.let {
            viewModelScope.launch {
                showLoading()
                _accountRepo.rejectInviteInterview(getSelfUserID(), it.jiid)
                    .handleResult({
                        interviewStateChangeEvent.value = VMEvent(Unit)
                        refreshInterviewInfo()
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    fun acceptInterview() {
        interviewInfo.value?.let {
            viewModelScope.launch {
                showLoading()
                _accountRepo.acceptInviteInterview(getSelfUserID(), it.jiid)
                    .handleResult({
                        interviewStateChangeEvent.value = VMEvent(Unit)
                        refreshInterviewInfo()
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    fun callHR() {
        interviewInfo.value?.let {
            if (it.jiPhone.isBlank()) {
                showToast("联系人电话为空")
                return
            }
            callHRCommand.value = VMEvent(it.jiPhone)
        }
    }

    fun openLocalMap() {
        interviewInfo.value?.let {
            openLocalMapCommand.value = VMEvent(it)
        }
    }

    private fun refreshInterviewInfo() {
        viewModelScope.launch {
            showLoading()
            _chatRepo.getInterviewInfo(_jobId, _resumeId, AppConstants.PERSONAL_TYPE)
                .handleResult({
                    interviewInfo.value = it
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }
}