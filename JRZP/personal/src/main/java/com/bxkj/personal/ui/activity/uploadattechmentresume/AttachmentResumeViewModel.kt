package com.bxkj.personal.ui.activity.uploadattechmentresume

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.personal.data.AttachmentResumeInfoBean
import com.bxkj.personal.data.source.MyResumeRepo
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

/**
 * Description:
 * Author:Sanjin
 * Date:2024/4/19
 **/
class AttachmentResumeViewModel
  @Inject
  constructor(
    private val myResumeRepo: MyResumeRepo,
  ) : BaseViewModel() {
    val showConfirmUploadDialog = MutableLiveData<VMEvent<String>>()
    val previewResumeCommand = MutableLiveData<VMEvent<String>>()
    val showCompleteResumeTipsCommand = MutableLiveData<VMEvent<Unit>>()
    val uploadSuccessCommand = MutableLiveData<VMEvent<String>>()

    val hasResume = MutableLiveData<Boolean>()

    private var _attachmentResumeInfoBean: AttachmentResumeInfoBean? = null

    fun start() {
      viewModelScope.launch {
        showLoading()
        myResumeRepo
          .getAttachmentResumeUrl()
          .handleResult({
            it?.let {
              _attachmentResumeInfoBean = it
              if (it.resid == "0") {
                showCompleteResumeTipsCommand.value = VMEvent(Unit)
                return@let
              }
              if (it.resfj.isBlank()) {
                setHasResume(false)
              } else {
                setHasResume(true)
              }
            } ?: setHasResume(false)
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    }

    fun uploadAttachmentResume(filePath: String) {
      _attachmentResumeInfoBean?.let {
        val file = File(filePath)
        viewModelScope.launch {
          showLoading()
          myResumeRepo
            .uploadAttachmentResume(getSelfUserID(), it.resid, file)
            .handleResult({
              uploadSuccessCommand.value = VMEvent(it?.msg.getOrDefault())
              start()
            }, {
              showToast(it.errMsg)
            }, {
              hideLoading()
            })
        }
      }
    }

    fun uploadPreCheck(filePath: String) {
      val fileType = filePath.substringAfterLast(".")
      when (fileType.lowercase()) {
        "doc", "docx", "pdf", "jpg", "png", "jpeg" -> {
          showConfirmUploadDialog.value = VMEvent(filePath)
        }

        else -> showToast("请选择指定类型文件")
      }
    }

    fun previewResume() {
      _attachmentResumeInfoBean?.let {
        previewResumeCommand.value = VMEvent(it.getFullResumeFileUrl())
      }
    }

    fun deleteAttachmentResume() {
      _attachmentResumeInfoBean?.let {
        viewModelScope.launch {
          showLoading()
          myResumeRepo
            .deleteAttachmentResume(getSelfUserID(), it.resid)
            .handleResult({
              showToast("删除成功")
              start()
            }, {
              showToast(it.errMsg)
            }, {
              hideLoading()
            })
        }
      }
    }

    private fun setHasResume(has: Boolean) {
      hasResume.value = has
    }
  }
