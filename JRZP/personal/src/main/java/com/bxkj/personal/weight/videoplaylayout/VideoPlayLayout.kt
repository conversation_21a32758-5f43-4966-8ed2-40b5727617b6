package com.bxkj.personal.weight.videoplaylayout

import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import cn.jzvd.JZDataSource
import cn.jzvd.Jzvd
import com.bxkj.common.imageloder.base.ImageLoader
import com.bxkj.common.util.HtmlUtils
import com.bxkj.common.util.JZMediaExo
import com.bxkj.common.util.imageloader.GlideLoadConfig
import com.bxkj.common.widget.MyVideoPlayer
import com.bxkj.common.widget.MyVideoPlayer.UI_STATUS_COMPLETE
import com.bxkj.common.widget.MyVideoPlayer.UI_STATUS_NORMAL
import com.bxkj.common.widget.MyVideoPlayer.UI_STATUS_PAUSE_CLEAR
import com.bxkj.common.widget.MyVideoPlayer.UI_STATUS_PAUSE_SHOW
import com.bxkj.common.widget.MyVideoPlayer.UI_STATUS_PLAYING_CLEAR
import com.bxkj.common.widget.MyVideoPlayer.UI_STATUS_PLAYING_SHOW
import com.bxkj.common.widget.MyVideoPlayer.UI_STATUS_PREPARING
import com.bxkj.common.widget.MyVideoPlayer.UI_STATUS_PREPARING_CHANGE_URL
import com.bxkj.common.widget.MyVideoPlayer.UI_STATUS_PREPARING_PLAYING
import com.bxkj.common.widget.MyVideoPlayer.UI_STATUS_STATUS_ERROR
import com.bxkj.personal.R
import com.bxkj.personal.data.VideoItemData

/**
 * @date: 2020/3/28
 * @version: V1.0
 */
class VideoPlayLayout @JvmOverloads constructor(
  context: Context,
  attributeSet: AttributeSet? = null,
  defStyleAttr: Int = 0
) : CoordinatorLayout(context, attributeSet, defStyleAttr) {

  private var tvPlayCount: TextView? = null
  private var tvSignUp: TextView? = null
  private var tvTag: TextView? = null
  private var tvReviewStatus: TextView? = null
  private var videoPlayer: MyVideoPlayer? = null
  private var llBottomTags: ConstraintLayout? = null

  private var showTitle: Boolean = false
  private var showTag: Boolean = false
  private var showOption: Boolean = false
  private var showPlayCount: Boolean = false
  private var showViewStatus: Boolean = false

  private var mOnOptionClickListener: OnOptionClickListener? = null

  init {
    View.inflate(getContext(), R.layout.personal_layout_video_player, this)

    tvPlayCount = findViewById(R.id.tv_play_count)
    tvSignUp = findViewById(R.id.tv_video_sign_up)
    tvTag = findViewById(R.id.tv_tag)
    tvReviewStatus = findViewById(R.id.tv_review_status)
    videoPlayer = findViewById(R.id.video_player)
    llBottomTags = findViewById(R.id.ll_bottom_tag)

    tvSignUp?.text =
      HtmlUtils.fromHtml(getContext().getString(R.string.home_news_recruitment_type_text))

    tvSignUp?.setOnClickListener {
      Jzvd.goOnPlayOnPause()
      mOnOptionClickListener?.onOptionClicked(it)
    }

    setupAttrs(attributeSet)
  }

  private fun setupAttrs(attributeSet: AttributeSet?) {
    attributeSet?.let {
      val typedArray: TypedArray =
        context.obtainStyledAttributes(attributeSet, R.styleable.VideoPlayLayout)
      setShowTitle(typedArray.getBoolean(R.styleable.VideoPlayLayout_showTitle, false))
      setShowOption(typedArray.getBoolean(R.styleable.VideoPlayLayout_showOption, false))
      setShowTag(typedArray.getBoolean(R.styleable.VideoPlayLayout_showTag, false))
      setPlayCount(typedArray.getInt(R.styleable.VideoPlayLayout_playCount, 0))
      setShowPlayCount(typedArray.getBoolean(R.styleable.VideoPlayLayout_showPlayCount, false))
      setReviewStatus(typedArray.getInteger(R.styleable.VideoPlayLayout_reviewStatus, 1))
      typedArray.recycle()
    }

    initView()
  }

  private fun initView() {
    videoPlayer?.posterImageView?.scaleType = ImageView.ScaleType.FIT_CENTER
    videoPlayer?.setOnUiStatusChangeListener { status ->
      when (status) {
        UI_STATUS_NORMAL -> {
          setTagsVisibility(View.VISIBLE, View.VISIBLE, View.VISIBLE, View.VISIBLE)
        }

        UI_STATUS_COMPLETE -> {
          setTagsVisibility(View.VISIBLE, View.VISIBLE, View.VISIBLE, View.VISIBLE)
        }

        UI_STATUS_STATUS_ERROR -> {
          setTagsVisibility(View.VISIBLE, View.VISIBLE, View.VISIBLE, View.VISIBLE)
        }

        UI_STATUS_PAUSE_CLEAR -> {
          setTagsVisibility(View.GONE, View.GONE, View.GONE, View.GONE)
        }

        UI_STATUS_PAUSE_SHOW -> {
          setTagsVisibility(View.GONE, View.GONE, View.GONE, View.GONE)
        }

        UI_STATUS_PLAYING_CLEAR -> {
          setTagsVisibility(View.GONE, View.GONE, View.GONE, View.GONE)
        }

        UI_STATUS_PLAYING_SHOW -> {
          setTagsVisibility(View.GONE, View.GONE, View.GONE, View.GONE)
        }

        UI_STATUS_PREPARING -> {
          setTagsVisibility(View.GONE, View.GONE, View.GONE, View.GONE)
        }

        UI_STATUS_PREPARING_CHANGE_URL -> {
          setTagsVisibility(View.GONE, View.GONE, View.GONE, View.GONE)
        }

        UI_STATUS_PREPARING_PLAYING -> {
          setTagsVisibility(View.GONE, View.GONE, View.GONE, View.GONE)
        }
      }
    }
  }

  private fun setTagsVisibility(playCount: Int, reviewStatus: Int, signUp: Int, tag: Int) {
    if (showPlayCount) {
      tvPlayCount?.visibility = playCount
    }
    if (showViewStatus) {
      tvReviewStatus?.visibility = reviewStatus
    }
    if (showOption) {
      tvSignUp?.visibility = signUp
    }
    if (showTag) {
      tvTag?.visibility = tag
    }
  }

  fun setReviewStatus(status: Int) {
    when (status) {
      0 -> {
        showViewStatus = true
        tvReviewStatus?.visibility = View.VISIBLE
        tvReviewStatus?.text = "待审核"
        showPlayCount = false
        tvPlayCount?.visibility = View.INVISIBLE
      }

      1 -> {
        showViewStatus = false
        tvReviewStatus?.visibility = View.GONE
        if (showPlayCount) {
          tvPlayCount?.visibility = View.VISIBLE
        }
      }

      else -> {
        showViewStatus = true
        tvReviewStatus?.visibility = View.VISIBLE
        tvReviewStatus?.text = "审核失败"
        showPlayCount = false
        tvPlayCount?.visibility = View.INVISIBLE
      }
    }
  }

  fun setVideoInfo(videoInfo: VideoItemData) {
    ImageLoader.loadImage(
      context,
      GlideLoadConfig.Builder().url(videoInfo.pic).into(videoPlayer?.posterImageView).build()
    )
    videoPlayer?.setUp(
      JZDataSource(videoInfo.video, if (showTitle) videoInfo.title else ""),
      Jzvd.SCREEN_NORMAL,
      JZMediaExo::class.java
    )
    tvPlayCount?.text = context.getString(R.string.video_list_play_count_format, videoInfo.view)
    tvTag?.text = videoInfo.typeName
  }

  fun setCover(cover: String?) {
    ImageLoader.loadImage(
      context,
      GlideLoadConfig.Builder().url(cover).into(videoPlayer?.posterImageView).build()
    )
  }

  fun setVideoUrlAndTitle(videoUrl: String? = "", title: String? = "") {
    videoPlayer?.setUp(
      JZDataSource(videoUrl, if (showTitle) title else ""),
      Jzvd.SCREEN_NORMAL,
      JZMediaExo::class.java
    )
  }

  fun setShowTitle(show: Boolean) {
    showTitle = show
    if (!show) {
      videoPlayer?.titleTextView?.text = ""
    }
  }

  fun setPlayCount(count: Int) {
    tvPlayCount?.text = context.getString(R.string.video_list_play_count_format, count)
  }

  fun setShowTag(show: Boolean) {
    showTag = show
    if (show) {
      tvTag?.visibility = View.VISIBLE
    } else {
      tvTag?.visibility = View.GONE
    }
  }

  fun setShowPlayCount(show: Boolean) {
    showPlayCount = show
    if (show) {
      tvPlayCount?.visibility = View.VISIBLE
    } else {
      tvPlayCount?.visibility = View.GONE
    }
  }

  fun setShowOption(show: Boolean) {
    showOption = show
    if (show) {
      tvSignUp?.visibility = View.VISIBLE
    } else {
      tvSignUp?.visibility = View.GONE
    }
  }

  fun getVideoPlayer(): MyVideoPlayer? {
    return videoPlayer
  }

  fun setOnOptionClickListener(onOptionClickListener: OnOptionClickListener) {
    mOnOptionClickListener = onOptionClickListener
  }

  interface OnOptionClickListener {
    fun onOptionClicked(view: View)
  }
}