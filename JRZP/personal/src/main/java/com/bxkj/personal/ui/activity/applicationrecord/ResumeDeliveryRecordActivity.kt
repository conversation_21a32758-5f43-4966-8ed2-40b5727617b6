package com.bxkj.personal.ui.activity.applicationrecord

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.therouter.router.Route
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.kotlin.attachIndicator
import com.bxkj.personal.R
import com.bxkj.personal.databinding.CActivityResumeDeliveryRecordBinding
import com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord.DeliveryRecordFragment
import com.bxkj.personal.ui.fragment.applyrecordlist.ResumeDeliveryRecordFragment
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 * Description: 简历投递记录
 * Author:Sanjin
 * Date:2024/4/20
 **/
@Route(path = ResumeDeliveryRecordNavigation.PATH)
class ResumeDeliveryRecordActivity :
  BaseDBActivity<CActivityResumeDeliveryRecordBinding, BaseViewModel>() {
  override fun getViewModelClass(): Class<BaseViewModel> =
    BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.c_activity_resume_delivery_record

  override fun initPage(savedInstanceState: Bundle?) {
    setupContent()
  }

  private fun setupContent() {
    viewBinding.vpContent.adapter = object : FragmentStateAdapter(this) {
      override fun getItemCount(): Int = 3

      override fun createFragment(position: Int): Fragment {
        if (position == 0) {
          return ResumeDeliveryRecordFragment.newInstance(ResumeDeliveryRecordFragment.TYPE_FULL_TIME)
        } else if (position == 1) {
          return ResumeDeliveryRecordFragment.newInstance(ResumeDeliveryRecordFragment.TYPE_PART_TIME)
        } else {
          return DeliveryRecordFragment.newInstance(1)
        }
      }
    }

    viewBinding.indicatorType.navigator = CommonNavigator(this).apply {
      isAdjustMode = true
      adapter = MagicIndicatorAdapter(arrayOf("全职投递", "兼职投递", "校招投递")).apply {
        setOnTabClickListener(object : OnTabClickListener {
          override fun onTabClicked(v: View, index: Int) {
            viewBinding.vpContent.currentItem = index
          }
        })
      }
    }

    viewBinding.vpContent.attachIndicator(viewBinding.indicatorType)
  }

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, ResumeDeliveryRecordActivity::class.java)
    }
  }
}