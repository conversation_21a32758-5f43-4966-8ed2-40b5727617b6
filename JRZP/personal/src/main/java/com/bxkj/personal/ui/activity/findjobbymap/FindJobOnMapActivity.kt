package com.bxkj.personal.ui.activity.findjobbymap

import android.content.Context
import android.content.Intent
import android.graphics.Point
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.View.OnClickListener
import android.view.inputmethod.EditorInfo
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.TextView.OnEditorActionListener
import androidx.activity.addCallback
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.baidu.location.BDAbstractLocationListener
import com.baidu.location.BDLocation
import com.baidu.location.LocationClient
import com.baidu.location.LocationClientOption
import com.baidu.location.LocationClientOption.LocationMode.Hight_Accuracy
import com.baidu.mapapi.map.BaiduMap
import com.baidu.mapapi.map.BaiduMap.OnMapStatusChangeListener
import com.baidu.mapapi.map.MapStatus
import com.baidu.mapapi.map.MapStatusUpdateFactory
import com.baidu.mapapi.model.LatLng
import com.baidu.mapapi.search.core.SearchResult.ERRORNO.NO_ERROR
import com.baidu.mapapi.search.geocode.GeoCodeResult
import com.baidu.mapapi.search.geocode.GeoCoder
import com.baidu.mapapi.search.geocode.OnGetGeoCoderResultListener
import com.baidu.mapapi.search.geocode.ReverseGeoCodeOption
import com.baidu.mapapi.search.geocode.ReverseGeoCodeResult
import com.bxkj.common.adapter.paging3.SimplePageDataAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.PermissionUtils.OnRequestResultListener
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.handleState
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.recyclerutil.LoadStateFooterAdapter
import com.bxkj.common.widget.dropdown.DropDownPopup
import com.bxkj.common.widget.filterpopup.FilterGroupTitle
import com.bxkj.common.widget.filterpopup.FilterOptionData
import com.bxkj.common.widget.filterpopup.FilterOptionsGroup
import com.bxkj.common.widget.filterpopup.FilterUtils
import com.bxkj.common.widget.filterpopup.FilterView
import com.bxkj.common.widget.pagestatuslayout.v2.EmptyPageState
import com.bxkj.common.widget.pagestatuslayout.v2.ErrorPageState
import com.bxkj.jrzp.user.data.JobData
import com.bxkj.personal.R
import com.bxkj.personal.databinding.CActivityFindJobOnMapBinding
import com.bxkj.personal.databinding.CRecyclerJobItemBinding
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant
import com.bxkj.personal.weight.filterjobclasspopup.FilterJobClassPopup
import com.bxkj.personal.weight.filterjobclasspopup.FilterJobTypeView
import com.elvishew.xlog.XLog
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.hjq.permissions.Permission
import com.sanjindev.pagestatelayout.OnStateSetUpListener
import com.therouter.router.Route
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * Description: 地图找工作
 * Author:Sanjin
 * Date:2024/10/8
 **/
@Route(path = FindJobByMapNavigation.PATH)
class FindJobOnMapActivity : BaseDBActivity<CActivityFindJobOnMapBinding, FindJobOnMapViewModel>(),
  OnClickListener {

  private var jobListAdapter: SimplePageDataAdapter<JobData, CRecyclerJobItemBinding>? = null

  private var bottomSheetBehavior: BottomSheetBehavior<LinearLayout>? = null

  private var baiduMap: BaiduMap? = null

  private var filterDropdownPopup: DropDownPopup? = null
  private var filterJobTypeView: FilterJobTypeView? = null
  private var filterSalaryRangeView: FilterView? = null
  private var filterWorkExpView: FilterView? = null

  override fun getViewModelClass(): Class<FindJobOnMapViewModel> = FindJobOnMapViewModel::class.java

  override fun getLayoutId(): Int = R.layout.c_activity_find_job_on_map

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    statusBarManager.titleBar(viewBinding.constraintSearchBar).statusBarDarkFont(true, 0.4f).init()

    subscribeViewModelEvent()

    viewBinding.map.post {
      initBottomSheetBehavior()
    }

    initMapView()

    initJobList()

    initFilterDropDownPopup()

    handleSearch()

    onBackPressedDispatcher.addCallback {
      if (filterDropdownPopup?.isShowing == true) {
        filterDropdownPopup?.close()
      } else {
        if (bottomSheetBehavior?.state == BottomSheetBehavior.STATE_EXPANDED) {
          bottomSheetBehavior?.state = BottomSheetBehavior.STATE_COLLAPSED
        } else {
          finish()
        }
      }
    }
  }

  override fun onClick(v: View?) {
    v?.let {
      when (it.id) {
        R.id.iv_back -> {
          if (filterDropdownPopup?.isShowing == true) {
            filterDropdownPopup?.close()
          } else {
            finish()
          }
        }

        R.id.tv_expect_industry -> {
          filterDropdownPopup?.showItemAsDropDown(0)
        }

        R.id.tv_salary_treatment -> {
          filterDropdownPopup?.showItemAsDropDown(1)
        }

        R.id.tv_work_exp -> {
          filterDropdownPopup?.showItemAsDropDown(2)
        }

        R.id.tv_work_nature -> {
          filterDropdownPopup?.showItemAsDropDown(3)
        }

        else -> {
        }
      }
    }
  }

  private fun handleSearch() {
    viewBinding.etSearchJob.setOnEditorActionListener(OnEditorActionListener { textView: TextView?, i: Int, keyEvent: KeyEvent? ->
      if (i == EditorInfo.IME_ACTION_SEARCH) {
        if (CheckUtils.isNullOrEmpty(viewBinding.etSearchJob.getText().toString())) {
          showToast(getString(R.string.common_search_content_not_be_null))
        } else {
          viewBinding.etSearchJob.clearFocus()
          viewModel.setupSearchKeyword(viewBinding.etSearchJob.getText().toString())
          jobListAdapter?.refresh()
        }
        return@OnEditorActionListener true
      }
      false
    })

    viewBinding.etSearchJob.setOnCustomIconClickListener {
      viewBinding.etSearchJob.clearFocus()
      viewModel.setupSearchKeyword("")
      jobListAdapter?.refresh()
    }
  }

  private fun initFilterDropDownPopup() {
    filterDropdownPopup = DropDownPopup(this, viewBinding.clFilterBar).apply {
      setOnItemExpandStatusChangeListener { index, opened ->
        viewBinding.clFilterBar.getChildAt(index).isSelected = opened
      }
      addContentViews(
        getFilterJobTypeView(),
        getFilterSalaryRangeView(),
        getFilterWorkExpView(),
        getFilterWorkNatureView()
      )
    }
  }

  private fun getFilterWorkNatureView(): View? {
    return FilterView.Builder(this)
      .setHeight(DensityUtils.dp2px(this, 400f))
      .setItemClickedDismiss(true)
      .setBottomBarVisible(View.GONE)
      .build().apply {
        setOnFilterConfirmListener { positionHolderMap: Map<Int?, Int?> ->
          filterDropdownPopup?.close()
          val workNatureId =
            (getOptionsGroupData(0)?.get(positionHolderMap[FilterOptionData.WORK_NATURE].getOrDefault()) as FilterOptionData).id
          viewModel.setupWorkNatureId(
            if (workNatureId != 0) workNatureId + 6 else workNatureId
          )
          jobListAdapter?.refresh()
        }
        addGroupTitle(
          FilterGroupTitle(getString(R.string.personal_working_nature))
        )
        addGroupItems(
          FilterOptionsGroup(
            FilterOptionData.WORK_NATURE,
            FilterUtils.parseFilterOptions(
              *resources.getStringArray(R.array.personal_search_jobs_work_nature)
            )
          )
        )
      };
  }

  private fun getFilterWorkExpView(): View? {
    filterWorkExpView = FilterView.Builder(this)
      .setHeight(DensityUtils.dp2px(this, 400f))
      .setItemClickedDismiss(true)
      .setBottomBarVisible(View.GONE)
      .setOnFilterConfirmListener { positionHolderMap: Map<Int?, Int?> ->
        filterDropdownPopup?.close()
        filterWorkExpView?.getOptionsGroupData(0)
          ?.get(positionHolderMap[FilterOptionData.WORKING_EXP].getOrDefault())?.let {
            viewModel.setupWorkExpId((it as FilterOptionData).id)
          }
        jobListAdapter?.refresh()
      }
      .build();
    return filterWorkExpView
  }

  private fun getFilterSalaryRangeView(): FilterView? {
    filterSalaryRangeView = FilterView.Builder(this)
      .setHeight(DensityUtils.dp2px(this, 400f))
      .setItemClickedDismiss(true)
      .setBottomBarVisible(View.GONE)
      .setOnFilterConfirmListener { positionHolderMap: Map<Int?, Int?> ->
        filterDropdownPopup?.close()
        filterSalaryRangeView?.getOptionsGroupData(0)
          ?.get(positionHolderMap[FilterOptionData.SALARY].getOrDefault())?.let {
            viewModel.setupSalaryRangeId((it as FilterOptionData).id)
          }
        jobListAdapter?.refresh()
      }
      .build();
    return filterSalaryRangeView
  }

  private fun getFilterJobTypeView(): FilterJobTypeView? {
    filterJobTypeView = FilterJobTypeView(this).apply {
      setOnItemClickListener(object : FilterJobClassPopup.OnItemClickListener {
        override fun onFirstClassItemClicked(position: Int) {
          filterJobTypeView?.firstTypeData?.get(position)?.let {
            viewModel.setJobListFirstType(it.id)
          }
        }

        override fun onSecondClassClicked(position: Int) {
          filterDropdownPopup?.close()
          filterJobTypeView?.secondTypeData?.get(position)?.let {
            viewModel.setJobListSecondType(it.id)
            jobListAdapter?.refresh()
          }
        }
      })
    }
    return filterJobTypeView
  }

  private fun subscribeViewModelEvent() {
    viewModel.toCreateResumeCommand.observe(this, EventObserver {
      MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME).start()
    })

    viewModel.jobFirstTypeList.observe(this) {
      filterJobTypeView?.firstTypeData = it
    }

    viewModel.jobSecondTypeList.observe(this) {
      filterJobTypeView?.secondTypeData = it
    }

    viewModel.salaryRangeOptions.observe(this) {
      filterSalaryRangeView?.apply {
        addGroupTitle(FilterGroupTitle("月薪范围"))
        addGroupItems(
          FilterOptionsGroup(FilterOptionData.SALARY, it)
        )
      }
    }

    viewModel.workExpOptions.observe(this) {
      filterWorkExpView?.apply {
        addGroupTitle(FilterGroupTitle("工作经验"))
        addGroupItems(
          FilterOptionsGroup(FilterOptionData.WORKING_EXP, it)
        )
      }
    }
  }

  private fun initJobList() {
    jobListAdapter = SimplePageDataAdapter<JobData, CRecyclerJobItemBinding>(
      R.layout.c_recycler_job_item,
      JobData.DiffCallback()
    ).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          getItemBean(position)?.let {
            if (v.id == R.id.tv_application) {
              viewModel.sendResumePreCheck(it)
            } else {
              startActivity(JobDetailsActivityV2.newIntent(this@FindJobOnMapActivity, it.id))
            }
          }
        }
      }, R.id.tv_application)
      addLoadStateListener { loadStateFlow ->
        loadStateFlow.handleState({
          viewBinding.progressSearch.visibility = View.INVISIBLE
          viewBinding.pslContent.hidden()
        }, {
          viewBinding.pslContent.show(
            EmptyPageState::class.java,
            object : OnStateSetUpListener<EmptyPageState> {
              override fun onStateSetUp(pageState: EmptyPageState) {
                pageState.setContent("未查到数据")
              }
            })
        }, {
          viewBinding.pslContent.show(
            ErrorPageState::class.java,
            object : OnStateSetUpListener<ErrorPageState> {
              override fun onStateSetUp(pageState: ErrorPageState) {
                pageState.setContent(it.message.getOrDefault())
                pageState.setNextOptionClickListener { refresh() }
              }
            })
        })
      }
    }

    viewBinding.recyclerJobList.apply {
      layoutManager = LinearLayoutManager(this@FindJobOnMapActivity)
      addItemDecoration(
        LineItemDecoration.Builder()
          .divider(getResDrawable(R.drawable.divider_f4f4f4_6))
          .drawFoot(true)
          .build()
      )
      adapter = jobListAdapter?.withLoadStateFooter(
        LoadStateFooterAdapter {
          jobListAdapter?.refresh()
        }
      )
    }

    lifecycleScope.launch {
      viewModel.jobListFlow.collectLatest {
        jobListAdapter?.submitData(it)
      }
    }
  }

  private fun initBottomSheetBehavior() {
    bottomSheetBehavior = BottomSheetBehavior.from(viewBinding.llBottomSheet).apply {
      setPeekHeight(viewBinding.coordinatorContent.height - viewBinding.llMapContent.bottom)
    }
    //计算出底部layout的高度
    val layoutParams =
      viewBinding.llBottomSheet.layoutParams
    layoutParams.height =
      viewBinding.coordinatorContent.height - viewBinding.constraintSearchBar.height - viewBinding.clFilterBar.height
    viewBinding.llBottomSheet.setLayoutParams(layoutParams)
  }

  private fun initMapView() {
    val geoCoder = getGeoCoder();

    viewBinding.map.apply {
      showScaleControl(false)
      showZoomControls(false)
    }

    baiduMap = viewBinding.map.map
    baiduMap?.apply {
      setMaxAndMinZoomLevel(15f, 4f)
      uiSettings.isOverlookingGesturesEnabled = false
      setOnMapStatusChangeListener(object : OnMapStatusChangeListener {
        override fun onMapStatusChangeStart(mapStatus: MapStatus) {
          viewBinding.tvJobCount.setText(R.string.map_job_searching)
          changeIvMapCenterStatus(true)
        }

        override fun onMapStatusChangeStart(mapStatus: MapStatus, reason: Int) {
          if (reason == OnMapStatusChangeListener.REASON_GESTURE) {
            checkToLogin()
          }
        }

        override fun onMapStatusChange(mapStatus: MapStatus) {
        }

        override fun onMapStatusChangeFinish(mapStatus: MapStatus) {
          XLog.d("地图状态变化1")
          if (baiduMap == null) {
            return
          }
          changeIvMapCenterStatus(false)
          //地图左下角
          val llPoint = Point()
          llPoint.x = 0
          llPoint.y = viewBinding.map.getBottom()
          //地图右上角
          val trPoint = Point()
          trPoint.x = viewBinding.map.getRight()
          trPoint.y = 0
          if (baiduMap != null && projection != null) {
            //将地图左下坐标转换成经纬度
            val llLatlng: LatLng = projection.fromScreenLocation(llPoint)
            //将地图右上坐标转换成经纬度
            val trLatlng: LatLng = projection.fromScreenLocation(trPoint)

            geoCoder.reverseGeoCode(ReverseGeoCodeOption().location(mapStatus.target))
            viewModel.updateLocation(mapStatus.target, llLatlng, trLatlng)
          }
          viewBinding.progressSearch.visibility = View.VISIBLE
          jobListAdapter?.refresh()
        }
      })
      checkLocatePermission()
    }
  }

  private fun getGeoCoder(): GeoCoder {
    return GeoCoder.newInstance().apply {
      setOnGetGeoCodeResultListener(object : OnGetGeoCoderResultListener {
        override fun onGetGeoCodeResult(geoCodeResult: GeoCodeResult) {
        }

        override fun onGetReverseGeoCodeResult(reverseGeoCodeResult: ReverseGeoCodeResult?) {
          if (reverseGeoCodeResult == null
            || reverseGeoCodeResult.error != NO_ERROR
          ) {
            showToast(getString(R.string.map_job_get_location_failed))
            return
          }
          val addressDetails =
            reverseGeoCodeResult.addressDetail
          viewBinding.tvAddressName.setText(
            String.format(
              "%s%s%s", addressDetails.city, addressDetails.district,
              addressDetails.street
            )
          )
        }
      })
    }
  }

  private fun checkLocatePermission() {
    PermissionUtils.requestPermission(
      this, "无定位权限", "是否开启定位权限以便搜寻附近职位",
      object : OnRequestResultListener {
        override fun onRequestSuccess() {
          locateCurrentLocation()
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          showToast("定位失败")
        }
      }, Permission.ACCESS_FINE_LOCATION
    )
  }

  private fun locateCurrentLocation() {
    LocationClient(applicationContext).apply {
      registerLocationListener(object : BDAbstractLocationListener() {
        override fun onReceiveLocation(bdLocation: BDLocation?) {
          if (bdLocation == null || bdLocation.locType == BDLocation.TypeCriteriaException) {
            showToast(getString(R.string.map_job_locate_error_msg))
          } else {
            baiduMap!!.animateMapStatus(
              MapStatusUpdateFactory.newLatLngZoom(
                LatLng(bdLocation.latitude, bdLocation.longitude), 15f
              )
            )
          }
          stop()
        }
      })

      val locationClientOption = LocationClientOption()
      locationClientOption.locationMode = Hight_Accuracy
      locationClientOption.setCoorType("bd09ll")
      locOption = locationClientOption
      start()
    }
  }

  private fun changeIvMapCenterStatus(searching: Boolean) {
    viewBinding.ivMapCenter.animate()
      .translationY(
        DensityUtils.dp2px(
          this,
          (if (searching) -3 else 3).toFloat()
        ).toFloat()
      )
      .scaleX(if (searching) 0.9f else 1f)
      .scaleY(if (searching) 0.9f else 1f)
      .start()
  }

  companion object {
    fun newIntent(context: Context): Intent {
      return Intent(context, FindJobOnMapActivity::class.java)
    }
  }
}