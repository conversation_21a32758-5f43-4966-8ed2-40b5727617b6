package com.bxkj.personal.data;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/3/13
 * @version: V1.0
 */
public class SearchHotKeyItemData implements Parcelable {

    /**
     * id : 0
     * userID : 0
     * type : 0
     * key : 广西柳州市柳北区市场监督管理局招聘
     * value : 0.0
     */

    private int id;
    private int userID;
    private int type;
    private String key;
    private double value;

    protected SearchHotKeyItemData(Parcel in) {
        id = in.readInt();
        userID = in.readInt();
        type = in.readInt();
        key = in.readString();
        value = in.readDouble();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeInt(userID);
        dest.writeInt(type);
        dest.writeString(key);
        dest.writeDouble(value);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SearchHotKeyItemData> CREATOR = new Creator<SearchHotKeyItemData>() {
        @Override
        public SearchHotKeyItemData createFromParcel(Parcel in) {
            return new SearchHotKeyItemData(in);
        }

        @Override
        public SearchHotKeyItemData[] newArray(int size) {
            return new SearchHotKeyItemData[size];
        }
    };

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUserID() {
        return userID;
    }

    public void setUserID(int userID) {
        this.userID = userID;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public double getValue() {
        return value;
    }

    public void setValue(double value) {
        this.value = value;
    }
}
