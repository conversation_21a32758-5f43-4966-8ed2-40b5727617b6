package com.bxkj.personal.ui.activity.studynewsdetails

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.Observable
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.OnItemLongClickListener
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.InputDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.jrzp.support.comment.ui.replay.CommentReplyActivity
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.R.layout
import com.bxkj.personal.api.PersonalApiConstants
import com.bxkj.personal.data.RecommendNewsItemData
import com.bxkj.personal.data.RecommendNewsItemData.ItemDiffCallBack
import com.bxkj.personal.data.StudyNewsDetailsData
import com.bxkj.personal.databinding.PersonalActivityStudyNewsDetailsBinding
import com.bxkj.personal.ui.activity.gznews.GzNewsDetailsActivity
import com.bxkj.personal.ui.activity.searchnews.SearchNewsActivity
import com.bxkj.personal.ui.activity.typenews.TypeNewsActivity
import com.bxkj.personal.ui.activity.uploadavatar.UploadAvatarActivity
import com.bxkj.jrzp.support.comment.ui.CommentListAdapter
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.personal.ui.activity.web.WebNavigation
import com.bxkj.personal.weight.pagemoreoptions.PageMoreOptionsPopup
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.ViewSkeletonScreen
import me.wcy.htmltext.HtmlImageLoader
import me.wcy.htmltext.HtmlText

/**
 * @Project: gzgk
 * @Description:
 * @author:45457 公考资料详情
 * @date: 2020/4/2
 * @version: V1.0
 */
class StudyNewsDetailsActivity :
  BaseDBActivity<PersonalActivityStudyNewsDetailsBinding, StudyNewsDetailsViewModel>(),
  View.OnClickListener {

  companion object {

    const val EXTRA_NEWS_ID = "NEWS_ID"
    const val EXTRA_TO_COMMENT = "TO_COMMENT"

    fun newIntent(context: Context, studyNewsId: Int, toComment: Boolean = false): Intent {
      return Intent(context, StudyNewsDetailsActivity::class.java)
        .apply {
          putExtra(EXTRA_NEWS_ID, studyNewsId)
          putExtra(EXTRA_TO_COMMENT, toComment)
        }
    }
  }

  private var skeletonScreen: ViewSkeletonScreen? = null
  private var mCommentDialog: InputDialog? = null
  private var mPageMoreOptionsPopup: PageMoreOptionsPopup? = null

  override fun getViewModelClass(): Class<StudyNewsDetailsViewModel> =
    StudyNewsDetailsViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_activity_study_news_details

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()
    setupCommentListAdapter()
    setupRecommendNewsListAdapter()
    setupSkeletonScreen()
    viewModel.start(
      intent.getIntExtra(EXTRA_NEWS_ID, CommonApiConstants.NO_ID),
      intent.getBooleanExtra(EXTRA_TO_COMMENT, false)
    )
  }

  override fun onClick(v: View?) {
    v?.let {
      when (it.id) {
        R.id.iv_left -> finish()
        R.id.tv_news_type -> {
          viewModel.toTypeNewsPage()
        }
        R.id.iv_more, R.id.iv_share -> {
          viewModel.getShareInfo()
        }
        R.id.iv_search -> startActivity(SearchNewsActivity.newIntent(this))
        R.id.tv_comment -> showCommentDialog()
        else -> {
          viewBinding.scrollContent.smoothScrollTo(0, viewBinding.llCommentLikeInfo.top)
        }
      }
    }
  }

  /**
   * 评论dialog
   */
  private fun showCommentDialog() {
    if (checkToLogin()) {
      viewModel.checkAvatarIsUpload {
        mCommentDialog = InputDialog(this).apply {
          onSendClickListener = object : InputDialog.OnSendClickListener {
            override fun onSendClicked(commentDialog: InputDialog, content: String) {
              viewModel.addComment(content)
            }
          }
        }
        mCommentDialog?.show()
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.newsDetails.observe(this, Observer {
      setupPageMoreOptionsPopup(it)
      viewBinding.webContent.loadRichText(it.content)
    })

    viewModel.addCommentSuccessEvent.observe(this, Observer {
      hideCommentDialog()
    })

    viewModel.toUploadAvatarCommand.observe(this, Observer {
      showToast(getString(R.string.please_upload_avatar))
      startActivity(UploadAvatarActivity.newIntent(this))
    })

    viewModel.toAuthorHomeCommand.observe(this, Observer {
      UserHomeNavigation.navigate(it.userID, AuthenticationType.INSTITUTIONS, it.dwID).start()
//      startActivityForResult(
//        GzUserHomeActivity.newIntent(this, it.userID, it.dwID),
//        GzNewsDetailsActivity.TO_USER_HOME_CODE
//      )
    })

    viewModel.dataLoadSuccess.observe(this, Observer {
      skeletonScreen?.hide()
      skeletonScreen = null
    })

    viewModel.scrollToCommentCommand.observe(this, Observer {
      if (it) {
        viewBinding.scrollContent.postDelayed({
          viewBinding.scrollContent.smoothScrollTo(
            0,
            viewBinding.clTopLayout.height + viewBinding.recyclerRecommendNews.height
          )
        }, 500)
      } else {
        viewBinding.scrollContent.smoothScrollTo(0, viewBinding.llCommentLikeInfo.top)
      }
    })

    viewModel.toTypeNewsCommand.observe(this, Observer {
      startActivity(TypeNewsActivity.newIntent(this, it.typeId, it.typeName, true))
    })

    viewModel.showShareCommand.observe(this, Observer { shareInfo ->
      mPageMoreOptionsPopup?.let {
        it.setShareInfo(shareInfo.title, shareInfo.content)
        it.showBottom()
      }
    })

    viewModel.toAdUrlCommand.observe(this, Observer {
      WebNavigation.navigate(it).start()
    })
  }

  private fun setupPageMoreOptionsPopup(it: StudyNewsDetailsData) {
    mPageMoreOptionsPopup = PageMoreOptionsPopup(
      this,
      it.title,
      it.content,
      "http://m.jrzp.com/gongkaoziliao/view.aspx?newsId=${it.id}"
    ).apply {
      setOnItemClickListener(object :
        SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          if (position == PageMoreOptionsPopup.COLLECTION_POSITION) {
            viewModel.addOrRemoveCollection()
          }
        }
      })
    }
    mPageMoreOptionsPopup?.setCollection(it.isStow)
    it.addOnPropertyChangedCallback(object : Observable.OnPropertyChangedCallback() {
      override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
        if (propertyId == BR.stow) {
          mPageMoreOptionsPopup?.setCollection((sender as StudyNewsDetailsData).isStow)
        }
      }
    })
  }

  private fun hideCommentDialog() {
    mCommentDialog?.let {
      if (it.isShowing) {
        it.dismiss()
      }
    }
  }

  private fun setupSkeletonScreen() {
    skeletonScreen = Skeleton.bind(viewBinding.refreshContent)
      .load(R.layout.personal_activity_gz_news_details_skeleton)
      .color(R.color.common_f7f9fb)
      .duration(1500)
      .show()

    viewBinding.webContent.webChromeClient = object : WebChromeClient() {
      override fun onProgressChanged(view: WebView?, newProgress: Int) {
        super.onProgressChanged(view, newProgress)
        if (newProgress >= 80) {
          viewModel.getRecommendNewsBySubNewsTypeId()
        }
      }
    }
  }

  private fun setupRecommendNewsListAdapter() {
    val recommendListAdapter =
      SimpleDiffListAdapter<RecommendNewsItemData>(
        layout.personal_recycler_recommend_news_item,
        ItemDiffCallBack()
      )
    recommendListAdapter.setOnItemClickListener(object :
      SuperItemClickListener {
      override fun onClick(v: View, position: Int) {
        recommendListAdapter.getData()?.let {
          startActivity(
            GzNewsDetailsActivity.newIntent(
              this@StudyNewsDetailsActivity,
              it[position].id
            )
          )
          finish()
        }
      }
    })
    viewBinding.recyclerRecommendNews.layoutManager = LinearLayoutManager(this)
    viewBinding.recyclerRecommendNews.isNestedScrollingEnabled = false
    viewBinding.recyclerRecommendNews.adapter = recommendListAdapter
  }

  private fun setupCommentListAdapter() {
    val commentListAdapter =
      object : CommentListAdapter(this, R.layout.personal_recycler_moment_comment_item) {
        override fun convert(
          holder: SuperViewHolder,
          viewType: Int,
          item: CommentItemData,
          position: Int
        ) {
          super.convert(holder, viewType, item, position)
          val tvContent = holder.findViewById<TextView>(R.id.tv_content)
          HtmlText.from(item.content)
            .setImageLoader(object : HtmlImageLoader {
              override fun getErrorDrawable(): Drawable? {
                return ContextCompat.getDrawable(
                  this@StudyNewsDetailsActivity,
                  R.drawable.ic_no_photo_placeholder
                )
              }

              override fun getMaxWidth(): Int {
                return DensityUtils.dp2px(this@StudyNewsDetailsActivity, 16f)
              }

              override fun loadImage(url: String?, callback: HtmlImageLoader.Callback?) {
                Glide.with(this@StudyNewsDetailsActivity)
                  .asBitmap()
                  .load(url)
                  .into(object : SimpleTarget<Bitmap>() {
                    override fun onResourceReady(
                      resource: Bitmap,
                      transition: Transition<in Bitmap>?
                    ) {
                      callback?.onLoadComplete(resource)
                    }
                  })
              }

              override fun fitWidth(): Boolean {
                return true
              }

              override fun getDefaultDrawable(): Drawable? {
                return ContextCompat.getDrawable(
                  this@StudyNewsDetailsActivity,
                  R.drawable.ic_no_photo_placeholder
                )
              }
            }).into(tvContent)
        }
      }.apply {
        setOnItemClickListener(object :
          SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            when (v.id) {
              R.id.iv_avatar, R.id.tv_nick_name -> {
                UserHomeNavigation.navigate(data[position].uid).start()
              }
              R.id.tv_like -> {
                viewModel.commentLikeOrUnlike(data[position])
              }
              R.id.ll_reply -> {
                startActivityForResult(
                  CommentReplyActivity.newIntent(
                    this@StudyNewsDetailsActivity,
                    PersonalApiConstants.NEWS_TYPE_NEWS,
                    viewModel.getNewsId(),
                    position,
                    data[position]
                  ), GzNewsDetailsActivity.TO_COMMENT_REPLY_CODE
                )
              }
              else -> {
                showReplyDialog(position, data[position])
              }
            }
          }
        }, R.id.iv_avatar, R.id.tv_nick_name, R.id.tv_like, R.id.ll_reply)
        setOnItemLongClickListener(object : OnItemLongClickListener<CommentItemData> {
          override fun onLongClicked(v: View, position: Int, item: CommentItemData): Boolean {
            if (UserUtils.logged()) {
              if (item.uid == localUserId) {
                MenuPopup.Builder(this@StudyNewsDetailsActivity)
                  .setData(resources.getStringArray(R.array.moment_details_delete_menu))
                  .setOnItemClickListener { _, menuPosition ->
                    if (menuPosition == 0) {
                      showDeleteCommentConfirmDialog(position, item)
                    }
                  }.build().show()
              }
            }
            return true
          }
        })
      }
    val recyclerCommentList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerCommentList.layoutManager = LinearLayoutManager(this)
    recyclerCommentList.isNestedScrollingEnabled = false
    viewModel.commentListViewModel.setAdapter(commentListAdapter)
  }

  /**
   * 回复dialog
   */
  private fun showReplyDialog(commentPosition: Int, commentItemData: CommentItemData) {
    if (checkToLogin()) {
      viewModel.checkAvatarIsUpload {
        mCommentDialog = InputDialog(this)
          .apply {
            hint = <EMAIL>(
              R.string.reply_format,
              commentItemData.nickName
            )
            onSendClickListener = object : InputDialog.OnSendClickListener {
              override fun onSendClicked(commentDialog: InputDialog, content: String) {
                viewModel.addReply(commentPosition, commentItemData, content)
              }
            }
          }
        mCommentDialog?.show()
      }
    }
  }

  private fun showDeleteCommentConfirmDialog(position: Int, item: CommentItemData) {
    ActionDialog.Builder()
      .setTitle(getString(R.string.tips))
      .setContent(getString(R.string.delete_comment_confirm_tips))
      .setOnConfirmClickListener {
        viewModel.deleteComment(position, item)
      }.build().show(supportFragmentManager)
  }

}