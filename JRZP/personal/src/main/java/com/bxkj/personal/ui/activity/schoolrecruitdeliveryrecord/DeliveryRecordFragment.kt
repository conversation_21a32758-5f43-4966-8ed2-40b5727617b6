package com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.personal.R
import com.bxkj.personal.databinding.PersonalFragmentDeliveryRecordBinding
import java.util.*

/**
 * @description: 校招投递记录
 * @author: sanjin
 * @date: 2022/5/30
 */
class DeliveryRecordFragment :
  BaseDBFragment<PersonalFragmentDeliveryRecordBinding, DeliveryRecordViewModel>(),
  View.OnClickListener {

  companion object {

    private const val EXTRA_TYPE = "TYPE"

    fun newInstance(type: Int): Fragment {
      return DeliveryRecordFragment().apply {
        arguments = bundleOf(
          EXTRA_TYPE to type
        )
      }
    }
  }

  private var recordListAdapter: SchoolRecruitDeliveryRecordAdapter? = null

  override fun getViewModelClass(): Class<DeliveryRecordViewModel> =
    DeliveryRecordViewModel::class.java

  override fun getLayoutId(): Int = R.layout.personal_fragment_delivery_record

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    setupRecordListAdapter()

    subscribeViewModelEvent()

    viewModel.start(arguments?.getInt(EXTRA_TYPE, 0).getOrDefault())
  }

  fun setShowSelectAllState(show: Boolean) {
    viewModel.switchShowSelectAllState(show)
  }

  override fun onClick(v: View?) {
    v?.let {
      if (v.id == R.id.tv_confirm_delete) {
        ActionDialog.Builder()
          .setContent(getString(R.string.school_recruit_delivery_record_delete_tips))
          .setOnConfirmClickListener {
            viewModel.deleteSelected()
          }.build().show(childFragmentManager)
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.showSelectAll.observe(this) {
      recordListAdapter?.showSelect(it)
    }

    viewModel.selectedRecord.observe(this) {
      recordListAdapter?.notifyDataSetChanged()
    }
  }

  private fun setupRecordListAdapter() {
    recordListAdapter = SchoolRecruitDeliveryRecordAdapter(requireContext(), viewModel).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          val item = data[position]
          when (v.id) {
            R.id.iv_delete -> {
              ActionDialog.Builder()
                .setContent(getString(R.string.school_recruit_delivery_record_delete_tips))
                .setOnConfirmClickListener {
                  viewModel.deleteRecord(Collections.singletonList(item))
                }.build().show(childFragmentManager)
            }

            R.id.tv_email -> {
              SystemUtil.copy(
                requireContext(),
                "邮箱",
                item.email
              )
            }

            R.id.tv_mobile -> {
              val mobileList = item.mobile
              callSchoolRecruitPhone(mobileList)
            }

            R.id.tv_phone -> {
              val telList = item.tel
              callSchoolRecruitPhone(telList)
            }

            else -> {
            }
          }
        }
      }, R.id.iv_delete, R.id.tv_email, R.id.tv_mobile, R.id.tv_phone)
    }
    viewBinding.includeRecordList.recyclerContent.apply {
      layoutManager = LinearLayoutManager(requireContext())
      addItemDecoration(
        LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_f4f4f4_6))
          .build()
      )
    }
    viewModel.recordListViewModel.setAdapter(recordListAdapter)
  }

  /**
   * 拨打联系电话，处理单个和多个的情况
   */
  private fun callSchoolRecruitPhone(phoneNumberList: List<String>?) {
    if (!phoneNumberList.isNullOrEmpty()) {
      if (phoneNumberList.size == 1) {
        SystemUtil.callPhone(
          requireContext(),
          phoneNumberList[0]
        )
      } else {
        MenuPopup.Builder(requireActivity())
          .setData(phoneNumberList.toTypedArray())
          .setOnItemClickListener { _, position ->
            SystemUtil.callPhone(
              requireContext(),
              phoneNumberList[position]
            )
          }.build().show()
      }
    }
  }
}