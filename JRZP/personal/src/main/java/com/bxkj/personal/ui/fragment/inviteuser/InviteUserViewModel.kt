package com.bxkj.personal.ui.fragment.inviteuser

import android.app.Application
import android.os.Bundle
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.personal.data.FollowItemData
import com.bxkj.personal.data.InviteUserItemData
import com.bxkj.personal.data.source.AccountRepo
import com.bxkj.personal.data.source.QuestionRepo
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.inviteuser
 * @Description: 邀请用户
 * <AUTHOR>
 * @date 2020/2/26
 * @version V1.0
 */
class InviteUserViewModel @Inject constructor(application: Application
                                              , private val mQuestionRepo: QuestionRepo
                                              , private val mAccountRepo: AccountRepo) : BaseViewModel() {

    val userListViewModel = RefreshListViewModel()
    val setupRecommendListAdapterEvent =
      LiveEvent<Void>()
    val setupFriendListAdapterEvent = LiveEvent<Void>()
    private var mQuestionId: Int = CommonApiConstants.NO_ID
    private var mInviteType: Int = InviteUserFragment.TYPE_RECOMMEND

    init {
        userListViewModel.refreshLayoutViewModel.enableRefresh(false)
    }

    fun start(bundle: Bundle?) {
        bundle?.let {
            mQuestionId = it.getInt(InviteUserFragment.EXTRA_QUESTION_ID, CommonApiConstants.NO_ID)
            mInviteType = it.getInt(InviteUserFragment.EXTRA_INVITE_TYPE, InviteUserFragment.TYPE_RECOMMEND)
            if (mInviteType == InviteUserFragment.TYPE_RECOMMEND) {
                setupRecommendListAdapterEvent.call()
                setupRecommendListViewModel()
            } else {
                setupFriendListAdapterEvent.call()
                setupFriendListViewModel()
            }
            userListViewModel.refresh()
        }
    }

    private fun setupFriendListViewModel() {
        userListViewModel.setOnLoadDataListener { currentPage ->
            mAccountRepo.getFollowList(getSelfUserID(),false, getSelfUserID(), currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE
                    , object : ResultDataCallBack<List<FollowItemData>> {
                override fun onSuccess(data: List<FollowItemData>?) {
                    userListViewModel.autoAddAll(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    if (respondThrowable.errCode == 30001) {
                        userListViewModel.noMoreData()
                    } else {
                        userListViewModel.loadError()
                    }
                }
            })
        }
    }

    private fun setupRecommendListViewModel() {
        userListViewModel.refreshLayoutViewModel.enableLoadMore(false)
        userListViewModel.setOnLoadDataListener { currentPage ->
            mQuestionRepo.getInviteUserList(mQuestionId, currentPage, CommonApiConstants.DEFAULT_PAGE_SIZE
                    , object : ResultDataCallBack<List<InviteUserItemData>> {
                override fun onSuccess(data: List<InviteUserItemData>?) {
                    userListViewModel.autoAddAll(data)
                }

                override fun onError(respondThrowable: RespondThrowable) {
                    userListViewModel.noMoreData()
                }
            })
        }
    }

    fun inviteUser(userItem: InviteUserItemData) {
        mQuestionRepo.inviteUserToAnswer(getSelfUserID(), mQuestionId, userItem.userID
                , object : ResultCallBack {
            override fun onSuccess() {
                userItem.isInvited = true
            }

            override fun onError(respondThrowable: RespondThrowable) {
                showToast(respondThrowable.errMsg)
            }
        })
    }

    fun inviteFriend(friendItem: FollowItemData) {
        mQuestionRepo.inviteUserToAnswer(getSelfUserID(), mQuestionId, friendItem.id
                , object : ResultCallBack {
            override fun onSuccess() {
                friendItem.isInvited = true
            }

            override fun onError(respondThrowable: RespondThrowable) {
                showToast(respondThrowable.errMsg)
            }
        })
    }
}