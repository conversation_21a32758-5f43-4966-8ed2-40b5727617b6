package com.bxkj.personal.ui.fragment.invitationstodelivery;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import com.bxkj.common.adapter.superadapter.SuperAdapter;
import com.bxkj.common.adapter.superadapter.SuperViewHolder;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.personal.R;
import com.bxkj.personal.data.InvitationsToDeliveryItemData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.fragment.invitationstodelivery
 * @Description: 投递邀请列表适配器
 * @TODO: TODO
 * @date 2018/9/26
 */
public class InvitationsToDeliveryAdapter extends SuperAdapter<InvitationsToDeliveryItemData> {

  private int mPageType;

  public InvitationsToDeliveryAdapter(Context context, List<InvitationsToDeliveryItemData> list,
      int layoutResId, int pageType) {
    super(context, layoutResId, list);
    mPageType = pageType;
  }

  @Override
  protected void convert(SuperViewHolder holder, int viewType,
      InvitationsToDeliveryItemData invitationsToDeliveryItemData, int position) {
    holder.findViewById(R.id.ll_options_bar)
        .setVisibility(mPageType == 0 ? View.VISIBLE : View.GONE);
    holder.setText(R.id.tv_job_name, invitationsToDeliveryItemData.getRelName());
    holder.setText(R.id.tv_job_wages, invitationsToDeliveryItemData.getConvertSalary());
    holder.setText(R.id.tv_job_area, invitationsToDeliveryItemData.getQuName());
    holder.setText(R.id.tv_job_degree, invitationsToDeliveryItemData.getFormatEduName());
    holder.setText(R.id.tv_job_exp, invitationsToDeliveryItemData.getFormatExpName());
    holder.setText(R.id.tv_date, invitationsToDeliveryItemData.getFormatApplyTime());
    holder.setText(R.id.tv_job_publish_enterprise, invitationsToDeliveryItemData.getComName());

    TextView tvJobType = holder.findViewById(R.id.tv_type);
    if (invitationsToDeliveryItemData.emptyNatureName()) {
      tvJobType.setVisibility(View.GONE);
    } else {
      tvJobType.setVisibility(View.VISIBLE);
      tvJobType.setText(invitationsToDeliveryItemData.getJnName());
    }

    TextView tvJobArea = holder.findViewById(R.id.tv_job_area);
    if (CheckUtils.isNullOrEmpty(invitationsToDeliveryItemData.getQuName())) {
      tvJobArea.setVisibility(View.GONE);
    } else {
      tvJobArea.setVisibility(View.VISIBLE);
      holder.setText(R.id.tv_job_area, invitationsToDeliveryItemData.getQuName());
    }

    TextView tvIdentity = holder.findViewById(R.id.tv_identity);
    if (CheckUtils.isNullOrEmpty(invitationsToDeliveryItemData.getIdentityRequire())) {
      tvIdentity.setVisibility(View.GONE);
    } else {
      tvIdentity.setVisibility(View.VISIBLE);
      tvIdentity.setText(invitationsToDeliveryItemData.getIdentityRequire());
    }

    TextView tvPartner = holder.findViewById(R.id.tv_partner);
    if (CheckUtils.isNullOrEmpty(invitationsToDeliveryItemData.getPartnerNature())) {
      tvPartner.setVisibility(View.GONE);
    } else {
      tvPartner.setVisibility(View.VISIBLE);
      tvPartner.setText(invitationsToDeliveryItemData.getPartnerNature());
    }

    setOnChildClickListener(position, holder.itemView, holder.findViewById(R.id.tv_no),
        holder.findViewById(R.id.tv_yes));
  }
}
