package com.bxkj.personal.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.common.widget.filterpopup.FilterOptionData;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.contract
 * @Description: NewsTypeTwo
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface NewsTypeTwoContract {

    interface View extends BaseView {
        void getNewsTypeTwoSuccess(List<FilterOptionData> filterOptionData);
    }

    abstract class Presenter extends BaseMvpPresenter<View> {
        public abstract void getNewsTypeTwo();
    }
}
