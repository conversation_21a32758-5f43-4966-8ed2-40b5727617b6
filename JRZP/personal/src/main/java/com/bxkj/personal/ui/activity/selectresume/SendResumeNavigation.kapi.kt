package com.bxkj.personal.ui.activity.selectresume

import android.app.Activity
import androidx.core.os.bundleOf
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants
import com.bxkj.personal.ui.activity.selectresume.SendResumeMethod.Method

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/31
 * @version: V1.0
 */
class SendResumeNavigation {
    companion object {

        const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/sendresume"

        const val EXTRA_JOB_ID = "mJobId"
        const val EXTRA_COMPANY_USER_ID = "mCompanyUserId"

        const val EXTRA_SEND_METHOD = "SEND_METHOD"

        const val RESULT_SEND_RESUME_SUCCESS = Activity.RESULT_OK + 1

        @JvmStatic
        fun navigate(
            jobId: Int,
            companyUserId: Int,
            @Method sendMethod: Int = SendResumeMethod.SEND
        ): RouterNavigator {
            return Router.getInstance().to(PATH).with(
                bundleOf(
                    EXTRA_JOB_ID to jobId,
                    EXTRA_COMPANY_USER_ID to companyUserId,
                    EXTRA_SEND_METHOD to sendMethod
                )
            )
        }
    }
}