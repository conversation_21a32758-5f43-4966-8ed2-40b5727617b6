package com.bxkj.personal.data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.data
 * @Description: 在校情况
 * @TODO: TODO
 * @date 2018/5/12
 */

public class SchoolSituationItemData {

    private int id;
    private int resumeId;
    private String type;
    private String name;
    private String remark;
    private List<SchoolSituationItemData> schoolSituationItemData;

    public SchoolSituationItemData() {
    }

    public SchoolSituationItemData(int resumeId, List<SchoolSituationItemData> schoolSituationItemData) {
        this.resumeId = resumeId;
        this.schoolSituationItemData = schoolSituationItemData;
    }

    public List<SchoolSituationItemData> getSchoolSituationItemData() {
        return schoolSituationItemData;
    }

    public void setSchoolSituationItemData(List<SchoolSituationItemData> schoolSituationItemData) {
        this.schoolSituationItemData = schoolSituationItemData;
    }

    public int getResumeId() {
        return resumeId;
    }

    public void setResumeId(int resumeId) {
        this.resumeId = resumeId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
