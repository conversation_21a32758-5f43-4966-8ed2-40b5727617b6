package com.bxkj.personal.data.source

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.base.mvvm.callback.ResultCallBack
import com.bxkj.common.base.mvvm.callback.ResultDataCallBack
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CustomObserver
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.network.RxHelper
import com.bxkj.common.network.exception.RespondThrowable
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.personal.api.CoroutinesApi
import com.bxkj.personal.api.PersonalApi
import com.bxkj.personal.data.MomentDetailsData
import com.bxkj.personal.data.PaidUserItemData
import com.bxkj.support.upload.data.UploadFileItem
import com.google.gson.Gson
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @date 2019/11/20
 */
class MomentRepo @Inject constructor(
  private val mPersonalApi: PersonalApi,
  private val mCoroutinesApi: CoroutinesApi
) : BaseRepo() {

  fun post(
    userId: Int,
    cityId: Int,
    address: String,
    lng: String,
    lat: String,
    newType: Int,
    price: Int,
    content: String,
    picList: List<UploadFileItem>,
    callback: ResultCallBack
  ) {
    mPersonalApi.postNews(userId, cityId, address, lng, lat, newType, price, content, picList)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun getPaidUserList(
    userId: Int,
    momentId: Int,
    pageIndex: Int,
    pageSize: Int,
    callback: ResultDataCallBack<List<PaidUserItemData>>
  ) {
    mPersonalApi.getPaidUserList(userId, momentId, pageIndex, pageSize)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(CheckUtils.cast(baseResponse.dataList))
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun uploadMomentFile(
    filePath: String,
    params: UploadFileRequestParams,
    callback: ResultDataCallBack<String>
  ) {
    mPersonalApi.uploadFile(filePath, params)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse.msg)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  fun deleteMoment(userId: Int, momentId: Int, callback: ResultCallBack) {
    mPersonalApi.deleteMoment(userId, momentId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess()
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  suspend fun postMoment(
    userId: Int,
    cityId: Int,
    address: String,
    lng: String,
    lat: String,
    content: String,
    fileList: List<UploadFileItem>,
    picIds: String,
    momentType: Int,
    attachJobIds: String
  ): ReqResponse<Nothing> {
    return httpRequest {
      mCoroutinesApi.postMoment(
        ZPRequestBody().apply {
          put("uid", userId)
          put("sjcity", cityId)
          put("address", address)
          put("lng", lng)
          put("lat", lat)
          put("sjtype", momentType)
          put("glid", attachJobIds)
          put("content", content)
          put("pic", Gson().toJson(fileList))
          put("picId", picIds)
        }.paramsEncrypt()
      )
    }
  }

  fun getMomentDetails(
    userId: Int,
    momentId: Int,
    callback: ResultDataCallBack<MomentDetailsData>
  ) {
    mPersonalApi.getMomentDetails(userId, momentId)
      .compose(RxHelper.applyThreadSwitch())
      .subscribe(object : CustomObserver() {
        override fun onSuccess(baseResponse: BaseResponse<*>) {
          callback.onSuccess(baseResponse.data as MomentDetailsData?)
        }

        override fun onSubscribe(d: Disposable) {
          mCompositeDisposable.add(d)
        }

        override fun onError(respondThrowable: RespondThrowable) {
          callback.onError(respondThrowable)
        }
      })
  }

  /**
   *获取用户动态详情v2
   */
  suspend fun getUserMomentDetailsV2(momentId: Int, userId: Int): ReqResponse<MomentDetailsData> {
    return httpRequest {
      mCoroutinesApi.getUserMomentDetailsV2(
        ZPRequestBody().apply {
          put("id", momentId)
          put("curUid", userId)
        }.paramsEncrypt()
      )
    }
  }
}