package com.bxkj.personal.ui.fragment.homenews.itemviewbinder

import android.content.Intent
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import android.widget.TextView
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.data.NewsItemData
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivityV2

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.homenews.itemviewbinder
 * @Description: 首页资讯直招类数据绑定
 * <AUTHOR>
 * @date 2020/2/20
 * @version V1.0
 */
class JobItemViewBinder :
  DefaultViewBinder<NewsItemData>(R.layout.personal_recycler_news_list_job, BR.data, true) {
  override fun onBindViewHolder(
    holder: SuperViewHolder,
    item: NewsItemData,
    position: Int
  ) {
    super.onBindViewHolder(holder, item, position)
    val tvJobNames = holder.findViewById<TextView>(R.id.tv_job_names)
    val spannableBuilder = SpannableStringBuilder()
    item.listJobNames?.let {
      val jobNameList = item.listJobNames.split(",")
      for (i in jobNameList.indices) {
        val beforeLength = spannableBuilder.length
        if (i < jobNameList.size - 1) {
          spannableBuilder.append(jobNameList[i]).append(" | ")
        } else {
          spannableBuilder.append(jobNameList[i])
        }
        val clickSpan = object : ClickableSpan() {
          override fun onClick(widget: View) {
            holder.itemView.context.startActivity(JobDetailsActivityV2.newIntent(
              holder.itemView.context,
              item.listJobIDs.split(",")[i].toInt()
            )
              .apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
              })
          }

          override fun updateDrawState(ds: TextPaint) {
            ds.color = Color.parseColor("#000000")
          }
        }
        spannableBuilder.setSpan(
          clickSpan,
          beforeLength,
          spannableBuilder.length,
          Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        tvJobNames.movementMethod = LinkMovementMethod.getInstance()
        tvJobNames.text = spannableBuilder
      }
    }
  }
}