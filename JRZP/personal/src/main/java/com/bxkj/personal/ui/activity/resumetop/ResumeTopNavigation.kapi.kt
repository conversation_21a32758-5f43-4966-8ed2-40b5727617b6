package com.bxkj.personal.ui.activity.resumetop

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * Description:
 * Author:Sanjin
 * Date:2024/4/25
 **/
class ResumeTopNavigation {

  companion object {
    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/resume_top"

    fun create(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}