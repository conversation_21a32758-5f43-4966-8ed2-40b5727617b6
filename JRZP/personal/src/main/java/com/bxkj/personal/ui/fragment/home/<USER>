package com.bxkj.personal.ui.fragment.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.BR
import com.bxkj.personal.R
import com.bxkj.personal.data.CompanyADItemData
import com.bxkj.personal.ui.activity.recommendjob.RecommendJobActivity
import com.youth.banner.adapter.BannerAdapter

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/5/13
 * @version: V1.0
 */
class HomeADBannerAdapter(list: List<CompanyADItemData>?) :
  BannerAdapter<CompanyADItemData, SuperViewHolder>(list) {

  override fun onCreateHolder(parent: ViewGroup, viewType: Int): SuperViewHolder {
    //兼容databinding
    val viewDataBinding = DataBindingUtil.inflate<ViewDataBinding>(
      LayoutInflater.from(parent.context),
      R.layout.personal_recycler_home_company_ad_item,
      parent,
      false
    )
    return SuperViewHolder(viewDataBinding)
  }

  override fun onBindView(
    holder: SuperViewHolder,
    aditem: CompanyADItemData?,
    position: Int,
    size: Int
  ) {
    holder.bind(BR.data, aditem)
  }

  // override fun onViewDetachedFromWindow(holder: SuperViewHolder) {
  //   super.onViewDetachedFromWindow(holder)
  //   setOnBannerListener(null)
  // }
}