package com.bxkj.personal.ui.fragment.home

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.bxkj.common.widget.marquee.MarqueeView
import com.bxkj.personal.R
import com.bxkj.personal.data.SearchHotKeyItemData
import java.lang.StringBuilder

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.fragment.home
 * @Description: 热门搜索关键词适配器
 * <AUTHOR>
 * @date 2019/12/24
 * @version V1.0
 */
class SearchKeywordAdapter(
  private val mContext: Context,
  private val mKeywordList: List<SearchHotKeyItemData>
) : MarqueeView.Adapter<SearchKeywordAdapter.ViewHolder>() {

  override fun createViewHolder(parent: ViewGroup): ViewHolder {
    return ViewHolder(
      LayoutInflater.from(mContext).inflate(R.layout.personal_marquee_home_hot_keyword_item, null)
    )
  }

  override fun onBindViewHolder(holder: ViewHolder, position: Int) {
    holder.let {
      val itemText = StringBuilder()
      if (2 * position < mKeywordList.size) {
        itemText.append(mKeywordList[2 * position].key)
      }
      if (2 * position + 1 < mKeywordList.size) {
        itemText.append(" | ").append(mKeywordList[2 * position + 1].key)
      }
      (holder.itemView as TextView).text = itemText
      it.itemView.setOnClickListener {
        val tempList = ArrayList<SearchHotKeyItemData>()
        if (2 * position < mKeywordList.size) {
          tempList.add(mKeywordList[2 * position])
        }
        if (2 * position + 1 < mKeywordList.size) {
          tempList.add(mKeywordList[2 * position + 1])
        }
        ss?.onClicked(tempList)
      }
    }
  }

  private var ss: OnSSClickListener? = null

  fun setSSClickListener(c: OnSSClickListener) {
    ss = c
  }

  interface OnSSClickListener {
    fun onClicked(list: ArrayList<SearchHotKeyItemData>)
  }

  override fun getItemCount(): Int = mKeywordList.size / 2

  class ViewHolder constructor(itemView: View) : MarqueeView.ViewHolder(itemView)
}