package com.bxkj.personal.data

import androidx.recyclerview.widget.DiffUtil
import com.google.gson.annotations.SerializedName

/**
 *
 * @author: sanjin
 * @date: 2022/8/30
 */
data class JobDataV2(
    var comName: String?,
    var comid: Int?,
    var edate1: String?,
    var moneyName: String?,
    var quName: String?,
    var quaName: String?,
    var relName: String?,
    var relid: Int?,
    var shiName: String?,
    var wtName: String?,
    @SerializedName("isjobApply")
    var submitState: Int? = 0
) {

    fun isSubmitted(): Boolean {
        return submitState == 1
    }

    fun getSimpleDesc(): Array<String> {
        val descBuilder = StringBuilder()
        if (!quName.isNullOrBlank()) {
            descBuilder.append(quName).append(",")
        }
        if (!quaName.isNullOrBlank()) {
            descBuilder.append(quaName).append(",")
        }
        if (!wtName.isNullOrBlank()) {
            descBuilder.append(wtName).append(",")
        }
        if (descBuilder.isEmpty()) {
            return emptyArray()
        } else {
            return descBuilder.substring(0, descBuilder.length - 1).toString().split(",")
                .toTypedArray()
        }
    }

    fun hasUpdateTime(): Boolean {
        return !edate1.isNullOrBlank()
    }

    class DiffCallback : DiffUtil.ItemCallback<JobDataV2>() {
        override fun areItemsTheSame(oldItem: JobDataV2, newItem: JobDataV2): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: JobDataV2, newItem: JobDataV2): Boolean {
            return oldItem.relid == newItem.relid
        }

    }
}