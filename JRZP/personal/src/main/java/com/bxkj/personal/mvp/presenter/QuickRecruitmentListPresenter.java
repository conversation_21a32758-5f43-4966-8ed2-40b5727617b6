package com.bxkj.personal.mvp.presenter;

import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.personal.api.PersonalApi;
import com.bxkj.personal.data.QuickRecruitmentData;
import com.bxkj.personal.mvp.contract.QuickRecruitmentListContract;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.mvp.presenter
 * @Description: QuickRecruitmentList
 * @TODO: TODO
 * @date 2018/3/27
 */

public class QuickRecruitmentListPresenter extends QuickRecruitmentListContract.Presenter {

    private static final String TAG = QuickRecruitmentListPresenter.class.getSimpleName();
    private PersonalApi mPersonalApi;

    @Inject
    public QuickRecruitmentListPresenter(PersonalApi personalApi) {
        mPersonalApi = personalApi;
    }

    @Override
    public void getQuickRecruitmentList(int provinceId, int cityId, int areaId, int streetId, String title, int pageSize, int pageIndex) {
        mPersonalApi.getQuickRecruitmentList(provinceId, cityId, areaId, streetId, title, pageSize, pageIndex)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        List<QuickRecruitmentData> quickRecruitmentDataList = (List<QuickRecruitmentData>) baseResponse.getDataList();
                        mView.getQuickRecruitmentSuccess(quickRecruitmentDataList, quickRecruitmentDataList.size() < pageSize);
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        if (respondThrowable.getErrCode() == 30001) {
                            mView.onResultNoData();
                        } else {
                            mView.onRequestError(respondThrowable);
                        }
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }
}
