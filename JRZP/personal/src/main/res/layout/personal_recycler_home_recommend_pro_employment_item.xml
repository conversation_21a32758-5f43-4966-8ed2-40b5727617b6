<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.personal.data.NewsItemData" />
  </data>

  <!--资讯列表培训推荐-->
  <LinearLayout
    style="@style/match_wrap"
    android:orientation="vertical">

    <LinearLayout
      style="@style/match_wrap"
      android:layout_marginTop="@dimen/dp_16"
      android:orientation="horizontal">

      <TextView
        android:id="@+id/tv_title"
        style="@style/Text.14sp.333333"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_weight="1"
        android:text="@string/home_recommend_pro_employment" />

      <TextView
        android:id="@+id/tv_more"
        style="@style/Text.14sp.999999"
        android:layout_marginEnd="@dimen/dp_14"
        android:drawableEnd="@drawable/ic_home_recommend_more"
        android:drawablePadding="@dimen/dp_6"
        android:text="@string/more" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_pro_employment"
      style="@style/match_wrap"
      android:layout_marginTop="@dimen/dp_10"
      android:layout_marginBottom="@dimen/dp_14" />

  </LinearLayout>
</layout>
