<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.bxkj.common.util.CheckUtils" />

        <import type="com.bxkj.common.util.HtmlUtils" />

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.personal.data.StudyNewsItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:padding="@dimen/dp_16">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text.18sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginEnd="@dimen/dp_8"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="@{HtmlUtils.fromHtml(data.title)}"
            app:layout_constraintEnd_toStartOf="@id/iv_img"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/dp_0" />

        <ImageView
            android:id="@+id/iv_img"
            android:layout_width="114dp"
            android:layout_height="76dp"
            android:layout_marginStart="@dimen/dp_8"
            android:visibility="@{CheckUtils.isNullOrEmpty(data.pic)?View.GONE:View.VISIBLE}"
            bind:imgUrl="@{data.pic}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <!--        <LinearLayout-->
        <!--            android:id="@+id/ll_content"-->
        <!--            style="@style/common_match_wrap"-->
        <!--            android:layout_marginStart="@dimen/common_dp_16"-->
        <!--            android:layout_marginTop="@dimen/common_dp_8"-->
        <!--            android:layout_marginEnd="@dimen/common_dp_16"-->
        <!--            android:orientation="horizontal"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@id/tv_title">-->

        <!--            <ImageView-->
        <!--                android:layout_width="114dp"-->
        <!--                android:layout_height="74dp"-->
        <!--                android:layout_marginEnd="@dimen/common_dp_8"-->
        <!--                android:visibility="@{CheckUtils.isNullOrEmpty(data.pic)?View.GONE:View.VISIBLE}" />-->

        <!--            <TextView-->
        <!--                style="@style/common_Text.14sp.666666"-->
        <!--                android:ellipsize="end"-->
        <!--                android:maxLines="3"-->
        <!--                android:text="@{HtmlUtils.fromHtml(HtmlUtils.delHtmlTag(data.content))}" />-->

        <!--        </LinearLayout>-->

        <TextView
            android:id="@+id/tv_type"
            style="@style/Text.12sp.999999"
            android:layout_marginTop="@dimen/dp_8"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{@string/study_news_item_type_format(data.typeName,data.subTypeName)}"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_comment_count"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintVertical_bias="1" />

        <TextView
            android:id="@+id/tv_comment_count"
            style="@style/Text.12sp.999999"
            android:layout_marginStart="@dimen/dp_12"
            android:text="@{@string/study_news_item_comment_format(data.commentsCount)}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_type"
            app:layout_constraintEnd_toStartOf="@id/tv_date"
            app:layout_constraintStart_toEndOf="@id/tv_type" />

        <TextView
            android:id="@+id/tv_date"
            style="@style/Text.12sp.999999"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_8"
            android:text="@{data.timeDiff}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_comment_count"
            app:layout_constraintEnd_toStartOf="@id/iv_img"
            app:layout_constraintStart_toEndOf="@id/tv_comment_count"
            app:layout_goneMarginEnd="@dimen/dp_0" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>