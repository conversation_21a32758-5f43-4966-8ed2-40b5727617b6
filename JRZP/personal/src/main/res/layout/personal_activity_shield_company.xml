<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.shieldcompany.ShieldCompanyViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      style="@style/match_wrap"
      app:rightOptionClickListener="@{onClickListener}"
      app:right_img="@drawable/ic_page_qa"
      app:title="@string/shield_company_page_title" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:visibility="@{viewModel.shieldCompanyCount&gt;0?View.VISIBLE:View.GONE}">

      <TextView
        style="@style/Text.18sp.333333.Bold"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_weight="1"
        android:text="@{@string/shield_company_count_format(viewModel.shieldCompanyCount)}" />

      <TextView
        style="@style/Text.14sp.333333"
        android:layout_marginEnd="@dimen/dp_16"
        android:onClick="@{()->viewModel.switchEditState()}"
        android:text="@{viewModel.editStatus?@string/shield_company_complete:@string/shield_company_management}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal"
      android:visibility="@{viewModel.shieldCompanyCount&gt;0?View.VISIBLE:View.GONE}" />

    <include
      layout="@layout/include_mvvm_refresh_layout"
      app:listViewModel="@{viewModel.shieldCompanyListViewModel}" />

    <View style="@style/Line.Horizontal" />

    <TextView
      android:id="@+id/tv_add_shield_company"
      style="@style/Button.Basic"
      android:layout_marginStart="@dimen/dp_30"
      android:layout_marginTop="@dimen/dp_10"
      android:layout_marginEnd="@dimen/dp_30"
      android:layout_marginBottom="@dimen/dp_10"
      android:onClick="@{onClickListener}"
      android:text="@string/shield_company_add"
      android:visibility="@{viewModel.editStatus?View.GONE:View.VISIBLE}" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="68dp"
      android:gravity="center_vertical"
      android:visibility="@{viewModel.editStatus?View.VISIBLE:View.GONE}">

      <ImageView
        android:id="@+id/iv_all_selected"
        style="@style/wrap_wrap"
        android:layout_marginStart="@dimen/dp_16"
        android:onClick="@{onClickListener}"
        android:src="@drawable/common_bg_checkbox_selector"
        bind:selected="@{viewModel.allSelected}" />

      <TextView
        android:id="@+id/tv_selected_count"
        style="@style/Text.15sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_weight="1"
        android:onClick="@{onClickListener}"
        android:text="@{@string/shield_company_remove_selected_format(viewModel.selectedItem.size)}" />

      <TextView
        style="@style/Button.Basic"
        android:layout_width="@dimen/dp_0"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_weight="1"
        android:enabled="@{viewModel.selectedItem.size&gt;0}"
        android:onClick="@{()->viewModel.confirmDelete()}"
        android:text="@string/common_delete" />

    </LinearLayout>

  </LinearLayout>
</layout>