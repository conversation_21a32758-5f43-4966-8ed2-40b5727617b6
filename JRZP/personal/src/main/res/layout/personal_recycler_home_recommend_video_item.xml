<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.personal.data.NewsItemData" />
  </data>

  <!--资讯列表视频推荐-->
  <LinearLayout
    style="@style/match_wrap"
    android:orientation="vertical">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.14sp.333333"
      android:layout_marginStart="@dimen/dp_14"
      android:layout_marginTop="@dimen/dp_16"
      android:layout_weight="1"
      android:drawableEnd="@drawable/ic_home_recommend_more"
      android:drawablePadding="@dimen/dp_6"
      android:text="@string/home_recommend_video" />

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_video_list"
      android:layout_width="match_parent"
      android:layout_height="336dp"
      android:layout_marginTop="@dimen/dp_10"
      android:layout_marginBottom="@dimen/dp_16" />

  </LinearLayout>
</layout>
