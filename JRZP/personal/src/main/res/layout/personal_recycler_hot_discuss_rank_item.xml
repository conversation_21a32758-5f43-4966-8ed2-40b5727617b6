<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="data"
      type="com.bxkj.personal.data.DiscussData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:padding="@dimen/dp_16">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.16sp.333333.Bold"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.title}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_hot"
      style="@style/Text.10sp.FFFFFF"
      app:layout_constraintBottom_toBottomOf="@id/tv_title"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_title" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_content"
      style="@style/match_wrap"
      android:layout_marginTop="@dimen/common_dp_16"
      android:background="@drawable/bg_fff8ef_radius_8"
      android:padding="@dimen/dp_12"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title">

      <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:scaleType="centerCrop"
        bind:imgUrl="@{data.photo}"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/roundedCornerImageStyle.Avatar" />

      <TextView
        android:id="@+id/tv_nike_name"
        style="@style/Text.15sp.333333"
        android:layout_marginStart="@dimen/dp_6"
        android:text="@{data.nickName}"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

      <TextView
        style="@style/Text.14sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_marginTop="@dimen/dp_8"
        android:maxLines="2"
        android:text="@{data.content}"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
      style="@style/match_wrap"
      android:layout_marginTop="@dimen/common_dp_16"
      android:orientation="horizontal"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/cl_content">

      <TextView
        style="@style/Text.14sp.333333"
        android:drawableStart="@drawable/personal_ic_news_like"
        android:drawablePadding="@dimen/dp_4"
        android:text="@{String.valueOf(data.zanCount)}" />

      <TextView
        style="@style/Text.14sp.333333"
        android:layout_marginStart="@dimen/common_dp_20"
        android:drawableStart="@drawable/personal_ic_news_comment"
        android:drawablePadding="@dimen/dp_4"
        android:text="@{String.valueOf(data.plCount)}" />

    </LinearLayout>

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>