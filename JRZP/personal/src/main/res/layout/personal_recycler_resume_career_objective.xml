<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:orientation="vertical"
    android:paddingBottom="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_12"
    android:paddingStart="@dimen/dp_12"
    android:paddingTop="@dimen/dp_16">

    <TextView
        style="@style/Text.16sp.333333"
        android:text="@string/resume_details_career_objective" />

    <View
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_2"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@color/cl_ff7405" />

<!--    <TextView-->
<!--        android:id="@+id/tv_expected_industry"-->
<!--        style="@style/Text.14sp.333333"-->
<!--        android:layout_marginTop="@dimen/dp_16"-->
<!--        android:text="@string/resume_details_expected_industry_format" />-->

    <TextView
        android:id="@+id/tv_expected_career"
        style="@style/Text.14sp.333333"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/resume_details_expected_career_format" />

    <TextView
        android:id="@+id/tv_career"
        style="@style/Text.14sp.333333"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/resume_details_career_format" />

    <TextView
        android:id="@+id/tv_work_nature"
        style="@style/Text.14sp.333333"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/resume_details_work_nature_format" />

    <TextView
        android:id="@+id/tv_work_address"
        style="@style/Text.14sp.333333"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/resume_details_work_address_format" />

    <TextView
        android:id="@+id/tv_available_time"
        style="@style/Text.14sp.333333"
        android:layout_marginTop="@dimen/dp_16"
        android:text="@string/resume_details_available_time_format" />
</LinearLayout>