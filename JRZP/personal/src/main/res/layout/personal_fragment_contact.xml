<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_title_bar"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@drawable/chat_bg_contact_title_bar"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <net.lucode.hackware.magicindicator.MagicIndicator
                android:id="@+id/indicator_type"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="12dp"
                android:layout_weight="1" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_notice"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="8dp"
                android:onClick="@{onClickListener}"
                android:scaleType="centerInside"
                android:src="@drawable/chat_ic_set_all_readed" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_setting"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="@dimen/dp_8"
                android:onClick="@{onClickListener}"
                android:scaleType="centerInside"
                android:src="@drawable/chat_ic_setting" />

        </LinearLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>
</layout>