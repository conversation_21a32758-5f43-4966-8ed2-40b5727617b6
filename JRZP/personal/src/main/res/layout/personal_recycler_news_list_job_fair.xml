<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.CheckUtils" />

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.personal.data.JobFairItemData" />
  </data>

  <!--资讯列表招聘会分类-->
  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:padding="@dimen/dp_14">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.17sp.000000"
      android:layout_width="@dimen/dp_0"
      android:ellipsize="end"
      android:maxLines="2"
      android:text="@{data.title}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_school_name"
      style="@style/Text.Tertiary"
      android:layout_marginTop="@dimen/dp_6"
      android:text="@{data.comName}"
      android:visibility="@{CheckUtils.isNullOrEmpty(data.comName)?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_date"
      style="@style/Text.Tertiary"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginTop="@dimen/dp_6"
      android:text="@{data.date}"
      app:layout_constraintStart_toEndOf="@id/tv_school_name"
      app:layout_constraintTop_toBottomOf="@id/tv_title"
      app:layout_goneMarginStart="@dimen/dp_0" />

    <TextView
      android:id="@+id/tv_time"
      style="@style/Text.Tertiary"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginTop="@dimen/dp_6"
      android:text="@{data.timeRange}"
      android:visibility="@{CheckUtils.isNullOrEmpty(data.timeRange)?View.GONE:View.VISIBLE}"
      app:layout_constraintStart_toEndOf="@id/tv_date"
      app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_view_count"
      style="@style/Text.Tertiary"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginTop="@dimen/dp_6"
      android:text="@{@string/home_news_view_count_format(data.count)}"
      app:layout_constraintStart_toEndOf="@id/tv_time"
      app:layout_constraintTop_toBottomOf="@id/tv_title"
      app:layout_goneMarginStart="@dimen/dp_8" />
  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>