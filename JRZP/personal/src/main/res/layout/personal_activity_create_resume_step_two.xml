<?xml version="1.0" encoding="utf-8"?>
<layout>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <include layout="@layout/common_include_title_bar" />

    <androidx.core.widget.NestedScrollView style="@style/match_wrap">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <include layout="@layout/personal_include_create_resume_step" />

        <TextView
          style="@style/Text.InputTips"
          android:text="@string/personal_required_text" />

        <LinearLayout style="@style/Layout.InfoItem">

          <TextView
            style="@style/Text.15sp.333333"
            android:text="@string/career_objective_before_job" />

          <com.bxkj.common.widget.MyEditText
            android:id="@+id/et_before_job"
            style="@style/EditText.PublishItem"
            android:hint="@string/career_objective_before_job_hint" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout
          android:id="@+id/ll_expect_category"
          style="@style/Layout.InfoItem">

          <TextView
            style="@style/Text.15sp.333333"
            android:text="@string/career_objective_category" />

          <TextView
            android:id="@+id/tv_expect_position"
            style="@style/Text.InfoItem" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/Layout.InfoItem">

          <TextView
            style="@style/Text.15sp.333333"
            android:text="@string/personal_specific_position" />

          <com.bxkj.common.widget.MyEditText
            android:id="@+id/et_specific_position"
            style="@style/EditText.PublishItem"
            android:hint="@string/career_objective_details_name_hint" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/Layout.InfoItem">

          <TextView
            style="@style/Text.15sp.333333"
            android:text="@string/personal_working_nature" />

          <TextView
            android:id="@+id/tv_working_nature"
            style="@style/Text.InfoItem" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/Layout.InfoItem">

          <TextView
            style="@style/Text.15sp.333333"
            android:text="@string/personal_working_place" />

          <TextView
            android:id="@+id/tv_work_place"
            style="@style/Text.InfoItem" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/Layout.InfoItem">

          <TextView
            style="@style/Text.15sp.333333"
            android:text="@string/personal_work_exp" />

          <TextView
            android:id="@+id/tv_work_exp"
            style="@style/Text.InfoItem" />
        </LinearLayout>

        <TextView
          style="@style/Text.InputTips"
          android:text="@string/personal_privacy" />

        <LinearLayout style="@style/Layout.InfoItem">

          <TextView
            android:id="@+id/tv_privacy_state_text"
            style="@style/Text.15sp.333333"
            android:drawableEnd="@drawable/ic_rule_desc"
            android:drawablePadding="@dimen/dp_4"
            android:text="@string/my_resume_open_status" />

          <TextView
            android:id="@+id/tv_privacy_state"
            style="@style/Text.InfoItem.Select" />
        </LinearLayout>

        <TextView
          style="@style/Text.InputTips"
          android:text="@string/personal_optional_text" />

        <LinearLayout style="@style/Layout.InfoItem">

          <TextView
            style="@style/Text.15sp.333333"
            android:text="@string/personal_expected_salary" />

          <TextView style="@style/Text.12sp.FF7647" />

          <com.bxkj.common.widget.MyEditText
            android:id="@+id/et_expected_salary"
            style="@style/EditText.PublishItem"
            android:hint="@string/personal_default_expect_salary"
            android:inputType="number" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

        <LinearLayout style="@style/Layout.InfoItem">

          <TextView
            style="@style/Text.15sp.333333"
            android:text="@string/personal_duty_time" />

          <TextView
            android:id="@+id/tv_duty_time"
            style="@style/Text.InfoItem" />
        </LinearLayout>

        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />

        <Button
          android:id="@+id/btn_confirm"
          style="@style/Button.Basic"
          android:layout_marginStart="@dimen/dp_30"
          android:layout_marginTop="@dimen/common_dp_44"
          android:layout_marginEnd="@dimen/dp_30"
          android:layout_marginBottom="@dimen/dp_30"
          android:text="@string/common_complete" />
      </LinearLayout>

    </androidx.core.widget.NestedScrollView>

  </LinearLayout>
</layout>