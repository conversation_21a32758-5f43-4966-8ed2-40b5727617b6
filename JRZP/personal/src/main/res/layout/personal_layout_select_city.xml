<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  style="@style/match_wrap"
  android:background="@drawable/bg_ffffff"
  android:orientation="vertical"
  android:paddingStart="@dimen/dp_10"
  android:paddingEnd="@dimen/dp_10">

  <TextView
    android:id="@+id/tv_current_city"
    style="@style/Text.16sp.333333.Bold"
    android:layout_marginTop="@dimen/dp_16" />

  <LinearLayout
    android:id="@+id/ll_history"
    style="@style/match_wrap"
    android:orientation="vertical">

    <TextView
      style="@style/Text.16sp.888888"
      android:layout_marginTop="@dimen/dp_16"
      android:text="@string/select_city_history_text" />

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_history"
      style="@style/match_wrap"
      android:layout_marginTop="@dimen/dp_8"
      android:overScrollMode="never" />
  </LinearLayout>

  <LinearLayout
    android:id="@+id/ll_area_info"
    style="@style/match_wrap"
    android:orientation="vertical">

    <TextView
      style="@style/Text.16sp.888888"
      android:layout_marginTop="@dimen/dp_16"
      android:text="@string/select_city_area_switch" />

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_area"
      style="@style/match_wrap"
      android:layout_marginTop="@dimen/dp_8"
      android:overScrollMode="never" />

    <TextView
      android:id="@+id/tv_view_all_area"
      style="@style/Text.14sp.888888"
      android:layout_gravity="center_horizontal"
      android:layout_marginTop="@dimen/dp_16"
      android:drawableEnd="@drawable/common_ic_next"
      android:drawablePadding="@dimen/dp_8"
      android:gravity="center"
      android:text="@string/select_city_view_all_area" />
  </LinearLayout>

  <TextView
    style="@style/Text.16sp.888888"
    android:layout_marginTop="@dimen/dp_16"
    android:text="@string/select_city_hot_text" />

  <androidx.recyclerview.widget.RecyclerView
    android:id="@+id/recycler_hot_city"
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_10"
    android:overScrollMode="never" />

  <TextView
    android:id="@+id/tv_view_all"
    style="@style/Text.14sp.888888"
    android:layout_gravity="center_horizontal"
    android:layout_marginTop="@dimen/dp_16"
    android:layout_marginBottom="@dimen/dp_16"
    android:drawableEnd="@drawable/common_ic_next"
    android:drawablePadding="@dimen/dp_8"
    android:gravity="center"
    android:text="@string/select_city_view_all" />
</LinearLayout>