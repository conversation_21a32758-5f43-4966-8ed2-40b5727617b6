<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="com.bxkj.common.util.CheckUtils" />

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.personal.data.VideoItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        style="@style/match_wrap"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Text.18sp.333333.Bold"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:text="@{data.title}"
            app:layout_constraintEnd_toStartOf="@id/tv_salary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_salary"
            style="@style/Text.16sp.ff7647"
            android:text="@{data.money}"
            android:visibility="@{CheckUtils.isNullOrEmpty(data.money)?View.GONE:View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_title" />

        <TextView
            android:id="@+id/tv_address"
            style="@style/Text.14sp.888888"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_4"
            android:text="@{data.cityName+data.address}"
            android:visibility="@{CheckUtils.isNullOrEmpty(data.address)?View.GONE:View.VISIBLE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <View
            style="@style/Line.Horizontal"
            android:layout_marginTop="@dimen/dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_address"
            app:layout_goneMarginTop="@dimen/dp_16" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>