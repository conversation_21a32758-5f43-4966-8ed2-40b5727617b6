<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="com.bxkj.common.util.HtmlUtils" />

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.personal.data.SeenMeBusinessData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_wrap"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:padding="@dimen/dp_14">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_company_logo"
            android:layout_width="@dimen/dp_48"
            android:layout_height="48dp"
            android:padding="@dimen/dp_1"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/roundedCornerImageStyle.Avatar"
            bind:imgUrl="@{data.company.logo}" />

        <ImageView
            android:layout_width="@dimen/dp_8"
            android:layout_height="@dimen/dp_8"
            android:src="@drawable/shape_red_point"
            android:visibility="@{data.viewed?View.GONE:View.VISIBLE}"
            app:layout_constraintEnd_toEndOf="@id/iv_company_logo"
            app:layout_constraintTop_toTopOf="@id/iv_company_logo" />

        <TextView
            android:id="@+id/tv_company_name"
            style="@style/Text.16sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginEnd="@dimen/dp_18"
            android:layout_marginStart="@dimen/dp_12"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.company.name}"
            app:layout_constraintEnd_toStartOf="@id/tv_date"
            app:layout_constraintStart_toEndOf="@id/iv_company_logo"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_company_about"
            style="@style/Text.12sp.888888"
            android:layout_width="@dimen/dp_0"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.company.about}"
            app:layout_constraintBottom_toBottomOf="@id/iv_company_logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_company_name" />

        <TextView
            android:id="@+id/tv_date"
            style="@style/Text.12sp.888888"
            android:text="@{data.date}"
            app:layout_constraintBottom_toBottomOf="@id/tv_company_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_company_name" />

        <TextView
            android:id="@+id/tv_release_job_info"
            style="@style/Text.14sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_12"
            android:drawableEnd="@drawable/common_ic_next"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{HtmlUtils.fromHtml(@string/personal_seen_me_business_job_info_format(data.relName,data.relCount))}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_company_about"
            app:layout_constraintTop_toBottomOf="@id/iv_company_logo" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>