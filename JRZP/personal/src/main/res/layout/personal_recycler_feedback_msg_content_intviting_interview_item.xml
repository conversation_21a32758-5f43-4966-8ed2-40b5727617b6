<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:layout_marginTop="@dimen/dp_8"
    android:layout_marginBottom="@dimen/dp_8"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_12"
    android:paddingEnd="@dimen/dp_12">

    <TextView
        android:id="@+id/tv_date"
        style="@style/Text.10sp.333333"
        android:layout_marginBottom="@dimen/dp_10"
        android:background="@color/common_ececec"
        android:paddingStart="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_4" />

    <LinearLayout
        style="@style/match_wrap"
        android:background="@drawable/bg_ffffff_radius_4"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_msg_type"
            style="@style/Text.16sp.333333"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16" />

        <TextView
            android:id="@+id/tv_content"
            style="@style/Text.15sp.333333"
            android:layout_width="match_parent"
            android:padding="@dimen/dp_16" />

        <LinearLayout
            android:id="@+id/ll_interview_content"
            style="@style/match_wrap"
            android:layout_marginBottom="@dimen/dp_16"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_time"
                style="@style/Text.14sp.333333"
                android:layout_width="match_parent"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_16" />

            <TextView
                android:id="@+id/tv_address"
                style="@style/Text.14sp.333333"
                android:layout_width="match_parent"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_4"
                android:layout_marginEnd="@dimen/dp_16" />

            <TextView
                android:id="@+id/tv_contract"
                style="@style/Text.14sp.333333"
                android:layout_width="match_parent"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_4"
                android:layout_marginEnd="@dimen/dp_16" />

            <TextView
                android:id="@+id/tv_phone"
                style="@style/Text.14sp.333333"
                android:layout_width="match_parent"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_4"
                android:layout_marginEnd="@dimen/dp_16" />
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rl_confirm_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:visibility="gone">

            <View
                style="@style/Line.Horizontal"
                android:background="@color/commmon_f2f2f2" />

            <TextView
                android:id="@+id/tv_confirm"
                style="@style/match_match"
                android:gravity="center"
                android:text="@string/personal_deliver_now"
                android:textColor="@color/common_888888"
                android:textSize="@dimen/dp_16" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_cancel_or_accept_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:visibility="gone">

            <View
                style="@style/Line.Horizontal"
                android:background="@color/commmon_f2f2f2" />

            <TextView
                android:id="@+id/tv_cancel"
                style="@style/Text.16sp.888888"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:layout_toStartOf="@id/v_line"
                android:gravity="center" />

            <View
                android:id="@+id/v_line"
                style="@style/Line.Vertical"
                android:layout_centerHorizontal="true" />

            <TextView
                android:id="@+id/tv_accept"
                style="@style/Text.16sp.ff7647"
                android:layout_width="@dimen/dp_0"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_toEndOf="@id/v_line"
                android:gravity="center" />
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>