<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="data"
            type="com.bxkj.personal.data.FamousCompanyData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_match">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_logo"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_6"
            android:layout_marginStart="@dimen/dp_6"
            android:background="@drawable/bg_ffffff"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toTopOf="@id/tv_name"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:shapeAppearance="@style/roundedCornerImageStyle"
            bind:imgUrl="@{data.fullImgUrl}" />

        <TextView
            android:id="@+id/tv_name"
            style="@style/Text.10sp.FE6600"
            android:layout_marginEnd="@dimen/dp_2"
            android:layout_marginStart="@dimen/dp_2"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.name}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_logo" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>