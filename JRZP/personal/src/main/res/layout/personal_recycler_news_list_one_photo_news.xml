<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.CheckUtils" />

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.personal.data.NewsItemData" />
  </data>

  <!--资讯列表资讯一张图片-->
  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_14"
    android:paddingEnd="@dimen/dp_14">

    <de.hdodenhof.circleimageview.CircleImageView
      android:id="@+id/iv_logo"
      android:layout_width="@dimen/common_dp_32"
      android:layout_height="@dimen/common_dp_32"
      android:layout_marginTop="@dimen/dp_16"
      bind:imgUrl="@{data.dwLogo}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.14sp.333333.Bold"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_14"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.dwName}"
      app:layout_constraintBottom_toTopOf="@id/tv_publish_time"
      app:layout_constraintEnd_toStartOf="@id/tv_follow"
      app:layout_constraintStart_toEndOf="@id/iv_logo"
      app:layout_constraintTop_toTopOf="@id/iv_logo" />

    <TextView
      android:id="@+id/tv_publish_time"
      style="@style/Text.12sp.999999"
      android:text="@{data.createTime}"
      app:layout_constraintBottom_toBottomOf="@id/iv_logo"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_follow"
      style="@style/Text.14sp"
      android:text="@{data.followDW?@string/question_details_answerer_followed:@string/question_details_answerer_follow}"
      android:textColor="@{data.followDW?@color/cl_999999:@color/cl_ff7405}"
      app:layout_constraintBottom_toBottomOf="@id/tv_name"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_name" />

    <androidx.constraintlayout.widget.Group
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:visibility="@{data.normalLayout?View.GONE:View.VISIBLE}"
      app:constraint_referenced_ids="iv_logo,tv_name,tv_publish_time,tv_follow" />

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.17sp.000000"
      android:layout_width="@dimen/dp_0"
      android:layout_marginEnd="@dimen/dp_8"
      android:ellipsize="end"
      android:lineSpacingExtra="@dimen/dp_3"
      android:maxLines="2"
      android:text="@{data.title}"
      app:layout_constraintEnd_toStartOf="@id/iv_photo"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="@id/iv_photo" />

    <TextView
      android:id="@+id/tv_author"
      style="@style/Text.12sp.999999"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_16"
      android:ellipsize="end"
      android:lines="1"
      android:maxWidth="80dp"
      android:text="@{data.dwName}"
      android:visibility="@{CheckUtils.isNullOrEmpty(data.dwName)?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title"
      app:layout_constraintVertical_bias="1" />

    <TextView
      android:id="@+id/tv_type"
      style="@style/Text.12sp.999999"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_16"
      android:text="@{data.subTypeName}"
      android:visibility="@{CheckUtils.isNullOrEmpty(data.subTypeName)?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toEndOf="@id/tv_author"
      app:layout_constraintTop_toBottomOf="@id/tv_title"
      app:layout_constraintVertical_bias="1"
      app:layout_goneMarginStart="@dimen/dp_0" />

    <TextView
      android:id="@+id/tv_comment_count"
      style="@style/Text.12sp.999999"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_16"
      android:text="@{@string/home_news_comment_count_format(data.commentsCount)}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toEndOf="@id/tv_type"
      app:layout_constraintTop_toBottomOf="@id/tv_title"
      app:layout_constraintVertical_bias="1" />

    <TextView
      android:id="@+id/tv_publish_date"
      style="@style/Text.12sp.999999"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_16"
      android:text="@{data.createTime}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toEndOf="@id/tv_comment_count"
      app:layout_constraintTop_toBottomOf="@id/tv_title"
      app:layout_constraintVertical_bias="1" />

    <androidx.constraintlayout.widget.Group
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:visibility="@{data.normalLayout?View.VISIBLE:View.GONE}"
      app:constraint_referenced_ids="tv_author,tv_type,tv_comment_count,tv_publish_date" />

    <ImageView
      android:id="@+id/iv_photo"
      android:layout_width="114dp"
      android:layout_height="76dp"
      android:layout_marginTop="@dimen/dp_12"
      android:layout_marginBottom="@dimen/dp_12"
      android:scaleType="centerCrop"
      bind:imgUrl="@{data.listMedia[0].url}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toBottomOf="@id/iv_logo"
      app:layout_goneMarginTop="@dimen/dp_12" />

    <androidx.constraintlayout.widget.Group
      android:id="@+id/group_options"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:visibility="@{data.normalLayout?View.GONE:View.VISIBLE}"
      app:constraint_referenced_ids="tv_views,tv_comment,tv_like" />

    <Space
      android:visibility="@{data.normalLayout?View.VISIBLE:View.GONE}"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_12"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/iv_photo" />

    <TextView
      android:id="@+id/tv_views"
      style="@style/Text.12sp.333333"
      android:layout_marginStart="@dimen/dp_36"
      android:drawableStart="@drawable/ic_user_notice_views"
      android:drawablePadding="@dimen/dp_4"
      android:paddingTop="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_12"
      android:text="@{String.valueOf(data.count)}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toStartOf="@id/tv_comment"
      app:layout_constraintHorizontal_chainStyle="spread_inside"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/iv_photo" />

    <TextView
      android:id="@+id/tv_comment"
      style="@style/Text.12sp.333333"
      android:drawableStart="@drawable/ic_user_notice_comment"
      android:drawablePadding="@dimen/dp_4"
      android:paddingTop="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_12"
      android:text="@{String.valueOf(data.commentsCount)}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_views"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toStartOf="@id/tv_like"
      app:layout_constraintStart_toEndOf="@id/tv_views"
      app:layout_constraintTop_toBottomOf="@id/iv_photo" />

    <TextView
      android:id="@+id/tv_like"
      style="@style/Text.12sp.333333"
      android:layout_marginEnd="@dimen/dp_36"
      android:drawableStart="@{data.like?@drawable/ic_user_notice_like_sel:@drawable/ic_user_notice_like_nor}"
      android:drawablePadding="@dimen/dp_4"
      android:paddingTop="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_12"
      android:text="@{String.valueOf(data.likesCount)}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_comment"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toEndOf="@id/tv_comment"
      app:layout_constraintTop_toBottomOf="@id/iv_photo" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
