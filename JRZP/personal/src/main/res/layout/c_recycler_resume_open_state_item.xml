<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.personal.ui.activity.resumeopenstatesetting.ResumeOpenState" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/dp_14">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.16sp.333333"
      android:text="@{data.title}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_desc"
      style="@style/Text.12sp.888888"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_12"
      android:text="@{data.desc}"
      app:layout_constrainedWidth="true"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toStartOf="@id/iv_selected"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <ImageView
      android:id="@+id/iv_selected"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:src="@drawable/ic_selected"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>