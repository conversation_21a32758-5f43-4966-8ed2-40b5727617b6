<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

  <data>

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.UserUtils" />

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="data"
      type="com.bxkj.jrzp.support.chat.data.ChatMsgItemData" />
  </data>

  <LinearLayout
    style="@style/match_wrap"
    android:layout_marginBottom="@dimen/dp_8"
    android:orientation="vertical">

    <include layout="@layout/layout_conversation_time_tag" />

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
      style="@style/match_wrap"
      android:background="@drawable/bg_ffffff_radius_4"
      android:orientation="vertical"
      android:paddingStart="@dimen/dp_14"
      android:paddingTop="@dimen/dp_14"
      android:paddingEnd="@dimen/dp_14">

      <LinearLayout
        style="@style/match_wrap"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
          style="@style/Text.16sp.333333.Bold"
          android:layout_marginEnd="@dimen/dp_16"
          android:layout_weight="1"
          android:text="@{data.relInfo.relName}" />

        <TextView
          style="@style/Text.14sp.FE6600.Bold"
          android:text="@{data.relInfo.money}" />
      </LinearLayout>

      <TextView
        style="@style/Text.12sp.999999"
        android:layout_marginTop="@dimen/dp_2"
        android:text="@{data.relInfo.companyName}" />

      <TextView
        style="@style/Text.12sp.999999"
        android:layout_marginTop="@dimen/dp_4"
        android:text="@{data.relInfo.jobAbout}" />

      <TextView
        style="@style/Text.14sp.333333"
        android:layout_marginTop="@dimen/dp_12"
        android:text="@string/chat_job_desc"
        android:textStyle="bold" />

      <TextView
        style="@style/Text.12sp.333333"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="3"
        android:text="@{HtmlUtils.fromHtml(data.relInfo.description)}" />

      <View style="@style/Line.Horizontal" />

      <FrameLayout
        android:id="@+id/fl_send_resume"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48">

        <TextView
          android:id="@+id/tv_send_resume"
          style="@style/Text.16sp.ff7647"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_gravity="center"
          android:background="@drawable/frame_fe6600_radius_2"
          android:gravity="center"
          android:paddingStart="@dimen/dp_16"
          android:paddingTop="@dimen/dp_4"
          android:paddingEnd="@dimen/dp_16"
          android:paddingBottom="@dimen/dp_4"
          android:text="@string/chat_send_resume" />
      </FrameLayout>

    </LinearLayout>

  </LinearLayout>
</layout>