<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.fragment.campusrecruit.CampusRecruitListViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_fff8f2"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_filter_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/dp_6"
            android:paddingEnd="@dimen/dp_16"
            android:paddingStart="@dimen/dp_16"
            android:paddingTop="@dimen/dp_6">

            <FrameLayout
                android:id="@+id/fl_filter_school"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_ffffff_radius_4"
                android:onClick="@{onClickListener}">

                <TextView
                    style="@style/Text.14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingBottom="@dimen/dp_4"
                    android:paddingEnd="@dimen/dp_8"
                    android:paddingStart="@dimen/dp_8"
                    android:paddingTop="@dimen/dp_4"
                    android:text="@string/campus_talk_school_filter_text"
                    android:textColor="@color/cl_333333_to_fe6600_selector" />

                <ImageView
                    style="@style/wrap_wrap"
                    android:layout_gravity="end|bottom"
                    android:src="@drawable/personal_ic_part_time_job_filter_more" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_filter_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:background="@drawable/bg_ffffff_radius_4"
                android:onClick="@{onClickListener}">

                <TextView
                    style="@style/Text.14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingBottom="@dimen/dp_4"
                    android:paddingEnd="@dimen/dp_8"
                    android:paddingStart="@dimen/dp_8"
                    android:paddingTop="@dimen/dp_4"
                    android:text="@string/campus_talk_time_filter_text"
                    android:textColor="@color/cl_333333_to_fe6600_selector" />

                <ImageView
                    style="@style/wrap_wrap"
                    android:layout_gravity="end|bottom"
                    android:src="@drawable/personal_ic_part_time_job_filter_more" />

            </FrameLayout>

        </LinearLayout>

        <com.sanjindev.pagestatelayout.PageStateLayout
            android:id="@+id/psl_container"
            style="@style/match_match">

            <com.scwang.smartrefresh.layout.SmartRefreshLayout
                android:id="@+id/srl_content"
                style="@style/match_match"
                app:srlEnableLoadMore="false">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_campus_talk"
                    style="@style/match_match" />
            </com.scwang.smartrefresh.layout.SmartRefreshLayout>

        </com.sanjindev.pagestatelayout.PageStateLayout>
    </LinearLayout>
</layout>