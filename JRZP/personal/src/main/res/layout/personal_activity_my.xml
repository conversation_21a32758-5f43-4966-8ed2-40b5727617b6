<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.myhistory.MyHistoryViewModel" />
    </data>

    <LinearLayout
        style="@style/match_match"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_back"
                style="@style/wrap_wrap"
                android:onClick="@{onClickListener}"
                android:src="@drawable/common_ic_back" />

            <Space
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_0"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tv_edit"
                style="@style/Text.15sp"
                android:layout_marginEnd="@dimen/dp_14"
                android:enabled="@{viewModel.editEnableStatus[viewModel.currentPage]}"
                android:onClick="@{onClickListener}"
                android:text="@{viewModel.openEdit?@string/common_cancel:@string/common_edit}"
                android:textColor="@{viewModel.editEnableStatus[viewModel.currentPage]?@color/cl_333333:@color/common_b5b5b5}" />
        </LinearLayout>

        <View style="@style/Line.Horizontal" />

        <net.lucode.hackware.magicindicator.MagicIndicator
            android:id="@+id/indicator"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_36" />

        <View style="@style/Line.Horizontal" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp_content"
            style="@style/match_match" />

    </LinearLayout>
</layout>