<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:background="@drawable/bg_ffffff"
    android:orientation="vertical">

    <include layout="@layout/common_include_title_bar" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_area"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_10"
        android:layout_weight="1" />

    <View style="@style/Line.Horizontal" />

    <LinearLayout
        android:id="@+id/ll_options_bar_one"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_52"
        android:background="@color/common_fbfbfb"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_select_area_tips"
            style="@style/Text.15sp.888888"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/common_dp_32"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_select_area_confirm"
            style="@style/Button.Basic"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/common_dp_42"
            android:layout_marginEnd="@dimen/dp_14"
            android:layout_weight="1"
            android:text="@string/common_confirm"
            android:textSize="@dimen/dp_16" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_options_bar_two"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_52"
        android:background="@drawable/bg_ffffff"
        android:backgroundTint="@color/common_fbfbfb"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="gone">

        <TextView
            style="@style/Text.15sp"
            android:layout_marginStart="@dimen/dp_12"
            android:text="@string/personal_now_city"
            android:textColor="@color/common_888888" />

        <TextView
            android:id="@+id/tv_current_city"
            style="@style/Text.15sp"
            android:layout_width="@dimen/dp_0"
            android:layout_weight="1"
            android:textColor="@color/cl_ff7405" />

        <TextView
            android:id="@+id/tv_chang_city"
            style="@style/Text.15sp"
            android:layout_marginEnd="@dimen/dp_30"
            android:drawableEnd="@drawable/personal_ic_change_now_city"
            android:drawablePadding="@dimen/common_dp_5"
            android:text="@string/personal_change_city"
            android:textColor="@color/common_888888" />
    </LinearLayout>
</LinearLayout>