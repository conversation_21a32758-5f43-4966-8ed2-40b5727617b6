<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.jrzp.user.data.JobData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:layout_marginStart="@dimen/dp_14"
    android:layout_marginEnd="@dimen/dp_14"
    android:background="@drawable/bg_ffffff_radius_6"
    android:paddingStart="@dimen/dp_8"
    android:paddingTop="@dimen/dp_12"
    android:paddingEnd="@dimen/dp_8"
    android:paddingBottom="@dimen/dp_12">

    <TextView
      android:id="@+id/tv_real_job_tag"
      style="@style/Text.10sp.FFFFFF"
      android:layout_marginEnd="@dimen/dp_12"
      android:background="@drawable/bg_93ddb5_radius_4"
      android:paddingStart="@dimen/dp_4"
      android:paddingTop="@dimen/dp_2"
      android:paddingEnd="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_2"
      android:text="急速入职"
      android:visibility="gone"
      app:layout_constraintBottom_toBottomOf="@id/tv_name"
      app:layout_constraintEnd_toStartOf="@id/tv_salary"
      app:layout_constraintStart_toEndOf="@id/tv_name"
      app:layout_constraintTop_toTopOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.16sp.333333.Bold"
      android:layout_marginEnd="@dimen/dp_4"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.name}"
      app:layout_constrainedWidth="true"
      app:layout_constraintEnd_toStartOf="@id/tv_real_job_tag"
      app:layout_constraintHorizontal_bias="0"
      app:layout_constraintHorizontal_chainStyle="packed"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_salary"
      style="@style/Text.16sp.ff7647"
      android:text="@{data.convertSalary}"
      app:layout_constraintBottom_toBottomOf="@id/tv_name"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_name" />

    <com.bxkj.common.widget.labellayout.LabelLayout
      android:id="@+id/tv_desc"
      android:layout_width="@dimen/dp_0"
      android:layout_height="wrap_content"
      android:layout_marginTop="@dimen/dp_4"
      android:layout_marginEnd="@dimen/dp_12"
      app:itemBackground="@drawable/bg_f4f4f4_radius_2"
      app:itemMargin="@dimen/dp_4"
      app:itemPaddingLR="@dimen/dp_6"
      app:itemPaddingTB="@dimen/dp_2"
      app:itemTextColor="@color/common_888888"
      app:itemTextSize="@dimen/dp_12"
      app:layout_constraintEnd_toStartOf="@id/tv_application"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_name"
      bind:items="@{data.jobParams}" />

    <TextView
      android:id="@+id/tv_time"
      style="@style/Text.12sp.888888"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_4"
      android:drawableStart="@drawable/common_ic_job_publish_date"
      android:drawablePadding="@dimen/dp_4"
      android:text="@{data.lastUpdateTime}"
      android:visibility="@{data.hasLastUpdateTime()?View.VISIBLE:View.GONE}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_desc" />

    <TextView
      android:id="@+id/tv_company_name"
      style="@style/Text.14sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_12"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.comName}"
      app:layout_constraintEnd_toStartOf="@id/tv_distance"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_time"
      app:layout_goneMarginEnd="0dp" />

    <TextView
      android:id="@+id/tv_application"
      style="@style/Button.Basic"
      android:layout_width="@dimen/dp_52"
      android:layout_height="@dimen/common_dp_28"
      android:enabled="@{!data.applied}"
      android:gravity="center"
      android:text="@{data.applicationStatusText}"
      android:textSize="@dimen/common_sp_12"
      app:layout_constraintBottom_toBottomOf="@id/tv_time"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_desc" />

    <TextView
      android:id="@+id/tv_distance"
      style="@style/common_Text.14sp.666666"
      android:text="@{data.distanceText}"
      android:visibility="@{data.distanceText==null?View.GONE:View.VISIBLE}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_company_name"
      app:layout_constraintEnd_toEndOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>