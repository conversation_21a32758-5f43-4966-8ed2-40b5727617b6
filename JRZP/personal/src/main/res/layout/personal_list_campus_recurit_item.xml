<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="data"
            type="com.bxkj.personal.data.CampusRecruitData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_wrap"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_marginEnd="@dimen/dp_16"
        android:layout_marginStart="@dimen/common_dp_16"
        android:background="@drawable/bg_ffffff_radius_6"
        android:padding="@dimen/dp_8">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_company_logo"
            android:layout_width="@dimen/common_dp_44"
            android:layout_height="@dimen/common_dp_44"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/roundedCornerImageStyle.Avatar"
            bind:imgUrl="@{data.comLogo}" />

        <TextView
            android:id="@+id/tv_company_name"
            style="@style/common_Text.16sp.333333"
            android:layout_width="0dp"
            android:layout_marginStart="@dimen/dp_12"
            android:text="@{data.title}"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_company_logo"
            app:layout_constraintTop_toTopOf="@id/iv_company_logo" />

        <TextView
            android:id="@+id/tv_date"
            style="@style/Text.14sp.B5B5B5"
            android:layout_marginTop="@dimen/dp_4"
            android:text="@{data.createTime}"
            app:layout_constraintStart_toStartOf="@id/tv_company_name"
            app:layout_constraintTop_toBottomOf="@id/tv_company_name" />

        <View
            android:id="@+id/v_split"
            style="@style/Line.Vertical.Light"
            android:layout_height="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_10"
            app:layout_constraintBottom_toBottomOf="@id/tv_date"
            app:layout_constraintStart_toEndOf="@id/tv_date"
            app:layout_constraintTop_toTopOf="@id/tv_date" />

        <TextView
            android:id="@+id/tv_city"
            style="@style/Text.14sp.B5B5B5"
            android:layout_width="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_10"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.shengstr}"
            app:layout_constraintBottom_toBottomOf="@id/tv_date"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/v_split"
            app:layout_constraintTop_toTopOf="@id/tv_date" />

        <com.bxkj.common.widget.labellayout.LabelLayout
            android:id="@+id/labs_expect"
            style="@style/match_wrap"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_4"
            app:itemBackground="@drawable/bg_f4f4f4_radius_2"
            app:itemMargin="@dimen/dp_4"
            app:itemPaddingLR="@dimen/dp_6"
            app:itemPaddingTB="@dimen/dp_2"
            app:itemTextSize="12sp"
            app:items="@{data.convertJobsText}"
            app:layout_constraintEnd_toEndOf="@id/tv_company_name"
            app:layout_constraintStart_toStartOf="@id/tv_company_name"
            app:layout_constraintTop_toBottomOf="@id/tv_date" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>