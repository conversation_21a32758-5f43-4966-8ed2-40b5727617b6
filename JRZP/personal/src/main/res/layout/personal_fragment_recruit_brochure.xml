<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.campusrecruitdetails.RecruitBrochureViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scroll_content"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            android:layout_weight="1">

            <LinearLayout
                style="@style/match_match"
                android:orientation="vertical">

                <com.bxkj.common.widget.YUIWebView
                    android:id="@+id/web_brochure"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:layout_marginStart="@dimen/dp_8"
                    android:background="@drawable/bg_ffffff_radius_4" />

                <TextView
                    style="@style/Text.16sp.333333"
                    android:layout_marginBottom="@dimen/dp_8"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/personal_campus_recruit_job_text" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_job_list"
                    style="@style/match_wrap"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:layout_marginStart="@dimen/dp_8"
                    android:background="@drawable/bg_ffffff_radius_4"
                    bind:items="@{viewModel.campusRecruitJobList}" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <TextView
            android:id="@+id/tv_view_job_list"
            style="@style/Text.16sp.FFFFFF"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:background="@drawable/bg_ff7405"
            android:gravity="center"
            android:text="@string/personal_campus_recruit_job_view_job_list" />
    </LinearLayout>

</layout>