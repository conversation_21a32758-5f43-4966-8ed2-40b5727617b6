<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="data"
      type="com.bxkj.personal.data.DiscussData" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/dp_24"
    android:paddingTop="@dimen/dp_4"
    android:paddingEnd="@dimen/dp_24"
    android:paddingBottom="@dimen/dp_4">

    <TextView
      android:id="@+id/tv_rank"
      style="@style/Text.16sp.333333.Bold" />

    <TextView
      style="@style/Text.12sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_8"
      android:layout_weight="1"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.title}" />

    <ImageView
      android:id="@+id/iv_tag"
      style="@style/wrap_wrap"
      android:src="@drawable/personal_ic_hot_discuss" />

  </LinearLayout>
</layout>