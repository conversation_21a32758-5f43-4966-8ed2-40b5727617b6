<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:id="@+id/cl_contract_info_container"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  style="@style/match_wrap"
  android:paddingBottom="@dimen/dp_16">

  <View
    android:id="@+id/v_line_three"
    style="@style/Line.JobDetailsDivider"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

  <com.bxkj.common.widget.ZPTextView
    android:id="@+id/tv_contract_way"
    style="@style/Text.JobDetailsItemTitle"
    android:text="@string/common_contract_way"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/v_line_three" />

  <TextView
    android:id="@+id/tv_click_to_view"
    style="@style/Text.14sp.FFFFFF"
    android:layout_height="@dimen/dp_36"
    android:layout_marginTop="@dimen/dp_16"
    android:background="@drawable/bg_ff7405_radius_4"
    android:gravity="center"
    android:paddingStart="@dimen/dp_12"
    android:paddingEnd="@dimen/dp_12"
    android:visibility="gone"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/tv_contract_way" />

  <androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/cl_contract_info"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp_16"
    app:layout_constraintTop_toBottomOf="@id/tv_contract_way">

    <TextView
      android:id="@+id/tv_contracts"
      style="@style/Text.16sp.333333"
      android:layout_marginStart="@dimen/dp_12"
      android:drawableStart="@drawable/personal_ic_job_desc_contracts"
      android:drawablePadding="@dimen/common_dp_5"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_contract_phone"
      style="@style/Text.16sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_22"
      android:layout_marginEnd="@dimen/dp_24"
      android:drawableStart="@drawable/personal_ic_job_desc_contract_phone"
      android:drawablePadding="@dimen/common_dp_5"
      android:ellipsize="end"
      android:lines="1"
      android:text="@string/personal_content_contract_phone"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_contracts"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toEndOf="@id/tv_contracts" />

    <TextView
      android:id="@+id/tv_contract_QQ"
      style="@style/Text.16sp.333333"
      android:layout_marginTop="@dimen/dp_16"
      android:drawableStart="@drawable/personal_ic_job_desc_contract_qq"
      android:drawablePadding="@dimen/common_dp_5"
      android:text="@string/personal_content_contract_QQ"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_contracts"
      app:layout_constraintTop_toBottomOf="@id/tv_contracts" />
  </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>