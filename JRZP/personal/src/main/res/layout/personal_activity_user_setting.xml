<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.usersetting.UserSettingViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      style="@style/match_wrap"
      app:title="@string/user_setting_page_title" />

    <LinearLayout
      android:id="@+id/ll_user_status"
      style="@style/Layout.InfoItem"
      android:onClick="@{()->viewModel.showUserStatusPicker()}">

      <TextView
        style="@style/Text.16sp.333333"
        android:text="@string/user_setting_status" />

      <TextView
        style="@style/Text.InfoItem.Select"
        android:text="@{viewModel.userStatus.stateName}" />
    </LinearLayout>

    <View style="@style/Line.Horizontal" />

    <LinearLayout
      android:id="@+id/ll_shield_company"
      style="@style/Layout.InfoItem"
      android:onClick="@{onClickListener}">

      <TextView
        style="@style/Text.16sp.333333"
        android:text="@string/user_setting_shield_company" />

      <TextView
        style="@style/Text.InfoItem.Select"
        android:hint="@string/empty" />
    </LinearLayout>

    <View style="@style/Line.Horizontal" />

  </LinearLayout>
</layout>