<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <import type="com.bxkj.common.util.CheckUtils" />

        <variable
            name="data"
            type="com.bxkj.personal.data.VideoItemData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_8">

        <TextView
            style="@style/Text.16sp.333333"
            android:layout_width="0dp"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginEnd="@dimen/dp_8"
            android:ellipsize="end"
            android:lineSpacingExtra="@dimen/dp_3"
            android:maxLines="2"
            android:text="@{data.title}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_photo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/dp_14" />

        <TextView
            android:id="@+id/tv_author"
            style="@style/Text.12sp.999999"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_8"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.userName}"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_views_count"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tv_views_count"
            style="@style/Text.12sp.999999"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_12"
            android:text="@{@string/video_details_recommend_views_count_format(data.view)}"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_author"
            app:layout_constraintEnd_toStartOf="@id/iv_photo"
            app:layout_constraintStart_toEndOf="@id/tv_author" />

        <ImageView
            android:id="@+id/iv_photo"
            android:layout_width="114dp"
            android:layout_height="74dp"
            android:layout_marginEnd="@dimen/dp_14"
            android:layout_marginBottom="@dimen/dp_8"
            android:scaleType="centerCrop"
            android:visibility="@{CheckUtils.isNullOrEmpty(data.pic)?View.GONE:View.VISIBLE}"
            bind:imgUrl="@{data.pic}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            style="@style/Line.Horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>