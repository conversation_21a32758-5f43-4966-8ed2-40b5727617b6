<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_16"
    android:paddingBottom="@dimen/dp_16">

    <TextView
        android:id="@+id/tv_start_to_end_time"
        style="@style/Text.14sp.888888"
        android:layout_marginTop="@dimen/dp_16"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_about"
        style="@style/Text.14sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_marginTop="@dimen/common_dp_5"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintEnd_toStartOf="@id/tv_delete"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_start_to_end_time" />

    <TextView
        android:id="@+id/tv_delete"
        style="@style/Text.12sp.767676"
        android:layout_marginEnd="@dimen/dp_12"
        android:text="@string/common_delete"
        app:layout_constraintEnd_toStartOf="@id/v_line"
        app:layout_constraintBottom_toBottomOf="@id/iv_edit"
        app:layout_constraintTop_toTopOf="@id/iv_edit" />

    <View
        android:id="@+id/v_line"
        style="@style/Line.Vertical"
        android:layout_height="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_2"
        app:layout_constraintBottom_toBottomOf="@id/iv_edit"
        app:layout_constraintEnd_toStartOf="@id/iv_edit"
        app:layout_constraintTop_toTopOf="@id/iv_edit" />

    <ImageView
        android:id="@+id/iv_edit"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_2"
        android:src="@drawable/ic_create_resume_edit_work_exp"
        app:layout_constraintBottom_toBottomOf="@id/tv_start_to_end_time"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_start_to_end_time" />

</androidx.constraintlayout.widget.ConstraintLayout>