<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.myresume.workexp.EditWorkExpViewModel" />
  </data>

  <LinearLayout
    style="@style/match_wrap"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_44"
      app:right_text="@string/save"
      app:title="@string/personal_work_exp" />

    <TextView
      style="@style/Text.InputTips"
      android:text="@string/personal_required_text" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/JrzpCommonListItemView"
      app:yui_accessory_type="chevron"
      app:yui_content="@{viewModel.workExpData.date1}"
      app:yui_content_hint="@string/common_please_select"
      android:onClick="@{()->viewModel.showStartDatePicker()}"
      app:yui_content_type="text"
      app:yui_title="@string/personal_start_time" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/JrzpCommonListItemView"
      app:yui_accessory_type="chevron"
      app:yui_content="@{viewModel.workExpData.date2}"
      app:yui_content_hint="@string/common_please_select"
      android:onClick="@{()->viewModel.showEndDatePicker()}"
      app:yui_content_type="text"
      app:yui_title="@string/personal_end_time" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/JrzpCommonListItemView"
      app:yui_content="@={viewModel.workExpData.coname}"
      app:yui_content_hint="@string/please_enter"
      app:yui_content_type="edit"
      app:yui_title="@string/personal_company_name" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/JrzpCommonListItemView"
      app:yui_content="@={viewModel.workExpData.job}"
      app:yui_content_hint="@string/please_enter"
      app:yui_content_type="edit"
      app:yui_title="@string/personal_position_name" />

    <com.sanjindev.mui.weiget.commonitemview.YUICommonListItemView
      style="@style/JrzpCommonListItemView"
      app:yui_accessory_type="chevron"
      app:yui_content="@{viewModel.workExpData.des}"
      app:yui_content_hint="@string/please_enter"
      app:yui_content_type="text"
      android:onClick="@{()->viewModel.toEditJobDesc()}"
      app:yui_title="@string/personal_work_desc" />

    <TextView
      android:id="@+id/tv_delete_this"
      style="@style/Text.DeleteResumeItem"
      android:text="@string/personal_delete_this_work_exp"
      android:visibility="@{viewModel.editMode?View.VISIBLE:View.GONE}" />
  </LinearLayout>

</layout>