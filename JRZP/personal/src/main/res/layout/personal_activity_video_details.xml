<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.videodetails.VideoDetailsViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      android:layout_width="match_parent"
      android:background="@color/cl_transparent">

      <FrameLayout
        android:id="@+id/fl_video_player"
        android:layout_width="match_parent"
        android:layout_height="212dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.bxkj.common.widget.MyVideoPlayer
          android:id="@+id/video_player"
          android:layout_width="match_parent"
          android:layout_height="212dp" />

      </FrameLayout>

      <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_dp_44"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/fl_video_player">

        <ImageView
          android:id="@+id/iv_back"
          style="@style/wrap_wrap"
          android:onClick="@{onClickListener}"
          android:src="@drawable/common_ic_back"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent" />

        <ImageView
          android:id="@+id/iv_more"
          style="@style/wrap_wrap"
          android:layout_marginEnd="@dimen/dp_12"
          android:onClick="@{onClickListener}"
          android:src="@drawable/ic_white_more"
          app:layout_constraintBottom_toBottomOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintTop_toTopOf="parent" />
      </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_52">

      <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/common_dp_32"
        android:layout_height="@dimen/common_dp_32"
        android:layout_marginStart="@dimen/dp_14"
        android:onClick="@{()->viewModel.toVideoAuthorPage()}"
        bind:imgErrorPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
        bind:imgPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
        bind:imgUrl="@{viewModel.videoInfo.userPhoto}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/roundedCornerImageStyle" />

      <TextView
        android:id="@+id/tv_name"
        style="@style/Text.13sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:ellipsize="end"
        android:lines="1"
        android:onClick="@{()->viewModel.toVideoAuthorPage()}"
        android:text="@{viewModel.videoInfo.userName}"
        app:layout_constraintBottom_toTopOf="@id/tv_fans_count"
        app:layout_constraintEnd_toStartOf="@id/tv_contract"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

      <TextView
        android:id="@+id/tv_fans_count"
        style="@style/Text.11sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_marginEnd="@dimen/dp_16"
        android:text="@{@string/video_details_fans_count_format(viewModel.videoInfo.fansCount)}"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toStartOf="@id/tv_contract"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tv_name" />

      <TextView
        android:id="@+id/tv_contract"
        style="@style/Text.12sp.FFFFFF"
        android:layout_height="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@drawable/bg_fa3a3b_round"
        android:gravity="center"
        android:onClick="@{onClickListener}"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:text="@string/video_details_contract"
        android:visibility="@{(viewModel.videoInfo.type==3)?View.VISIBLE:View.GONE}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_follow"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_follow"
        style="@style/Text.12sp"
        android:layout_width="@dimen/common_dp_60"
        android:layout_height="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_14"
        android:background="@{viewModel.videoInfo.isFollowUser?@drawable/frame_10c198_round:@drawable/bg_10c198_round}"
        android:gravity="center"
        android:onClick="@{onClickListener}"
        android:text="@{viewModel.videoInfo.isFollowUser?@string/video_list_followed:@string/video_list_follow}"
        android:textColor="@{viewModel.videoInfo.isFollowUser?@color/cl_ff7405:@color/common_white}"
        android:visibility="@{viewModel.videoInfo==null||viewModel.videoInfo.self?View.GONE:View.VISIBLE}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <View
        style="@style/Line.Horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
      layout="@layout/include_mvvm_refresh_layout"
      app:listViewModel="@{viewModel.commentListViewModel}" />

    <View style="@style/Line.Horizontal" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44"
      android:gravity="center_vertical"
      android:orientation="horizontal">

      <TextView
        android:id="@+id/tv_comment"
        style="@style/Text.12sp.888888"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_6"
        android:layout_weight="1"
        android:background="@drawable/bg_news_details_comment"
        android:drawableStart="@drawable/ic_news_details_comment_edit"
        android:drawablePadding="@dimen/dp_8"
        android:gravity="center_vertical"
        android:onClick="@{onClickListener}"
        android:paddingStart="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_8"
        android:text="@string/news_details_comment_hint" />

      <FrameLayout
        android:id="@+id/fl_comment"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp_24"
        android:onClick="@{onClickListener}">

        <ImageView
          style="@style/wrap_wrap"
          android:layout_gravity="center"
          android:layout_marginStart="@dimen/dp_8"
          android:layout_marginEnd="@dimen/dp_8"
          android:src="@drawable/ic_news_details_comment" />

        <TextView
          style="@style/wrap_wrap"
          android:layout_gravity="end"
          android:layout_marginTop="@dimen/dp_6"
          android:background="@drawable/bg_fe6600_round"
          android:paddingStart="@dimen/dp_4"
          android:paddingEnd="@dimen/dp_4"
          android:text="@{String.valueOf(viewModel.videoInfo.commentsCount)}"
          android:textColor="@color/common_white"
          android:textSize="8sp" />

      </FrameLayout>

      <ImageView
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_24"
        android:onClick="@{()->viewModel.addOrRemoveCollection()}"
        android:src="@{viewModel.videoInfo.stow?@drawable/ic_news_details_collect_selected:@drawable/ic_news_details_collect}" />

      <ImageView
        android:id="@+id/iv_like"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_24"
        android:onClick="@{onClickListener}"
        android:src="@{viewModel.videoInfo.isLike?@drawable/ic_moment_like_selected:@drawable/ic_moment_like_normal}" />

      <ImageView
        android:id="@+id/iv_share"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_16"
        android:onClick="@{onClickListener}"
        android:src="@drawable/ic_news_details_share" />

    </LinearLayout>

  </LinearLayout>
</layout>