<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsViewModel" />
    </data>

    <LinearLayout
        style="@style/match_match"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44">

            <ImageView
                android:id="@+id/iv_left"
                style="@style/wrap_wrap"
                android:onClick="@{onClickListener}"
                android:src="@drawable/common_ic_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/ll_title_info"
                style="@style/wrap_wrap"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    style="@style/wrap_wrap"
                    android:layout_height="@dimen/dp_22"
                    android:adjustViewBounds="true"
                    android:src="@drawable/ic_title_bar_logo" />

                <TextView
                    style="@style/wrap_wrap"
                    android:layout_marginStart="@dimen/dp_4"
                    android:layout_marginEnd="@dimen/dp_4"
                    android:text="@string/common_point"
                    android:textColor="@color/common_black"
                    android:textSize="@dimen/dp_20" />

                <TextView
                    style="@style/Text.16sp.000000"
                    android:text="@string/question_details_title" />
            </LinearLayout>

            <ImageView
                android:id="@+id/iv_more"
                style="@style/wrap_wrap"
                android:layout_marginEnd="@dimen/dp_8"
                android:minWidth="@dimen/dp_36"
                android:minHeight="@dimen/dp_36"
                android:onClick="@{onClickListener}"
                android:scaleType="centerInside"
                android:src="@drawable/ic_more_options"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                style="@style/Line.Horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.coordinatorlayout.widget.CoordinatorLayout style="@style/match_match">

            <include
                layout="@layout/include_mvvm_refresh_layout"
                app:listViewModel="@{viewModel.listViewModel}" />

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/iv_answer"
                style="@style/wrap_wrap"
                android:layout_gravity="end|bottom"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_marginBottom="@dimen/dp_70"
                android:onClick="@{()->viewModel.addAnswer()}"
                android:src="@drawable/ic_answer" />
        </androidx.coordinatorlayout.widget.CoordinatorLayout>

    </LinearLayout>
</layout>