<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:bind="http://schemas.android.com/tools">

    <data>

        <variable
            name="data"
            type="com.bxkj.personal.data.CampusRecruitData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_wrap"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_marginEnd="@dimen/dp_8"
        android:layout_marginStart="@dimen/dp_8"
        android:background="@drawable/bg_fff8ef_radius_8"
        android:paddingBottom="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_8"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_12">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_logo"
            android:layout_width="@dimen/common_dp_44"
            android:layout_height="@dimen/common_dp_44"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            bind:imgUrl="@{data.comLogo}" />

        <TextView
            android:id="@+id/tv_name"
            style="@style/Text.16sp.333333.Bold"
            android:layout_marginStart="@dimen/dp_12"
            android:text="@{data.comName}"
            app:layout_constraintBottom_toTopOf="@id/tv_job"
            app:layout_constraintStart_toEndOf="@id/iv_logo"
            app:layout_constraintTop_toTopOf="@id/iv_logo"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tv_job"
            style="@style/Text.14sp.666666"
            android:layout_width="@dimen/dp_0"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.appendJobText}"
            app:layout_constraintBottom_toBottomOf="@id/iv_logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_name"
            app:layout_constraintTop_toBottomOf="@id/tv_name" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>