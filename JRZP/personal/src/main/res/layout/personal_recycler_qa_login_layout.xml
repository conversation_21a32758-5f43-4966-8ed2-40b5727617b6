<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.bxkj.personal.data.QAUserData" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_user_info"
            style="@style/match_wrap">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/iv_user_avatar"
                android:layout_width="@dimen/common_dp_32"
                android:layout_height="@dimen/common_dp_32"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginTop="@dimen/dp_16"
                android:layout_marginBottom="@dimen/dp_16"
                bind:imgUrl="@{data.userPhoto}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_user_name"
                style="@style/Text.16sp.333333.Bold"
                android:layout_marginStart="@dimen/dp_8"
                android:text="@{data.userName}"
                app:layout_constraintStart_toEndOf="@id/iv_user_avatar"
                app:layout_constraintTop_toTopOf="@id/iv_user_avatar" />

            <TextView
                android:id="@+id/tv_views_count"
                style="@style/Text.12sp.888888"
                android:text="@{@string/qa_user_views_format(data.count)}"
                app:layout_constraintBottom_toBottomOf="@id/iv_user_avatar"
                app:layout_constraintStart_toStartOf="@id/tv_user_name"
                app:layout_constraintTop_toBottomOf="@id/tv_user_name" />

            <TextView
                android:id="@+id/tv_my_qa"
                style="@style/Text.14sp.888888"
                android:layout_marginEnd="@dimen/dp_12"
                android:background="@drawable/bg_e8e8e8_round"
                android:drawableEnd="@drawable/ic_small_more"
                android:drawablePadding="@dimen/dp_4"
                android:paddingStart="@dimen/dp_14"
                android:paddingTop="@dimen/dp_4"
                android:paddingEnd="@dimen/dp_14"
                android:paddingBottom="@dimen/dp_4"
                android:text="@string/qa_my_history"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View style="@style/Line.Horizontal" />

        <LinearLayout
            style="@style/match_wrap"
            android:orientation="horizontal">

            <com.bxkj.common.widget.DrawableCenterTextView
                android:id="@+id/tv_to_answer"
                style="@style/Text.16sp.333333.Bold"
                android:layout_weight="1"
                android:drawableStart="@drawable/ic_qa_answer"
                android:drawablePadding="@dimen/dp_8"
                android:paddingTop="@dimen/dp_12"
                android:paddingBottom="@dimen/dp_12"
                android:text="@string/qa_answer" />

            <View
                style="@style/Line.Vertical"
                android:layout_marginTop="@dimen/dp_6"
                android:layout_marginBottom="@dimen/dp_6" />

            <com.bxkj.common.widget.DrawableCenterTextView
                android:id="@+id/tv_add_question"
                style="@style/Text.16sp.333333.Bold"
                android:layout_weight="1"
                android:drawableStart="@drawable/ic_qa_add_question"
                android:drawablePadding="@dimen/dp_8"
                android:paddingTop="@dimen/dp_12"
                android:paddingBottom="@dimen/dp_12"
                android:text="@string/qa_add" />

            <View
                style="@style/Line.Vertical"
                android:layout_marginTop="@dimen/dp_6"
                android:layout_marginBottom="@dimen/dp_6" />

            <com.bxkj.common.widget.DrawableCenterTextView
                android:id="@+id/tv_rank_list"
                style="@style/Text.16sp.333333.Bold"
                android:layout_weight="1"
                android:drawableStart="@drawable/ic_qa_rank_list"
                android:drawablePadding="@dimen/dp_8"
                android:paddingTop="@dimen/dp_12"
                android:paddingBottom="@dimen/dp_12"
                android:text="@string/qa_rank_list" />
        </LinearLayout>

        <View
            style="@style/Line.Horizontal"
            android:layout_height="@dimen/dp_8" />

    </LinearLayout>
</layout>