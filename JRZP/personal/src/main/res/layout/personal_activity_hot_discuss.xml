<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.hotdiscuss.HotDiscussViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap" />

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
      android:id="@+id/srl_content"
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1"
      app:srlEnableRefresh="false">

      <com.donkingliang.consecutivescroller.ConsecutiveScrollerLayout style="@style/match_match">

        <TextView
          style="@style/Text.20sp.333333.Bold"
          android:padding="@dimen/dp_16"
          android:text="@{viewModel.discussInfo.title}" />

        <LinearLayout
          style="@style/wrap_wrap"
          android:layout_marginStart="@dimen/dp_16"
          android:layout_marginTop="@dimen/dp_10"
          android:layout_marginEnd="@dimen/dp_16"
          android:layout_marginBottom="@dimen/dp_10"
          android:gravity="center_vertical"
          android:onClick="@{()->viewModel.toAuthorHomePage()}"
          android:orientation="horizontal">

          <com.google.android.material.imageview.ShapeableImageView
            bind:imgUrl="@{viewModel.discussInfo.photo}"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:scaleType="centerCrop"
            app:shapeAppearance="@style/ImageView.UserAvatar" />

          <TextView
            style="@style/Text.15sp.333333"
            android:layout_marginStart="@dimen/dp_6"
            android:text="@{viewModel.discussInfo.nickName}" />

        </LinearLayout>

        <TextView
          android:id="@+id/tv_content"
          style="@style/Text.16sp.333333"
          android:layout_marginStart="@dimen/dp_16"
          android:layout_marginEnd="@dimen/dp_16"
          android:paddingTop="@dimen/dp_8"
          android:paddingBottom="@dimen/dp_8"
          android:text="@{viewModel.discussInfo.content}" />

        <View
          style="@style/Line.Horizontal.Light"
          android:layout_height="@dimen/dp_8" />

        <TextView
          style="@style/Text.14sp.333333.Bold"
          android:paddingStart="@dimen/dp_18"
          android:paddingTop="@dimen/dp_8"
          android:paddingBottom="@dimen/dp_8"
          android:text="@string/personal_fine_article_comment_count" />

        <com.bxkj.jrzp.support.comment.ui.CommentLayout
          android:id="@+id/comment_layout"
          style="@style/match_wrap" />

      </com.donkingliang.consecutivescroller.ConsecutiveScrollerLayout>

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    <View style="@style/Line.Horizontal" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44"
      android:gravity="center_vertical"
      android:orientation="horizontal">

      <TextView
        android:id="@+id/tv_comment"
        style="@style/Text.12sp.888888"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_6"
        android:layout_weight="1"
        android:background="@drawable/bg_news_details_comment"
        android:drawableStart="@drawable/ic_news_details_comment_edit"
        android:drawablePadding="@dimen/dp_8"
        android:gravity="center_vertical"
        android:onClick="@{onClickListener}"
        android:paddingStart="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_8"
        android:text="@string/news_details_comment_hint" />

      <FrameLayout
        android:id="@+id/fl_comment"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp_24"
        android:onClick="@{onClickListener}">

        <ImageView
          style="@style/wrap_wrap"
          android:layout_gravity="center"
          android:layout_marginStart="@dimen/dp_8"
          android:layout_marginEnd="@dimen/dp_8"
          android:src="@drawable/ic_news_details_comment" />

        <TextView
          style="@style/wrap_wrap"
          android:layout_gravity="end"
          android:layout_marginTop="@dimen/dp_6"
          android:background="@drawable/bg_fe6600_round"
          android:paddingStart="@dimen/dp_4"
          android:paddingEnd="@dimen/dp_4"
          android:text="@{String.valueOf(viewModel.commentCount)}"
          android:textColor="@color/common_white"
          android:textSize="8sp" />

      </FrameLayout>

      <!--      <ImageView-->
      <!--        android:id="@+id/iv_collect"-->
      <!--        style="@style/wrap_wrap"-->
      <!--        android:layout_marginEnd="@dimen/dp_24"-->
      <!--        android:onClick="@{()->viewModel.addOrRemoveCollection()}"-->
      <!--        android:src="@{viewModel.fineArticleDetails.isStow?@drawable/ic_news_details_collect_selected:@drawable/ic_news_details_collect}" />-->

      <ImageView
        android:id="@+id/iv_like"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_4"
        android:onClick="@{()->viewModel.addOrRemoveLike()}"
        android:src="@{viewModel.discussInfo.liked==1?@drawable/ic_moment_like_selected:@drawable/ic_moment_like_normal}" />

      <TextView
        style="@style/Text.14sp.333333"
        android:layout_marginEnd="@dimen/dp_24"
        android:onClick="@{()->viewModel.addOrRemoveLike()}"
        android:text="@{String.valueOf(viewModel.discussInfo.zanCount)}" />

      <!--      <ImageView-->
      <!--        android:id="@+id/iv_share"-->
      <!--        style="@style/wrap_wrap"-->
      <!--        android:layout_marginEnd="@dimen/dp_16"-->
      <!--        android:onClick="@{onClickListener}"-->
      <!--        android:src="@drawable/ic_news_details_share" />-->

    </LinearLayout>
  </LinearLayout>
</layout>