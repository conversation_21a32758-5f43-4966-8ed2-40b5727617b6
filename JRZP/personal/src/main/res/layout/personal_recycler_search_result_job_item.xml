<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="data"
            type="com.bxkj.jrzp.user.data.JobData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        style="@style/match_wrap"
        android:background="@drawable/bg_ffffff"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_16">

        <TextView
            android:id="@+id/tv_job_name"
            style="@style/Text.18sp.333333.Bold"
            android:layout_width="@dimen/dp_0"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.name}"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tv_job_wages"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_job_wages"
            style="@style/Text.17sp"
            android:text="@{data.convertSalary}"
            android:textColor="@color/cl_ff7405"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.bxkj.common.widget.labellayout.LabelLayout
            android:id="@+id/tv_desc"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_4"
            app:itemBackground="@drawable/bg_f4f4f4_radius_2"
            app:itemMargin="@dimen/dp_4"
            app:itemPaddingLR="@dimen/dp_6"
            app:itemPaddingTB="@dimen/dp_2"
            app:itemTextColor="@color/common_888888"
            app:itemTextSize="@dimen/dp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_job_name"
            bind:items="@{data.jobParams}" />

        <TextView
            android:id="@+id/tv_identity"
            style="@style/Text.12sp.888888"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_8"
            android:background="@drawable/bg_f4f4f4_radius_4"
            android:paddingStart="@dimen/dp_4"
            android:paddingTop="@dimen/dp_2"
            android:paddingEnd="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_2"
            app:layout_constraintStart_toEndOf="@id/tv_desc"
            app:layout_constraintTop_toBottomOf="@id/tv_job_name" />

        <TextView
            android:id="@+id/tv_partner"
            style="@style/Text.12sp.888888"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_8"
            android:background="@drawable/bg_f4f4f4_radius_4"
            android:paddingStart="@dimen/dp_4"
            android:paddingTop="@dimen/dp_2"
            android:paddingEnd="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_2"
            app:layout_constraintStart_toEndOf="@id/tv_identity"
            app:layout_constraintTop_toBottomOf="@id/tv_job_name" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_video_cover"
            app:shapeAppearance="@style/ImageView.Radius4"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginBottom="@dimen/dp_8"
            android:scaleType="centerCrop"
            android:visibility="@{data.hasVideo()?View.VISIBLE:View.GONE}"
            app:layout_constraintBottom_toTopOf="@id/barrier_job_bottom_info"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_job_name"
            app:layout_goneMarginEnd="@dimen/dp_0"
            bind:imgUrl="@{data.videoPic}" />

        <TextView
            android:id="@+id/tv_date"
            style="@style/Text.14sp.888888"
            android:layout_marginTop="@dimen/dp_8"
            android:drawableStart="@drawable/ic_job_fair_start_date"
            android:drawablePadding="@dimen/dp_4"
            android:text="@{data.formatPublishDate}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_desc" />

        <TextView
            android:id="@+id/tv_job_publish_enterprise"
            style="@style/Text.14sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_16"
            android:ellipsize="end"
            android:lines="1"
            android:text="@{data.comName}"
            app:layout_constraintEnd_toStartOf="@id/barrier_job_right_info"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_date"
            app:layout_goneMarginTop="@dimen/dp_10" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_job_right_info"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            app:barrierDirection="left"
            app:constraint_referenced_ids="tv_application,tv_distance,iv_video_cover" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_job_bottom_info"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            app:barrierDirection="top"
            app:constraint_referenced_ids="tv_distance,tv_application" />

        <TextView
            android:id="@+id/tv_application"
            style="@style/Button.Basic"
            android:layout_width="@dimen/common_dp_64"
            android:layout_height="@dimen/dp_24"
            android:enabled="@{!data.applied}"
            android:gravity="center"
            android:text="@{data.applicationStatusText}"
            android:textSize="@dimen/dp_12"
            app:layout_constraintBottom_toBottomOf="@id/tv_job_publish_enterprise"
            app:layout_constraintEnd_toStartOf="@id/tv_distance"
            app:layout_constraintTop_toTopOf="@id/tv_job_publish_enterprise"
            app:layout_goneMarginEnd="@dimen/dp_0" />

        <TextView
            android:id="@+id/tv_distance"
            style="@style/Text.14sp.333333"
            android:paddingStart="@dimen/dp_4"
            android:paddingTop="@dimen/dp_2"
            android:paddingEnd="@dimen/dp_4"
            android:paddingBottom="@dimen/dp_2"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_job_publish_enterprise"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_job_publish_enterprise" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>