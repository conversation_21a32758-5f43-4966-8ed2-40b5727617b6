<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.bxkj.personal.data.NewsItemData.MedialistBean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap">

        <ImageView
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:scaleType="centerCrop"
            bind:imgUrl="@{data.url}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="114:76"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>