<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:orientation="vertical">

    <LinearLayout
        style="@style/match_match"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/match_match"
            android:paddingStart="@dimen/dp_14"
            android:paddingTop="@dimen/dp_6"
            android:paddingEnd="@dimen/dp_14">

            <TextView
                android:id="@+id/tv_title"
                style="@style/Text.22sp.333333"
                android:layout_width="@dimen/dp_0"
                android:background="@color/common_f1f3f6"
                android:lines="2"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_user_info"
                style="@style/match_wrap"
                android:layout_marginTop="@dimen/dp_24"
                android:background="@color/common_f1f3f6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_title">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/iv_author_avatar"
                    android:layout_width="@dimen/common_dp_32"
                    android:layout_height="@dimen/common_dp_32"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_nick_name"
                    style="@style/Text.13sp.333333"
                    android:layout_marginStart="@dimen/dp_8"
                    app:layout_constraintBottom_toTopOf="@id/tv_publish_time"
                    app:layout_constraintStart_toEndOf="@id/iv_author_avatar"
                    app:layout_constraintTop_toTopOf="@id/iv_author_avatar" />

                <TextView
                    android:id="@+id/tv_publish_time"
                    style="@style/Text.12sp.999999"
                    app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                    app:layout_constraintStart_toStartOf="@id/tv_nick_name"
                    app:layout_constraintTop_toBottomOf="@id/tv_nick_name" />

                <TextView
                    android:id="@+id/tv_follow"
                    style="@style/Text.12sp"
                    android:layout_width="@dimen/common_dp_60"
                    android:layout_height="@dimen/dp_24"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/iv_author_avatar" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_0"
                android:layout_marginTop="@dimen/dp_8"
                android:background="@color/common_f1f3f6"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_user_info"
                app:layout_goneMarginTop="@dimen/dp_24" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

<!--    <View style="@style/Line.Horizontal" />-->

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="@dimen/common_dp_44"-->
<!--        android:gravity="center_vertical"-->
<!--        android:orientation="horizontal">-->

<!--        <TextView-->
<!--            android:id="@+id/tv_comment"-->
<!--            style="@style/common_Text.12sp.888888"-->
<!--            android:layout_height="match_parent"-->
<!--            android:layout_marginStart="@dimen/common_dp_14"-->
<!--            android:layout_marginTop="@dimen/common_dp_6"-->
<!--            android:layout_marginEnd="@dimen/common_dp_20"-->
<!--            android:layout_marginBottom="@dimen/common_dp_6"-->
<!--            android:layout_weight="1"-->
<!--            android:background="@drawable/bg_news_details_comment"-->
<!--            android:drawablePadding="@dimen/common_dp_8"-->
<!--            android:gravity="center_vertical"-->
<!--            android:paddingStart="@dimen/common_dp_8"-->
<!--            android:paddingEnd="@dimen/common_dp_8" />-->

<!--        <ImageView-->
<!--            android:id="@+id/iv_comment"-->
<!--            android:layout_width="@dimen/common_dp_20"-->
<!--            android:layout_height="@dimen/common_dp_20"-->
<!--            android:layout_marginEnd="@dimen/common_dp_24"-->
<!--            android:background="@color/common_f1f3f6" />-->

<!--        <ImageView-->
<!--            android:id="@+id/iv_collect"-->
<!--            android:layout_width="@dimen/common_dp_20"-->
<!--            android:layout_height="@dimen/common_dp_20"-->
<!--            android:layout_marginEnd="@dimen/common_dp_24"-->
<!--            android:background="@color/common_f1f3f6" />-->

<!--        <ImageView-->
<!--            android:id="@+id/iv_like"-->
<!--            style="@style/common_wrap_wrap"-->
<!--            android:layout_width="@dimen/common_dp_20"-->
<!--            android:layout_height="@dimen/common_dp_20"-->
<!--            android:layout_marginEnd="@dimen/common_dp_24"-->
<!--            android:background="@color/common_f1f3f6" />-->

<!--        <ImageView-->
<!--            android:layout_width="@dimen/common_dp_20"-->
<!--            android:layout_height="@dimen/common_dp_20"-->
<!--            android:layout_marginEnd="@dimen/common_dp_16"-->
<!--            android:background="@color/common_f1f3f6" />-->

<!--    </LinearLayout>-->
</LinearLayout>