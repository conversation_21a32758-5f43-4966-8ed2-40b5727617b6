<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:bind="http://schemas.android.com/tools">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoViewModel" />
  </data>

  <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match">

    <androidx.core.widget.NestedScrollView style="@style/match_match">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <LinearLayout
          android:id="@+id/ll_header"
          style="@style/match_wrap"
          android:background="@drawable/bg_ff7405"
          android:orientation="vertical"
          android:paddingBottom="@dimen/dp_18"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent">

          <ImageView
            android:id="@+id/iv_back"
            style="@style/wrap_wrap"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginBottom="@dimen/dp_8"
            android:onClick="@{onClickListener}"
            android:src="@drawable/common_ic_white_back" />

          <TextView
            android:id="@+id/tv_title"
            style="@style/Text.24sp.FFFFFF"
            android:layout_marginStart="@dimen/dp_32"
            android:text="@string/personal_micro_resume_create_title" />

          <TextView
            android:id="@+id/tv_desc"
            style="@style/Text.14sp.FFFFFF"
            android:layout_marginStart="@dimen/dp_32"
            android:text="@string/personal_micro_resume_create_tips" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_wrap">

          <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_user_info_center"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.35" />

          <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_avatar"
            android:layout_width="82dp"
            android:layout_height="82dp"
            android:layout_marginTop="@dimen/dp_32"
            android:onClick="@{onClickListener}"
            app:layout_constraintEnd_toStartOf="@id/gl_user_info_center"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/roundedCornerImageStyle.Avatar"
            bind:imgUrl="@{viewModel.fixAvatarPath}" />

          <TextView
            android:id="@+id/tv_avatar_desc"
            style="@style/Text.12sp.888888"
            android:layout_marginTop="@dimen/dp_4"
            android:text="@string/personal_micro_resume_edit_avatar"
            app:layout_constraintEnd_toEndOf="@id/iv_avatar"
            app:layout_constraintStart_toStartOf="@id/iv_avatar"
            app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

          <EditText
            android:id="@+id/et_name"
            style="@style/Text.22sp.333333"
            android:layout_width="@dimen/dp_0"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:background="@null"
            android:hint="@string/personal_micro_resume_name_hint"
            android:singleLine="true"
            android:text="@={viewModel.microResumeInfo.userName}"
            android:textCursorDrawable="@drawable/common_ic_custom_cursor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_avatar"
            app:layout_constraintTop_toTopOf="@id/iv_avatar" />

          <RadioGroup
            android:id="@+id/rg_gender"
            style="@style/wrap_wrap"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_36"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@id/tv_avatar_desc"
            app:layout_constraintEnd_toEndOf="@id/et_name"
            app:layout_constraintStart_toStartOf="@id/et_name">

            <RadioButton
              android:id="@+id/rb_male"
              style="@style/Text.18sp.333333"
              android:background="@drawable/frame_f4f4f4_to_fe6600_round"
              android:checked="@{viewModel.microResumeInfo.userSex==0}"
              android:paddingEnd="@dimen/dp_12"
              android:text="@string/gender_male"
              android:textColor="@color/cl_888888_to_fe6600_selector"
              android:theme="@style/MicroGenderRadioButtonStyle" />

            <RadioButton
              android:id="@+id/rb_female"
              style="@style/Text.18sp.333333"
              android:layout_marginStart="@dimen/dp_20"
              android:background="@drawable/frame_f4f4f4_to_fe6600_round"
              android:checked="@{viewModel.microResumeInfo.userSex==1}"
              android:paddingEnd="@dimen/dp_12"
              android:text="@string/gender_female"
              android:textColor="@color/cl_888888_to_fe6600_selector"
              android:theme="@style/MicroGenderRadioButtonStyle" />

          </RadioGroup>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
          android:id="@+id/line1"
          style="@style/Line.Horizontal.Light"
          android:layout_marginStart="@dimen/dp_22"
          android:layout_marginTop="@dimen/dp_22"
          android:layout_marginEnd="@dimen/dp_22"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@id/rg_gender" />

        <LinearLayout
          android:id="@+id/ll_basic_info"
          style="@style/match_wrap"
          android:orientation="horizontal"
          android:paddingStart="@dimen/dp_20"
          android:paddingEnd="@dimen/dp_20"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@id/line1">

          <LinearLayout
            android:id="@+id/ll_birthday"
            style="@style/match_wrap"
            android:layout_weight="1"
            android:gravity="center"
            android:onClick="@{()->viewModel.showBirthdayPicker()}"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line2">

            <TextView
              style="@style/Text.12sp.888888"
              android:text="@string/personal_micro_resume_birthday" />

            <TextView
              style="@style/Text.18sp.333333"
              android:layout_marginTop="@dimen/dp_4"
              android:ellipsize="end"
              android:gravity="center"
              android:hint="@string/common_please_select"
              android:lines="1"
              android:text="@{viewModel.microResumeInfo.birthDate}" />
          </LinearLayout>

          <LinearLayout
            android:id="@+id/ll_education"
            style="@style/match_wrap"
            android:layout_weight="1"
            android:gravity="center"
            android:onClick="@{onClickListener}"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line2">

            <TextView
              style="@style/Text.12sp.888888"
              android:text="@string/personal_micro_resume_education" />

            <TextView
              style="@style/Text.18sp.333333"
              android:layout_marginTop="@dimen/dp_4"
              android:ellipsize="end"
              android:gravity="center"
              android:hint="@string/common_please_select"
              android:lines="1"
              android:text="@{viewModel.microResumeInfo.quaName}" />
          </LinearLayout>

          <LinearLayout
            android:id="@+id/ll_work_exp"
            style="@style/match_wrap"
            android:layout_weight="1"
            android:gravity="center"
            android:onClick="@{onClickListener}"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line2">

            <TextView
              style="@style/Text.12sp.888888"
              android:text="@string/personal_micro_resume_work_exp" />

            <TextView
              style="@style/Text.18sp.333333"
              android:layout_marginTop="@dimen/dp_4"
              android:ellipsize="end"
              android:gravity="center"
              android:hint="@string/common_please_select"
              android:lines="1"
              android:text="@{viewModel.microResumeInfo.wtName}" />
          </LinearLayout>

        </LinearLayout>

        <View
          android:id="@+id/line2"
          style="@style/Line.Horizontal.Light"
          android:layout_marginStart="@dimen/dp_20"
          android:layout_marginEnd="@dimen/dp_20"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@id/ll_basic_info" />

        <LinearLayout
          android:id="@+id/ll_job_intention"
          style="@style/match_wrap"
          android:orientation="horizontal"
          android:paddingStart="@dimen/dp_20"
          android:paddingEnd="@dimen/dp_20"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toBottomOf="@id/line1">

          <LinearLayout
            android:id="@+id/ll_expect"
            style="@style/match_wrap"
            android:layout_weight="1"
            android:gravity="center"
            android:onClick="@{()->viewModel.toSelectExpectJobType()}"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line2">

            <TextView
              style="@style/Text.12sp.888888"
              android:text="@string/personal_job_intention_job" />

            <TextView
              style="@style/Text.18sp.333333"
              android:layout_marginTop="@dimen/dp_4"
              android:ellipsize="end"
              android:gravity="center"
              android:hint="@string/common_please_select"
              android:lines="1"
              android:text="@{viewModel.microResumeInfo.jobIntentionText}" />
          </LinearLayout>

          <LinearLayout
            android:id="@+id/ll_salary"
            style="@style/match_wrap"
            android:layout_weight="1"
            android:gravity="center"
            android:onClick="@{onClickListener}"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line2">

            <TextView
              style="@style/Text.12sp.888888"
              android:text="@string/personal_job_intention_salary" />

            <TextView
              style="@style/Text.18sp.333333"
              android:layout_marginTop="@dimen/dp_4"
              android:ellipsize="end"
              android:gravity="center"
              android:hint="@string/common_please_select"
              android:lines="1"
              android:text="@{viewModel.microResumeInfo.formatSalaryText}" />
          </LinearLayout>

          <LinearLayout
            android:id="@+id/ll_area"
            style="@style/match_wrap"
            android:layout_weight="1"
            android:gravity="center"
            android:onClick="@{()->viewModel.toSelectExpectAddress()}"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp_16"
            android:paddingBottom="@dimen/dp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line2">

            <TextView
              style="@style/Text.12sp.888888"
              android:text="@string/personal_job_intention_area" />

            <TextView
              style="@style/Text.18sp.333333"
              android:layout_marginTop="@dimen/dp_4"
              android:ellipsize="end"
              android:gravity="center"
              android:hint="@string/common_please_select"
              android:lines="1"
              android:text="@{viewModel.microResumeInfo.addressText}" />
          </LinearLayout>

        </LinearLayout>

        <!--        <LinearLayout-->
        <!--          android:id="@+id/ll_expect"-->
        <!--          style="@style/match_wrap"-->
        <!--          android:gravity="center"-->
        <!--          android:onClick="@{()->viewModel.toCompleteJobIntention()}"-->
        <!--          android:orientation="vertical"-->
        <!--          android:paddingTop="@dimen/dp_16"-->
        <!--          android:paddingBottom="@dimen/dp_16"-->
        <!--          app:layout_constraintEnd_toEndOf="parent"-->
        <!--          app:layout_constraintStart_toStartOf="parent"-->
        <!--          app:layout_constraintTop_toBottomOf="@id/line2">-->

        <!--          <TextView-->
        <!--            style="@style/Text.12sp.888888"-->
        <!--            android:text="@string/personal_micro_resume_expect" />-->

        <!--          <LinearLayout-->
        <!--            style="@style/match_wrap"-->
        <!--            android:layout_marginTop="@dimen/dp_4"-->
        <!--            android:gravity="center_vertical"-->
        <!--            android:orientation="horizontal"-->
        <!--            android:paddingStart="@dimen/common_dp_12"-->
        <!--            android:paddingEnd="@dimen/common_dp_12"-->
        <!--            android:visibility="@{viewModel.microResumeInfo.hasJobIntentionInfo()?View.VISIBLE:View.GONE}">-->

        <!--            <TextView-->
        <!--              style="@style/Text.18sp.333333"-->
        <!--              android:layout_width="0dp"-->
        <!--              android:layout_weight="1.5"-->
        <!--              android:ellipsize="end"-->
        <!--              android:gravity="center"-->
        <!--              android:hint="@string/common_please_select"-->
        <!--              android:lines="1"-->
        <!--              android:text="@{viewModel.microResumeInfo.jobIntentionText}" />-->

        <!--            <TextView-->
        <!--              style="@style/common_Text.18sp.333333"-->
        <!--              android:layout_marginStart="@dimen/dp_4"-->
        <!--              android:layout_marginEnd="@dimen/dp_4"-->
        <!--              android:text="·" />-->

        <!--            <TextView-->
        <!--              style="@style/Text.18sp.333333"-->
        <!--              android:layout_width="0dp"-->
        <!--              android:layout_weight="1"-->
        <!--              android:ellipsize="end"-->
        <!--              android:gravity="center"-->
        <!--              android:hint="@string/common_please_select"-->
        <!--              android:lines="1"-->
        <!--              android:text="@{viewModel.microResumeInfo.formatSalaryText}" />-->

        <!--            <TextView-->
        <!--              style="@style/common_Text.18sp.333333"-->
        <!--              android:layout_marginStart="@dimen/dp_4"-->
        <!--              android:layout_marginEnd="@dimen/dp_4"-->
        <!--              android:text="·" />-->

        <!--            <TextView-->
        <!--              style="@style/Text.18sp.333333"-->
        <!--              android:layout_width="0dp"-->
        <!--              android:layout_weight="1.5"-->
        <!--              android:ellipsize="end"-->
        <!--              android:gravity="center"-->
        <!--              android:hint="@string/common_please_select"-->
        <!--              android:lines="1"-->
        <!--              android:text="@{viewModel.microResumeInfo.addressText}" />-->

        <!--          </LinearLayout>-->

        <!--          <TextView-->
        <!--            android:id="@+id/tv_expect"-->
        <!--            style="@style/Text.18sp.333333"-->
        <!--            android:layout_marginTop="@dimen/dp_4"-->
        <!--            android:ellipsize="end"-->
        <!--            android:hint="@string/common_please_select"-->
        <!--            android:lines="1"-->
        <!--            android:visibility="@{viewModel.microResumeInfo.hasJobIntentionInfo()?View.GONE:View.VISIBLE}" />-->
        <!--        </LinearLayout>-->

        <!--        <View-->
        <!--          style="@style/Line.Horizontal.Light"-->
        <!--          android:layout_marginStart="@dimen/dp_20"-->
        <!--          android:layout_marginEnd="@dimen/dp_20" />-->

        <View
          style="@style/Line.Horizontal.Light"
          android:layout_marginStart="@dimen/dp_20"
          android:layout_marginEnd="@dimen/dp_20" />

        <TextView
          android:id="@+id/tv_save"
          style="@style/Button.Basic.Round"
          android:layout_margin="@dimen/dp_32"
          android:onClick="@{()->viewModel.save()}"
          android:text="@string/personal_micro_resume_create" />

      </LinearLayout>


    </androidx.core.widget.NestedScrollView>


  </FrameLayout>
</layout>