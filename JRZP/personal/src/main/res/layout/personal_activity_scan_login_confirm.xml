<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.personal.ui.activity.scanloginconfirm.ScanLoginConfirmViewModel" />
    </data>

    <LinearLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        style="@style/match_match"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            style="@style/match_wrap"
            app:title="@string/scan_login_title" />

        <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_match">

            <ImageView
                android:id="@+id/iv_img"
                style="@style/wrap_wrap"
                android:layout_marginTop="95dp"
                android:src="@drawable/img_scan_login_confirm"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_tips_one"
                style="@style/Text.16sp.333333"
                android:text="@string/login_for_computer_tips_one"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/tv_tips_two"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_img"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/tv_tips_two"
                style="@style/Text.14sp.767676"
                android:layout_marginTop="@dimen/dp_12"
                android:text="@string/login_for_computer_tips_two"
                app:layout_constraintBottom_toTopOf="@id/tv_confirm"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginBottom="@dimen/dp_20"
                app:layout_constraintTop_toBottomOf="@id/tv_tips_one" />

            <TextView
                android:id="@+id/tv_confirm"
                style="@style/Button.Basic"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginEnd="@dimen/dp_30"
                android:layout_marginBottom="@dimen/dp_20"
                android:gravity="center"
                android:onClick="@{()->viewModel.loginToComputer()}"
                android:text="@string/login_for_computer_confirm"
                android:textSize="@dimen/dp_16"
                app:layout_constraintBottom_toTopOf="@id/tv_cancel"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/tv_cancel"
                style="@style/Text.16sp.767676"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_dp_44"
                android:layout_marginStart="@dimen/dp_30"
                android:layout_marginEnd="@dimen/dp_30"
                android:layout_marginBottom="100dp"
                android:background="@drawable/frame_e8e8e8_radius_2"
                android:gravity="center"
                android:onClick="@{onClickListener}"
                android:text="@string/login_cancel"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>
</layout>
