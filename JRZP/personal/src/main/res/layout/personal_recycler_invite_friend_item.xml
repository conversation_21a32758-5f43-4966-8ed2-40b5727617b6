<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.personal.data.FollowItemData" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="@dimen/dp_14">

    <com.google.android.material.imageview.ShapeableImageView
      android:id="@+id/iv_avatar"
      android:layout_width="@dimen/dp_36"
      android:layout_height="@dimen/dp_36"
      bind:imgUrl="@{data.type==3?data.dwLogo:data.photo}"
      app:shapeAppearance="@style/roundedCornerImageStyle" />

    <TextView
      android:id="@+id/tv_nick_name"
      style="@style/Text.14sp.333333"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_12"
      android:layout_weight="1"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.type==3?data.dwName:data.nickName}"
      android:textStyle="bold" />

    <TextView
      android:id="@+id/tv_invite"
      style="@style/Text.12sp"
      android:layout_width="@dimen/common_dp_60"
      android:layout_height="@dimen/dp_24"
      android:background="@{data.invited?@drawable/frame_eaeaea_round:@drawable/bg_10c198_round}"
      android:enabled="@{!data.invited}"
      android:gravity="center"
      android:text="@{data.invited?@string/question_details_invited:@string/question_details_invite}"
      android:textColor="@{data.invited?@color/cl_333333:@color/common_white}" />

  </LinearLayout>
</layout>