<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="data"
      type="com.bxkj.personal.data.UserResumeData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:paddingTop="@dimen/dp_16"
    android:paddingBottom="@dimen/dp_12">

    <ImageView
      android:id="@+id/iv_select"
      style="@style/wrap_wrap"
      android:layout_marginStart="@dimen/dp_20"
      android:src="@drawable/ic_select_selector"
      android:visibility="gone"
      app:layout_constraintBottom_toBottomOf="@id/tv_resume_name"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_resume_name" />

    <TextView
      android:id="@+id/tv_resume_name"
      style="@style/Text.15sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_8"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.name}"
      app:layout_constraintEnd_toStartOf="@id/tv_state"
      app:layout_constraintStart_toEndOf="@id/iv_select"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_goneMarginStart="@dimen/dp_12" />

    <TextView
      android:id="@+id/tv_state"
      style="@style/Text.14sp.FE6600"
      android:layout_marginEnd="@dimen/dp_12"
      android:drawableEnd="@drawable/ic_small_more"
      android:drawablePadding="@dimen/dp_4"
      android:text="@{data.stateText}"
      app:layout_constraintBottom_toBottomOf="@id/tv_resume_name"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_resume_name" />

    <TextView
      android:id="@+id/tv_see"
      style="@style/Text.12sp.888888"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginTop="@dimen/dp_12"
      android:text="@{@string/resume_list_views_count_format(data.count)}"
      app:layout_constraintStart_toEndOf="@id/iv_select"
      app:layout_constraintTop_toBottomOf="@id/tv_resume_name"
      app:layout_goneMarginStart="@dimen/dp_12" />

    <ImageView
      android:id="@+id/iv_delete"
      style="@style/wrap_wrap"
      android:layout_marginEnd="@dimen/dp_12"
      android:src="@drawable/personal_ic_delete"
      app:layout_constraintBottom_toBottomOf="@id/tv_see"
      app:layout_constraintEnd_toStartOf="@id/tv_top"
      app:layout_constraintTop_toTopOf="@id/tv_see" />

    <TextView
      android:id="@+id/tv_top"
      style="@style/Text.12sp"
      android:layout_width="@dimen/dp_50"
      android:layout_marginEnd="@dimen/dp_8"
      android:background="@drawable/bg_ff865d_to_ffae94_radius_2_selector"
      android:enabled="@{data.top==0}"
      android:gravity="center"
      android:paddingTop="2dp"
      android:paddingBottom="@dimen/dp_2"
      android:text="@{data.top==0?@string/resume_list_top:@string/resume_list_topped}"
      android:textColor="@{data.top==0?@color/common_white:@color/cl_ff7405}"
      app:layout_constraintBottom_toBottomOf="@id/tv_edit"
      app:layout_constraintEnd_toStartOf="@id/tv_edit"
      app:layout_constraintTop_toTopOf="@id/tv_edit" />

    <TextView
      android:id="@+id/tv_edit"
      style="@style/wrap_wrap"
      android:layout_width="50dp"
      android:layout_marginEnd="@dimen/dp_12"
      android:background="@drawable/common_bg_rounded_rectangle_ff865d"
      android:gravity="center"
      android:paddingTop="@dimen/dp_2"
      android:paddingBottom="@dimen/dp_2"
      android:text="@string/common_edit"
      android:textColor="@color/common_white"
      android:textSize="@dimen/common_sp_12"
      app:layout_constraintBottom_toBottomOf="@id/iv_delete"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/iv_delete" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>