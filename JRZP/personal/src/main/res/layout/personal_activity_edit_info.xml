<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <include layout="@layout/common_include_title_bar" />

    <EditText
        android:id="@+id/et_info"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_weight="1"
        android:background="@null"
        android:gravity="top|start"
        android:inputType="textMultiLine"
        android:lineSpacingExtra="@dimen/dp_4"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_16"
        android:textColor="@color/cl_333333"
        android:textColorHint="@color/common_b5b5b5"
        android:textSize="@dimen/sp_14" />

    <TextView
        android:id="@+id/tv_max_number"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_36"
        android:background="@drawable/bg_f4f4f4"
        android:gravity="center_vertical|end"
        android:paddingEnd="@dimen/dp_12" />
</LinearLayout>