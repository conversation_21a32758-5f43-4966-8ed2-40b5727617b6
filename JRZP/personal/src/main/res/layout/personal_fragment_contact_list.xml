<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RadioGroup
            android:id="@+id/rg_filter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_16"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/dp_8"
            android:paddingTop="@dimen/dp_8">

            <RadioButton
                android:id="@+id/rb_all"
                style="@style/Text.12sp.333333"
                android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
                android:button="@null"
                android:paddingBottom="@dimen/dp_2"
                android:paddingEnd="@dimen/dp_8"
                android:paddingStart="@dimen/dp_8"
                android:paddingTop="@dimen/dp_2"
                android:text="@string/chat_type_all"
                android:textColor="@color/cl_333333_to_fe6600_selector" />

            <RadioButton
                android:id="@+id/rb_me"
                style="@style/Text.12sp.333333"
                android:layout_marginStart="@dimen/dp_8"
                android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
                android:button="@null"
                android:paddingBottom="@dimen/dp_2"
                android:paddingEnd="@dimen/dp_8"
                android:paddingStart="@dimen/dp_8"
                android:paddingTop="@dimen/dp_2"
                android:text="@string/chat_type_me"
                android:textColor="@color/cl_333333_to_fe6600_selector" />

            <RadioButton
                android:id="@+id/rb_you"
                style="@style/Text.12sp.333333"
                android:layout_marginStart="@dimen/dp_8"
                android:background="@drawable/bg_f4f4f4_to_ffe8cc_radius_2"
                android:button="@null"
                android:paddingBottom="@dimen/dp_2"
                android:paddingEnd="@dimen/dp_8"
                android:paddingStart="@dimen/dp_8"
                android:paddingTop="@dimen/dp_2"
                android:text="@string/chat_type_you"
                android:textColor="@color/cl_333333_to_fe6600_selector" />
        </RadioGroup>

        <FrameLayout
            android:id="@+id/fl_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>
</layout>