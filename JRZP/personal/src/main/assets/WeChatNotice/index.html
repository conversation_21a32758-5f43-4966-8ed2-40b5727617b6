<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>微信通知</title>
  <style>
    html, body {
      width: 100vw;
      overflow-x: hidden;
    }
    body {
      margin: 0;
      padding: 0;
      background: #fff6f3;
      font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
      color: #222;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .header {
      display: flex;
      align-items: center;
      height: 56px;
      padding: 0 16px;
      background: #fff;
      box-shadow: 0 1px 4px rgba(0,0,0,0.03);
      position: relative;
    }
    .header .close {
      font-size: 28px;
      margin-right: 12px;
      cursor: pointer;
    }
    .header .title {
      font-size: 22px;
      font-weight: 600;
    }
    .container {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
    }
    .card {
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 2px 16px rgba(255, 102, 51, 0.06);
      padding: 40px 24px 32px 24px;
      width: 100%;
      max-width: 420px;
      margin-bottom: 24px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
    .card-title {
      font-size: 22px;
      font-weight: bold;
      margin-bottom: 18px;
      text-align: left;
    }
    .card-list {
      font-size: 17px;
      line-height: 2.1;
      color: #333;
      padding-left: 0;
      margin: 0;
      list-style: none;
    }
    .card-list li {
      margin-bottom: 2px;
    }
    .card-quote {
      text-align: center;
      color: #ff6a3a;
      font-size: 32px;
      margin-top: 18px;
      font-family: serif;
    }
    .btn {
      width: 100%;
      max-width: 420px;
      height: 52px;
      background: linear-gradient(90deg, #ff6a3a 0%, #ff3a3a 100%);
      color: #fff;
      font-size: 20px;
      font-weight: bold;
      border: none;
      border-radius: 10px;
      margin: 0 auto;
      display: block;
      box-shadow: 0 4px 16px rgba(255, 102, 51, 0.10);
      cursor: pointer;
      letter-spacing: 2px;
      margin-bottom: 30px;
    }
    @media (max-width: 500px) {
      .card, .btn {
        width: 100%;
        max-width: 100vw;
      }
    }
  </style>
</head>
<body>
<div class="container">
  <div class="card">
    <div class="card-title">开启微信通知</div>
    <ul class="card-list">
      <li>1、第一时间获取最新职位推荐</li>
      <li>2、接收面试邀请和进度提醒</li>
      <li>3、关注感兴趣企业的招聘动态</li>
      <li>4、及时掌握行业薪资行情</li>
      <li>5、享受专属求职顾问服务</li>
    </ul>
    <div class="card-quote">”</div>
  </div>
  <button class="btn" onclick="openMiniProgram()">立即开启</button>
</div>
<script>
  function openMiniProgram() {
    if (WebViewJavascriptBridge !== 'undefined' && WebViewJavascriptBridge !== null) {
      WebViewJavascriptBridge.callHandler('openMiniProgramFollowPage');
    }
  }
</script>
</body>
</html>