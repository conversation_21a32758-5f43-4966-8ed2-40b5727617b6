pluginManagement {
  includeBuild("../build_logic")
}

include ':group-support:db'
include ':ugc:beautykit'
apply from: file('pins_init.gradle')

include ':app', ':lib-res'

//共有
include ':lib-common'

//支持库
include ':group-support:share'
include ':group-support:feedback'
include ':group-support:comment'
include ':group-support:upload'
include ':group-support:yd_login'
include ':group-support:feature'
include ':group-support:scan'
include ':group-support:yui'
include ':group-support:chat'

//业务
includeWithApi ':group-business:video'
includeWithApi ':group-business:live'
includeWithApi ':group-business:learning'
includeWithApi ':group-business:user'
includeWithApi ':accountmodule'
includeWithApi ':personal'

//企业版
//include ':group-enterprise:resume'
//include ':group-enterprise:enterpriseaccount'
include ':group-enterprise:commonlib'
include ':group-enterprise:reslib'
includeWithApi ':group-enterprise:enterprise'
//includeWithApi ':group-enterprise:resume'

//腾讯ugc
include ':ugc:beauty', ':ugc:ugckit'
include ':lintlib'
