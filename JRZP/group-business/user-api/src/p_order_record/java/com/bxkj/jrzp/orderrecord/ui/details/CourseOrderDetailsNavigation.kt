package com.bxkj.jrzp.orderrecord.ui.details

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/13
 * @version: V1.0
 */
object CourseOrderDetailsNavigation {

  const val PATH = "${UserConstants.DIRECTORY}/orderdetails"

  const val EXTRA_ORDER_ID = "ORDER_ID"

  fun navigate(orderID: Int): RouterNavigator {
    return Router.getInstance().to(PATH).withInt(EXTRA_ORDER_ID, orderID)
  }
}