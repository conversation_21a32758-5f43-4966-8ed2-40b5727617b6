package com.bxkj.jrzp.user

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/31
 * @version: V1.0
 */
class LikeType {
  companion object {
    const val LIKE_NEWS = 1
    const val LIKE_MOMENT = 3
    const val LIKE_QA = 4
    const val LIKE_VIDEO = 7
  }

  @IntDef(LIKE_NEWS, LIKE_MOMENT, LIKE_QA, LIKE_VIDEO)
  @Retention(SOURCE)
  @Target(VALUE_PARAMETER)
  annotation class Type
}