package com.bxkj.jrzp.user.data;

import com.bxkj.common.util.CheckUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description: 账户信息
 * @TODO: TODO
 * @date 2018/7/24
 */
public class AccountVipData {

  private int id;
  private int integral;
  private int look;
  private int refresh;
  private int sms;
  private int level;
  private String levelName;
  private String edate;
  private int release;
  private int douCount;
  private int jingpinCount;
  private int yaoyueCount;
  private int top;
  private int zhiliaoCount;
  private int zhuzhanAdvCount;
  private int shengzhanAdvCount;
  private int shizhanAdvCount;
  private int quzhanAdvCount;
  private int newsTuijianCount;

  public int getZhuzhanAdvCount() {
    return zhuzhanAdvCount;
  }

  public int getShengzhanAdvCount() {
    return shengzhanAdvCount;
  }

  public int getShizhanAdvCount() {
    return shizhanAdvCount;
  }

  public int getQuzhanAdvCount() {
    return quzhanAdvCount;
  }

  public int getNewsTuijianCount() {
    return newsTuijianCount;
  }

  public int getJingpinCount() {
    return jingpinCount;
  }

  public int getYaoyueCount() {
    return yaoyueCount;
  }

  public int getTop() {
    return top;
  }

  public int getZhiliaoCount() {
    return zhiliaoCount;
  }

  public String getLevelName() {
    if (!CheckUtils.isNullOrEmpty(levelName)){
      return levelName+">";
    }
    return levelName;
  }

  public String getFormatMemberLevel() {
    switch (level) {
      case 1101:
        return "蓝V会员";
      case 2101:
        return "金V会员";
      case 3101:
        return "钻石会员";
      default:
        return "试用会员";
    }
  }

  public String getVipInfo() {
    if (level == 101) {
      return "认证：" + getFormatMemberLevel();
    } else {
      return "认证：" + getFormatMemberLevel() + "  " + edate + "到期";
    }
  }

  public void setLevelName(String levelName) {
    this.levelName = levelName;
  }

  public int getSms() {
    return sms;
  }

  public void setSms(int sms) {
    this.sms = sms;
  }

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getIntegral() {
    return integral;
  }

  public void setIntegral(int integral) {
    this.integral = integral;
  }

  public int getLook() {
    return look;
  }

  public void setLook(int look) {
    this.look = look;
  }

  public int getRefresh() {
    return refresh;
  }

  public void setRefresh(int refresh) {
    this.refresh = refresh;
  }

  public String getEdate() {
    return edate;
  }

  public void setEdate(String edate) {
    this.edate = edate;
  }

  public int getRelease() {
    return release;
  }

  public void setRelease(int release) {
    this.release = release;
  }

  public int getLevel() {
    return level;
  }

  public void setLevel(int level) {
    this.level = level;
  }

  public int getDouCount() {
    return douCount;
  }

  public boolean isFreeMember() {
    return level == 0;
  }
}
