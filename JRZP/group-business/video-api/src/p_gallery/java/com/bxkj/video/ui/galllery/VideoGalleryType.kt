package com.bxkj.video.ui.galllery

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/21
 * @version: V1.0
 */
class VideoGalleryType {
  companion object {

    //多种视频
    const val MULTIPLE_VIDEO = 0

    //招聘视频
    const val RECRUITMENT_VIDEO = 1

    //求职者视频
    const val APPLICANT_VIDEO = 2

    //自己发布
    const val USER_RELEASE_VIDEO = 3

    //普通查看
    const val NORMAL_GALLERY = 4

  }

  @IntDef(
    MULTIPLE_VIDEO,
    RECRUITMENT_VIDEO,
    APPLICANT_VIDEO,
    USER_RELEASE_VIDEO,
    NORMAL_GALLERY
  )
  @Retention(SOURCE)
  @Target(VALUE_PARAMETER)
  annotation class Type
}