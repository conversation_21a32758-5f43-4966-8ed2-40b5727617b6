<manifest package="com.bxkj.video.api">
  <application android:allowBackup="false" xmlns:android="http://schemas.android.com/apk/res/android" tools:replace="android:allowBackup" xmlns:tools="http://schemas.android.com/tools">
    <activity android:name="com.bxkj.videorecord.ui.videorecord.VideoRecordActivityV2" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.video.ui.addinfolinkvideos.AddInfoLinkVideosActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.videorecord.ui.videorecord.VideoRecordActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait" android:theme="@style/DefaultRecordActivityTheme"/>
    <activity android:name="com.bxkj.video.ui.galllery.VideoGalleryActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.report.ui.videoreport.VideoReportActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.addvideolinkinfo.ui.addvideolinkinfos.AddVideoLinkInfoActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.videocut.ui.videocut.VideoCutActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait" android:theme="@style/EditerActivityTheme"/>
    <activity android:name="com.bxkj.video.ui.fullscreenplayer.FullScreenPlayerActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:theme="@style/EditerActivityTheme"/>
  </application>
</manifest>
