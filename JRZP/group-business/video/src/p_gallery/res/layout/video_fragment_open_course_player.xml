<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:background="@color/common_black">

    <ImageView
      android:id="@+id/iv_course_cover"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.20sp.FFFFFF"
      android:layout_width="match_parent"
      android:layout_marginStart="@dimen/dp_18"
      android:layout_marginEnd="@dimen/dp_18"
      android:layout_marginBottom="@dimen/dp_18"
      app:layout_constraintBottom_toTopOf="@id/tv_view_details"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent" />

    <TextView
      android:id="@+id/tv_view_details"
      style="@style/Button.Basic"
      android:layout_marginStart="@dimen/dp_18"
      android:layout_marginEnd="@dimen/dp_18"
      android:layout_marginBottom="@dimen/dp_70"
      android:text="@string/video_open_course_view_details"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent" />
  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>