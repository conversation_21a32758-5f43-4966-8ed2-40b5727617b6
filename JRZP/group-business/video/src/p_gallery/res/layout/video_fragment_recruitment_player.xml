<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.video.ui.recruitmentplayer.RecruitmentPlayerViewModel" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout style="@style/match_match">

    <com.bxkj.common.widget.GestureLayout
      android:id="@+id/gl_group"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent">

      <com.bxkj.video.weiget.TikTokVideoPlayer
        android:id="@+id/video_player"
        style="@style/match_match" />
    </com.bxkj.common.widget.GestureLayout>

    <androidx.constraintlayout.widget.Guideline
      android:id="@+id/guideline"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:orientation="horizontal"
      app:layout_constraintGuide_percent="0.5" />

    <View
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:background="@drawable/shadow_bottom"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/guideline" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.20sp.FFFFFF"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_20"
      android:layout_marginEnd="@dimen/dp_14"
      android:layout_marginBottom="@dimen/dp_16"
      android:text="@{viewModel.videoItemInfo.jobName}"
      android:textStyle="bold"
      app:layout_constraintBottom_toTopOf="@id/tv_salary"
      app:layout_constraintEnd_toStartOf="@id/tv_send_resume"
      app:layout_constraintStart_toStartOf="parent" />

    <FrameLayout
      android:id="@+id/fl_user_avatar"
      style="@style/wrap_wrap"
      android:clipChildren="false"
      app:layout_constraintBottom_toTopOf="@id/ll_like"
      app:layout_constraintEnd_toEndOf="@id/ll_like"
      app:layout_constraintStart_toStartOf="@id/ll_like">

      <de.hdodenhof.circleimageview.CircleImageView
        bind:imgUrl="@{viewModel.videoItemInfo.userPhoto}"
        android:layout_width="@dimen/dp_40"
        android:layout_height="@dimen/dp_40"
        android:layout_marginBottom="@dimen/dp_30"
        android:onClick="@{()->viewModel.seeMoreJob()}"
        app:civ_border_color="@color/common_white"
        app:civ_border_width="@dimen/dp_2" />

      <ImageView
        android:id="@+id/iv_follow"
        style="@style/wrap_wrap"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="@dimen/dp_20"
        android:onClick="@{()->viewModel.followAuthor()}"
        android:src="@drawable/video_ic_follow_author"
        android:visibility="@{viewModel.videoItemInfo.followUser?View.GONE:View.VISIBLE}" />

    </FrameLayout>

    <LinearLayout
      android:id="@+id/ll_like"
      style="@style/wrap_wrap"
      android:layout_marginBottom="@dimen/dp_20"
      android:gravity="center_horizontal"
      android:onClick="@{()->viewModel.likeThisVideo()}"
      android:orientation="vertical"
      app:layout_constraintBottom_toTopOf="@id/tv_comment"
      app:layout_constraintEnd_toEndOf="@id/tv_share"
      app:layout_constraintStart_toStartOf="@id/tv_share">

      <com.yx95.likeview.LikeView
        android:id="@+id/like_video"
        style="@style/wrap_wrap"
        app:lv_like_icon="@drawable/video_ic_like_sel"
        app:lv_unlike_icon="@drawable/video_ic_like_nor" />

      <!--        android:src="@{viewModel.videoItemInfo.like?@drawable/video_ic_like_sel:@drawable/video_ic_like_nor}"-->

      <TextView
        style="@style/common_Text.14sp.FFFFFF"
        android:text="@{String.valueOf(viewModel.videoItemInfo.likesCount)}" />
    </LinearLayout>

    <TextView
      android:id="@+id/tv_comment"
      style="@style/Text.14sp.FFFFFF"
      android:layout_marginBottom="@dimen/dp_20"
      android:drawableTop="@drawable/video_ic_video_player_comments"
      android:gravity="center"
      android:onClick="@{()->viewModel.showComments()}"
      android:text="@{String.valueOf(viewModel.videoItemInfo.commentsCount)}"
      app:layout_constraintBottom_toTopOf="@id/tv_share"
      app:layout_constraintEnd_toEndOf="@id/tv_send_resume"
      app:layout_constraintStart_toStartOf="@id/tv_send_resume" />

    <TextView
      android:id="@+id/tv_share"
      style="@style/Text.14sp.FFFFFF"
      android:layout_marginBottom="@dimen/dp_20"
      android:drawableTop="@drawable/video_ic_share"
      android:gravity="center"
      android:onClick="@{onClickListener}"
      android:text="@string/video_player_share"
      app:layout_constraintBottom_toTopOf="@id/tv_send_resume"
      app:layout_constraintEnd_toEndOf="@id/tv_send_resume"
      app:layout_constraintStart_toStartOf="@id/tv_send_resume" />

    <TextView
      android:id="@+id/tv_send_resume"
      style="@style/Text.14sp.FFFFFF"
      android:layout_marginEnd="@dimen/dp_20"
      android:drawableTop="@drawable/video_ic_send_resume"
      android:onClick="@{()->viewModel.toSendResume()}"
      android:text="@string/video_player_send_resume"
      app:layout_constraintBottom_toBottomOf="@id/tv_name"
      app:layout_constraintEnd_toEndOf="parent" />

    <TextView
      android:id="@+id/tv_job_count"
      style="@style/Text.14sp.FFFFFF"
      android:background="@drawable/video_bg_job_count"
      android:drawableStart="@drawable/video_ic_job_count_tag"
      android:drawablePadding="@dimen/dp_8"
      android:gravity="center"
      android:onClick="@{()->viewModel.seeMoreJob()}"
      android:paddingStart="@dimen/dp_10"
      android:paddingTop="@dimen/dp_12"
      android:paddingEnd="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_12"
      android:text="@{@string/video_player_job_count_format(viewModel.videoItemInfo.jobCounts)}"
      android:visibility="@{viewModel.videoItemInfo.jobCounts==0?View.GONE:View.VISIBLE}"
      app:layout_constraintBottom_toBottomOf="@id/recycler_job_welfare"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_salary" />

    <TextView
      android:id="@+id/tv_salary"
      style="@style/Text.14sp.FFFFFF"
      android:layout_marginStart="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_14"
      android:background="@drawable/bg_80000000_radius_4"
      android:paddingStart="@dimen/dp_6"
      android:paddingTop="@dimen/dp_4"
      android:paddingEnd="@dimen/dp_6"
      android:paddingBottom="@dimen/dp_4"
      android:text="@{viewModel.videoItemInfo.jobMoney}"
      app:layout_constraintBottom_toTopOf="@id/recycler_job_welfare"
      app:layout_constraintEnd_toStartOf="@id/tv_job_count"
      app:layout_constraintHorizontal_bias="0"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_goneMarginEnd="@dimen/dp_20" />

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/recycler_job_welfare"
      bind:items="@{viewModel.jobWelfare}"
      android:layout_width="@dimen/dp_0"
      android:layout_height="wrap_content"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_70"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toStartOf="@id/tv_job_count"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_goneMarginEnd="@dimen/dp_20" />

  </androidx.constraintlayout.widget.ConstraintLayout>

</layout>