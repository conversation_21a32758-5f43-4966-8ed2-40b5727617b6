package com.bxkj.video.ui.galllery

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewAnimationUtils
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.lifecycle.Observer
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import cn.jzvd.Jzvd
import com.therouter.router.Route
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.CheckUtils
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxBus.Message
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.report.ui.videoreport.VideoReportActivity
import com.bxkj.video.R
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.data.VideoData
import com.bxkj.video.databinding.ActivityVideoGalleryBinding
import com.bxkj.video.message.VideoListMassage
import com.bxkj.video.message.VideoLoadResultMessage
import com.bxkj.video.ui.galllery.VideoGalleryNavigation.Companion.RevealCenter
import kotlin.math.abs
import kotlin.math.hypot
import kotlin.math.max

/**
 * @date: 2020/5/11
 * @version: V1.0
 */
@Route(path = VideoGalleryNavigation.PATH)
class VideoGalleryActivity : BaseDBActivity<ActivityVideoGalleryBinding, VideoGalleryViewModel>(),
    View.OnClickListener {

    companion object {

        /**
         * 返回给定视频类型[videoType]、关联信息id[linkInfoId]、来源页面tag[fromPageTag]的Intent
         */
        fun newIntent(
            context: Context,
            videoType: Int = VideoGalleryType.RECRUITMENT_VIDEO,
            linkInfoId: Int = CommonApiConstants.NO_ID,
            fromPageTag: String = CommonApiConstants.NO_TEXT,
        ): Intent {
            return Intent(context, VideoGalleryActivity::class.java)
                .apply {
                    putExtra(VideoGalleryNavigation.EXTRA_VIDEO_GALLERY_TYPE, videoType)
                    putExtra(VideoGalleryNavigation.EXTRA_LINK_INFO_ID, linkInfoId)
                    putExtra(VideoGalleryNavigation.EXTRA_FROM_PAGE_TAG, fromPageTag)
                }
        }
    }

    private val revealCenter: RevealCenter? by lazy {
        intent.getParcelableExtra(VideoGalleryNavigation.EXTRA_REVEAL_CENTER)
    }

    private var videoGalleryPagerAdapter: VideoGalleryPagerAdapter? = null

    override fun getViewModelClass(): Class<VideoGalleryViewModel> =
        VideoGalleryViewModel::class.java

    override fun getLayoutId(): Int = R.layout.activity_video_gallery

    override fun initPage(savedInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel
        viewBinding.onClickListener = this

        statusBarManager.titleBar(viewBinding.clTitleBar).init()

        viewModel.start(
            intent.getIntExtra(
                VideoGalleryNavigation.EXTRA_VIDEO_GALLERY_TYPE,
                VideoGalleryType.NORMAL_GALLERY
            ),
            intent.getIntExtra(VideoGalleryNavigation.EXTRA_LINK_INFO_ID, CommonApiConstants.NO_ID),
            intent.getBooleanExtra(VideoGalleryNavigation.EXTRA_CLEAR_PAGE, false)
        )

        subscribeViewModelEvent()

        subscribeVideoListData()

        setupRefreshContentLayout()
    }

    private fun generateRevealAnim() {
        revealCenter?.let { center ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                viewBinding.flRoot.viewTreeObserver.addOnGlobalLayoutListener(object :
                    OnGlobalLayoutListener {
                    override fun onGlobalLayout() {

                        val layoutW = viewBinding.flRoot.width
                        val layoutH = viewBinding.flRoot.height

                        val w = max(abs(layoutW - center.x), center.x)
                        val h = max(abs(layoutH - center.y), center.y)

                        // get the end radius for the clipping circle
                        val endRadius = hypot(
                            w.toDouble(),
                            h.toDouble()
                        ).toFloat()

                        // create the animation (the final radius is zero)
                        val anim =
                            ViewAnimationUtils.createCircularReveal(
                                viewBinding.flRoot,
                                center.x,
                                center.y,
                                dip(40).toFloat(),
                                endRadius
                            )

                        anim.duration = 400
                        // start the animation
                        anim.start()
                        viewBinding.flRoot.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    }
                })
            }
        }
    }

    private fun setupRefreshContentLayout() {
        viewBinding.refreshContent.setEnableRefresh(false)
        viewBinding.refreshContent.setEnableLoadMore(false)
        val videoGalleryType = intent.getIntExtra(
            VideoGalleryNavigation.EXTRA_VIDEO_GALLERY_TYPE,
            VideoGalleryType.NORMAL_GALLERY
        )
        if (videoGalleryType == VideoGalleryType.NORMAL_GALLERY || videoGalleryType == VideoGalleryType.USER_RELEASE_VIDEO) {
            viewBinding.refreshContent.setEnableLoadMore(false)
        }
        val fromPageTag = intent.getStringExtra(VideoGalleryNavigation.EXTRA_FROM_PAGE_TAG)
        if (!CheckUtils.isNullOrEmpty(fromPageTag)) {
            viewBinding.refreshContent.setEnableLoadMore(true)
            viewBinding.refreshContent.setEnableAutoLoadMore(false)
            viewBinding.refreshContent.setOnLoadMoreListener {
                RxBus.get().post(RxBus.Message(RxMsgCode.VIDEO_GALLERY_LOAD_MORE, fromPageTag))
            }
        }
    }

    private fun subscribeViewModelEvent() {
        viewModel.deleteSuccessEvent.observe(this, Observer { videoItem ->
            showToast(getString(R.string.common_delete_success))
            setResult(VideoGalleryNavigation.RESULT_DELETE_SUCCESS)
            videoGalleryPagerAdapter?.remove(videoItem)
            if (videoGalleryPagerAdapter?.itemCount == 0) {
                finish()
            }
        })
    }

    override fun onClick(v: View?) {
        v?.let {
            when (v.id) {
                R.id.iv_back -> {
                    finish()
                }
                R.id.iv_delete -> {  //删除或举报按钮
                    videoGalleryPagerAdapter?.let { adapter ->
                        val currentVideoItem =
                            adapter.getData()[viewBinding.vpVideoGallery.currentItem]
                        if (
                            intent.getIntExtra(
                                VideoGalleryNavigation.EXTRA_LINK_INFO_ID,
                                CommonApiConstants.NO_ID
                            ) != CommonApiConstants.NO_ID
                        ) {
                            MenuPopup.Builder(this)
                                .setData(resources.getStringArray(R.array.video_resume_video_delete_items))
                                .setOnItemClickListener { _, position ->
                                    if (position == 0) {
                                        deleteVideoConfirm(currentVideoItem)
                                    } else {
                                        deleteLinkInfoConfirm(currentVideoItem)
                                    }
                                }.build().show()
                        } else {
                            deleteVideoConfirm(currentVideoItem)
                        }
                    }
                }
                else -> {
                    videoGalleryPagerAdapter?.let { adapter ->
                        val currentVideoItem =
                            adapter.getData()[viewBinding.vpVideoGallery.currentItem]
                        startActivity(VideoReportActivity.newIntent(this, currentVideoItem.id))
                    }
                }
            }
        }
    }

    private fun deleteLinkInfoConfirm(currentVideoItem: VideoData) {
        ActionDialog.Builder()
            .setTitle(getString(R.string.tips))
            .setContent(getString(R.string.video_gallery_delete_link_info_tips))
            .setOnConfirmClickListener {
                viewModel.deleteVideoLink(currentVideoItem)
            }.build().show(supportFragmentManager)
    }

    private fun deleteVideoConfirm(currentVideoItem: VideoData) {
        ActionDialog.Builder()
            .setTitle(getString(R.string.tips))
            .setContent(getString(R.string.video_gallery_delete_tips))
            .setOnConfirmClickListener {
                viewModel.deleteVideo(currentVideoItem)
            }.build().show(supportFragmentManager)
    }

    private fun subscribeVideoListData() {
        addDisposable(RxBus.get().toStickObservable(VideoListMassage::class.java)
            .subscribe {
                if (it.galleryTag == viewModel.mVideoGalleryType) {
                    setupVideoGalleryPagerAdapter(it)
                }
            })

        addDisposable(RxBus.get().toObservable(VideoLoadResultMessage::class.java)
            .subscribe {
                when (it.status) {
                    VideoLoadResultMessage.RESULT_SUCCESS -> {
                        videoGalleryPagerAdapter?.addAll(it.list as List<OnlineVideoData>)
                        viewBinding.refreshContent.finishLoadMore()
                    }
                    VideoLoadResultMessage.RESULT_ERROR -> {
                        viewBinding.refreshContent.finishLoadMore(false)
                    }
                    VideoLoadResultMessage.RESULT_NO_MORE -> {
                        viewBinding.refreshContent.finishLoadMoreWithNoMoreData()
                    }
                }
            })
    }

    private fun setupVideoGalleryPagerAdapter(acceptVideoListMsg: VideoListMassage) {
        if (acceptVideoListMsg.videoList.isNullOrEmpty()) {
            showToast("获取视频信息失败，请重试")
        } else {
            videoGalleryPagerAdapter = VideoGalleryPagerAdapter(
                this,
                acceptVideoListMsg.videoList as List<OnlineVideoData>,
                intent.getIntExtra(
                    VideoGalleryNavigation.EXTRA_VIDEO_GALLERY_TYPE,
                    CommonApiConstants.NO_ID
                )
            )
            viewBinding.vpVideoGallery.adapter = videoGalleryPagerAdapter
            viewBinding.vpVideoGallery.registerOnPageChangeCallback(object :
                OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    videoGalleryPagerAdapter?.let {
                        if (position < it.getData().size) {
                            postVideoPositionChangeMsg(it.getData()[position].id)
                        }
                    }
                }
            })
            viewBinding.vpVideoGallery.setCurrentItem(acceptVideoListMsg.currentPosition, false)
        }
    }

    private fun postVideoPositionChangeMsg(videoId: Int) {
        RxBus.get().postStick(
            Message(
                RxMsgCode.ACTION_VIDEO_GALLERY_POSITION_CHANGE,
                videoId
            )
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        Jzvd.releaseAllVideos()
    }

}