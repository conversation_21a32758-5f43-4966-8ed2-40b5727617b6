package com.bxkj.video.ui.galllery

import android.app.Activity
import android.os.Parcel
import android.os.Parcelable
import android.os.Parcelable.Creator
import androidx.core.os.bundleOf
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.video.VideoConstants
import com.bxkj.video.message.VideoListMassage
import com.bxkj.video.ui.galllery.VideoGalleryType.Type

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/21
 * @version: V1.0
 */
class VideoGalleryNavigation {

    companion object {

        const val PATH = "${VideoConstants.VIDEO_DIRECTORY}/videogallery"

        const val EXTRA_VIDEO_GALLERY_TYPE = "VIDEO_TYPE"
        const val EXTRA_LINK_INFO_ID = "LINK_INFO_ID"
        const val EXTRA_FROM_PAGE_TAG = "FROM_PAGE_TAG"
        const val EXTRA_REVEAL_CENTER = "REVEAL_CENTER"
        const val EXTRA_CLEAR_PAGE = "CLEAR_PAGE"

        const val RESULT_DELETE_SUCCESS = Activity.RESULT_FIRST_USER + 1

        @JvmStatic
        @JvmOverloads
        fun navigate(
            @Type videoGalleryType: Int = VideoGalleryType.RECRUITMENT_VIDEO,
            videoListMsg: VideoListMassage,
            linkInfoId: Int = CommonApiConstants.NO_ID,
            fromPageTag: String = CommonApiConstants.NO_TEXT,
            revealCenter: RevealCenter? = null,
            clearPage: Boolean = false
        ): RouterNavigator {
            RxBus.get().postStick(videoListMsg)
            return Router.getInstance().to(PATH)
                .with(
                    bundleOf(
                        EXTRA_VIDEO_GALLERY_TYPE to videoGalleryType,
                        EXTRA_LINK_INFO_ID to linkInfoId,
                        EXTRA_FROM_PAGE_TAG to fromPageTag,
                        EXTRA_REVEAL_CENTER to revealCenter,
                        EXTRA_CLEAR_PAGE to clearPage
                    )
                )
        }

        data class RevealCenter(val x: Int, val y: Int) : Parcelable {
            constructor(parcel: Parcel) : this(
                parcel.readInt(),
                parcel.readInt()
            ) {
            }

            override fun writeToParcel(parcel: Parcel, flags: Int) {
                parcel.writeInt(x)
                parcel.writeInt(y)
            }

            override fun describeContents(): Int {
                return 0
            }

            companion object CREATOR : Creator<RevealCenter> {
                override fun createFromParcel(parcel: Parcel): RevealCenter {
                    return RevealCenter(parcel)
                }

                override fun newArray(size: Int): Array<RevealCenter?> {
                    return arrayOfNulls(size)
                }
            }
        }
    }
}