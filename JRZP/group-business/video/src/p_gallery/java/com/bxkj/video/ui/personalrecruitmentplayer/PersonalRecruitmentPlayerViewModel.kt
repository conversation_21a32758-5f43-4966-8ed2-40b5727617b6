package com.bxkj.video.ui.recruitmentplayer

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.data.ShareInfoData
import com.bxkj.common.data.repository.CommonRepository
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.CheckUtils
import com.bxkj.jrzp.user.FollowType
import com.bxkj.jrzp.user.LikeType
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.repository.VideoRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/30
 * @version: V1.0
 */
class PersonalRecruitmentPlayerViewModel @Inject constructor(
  private val mOpenUserRepository: OpenUserRepository,
  private val mCommonRepository: CommonRepository,
  private val mVideoRepository: VideoRepository
) : BaseViewModel() {

  val videoItemInfo = MutableLiveData<OnlineVideoData>()
  val jobWelfare = MutableLiveData<List<String>>()

  val seeMoreJobCommand = LiveEvent<OnlineVideoData>()
  val toSendResumeCommand =
    LiveEvent<OnlineVideoData>()
  val toJobDetailsCommand = LiveEvent<Int>()
  val followAuthorSuccessEvent =
    LiveEvent<OnlineVideoData>()

  val initShareEvent = LiveEvent<ShareInfoData>()

  val showCommentsCommand =
    LiveEvent<OnlineVideoData>()

  val likedEvent = LiveEvent<Boolean>()

  fun setupVideoInfo(
    recruitmentVideoInfo: OnlineVideoData?
  ) {
    videoItemInfo.value = recruitmentVideoInfo
    recruitmentVideoInfo?.let {
      addVideoViewCount(it.id)
      if (!CheckUtils.isNullOrEmpty(recruitmentVideoInfo.jobWelfare)) {
        jobWelfare.value = recruitmentVideoInfo.jobWelfare?.split(";")
      }
    }
  }

  fun seeMoreJob() {
    videoItemInfo.value?.let {
      seeMoreJobCommand.value = it
    }
  }

  fun doubleTapLike() {
    videoItemInfo.value?.let { videoInfo ->
      if (videoInfo.isLike) {
        likedEvent.value = true
        return@let
      }
      likeThisVideo()
    }
  }

  fun likeThisVideo() {
    afterLogin {
      videoItemInfo.value?.let { videoInfo ->
        viewModelScope.launch {
          mOpenUserRepository.like(
            getSelfUserID(),
            LikeType.LIKE_VIDEO,
            videoInfo.id,
//            UserUtils.getUserType()
            AppConstants.USER_TYPE_PERSONAL
          ).handleResult({
            likedEvent.value = true
            videoInfo.like()
          }, {
            if (it.errCode == 10002) {  //取消点赞
              likedEvent.value = false
              videoInfo.unlike()
            }
          })
        }
      }
    }
  }

  fun toSendResume() {
    afterLogin {
      videoItemInfo.value?.let {
        toSendResumeCommand.value = it
      }
    }
  }

  fun toJobDetails() {
    videoItemInfo.value?.let {
      toJobDetailsCommand.value = it.jobID
    }
  }

  fun followAuthor() {
    afterLogin {
      videoItemInfo.value?.let { video ->
        viewModelScope.launch {
          mOpenUserRepository.follow(getSelfUserID(), FollowType.FOLLOW_USER, video.userID)
            .handleResult({
              video.isFollowUser = true
              followAuthorSuccessEvent.value = it
            }, {
              if (it.errCode == 10002) {

              } else {
                showToast(it.errMsg)
              }
            })
        }
      }
    }
  }

  fun showComments() {
    videoItemInfo.value?.let {
      showCommentsCommand.value = it
    }
  }

  fun getShareInfo() {
    videoItemInfo.value?.let { video ->
      viewModelScope.launch {
        showLoading()
        mCommonRepository.getShareInfo(
          CommonApiConstants.SHARE_VIDEO,
          video.type,
          video.id
        ).handleResult({
          it?.let {
            it.title = video.getShareTitle(true)
            it.content = video.getRealShareContent()
            it.sharePic = video.pic
            it.shareUrl =
              CommonApiConstants.SHARE_VIDEO_URL_PREFIX + video.id
            initShareEvent.value = it
          }
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  fun addNewCommentCount(commentCount: Int) {
    videoItemInfo.value?.let {
      it.addNewComment(commentCount)
    }
  }

  private fun addVideoViewCount(videoID: Int) {
    viewModelScope.launch {
      mVideoRepository.addVideoPlayCount(videoID)
    }
  }

}