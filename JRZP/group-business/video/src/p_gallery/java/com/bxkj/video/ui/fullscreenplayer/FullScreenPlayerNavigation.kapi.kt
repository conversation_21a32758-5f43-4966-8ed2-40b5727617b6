package com.bxkj.video.ui.fullscreenplayer

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.video.VideoConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/14
 * @version: V1.0
 */
class FullScreenPlayerNavigation {
  companion object {

    const val PATH = "${VideoConstants.VIDEO_DIRECTORY}/fullscreenpalyer"

    const val EXTRA_VIDEO_URL = "VIDEO_URL"

    fun create(videoUrl: String): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withString(EXTRA_VIDEO_URL, videoUrl)
    }
  }
}