package com.bxkj.video.ui.galllery

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.data.VideoData
import com.bxkj.video.ui.normalplayer.NormalPlayerFragment
import com.bxkj.video.ui.opencourseplayer.OpenCoursePlayerFragment
import com.bxkj.video.ui.personalrecruitmentplayer.PersonalRecruitmentPlayerFragment
import com.bxkj.video.ui.recruitmentplayer.RecruitmentPlayerFragment

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @version: V1.0
 */
class VideoGalleryPagerAdapter(
  activity: FragmentActivity,
  list: List<VideoData>,
  private var mVideoType: Int = VideoGalleryType.RECRUITMENT_VIDEO
) : FragmentStateAdapter(activity) {

  private var mList = ArrayList(list)
  private val pageIds = mList.map { it.hashCode().toLong() }

  override fun createFragment(position: Int): Fragment {
    return when (mVideoType) {
      VideoGalleryType.NORMAL_GALLERY -> {
        NormalPlayerFragment.newInstance(mList[position])
      }

      VideoGalleryType.RECRUITMENT_VIDEO -> {
        RecruitmentPlayerFragment.newInstance(mList[position] as OnlineVideoData)
      }

      VideoGalleryType.MULTIPLE_VIDEO -> {
        val currentItem = mList[position] as OnlineVideoData
        when {
          currentItem.isXspVideo() -> {
            RecruitmentPlayerFragment.newInstance(currentItem)
          }

          currentItem.isOpenCourse() -> {
            OpenCoursePlayerFragment.newInstance(currentItem)
          }

          currentItem.isPersonalRecruitment() -> {
            PersonalRecruitmentPlayerFragment.newInstance(currentItem)
          }

          else -> {
            NormalPlayerFragment.newInstance(mList[position])
          }
        }
      }

      else -> {
        NormalPlayerFragment.newInstance(mList[position])
      }
    }
  }

  override fun getItemCount(): Int {
    return mList.size
  }

  override fun getItemId(position: Int): Long {
    return mList[position].hashCode().toLong()
  }

  override fun containsItem(itemId: Long): Boolean {
    return pageIds.contains(itemId)
  }

  fun getData(): ArrayList<out VideoData> {
    return mList;
  }

  /**
   * 添加list
   *
   * @param items
   */
  fun addAll(items: List<VideoData>) {
    mList.addAll(items)
    notifyItemRangeChanged(mList.size - items.size, items.size)
  }

  /**
   * 添加list
   *
   * @param items
   */
  fun addAll(index: Int, items: List<VideoData>) {
    mList.addAll(index, items)
    notifyItemRangeChanged(index, items.size + mList.size - index)
  }

  /**
   * remove单个
   *
   * @param position
   */
  fun removeAt(position: Int) {
    mList.removeAt(position)
    notifyItemRemoved(position)
    notifyItemRangeChanged(position, mList.size - position)
  }

  /**
   * remove
   *
   * @param item
   */
  fun remove(item: VideoData) {
    val itemPosition = mList.indexOf(item)
    removeAt(itemPosition)
  }

  /**
   * 删除list
   *
   * @param itemRecruitments
   */
  fun removeAll(itemRecruitments: List<VideoData>) {
    mList.removeAll(itemRecruitments.toSet())
    notifyDataSetChanged()
  }

  /**
   * 清空列表
   */
  fun clear() {
    mList.clear()
    notifyDataSetChanged()
  }
}