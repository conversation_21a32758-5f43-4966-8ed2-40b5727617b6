package com.bxkj.video.ui.personalrecruitmentplayer

import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import cn.jzvd.JZDataSource
import cn.jzvd.Jzvd
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.imageloder.base.ImageLoader
import com.bxkj.common.util.AnimationUtils
import com.bxkj.common.util.JZMediaExo
import com.bxkj.common.util.imageloader.GlideLoadConfig
import com.bxkj.common.widget.GestureLayout.SimpleOnGestureListener
import com.bxkj.jrzp.support.comment.CommentInfoType
import com.bxkj.jrzp.support.comment.ui.BottomSheetCommentDialog
import com.bxkj.jrzp.support.comment.ui.BottomSheetCommentDialog.OnCommentCountChangeListener
import com.bxkj.jrzp.userhome.data.UserInfoNavigationData
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.ui.activity.jobdetails.JobDetailsNavigation
import com.bxkj.share.ui.StandardShareDialog
import com.bxkj.video.R
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.databinding.VideoFragmentPersonalRecruitmentPlayerBinding
import com.bxkj.video.ui.recruitmentplayer.PersonalRecruitmentPlayerViewModel
import com.bxkj.video.ui.sendcontractinfo.VideoSendContractInfoDialog

/**
 * @Description: 个人发布的招聘视频
 * @author:45457
 * @date: 2020/7/30
 * @version: V1.0
 */
class PersonalRecruitmentPlayerFragment :
  BaseDBFragment<VideoFragmentPersonalRecruitmentPlayerBinding, PersonalRecruitmentPlayerViewModel>(),
  OnClickListener {

  companion object {

    const val EXTRA_VIDEO_DATE = "VIDEO_DATE"

    fun newInstance(videoData: OnlineVideoData): Fragment {
      return PersonalRecruitmentPlayerFragment().apply {
        arguments = bundleOf(
          EXTRA_VIDEO_DATE to videoData
        )
      }
    }
  }

  private var mShareDialog: StandardShareDialog? = null

  override fun getViewModelClass(): Class<PersonalRecruitmentPlayerViewModel> =
    PersonalRecruitmentPlayerViewModel::class.java

  override fun getLayoutId(): Int = R.layout.video_fragment_personal_recruitment_player

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    arguments?.let {
      val videoItemInfo = it.getParcelable<OnlineVideoData>(EXTRA_VIDEO_DATE)

      videoItemInfo?.let { video ->
        viewBinding.likeVideo.setLike(video.isLike)

        viewModel.setupVideoInfo(videoItemInfo)
        ImageLoader.loadImage(
          this,
          GlideLoadConfig.Builder().url(videoItemInfo.pic)
            .into(viewBinding.videoPlayer.posterImageView).build()
        )
        viewBinding.videoPlayer.setUp(
          JZDataSource(videoItemInfo.video, videoItemInfo.title),
          Jzvd.SCREEN_NORMAL,
          JZMediaExo::class.java
        )
      }

      viewBinding.glGroup.setOnGestureListener(object : SimpleOnGestureListener() {
        override fun onSingleTapConfirm(): Boolean {
          viewBinding.videoPlayer.onClickUiToggle()
          return true
        }

        override fun onDoubleTap(): Boolean {
          viewModel.doubleTapLike()
          return true
        }
      })
    }
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.tv_share) {
        if (mShareDialog != null) {
          mShareDialog?.show(childFragmentManager)
        } else {
          viewModel.getShareInfo()
        }
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.seeMoreJobCommand.observe(this, Observer { videoInfo ->
      UserHomeNavigation.navigate(
        videoInfo.userID,
        targetTab = UserInfoNavigationData.NAVIGATION_JOB
      ).start()
    })

    viewModel.showCommentsCommand.observe(this, Observer {
      if (checkLoginAndToLogin()) {
        BottomSheetCommentDialog.Builder()
          .setCommentInfoType(CommentInfoType.VIDEO)
          .setCommentInfoId(it.id)
          .setCommentCount(it.commentsCount)
          .setOnCommentCountChangeListener(object : OnCommentCountChangeListener {
            override fun onAddComment(commentCount: Int) {
              viewModel.addNewCommentCount(commentCount)
            }
          })
          .build().show(childFragmentManager)
      }
    })

    viewModel.toSendResumeCommand.observe(this, Observer {
      VideoSendContractInfoDialog(it.id).show(childFragmentManager)
    })

    viewModel.toJobDetailsCommand.observe(this, Observer {
      JobDetailsNavigation.navigate(it).start()
    })

    viewModel.followAuthorSuccessEvent.observe(this, Observer {
      AnimationUtils.scaleToHidden(viewBinding.ivFollow, 800)
    })

    viewModel.initShareEvent.observe(this, Observer {
      mShareDialog =
        StandardShareDialog.Builder()
          .setShareTitle(it.title)
          .setShareContent(it.content)
          .setShareUrl(it.shareUrl)
          .setSharePic(it.sharePic)
          .build()
      mShareDialog?.show(childFragmentManager)
    })

    viewModel.likedEvent.observe(this, Observer {
      if (it) {
        viewBinding.likeVideo.like(true)
      } else {
        viewBinding.likeVideo.unlike()
      }
    })
  }

  override fun onResume() {
    super.onResume()
    viewBinding.videoPlayer.startVideoAfterPreloading()
  }

  override fun onPause() {
    super.onPause()
    Jzvd.releaseAllVideos()
  }

}