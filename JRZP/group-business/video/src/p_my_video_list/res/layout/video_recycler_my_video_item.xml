<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.video.data.OnlineVideoData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap">

    <ImageView
      android:id="@+id/iv_cover"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:scaleType="centerCrop"
      bind:imgUrl="@{data.pic}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintDimensionRatio="1:1"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      style="@style/Text.12sp.FFFFFF"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginTop="@dimen/dp_6"
      android:background="@drawable/bg_80000000_round"
      android:paddingStart="@dimen/dp_6"
      android:paddingTop="@dimen/dp_2"
      android:paddingEnd="@dimen/dp_6"
      android:paddingBottom="@dimen/dp_2"
      android:text="@{data.videoStateText}"
      android:visibility="@{data.reviewSuccess()?View.GONE:View.VISIBLE}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <ImageView
      android:id="@+id/iv_checked"
      style="@style/wrap_wrap"
      android:src="@drawable/common_bg_checkbox_selector"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      style="@style/Text.12sp.FFFFFF"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_8"
      android:drawableStart="@drawable/video_ic_video_play_count"
      android:drawablePadding="@dimen/dp_4"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{String.valueOf(data.view)}"
      app:layout_constraintBottom_toBottomOf="@id/iv_cover"
      app:layout_constraintEnd_toStartOf="@id/tv_relate_count"
      app:layout_constraintStart_toStartOf="parent" />

    <TextView
      android:id="@+id/tv_relate_count"
      style="@style/Text.12sp.FFFFFF"
      android:layout_marginEnd="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_8"
      android:drawableStart="@drawable/video_ic_my_video_relate_count"
      android:drawablePadding="@dimen/dp_4"
      android:text="@{String.valueOf(data.glCount)}"
      app:layout_constraintBottom_toBottomOf="@id/iv_cover"
      app:layout_constraintEnd_toEndOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>