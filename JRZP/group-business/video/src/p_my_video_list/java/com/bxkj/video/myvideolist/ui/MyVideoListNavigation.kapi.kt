package com.bxkj.video.myvideolist.ui

import androidx.fragment.app.Fragment
import com.bxkj.common.constants.AppConstants.UserType
import com.bxkj.common.util.router.Router
import com.bxkj.video.VideoConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/28
 * @version: V1.0
 */
class MyVideoListNavigation {

  companion object {

    const val PATH = "${VideoConstants.VIDEO_DIRECTORY}/myvideolist"

    const val EXTRA_USER_TYPE = "USER_TYPE"

    fun from(@UserType userType: Int): Fragment {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_USER_TYPE, userType)
        .createFragment() as Fragment
    }
  }
}