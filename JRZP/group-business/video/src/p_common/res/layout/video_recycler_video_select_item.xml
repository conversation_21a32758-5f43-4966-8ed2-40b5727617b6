<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.video.data.VideoData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap">

    <androidx.constraintlayout.widget.Group
      style="@style/wrap_wrap"
      android:visibility="@{data.addItem?View.GONE:View.VISIBLE}"
      app:constraint_referenced_ids="iv_cover,iv_select,tv_views,iv_select" />

    <ImageView
      android:id="@+id/iv_cover"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:scaleType="centerCrop"
      bind:imgUrl="@{data.pic}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintDimensionRatio="1:1"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_views"
      style="@style/Text.12sp.FFFFFF"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_8"
      android:drawableStart="@drawable/ic_view_count"
      android:drawablePadding="@dimen/dp_8"
      android:text="@{String.valueOf(data.view)}"
      app:layout_constraintBottom_toBottomOf="@id/iv_cover"
      app:layout_constraintStart_toStartOf="parent" />

    <ImageView
      android:id="@+id/iv_select"
      style="@style/wrap_wrap"
      android:layout_marginTop="@dimen/dp_4"
      android:layout_marginEnd="@dimen/dp_4"
      android:src="@drawable/ic_select_selector"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <ImageView
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:scaleType="center"
      android:src="@drawable/ic_add_video"
      android:visibility="@{data.addItem?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintDimensionRatio="1:1"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>

</layout>