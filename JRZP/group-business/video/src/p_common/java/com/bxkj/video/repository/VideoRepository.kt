package com.bxkj.video.repository

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.staggeredlist.VideoListType
import com.bxkj.staggeredlist.VideoListType.Type
import com.bxkj.staggeredlist.data.VideoListResponse
import com.bxkj.video.data.VideoSignInData
import com.bxkj.video.openapi.VideoApi
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/18
 * @version: V1.0
 */
class VideoRepository @Inject constructor(
  private val mVideoApi: VideoApi
) : BaseRepo() {

  /**
   * 删除视频
   */
  suspend fun deleteVideo(userId: Int, videoIds: String): ReqResponse<Nothing> {
    return httpRequest {
      mVideoApi.deleteVideo(
        ZPRequestBody()
          .apply {
            put("userID", userId)
            put("ids", videoIds)
          }
      )
    }
  }

  suspend fun getVideoList(
    userId: Int,
    @Type videoType: Int,
    pageIndex: Int,
    pageSize: Int,
    recommendVideoId: Int = 0,
    cityId: Int = 0,
    keyword: String = "",
  ): ReqResponse<VideoListResponse> {
    return httpRequestResultOrigin {
      mVideoApi.getVideoList(
        ZPRequestBody().apply {
          put("userID", userId)
          put("subType", videoType)
          put("pageIndex", pageIndex)
          put("pageSize", pageSize)
          put("videoID", recommendVideoId)
          put("cityID", if (videoType == VideoListType.ADDRESS) cityId else 0)
          put("title", keyword)
        }
      ).apply {
        if (status == 10002) {
          status = 10001
          notHasFollowRecommend = true
        }
      }
    }
  }

  suspend fun reportInfo(userId: Int, videoId: Int, reason: String): ReqResponse<Nothing> {
    return httpRequest {
      mVideoApi.reportInfo(
        ZPRequestBody().apply {
          put("userID", userId)
          put("videoID", videoId)
          put("reason", reason)
        }
      )
    }
  }

  suspend fun videoRecruitSignUp(
    videoId: Int,
    name: String,
    phone: String,
  ): ReqResponse<String> {
    return httpRequest {
      mVideoApi.videoRecruitSignUp(
        ZPRequestBody().apply {
          put("name", name)
          put("id", videoId)
          put("phone", phone)
        }
      ).apply {
        if (status == 10001) {
          data = msg
        }
      }
    }
  }

  suspend fun getUserSignInInfo(userId: Int): ReqResponse<VideoSignInData> {
    return httpRequest {
      mVideoApi.getUserSignInData(
        ZPRequestBody().apply {
          put("userID", userId)
        }
      )
    }
  }

  /**
   * 添加视频播放数量
   */
  suspend fun addVideoPlayCount(videoID: Int): ReqResponse<Nothing> {
    return httpRequest {
      mVideoApi.addVideoPlayCount(
        ZPRequestBody().apply {
          put("id", videoID)
        }.paramsEncrypt()
      )
    }
  }
}