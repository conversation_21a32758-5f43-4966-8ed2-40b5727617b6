package com.bxkj.video.openapi

import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.staggeredlist.data.VideoListResponse
import com.bxkj.video.data.VideoSignInData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Project: gzgk
 * @Description: 视频API
 * @author:45457
 * @date: 2020/7/18
 * @version: V1.0
 */
interface VideoApi {
  @POST(VideoApiConstants.I_DELETE_VIDEO)
  suspend fun deleteVideo(
    @Body requestBody: ZPRequestBody,
  ): BaseResponse<Nothing>

  @POST(VideoApiConstants.I_GET_VIDEO_LIST)
  suspend fun getVideoList(
    @Body requestBody: ZPRequestBody,
  ): VideoListResponse

  @POST(VideoApiConstants.I_REPORT_INFO)
  suspend fun reportInfo(
    @Body requestBody: ZPRequestBody,
  ): BaseResponse<Nothing>

  @POST(VideoApiConstants.I_VIDEO_RECRUIT_SIGN_UP)
  suspend fun videoRecruitSignUp(
    @Body mRequestBody: ZPRequestBody,
  ): BaseResponse<String>

  @POST(VideoApiConstants.I_GET_USER_SIGN_IN_INFO)
  suspend fun getUserSignInData(
    @Body mRequestBody: ZPRequestBody,
  ): BaseResponse<VideoSignInData>

  @POST("/Video/AddShipin2View/")
  suspend fun addVideoPlayCount(
    @Body encryptReqParams: EncryptReqParams,
  ): BaseResponse<Nothing>
}
