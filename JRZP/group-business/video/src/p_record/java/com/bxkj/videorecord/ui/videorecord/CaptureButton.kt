package com.bxkj.videorecord.ui.videorecord

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.bxkj.video.R
import kotlin.math.max
import kotlin.math.min

/**
 * @date 2024/11/22
 * <AUTHOR>
 */
class CaptureButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var outerCircleWidth = 0
    private var outerCircleColor = Color.WHITE
    private var circleMargin = 30
    private var circleColor = Color.RED
    private var smallCircleCorner = 0
    private var smallCircleSize = 0

    private var animState = AnimState.IDLE
    private var animPercent = 0f
    private var valueAnim: ValueAnimator? = null

    private val paint = Paint()

    init {
        val typedArray =
            getContext().obtainStyledAttributes(attrs, R.styleable.CaptureButton, defStyleAttr, 0)
        outerCircleWidth =
            typedArray.getDimensionPixelSize(R.styleable.CaptureButton_outerCircleWidth, dip(3))
        outerCircleColor =
            typedArray.getColor(R.styleable.CaptureButton_outerCircleColor, Color.WHITE)
        circleMargin =
            typedArray.getDimensionPixelSize(R.styleable.CaptureButton_circleMargin, dip(2))
        circleColor = typedArray.getColor(R.styleable.CaptureButton_circleColor, Color.RED)
        smallCircleCorner =
            typedArray.getDimensionPixelSize(R.styleable.CaptureButton_smallCircleRadius, dip(4))
        smallCircleSize =
            typedArray.getDimensionPixelSize(R.styleable.CaptureButton_smallCircleSize, dip(30))

        typedArray.recycle()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val size = min(width, height)

        drawOuterCircle(size, canvas)

        drawInnerCircle(size, canvas)
    }

    private fun drawInnerCircle(size: Int, canvas: Canvas) {
        paint.setColor(circleColor)
        paint.style = Paint.Style.FILL
        val circleSize = size - outerCircleWidth * 2 - circleMargin * 2
        val circleCorner = circleSize.toFloat() / 2
        val leftTop = (outerCircleWidth + circleMargin).toFloat()
        val rightBottom = size - leftTop

        val circleSizeDiff = (circleSize - smallCircleSize / 2) / 2
        val cornerDiff = circleCorner - smallCircleCorner

        val smallCircleLeftTop = leftTop + circleSizeDiff
        val smallCircleRightBottom = rightBottom - circleSizeDiff

        var animLeftTop = leftTop
        var animRightBottom = rightBottom
        var animCorner = circleCorner

        when (animState) {
            AnimState.STARTING -> {
                animLeftTop = leftTop + circleSizeDiff * animPercent
                animRightBottom = rightBottom - circleSizeDiff * animPercent
                animCorner = circleCorner - cornerDiff * animPercent
            }

            AnimState.STOPPING -> {
                animLeftTop = smallCircleLeftTop - circleSizeDiff * animPercent
                animRightBottom = smallCircleRightBottom + circleSizeDiff * animPercent
                animCorner = smallCircleCorner + cornerDiff * animPercent
            }

            AnimState.IDLE -> {
                animLeftTop = leftTop
                animRightBottom = rightBottom
                animCorner = circleCorner
            }
        }

        canvas.drawRoundRect(
            animLeftTop,
            animLeftTop,
            animRightBottom,
            animRightBottom,
            animCorner,
            animCorner,
            paint
        )
    }

    private fun drawOuterCircle(size: Int, canvas: Canvas) {
        paint.setColor(outerCircleColor)
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = outerCircleWidth.toFloat()
        val radius = size.toFloat() / 2
        canvas.drawCircle(radius, radius, radius - outerCircleWidth / 2, paint)
    }

    fun start() {
        animState = AnimState.STARTING
        runAnim()
    }

    fun stop() {
        animState = AnimState.STOPPING
        runAnim()
    }

    private fun runAnim() {
        if (valueAnim == null) {
            valueAnim = ValueAnimator.ofFloat(0f, 1f).apply {
                addUpdateListener { value ->
                    animPercent = value.animatedValue as Float
                    invalidate()
                }
                duration = 300
            }
        }
        if (valueAnim?.isRunning == true) {
            return
        }
        valueAnim?.start()
    }

    private fun dip(value: Int): Int = (value * resources.displayMetrics.density + 0.5f).toInt()

    private enum class AnimState {
        IDLE, STARTING, STOPPING
    }
}