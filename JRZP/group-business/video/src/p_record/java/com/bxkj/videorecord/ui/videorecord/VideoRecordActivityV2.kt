package com.bxkj.videorecord.ui.videorecord

import android.app.Activity
import android.content.ContentValues
import android.content.Intent
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import android.view.View.OnClickListener
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.video.VideoRecordEvent
import androidx.lifecycle.setViewTreeLifecycleOwner
import com.bxkj.addvideolinkinfo.ui.addvideolinkinfos.AddVideoLinkInfoActivity
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.PermissionUtils.OnRequestResultListener
import com.bxkj.common.util.ZPFileUtils
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.video.R
import com.bxkj.video.VideoConstants
import com.bxkj.video.VideoType
import com.bxkj.video.databinding.ActivityVideoRecordV2Binding
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.tencent.qcloud.ugckit.basic.UGCKitResult
import com.tencent.qcloud.ugckit.utils.CoverUtil
import com.tencent.ugc.TXVideoInfoReader
import com.therouter.router.Route
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.TimeUnit

/**
 * @date 2024/11/22
 * <AUTHOR>
 */
@Route(path = VideoRecordNavigation.PATH)
class VideoRecordActivityV2 : BaseDBActivity<ActivityVideoRecordV2Binding, BaseViewModel>(),
  OnClickListener {

  private var isCancelRecording = false

  //剪辑视频，授权过期停止使用
  private val videoCutLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
      result.data?.let { data ->
        val resultVideo =
          data.getParcelableExtra<UGCKitResult>(VideoConstants.EXTRA_RESULT_VIDEO_INFO)
        resultVideo?.let { result ->
          toNext(result)
        }
      }
    }

  override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.activity_video_record_v2

  override fun initPage(savedInstanceState: Bundle?) {
    statusBarManager.titleBar(viewBinding.titleBar).init()

    viewBinding.onClickListener = this

    checkCaptureVideoPermission()

    viewBinding.cameraxView.apply {
      setViewTreeLifecycleOwner(this@VideoRecordActivityV2)
    }

    viewBinding.recordProgressView.apply {
      setMinDuration(MIN_VIDEO_DURATION)
      setMaxDuration(MAX_VIDEO_DURATION)
    }

    viewBinding.captureButton.setOnClickListener {
      if (viewBinding.cameraxView.isRecording()) {
        stopRecording()
      } else {
        startRecording()
      }
    }
  }

  private fun startRecording() {
    viewBinding.captureButton.start()
    viewBinding.cameraxView.takeVideo(generateVideoContentValues()) { videoEvent ->
      val videoDuration =
        TimeUnit.NANOSECONDS.toMillis(videoEvent.recordingStats.recordedDurationNanos)
      viewBinding.recordProgressView.setProgress(
        videoDuration.toInt()
      )
      if (videoDuration >= MAX_VIDEO_DURATION && viewBinding.cameraxView.isRecording()) {
        stopRecording()
      }
      when (videoEvent) {
        is VideoRecordEvent.Start -> {
          viewBinding.ivSwitchCamera.visibility = View.GONE
          viewBinding.ivAlbum.visibility = View.GONE
        }

        is VideoRecordEvent.Finalize -> {
          if (isCancelRecording) {
            ZPFileUtils.deleteFileByUri(
              contentResolver,
              videoEvent.outputResults.outputUri
            )
            finish()
          } else {
            viewBinding.ivSwitchCamera.visibility = View.VISIBLE
            viewBinding.ivAlbum.visibility = View.VISIBLE

            if (videoDuration < MIN_VIDEO_DURATION) {
              showToast("录制时间太短，请重新录制")
              viewBinding.recordProgressView.setProgress(0)
              viewBinding.recordProgressView.clipComplete()
              ZPFileUtils.deleteFileByUri(
                contentResolver,
                videoEvent.outputResults.outputUri
              )
            } else {
              ZPFileUtils.getFileAbsolutePath(
                this,
                videoEvent.outputResults.outputUri
              )?.let {
                getVideoUGCResult(it) { result ->
                  toNext(result)
                }
              }
            }
          }
        }
      }
    }
  }

  private fun checkCaptureVideoPermission() {
    PermissionUtils.requestPermission(
      this,
      "权限说明",
      "录制视频需要相机、麦克风权限",
      object : OnRequestResultListener {
        override fun onRequestSuccess() {
          viewBinding.cameraxView.startPreview()
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          showToast("权限获取失败")
          finish()
        }
      },
      Permission.CAMERA,
      Permission.RECORD_AUDIO
    )
  }

  /**
   * 获取视频ContentValues信息
   */
  private fun generateVideoContentValues(): ContentValues {
    val name = "Jrzp-recording-" +
      SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.CHINA)
        .format(System.currentTimeMillis()) + ".mp4"
    val contentValues = ContentValues().apply {
      put(MediaStore.MediaColumns.DISPLAY_NAME, name)
    }
    return contentValues
  }

  /**
   * 停止录制
   */
  private fun stopRecording() {
    viewBinding.captureButton.stop()
    viewBinding.cameraxView.stop()
    viewBinding.recordProgressView.setProgress(0)
    viewBinding.recordProgressView.clipComplete()
  }

  override fun onClick(v: View?) {
    v?.let {
      when (it.id) {
        R.id.iv_back -> {
          finish()
        }

        R.id.iv_switch_camera -> {
          viewBinding.cameraxView.switchCamera()
        }

        R.id.iv_album -> {
          toSelectLocalVideo()
        }
      }
    }
  }

  override fun finish() {
    if (viewBinding.cameraxView.isRecording()) {
      ActionDialog.Builder()
        .setContent("视频正在录制中，是否退出？")
        .setOnConfirmClickListener {
          isCancelRecording = true
          stopRecording()
        }.build().show(supportFragmentManager)
    } else {
      super.finish()
    }
  }

  private fun toSelectLocalVideo() {
    PermissionUtils.requestPermission(
      this,
      getString(R.string.permission_tips_title),
      getString(R.string.permission_select_img_tips),
      object : OnRequestResultListener {
        override fun onRequestSuccess() {
          PictureSelector.create(this@VideoRecordActivityV2)
            .openGallery(SelectMimeType.ofVideo())
            .isDisplayCamera(false)
            .setSelectionMode(SelectModeConfig.SINGLE)
            .setFilterVideoMaxSecond(60)
            .setSandboxFileEngine(SandboxFileEngine.getInstance())
            .setCompressEngine(ImageCompressEngine.getInstance())
            .setImageEngine(GlideEngine.getInstance())
            .setImageSpanCount(4)
            .forResult(object : OnResultCallbackListener<LocalMedia> {
              override fun onResult(result: ArrayList<LocalMedia>?) {
                result?.let {
                  if (it.isNotEmpty()) {
                    getVideoUGCResult(it[0].availablePath) { result ->
                      toNext(result)
//                      videoCutLauncher.launch(
//                        VideoCutNavigation.navigate(
//                          result.outputPath, TXRecordCommon.VIDEO_RESOLUTION_540_960,
//                          VideoConstants.VIDEO_DEFAULT_BITRATE
//                        ).createIntent(this@VideoRecordActivityV2)
//                      )
                    }
                  }
                }
              }

              override fun onCancel() {
              }
            })
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          showToast(getString(R.string.cancel_operation))
        }
      },
      Permission.WRITE_EXTERNAL_STORAGE,
      Permission.READ_EXTERNAL_STORAGE
    )
  }

  private fun toNext(videoInfo: UGCKitResult) {
    when (intent.getIntExtra(
      VideoRecordNavigation.EXTRA_NEXT_STEP,
      VideoRecordNavigation.NEXT_BACK
    )) {
      VideoRecordNavigation.NEXT_BACK -> {    //带数据返回
        val resultIntent = Intent()
        resultIntent.putExtra(VideoConstants.EXTRA_RESULT_VIDEO_INFO, videoInfo)
        setResult(Activity.RESULT_OK, resultIntent)
        finish()
      }

      VideoRecordNavigation.NEXT_BIND_RESUME -> { //求职视频
        startActivity(
          AddVideoLinkInfoActivity.newIntent(
            this@VideoRecordActivityV2,
            videoInfo,
            VideoType.VIDEO_TYPE_RESUME
          )
        )
      }

      VideoRecordNavigation.NEXT_BIND_JOB -> {  //招聘视频
        startActivity(
          AddVideoLinkInfoActivity.newIntent(
            this@VideoRecordActivityV2,
            videoInfo,
            VideoType.VIDEO_TYPE_RECRUIT
          )
        )
      }
    }
  }

  private fun getVideoUGCResult(videoPath: String, resultCallback: (UGCKitResult) -> Unit) {
    val result = UGCKitResult.fromUncompress(videoPath)
    TXVideoInfoReader.getInstance(this).getVideoFileInfo(videoPath).let {
      result.width = it.width
      result.height = it.height
    }
    CoverUtil.getInstance().setInputPath(videoPath)
    showLoading()
    CoverUtil.getInstance().createThumbFile {
      hiddenLoading()
      result.coverPath = it
      resultCallback.invoke(result)
    }
  }

  override fun onDestroy() {
    viewBinding.recordProgressView.release()
    super.onDestroy()
  }

  companion object {
    private const val MIN_VIDEO_DURATION = 6 * 1000
    private const val MAX_VIDEO_DURATION = 60 * 1000
  }

}