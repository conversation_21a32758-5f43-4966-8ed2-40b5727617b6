package com.bxkj.staggeredlist.ui.follow

import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
import androidx.recyclerview.widget.RecyclerView
import com.therouter.router.Route
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.getCenterXY
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.video.BR
import com.bxkj.video.R
import com.bxkj.video.R.drawable
import com.bxkj.video.R.layout
import com.bxkj.video.data.OnlineVideoData
import com.bxkj.video.data.VideoData
import com.bxkj.video.databinding.VideoFollowVideoListBinding
import com.bxkj.video.message.VideoListMassage
import com.bxkj.video.ui.galllery.VideoGalleryNavigation
import com.bxkj.video.ui.galllery.VideoGalleryType
import com.youth.banner.util.LogUtils

/**
 * 关注的视频列表
 * @author: YangXin
 * @date: 2021/6/2
 */
@Route(path = FollowVideoNavigation.PATH)
class FollowVideoFragment : BaseDBFragment<VideoFollowVideoListBinding, FollowVideoViewModel>() {

  private var followVideoListAdapter: MultiTypeAdapter? = null

  override fun getViewModelClass(): Class<FollowVideoViewModel> = FollowVideoViewModel::class.java

  override fun getLayoutId(): Int = R.layout.video_follow_video_list

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupVideoListAdapter()
    subscribeRxBusMsg()

    viewModel.refreshPage()
  }

  private fun subscribeRxBusMsg() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe {
          if (it.code == RxMsgCode.ACTION_VIDEO_GALLERY_POSITION_CHANGE) {   //图片浏览position改变

            val currentVideoId = it.msg as Int
            followVideoListAdapter?.let { adapter ->
              adapter.data.find { item ->
                if (item is VideoData) {
                  item.id == currentVideoId
                } else {
                  false
                }
              }?.let { result ->
                (result as VideoData).addViewCount()
              }
            }
          } else if (it.code == RxMsgCode.VIDEO_GALLERY_LOAD_MORE) { //图片浏览加载更多
            val loadMorePageTag = it.msg as String
            LogUtils.d("loadMorePageTag:${loadMorePageTag}")
            LogUtils.d("myTag:${this}")
            if (loadMorePageTag == this.toString()) {
              viewModel.loadMoreVideo()
            }
          } else if (it.code == RxMsgCode.ACTION_LOGIN_SUCCESS) {
            viewModel.refreshPage()
          }
        }
    )
  }

  private fun setupVideoListAdapter() {
    followVideoListAdapter = MultiTypeAdapter(parentActivity)
      .apply {
        register(
          OnlineVideoData::class.java,
          DefaultViewBinder<OnlineVideoData>(
              layout.video_recycler_staggered_video_list_item,
              BR.data
          ).apply {
            setOnItemClickListener(object :
              DefaultViewBinder.OnItemClickListener<OnlineVideoData> {
              override fun onItemClicked(v: View, position: Int, item: OnlineVideoData) {
                val noVideoTipsPosition = data.indexOfFirst { it is String }
                val realPosition =
                  if (noVideoTipsPosition == -1) { //没有提示
                    position
                  } else {  //有推荐提示
                    (if (position > noVideoTipsPosition) position - 1 else position)
                  }
                val videoList = data.toMutableList().filter {
                  (it is VideoData)
                }.map {
                  it as VideoData
                }

                VideoGalleryNavigation.navigate(
                  VideoGalleryType.MULTIPLE_VIDEO,
                  VideoListMassage.from(
                    realPosition,
                    videoList,
                    VideoGalleryType.MULTIPLE_VIDEO
                  ),
                  fromPageTag = <EMAIL>(),
                  revealCenter = VideoGalleryNavigation.Companion.RevealCenter(
                    v.getCenterXY()[0],
                    v.getCenterXY()[1]
                  )
                ).start()
              }
            })
          }
        )
        register(String::class.java,
          DefaultViewBinder<String>(R.layout.video_layou_no_info_tips, BR.data))
      }

    val videoList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    videoList.layoutManager = GridLayoutManager(parentActivity, 2).apply {
      spanSizeLookup = object : SpanSizeLookup() {
        override fun getSpanSize(position: Int): Int {
          return followVideoListAdapter?.let {
            if (it.data[position] is String) {
              2
            } else {
              1
            }
          } ?: 1
        }
      }
    }
    videoList.addItemDecoration(
      GridItemDecoration(ContextCompat.getDrawable(parentActivity, drawable.divider_4)) {
        val videoList = viewModel.videoListViewModel.data
        if (videoList.isNullOrEmpty()) {
          false
        } else {
          if (it < videoList.size) {
            videoList[it] !is String
          } else {
            false
          }
        }
      }
    )
    viewModel.videoListViewModel.setAdapter(followVideoListAdapter)
  }
}