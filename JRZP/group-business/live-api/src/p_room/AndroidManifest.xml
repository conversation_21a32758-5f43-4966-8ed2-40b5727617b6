<manifest package="com.bxkj.jrzp.live.api">
  <uses-permission android:name="android.permission.INTERNET" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.RECORD_AUDIO" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.BLUETOOTH" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.CAMERA" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-permission android:name="android.permission.READ_PHONE_STATE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-feature android:name="android.hardware.Camera" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <uses-feature android:name="android.hardware.camera.autofocus" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <application android:allowBackup="false" xmlns:android="http://schemas.android.com/apk/res/android" tools:replace="android:allowBackup" xmlns:tools="http://schemas.android.com/tools">
    <activity android:name=".room.ui.createroom.CreateLiveRoomActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name=".room.ui.roomlist.LiveRoomListActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
    <activity android:name=".room.ui.livenotice.LiveNoticeActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:screenOrientation="portrait"/>
  </application>
</manifest>
