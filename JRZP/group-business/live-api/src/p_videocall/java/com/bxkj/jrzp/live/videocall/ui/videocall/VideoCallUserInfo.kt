package com.bxkj.jrzp.live.videocall.ui.videocall

import android.os.Parcel
import android.os.Parcelable
import android.os.Parcelable.Creator
import com.bxkj.common.util.kotlin.getOrDefault

/**
 *
 * @author: YangXin
 * @date: 2021/3/12
 */
data class VideoCallUserInfo(
  val userId: Int,
  val userName: String,
  val userAvatar: String
) : Parcelable {
  constructor(parcel: Parcel) : this(
    parcel.readInt(),
    parcel.readString().getOrDefault(),
    parcel.readString().getOrDefault()
  ) {
  }

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeInt(userId)
    parcel.writeString(userName)
    parcel.writeString(userAvatar)
  }

  override fun describeContents(): Int {
    return 0
  }

  companion object CREATOR : Creator<VideoCallUserInfo> {
    override fun createFromParcel(parcel: Parcel): VideoCallUserInfo {
      return VideoCallUserInfo(parcel)
    }

    override fun newArray(size: Int): Array<VideoCallUserInfo?> {
      return arrayOfNulls(size)
    }
  }

}