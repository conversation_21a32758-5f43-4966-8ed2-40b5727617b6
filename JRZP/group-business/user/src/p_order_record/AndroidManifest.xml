<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="com.bxkj.jrzp.user">

  <application
    android:allowBackup="false"
    tools:replace="android:allowBackup">

    <activity
      android:name="com.bxkj.jrzp.orderrecord.ui.review.CourseReviewActivity"
      android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
      android:screenOrientation="portrait" />

    <activity
      android:name="com.bxkj.jrzp.orderrecord.ui.details.CourseOrderDetailsActivity"
      android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
      android:screenOrientation="portrait" />

    <activity
      android:name="com.bxkj.jrzp.orderrecord.ui.ordergroup.OrderGroupActivity"
      android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
      android:screenOrientation="portrait" />

    <activity
      android:name="com.bxkj.jrzp.orderrecord.ui.servceorderdetails.ServiceOrderDetailsActivity"
      android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
      android:screenOrientation="portrait" />
  </application>

</manifest>