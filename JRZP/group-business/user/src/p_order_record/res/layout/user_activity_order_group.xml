<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout style="@style/match_match"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_52">

            <ImageView
                android:id="@+id/iv_back"
                style="@style/wrap_wrap"
                android:layout_marginStart="@dimen/dp_8"
                android:src="@drawable/common_ic_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <net.lucode.hackware.magicindicator.MagicIndicator
                android:id="@+id/indicator_info_type"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_0"
                android:layout_marginStart="@dimen/dp_16"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View style="@style/Line.Horizontal.Light" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>
</layout>