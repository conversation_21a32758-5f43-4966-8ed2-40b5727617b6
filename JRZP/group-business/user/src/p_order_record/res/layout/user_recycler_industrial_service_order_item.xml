<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.jrzp.orderrecord.data.IndustrialServiceOrderData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_14"
    android:paddingEnd="@dimen/dp_14">

    <LinearLayout
      android:id="@+id/ll_business_info"
      style="@style/match_wrap"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      app:layout_constraintEnd_toStartOf="@id/tv_order_state"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent">

      <com.google.android.material.imageview.ShapeableImageView
        android:layout_width="@dimen/user_courses_order_business_logo_size"
        android:layout_height="@dimen/user_courses_order_business_logo_size"
        bind:imgUrl="@{data.fixBusinessLogo}"
        app:shapeAppearance="@style/roundedCornerImageStyle" />

      <TextView
        style="@style/Text.14sp.333333"
        android:layout_marginStart="@dimen/dp_4"
        android:ellipsize="end"
        android:lines="1"
        android:text="@{data.businessName}" />

    </LinearLayout>

    <TextView
      android:id="@+id/tv_order_state"
      style="@style/common_Text.12sp.ff4040"
      android:text="@{data.payStatusName}"
      app:layout_constraintBottom_toBottomOf="@id/ll_business_info"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/ll_business_info" />

    <ImageView
      android:id="@+id/iv_cover"
      android:layout_width="@dimen/user_courses_order_cover_size"
      android:layout_height="@dimen/user_courses_order_cover_size"
      android:layout_marginTop="@dimen/dp_12"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/ll_business_info"
      bind:loadRadiusImg="@{data.fixProductImg}" />

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.14sp.333333.Bold"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_16"
      android:ellipsize="end"
      android:maxLines="2"
      android:text="@{data.productName}"
      app:layout_constraintEnd_toStartOf="@id/tv_price"
      app:layout_constraintStart_toEndOf="@id/iv_cover"
      app:layout_constraintTop_toTopOf="@id/iv_cover" />

    <TextView
      android:id="@+id/tv_price"
      style="@style/Text.14sp.FE6600.Bold"
      android:text="@{@string/user_industrial_service_order_price_format(data.price)}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_type"
      style="@style/Text.12sp.999999"
      android:layout_marginTop="@dimen/dp_6"
      android:layout_marginBottom="@dimen/dp_4"
      android:text="@{@string/user_courses_order_type_format(data.productTypeName)}"
      app:layout_constraintBottom_toTopOf="@id/tv_desc"
      app:layout_constraintStart_toStartOf="@id/tv_title"
      app:layout_constraintTop_toBottomOf="@id/tv_title"
      app:layout_constraintVertical_bias="1" />

    <TextView
      android:id="@+id/tv_desc"
      style="@style/Text.12sp.999999"
      android:layout_width="@dimen/dp_0"
      android:layout_marginBottom="@dimen/dp_4"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.productAttribute}"
      app:layout_constraintBottom_toBottomOf="@id/iv_cover"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_type" />

    <LinearLayout
      android:id="@+id/ll_options"
      style="@style/wrap_wrap"
      android:layout_marginTop="@dimen/dp_12"
      android:orientation="horizontal"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toBottomOf="@id/iv_cover">

      <TextView
        android:id="@+id/tv_cancel_order"
        style="@style/Text.12sp.999999"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/frame_eaeaea_round"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_4"
        android:text="@string/user_courses_order_cancel"
        android:visibility="@{data.payStatus==0?View.VISIBLE:View.GONE}"
        app:layout_constraintEnd_toStartOf="@id/tv_go_pay"
        app:layout_constraintTop_toBottomOf="@id/iv_cover" />

      <TextView
        android:id="@+id/tv_go_pay"
        style="@style/Text.12sp.FFFFFF"
        android:background="@drawable/bg_fe6600_round"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_4"
        android:text="@string/user_courses_order_pay"
        android:visibility="@{data.payStatus==0?View.VISIBLE:View.GONE}"
        app:layout_constraintEnd_toStartOf="@id/tv_review"
        app:layout_constraintTop_toBottomOf="@id/iv_cover" />

      <TextView
        android:id="@+id/tv_review"
        style="@style/Text.12sp.FFFFFF"
        android:layout_marginStart="@dimen/dp_16"
        android:background="@drawable/bg_fe6600_round"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_4"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_4"
        android:text="@{data.productScore==0?@string/user_courses_order_review:@string/user_courses_order_additional_review}"
        android:visibility="@{data.canReview?View.VISIBLE:View.GONE}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_cover" />

    </LinearLayout>

    <View
      style="@style/Line.Horizontal"
      android:layout_marginTop="@dimen/dp_12"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/ll_options" />
  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>