package com.bxkj.jrzp.orderrecord.data

import android.os.Parcel
import android.os.Parcelable
import android.os.Parcelable.Creator
import android.text.Spanned
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.HtmlUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.user.api.BR

/**
 * @Description: 课程订单
 * @author:45457
 * @date: 2020/7/30
 * @version: V1.0
 */
data class CoursesOrderData(
  var id: Int = 0,
  var type: Int = 0,
  var typeName: String? = "",
  var kcid: Int = 0,
  var kcName: String? = "",
  var kcPic: String? = "",
  var content: String? = "",
  var price: Float = 0f,
  var time: String? = "",
  var isPay: Int = 0,
  var num: String? = "",
  var payNum: String? = "",
  var payType: Int,
  var payTime: String? = "",

  var pintuanEdate: String? = "",

  var comName: String? = "",
  var logo: String? = "",

  @get:Bindable
  var pingjia: Int = 0,

  var uName: String? = "",
  var sexName: String? = "",
  var mobile: String? = "",
  var kefuPhone: String? = "",
  var pintuanTh: String? = "",
  var pintuanTz: Int = 0,
  var pintuanCount: Int,
  var pintuanState: Int,
  var pintuanList: List<OrderMember>? = null,

  var canshu1: Int,
  var canshu2: Int,
  var canshu3: Int
) : BaseObservable(), Parcelable {

  constructor(parcel: Parcel) : this(
    parcel.readInt(),
    parcel.readInt(),
    parcel.readString(),
    parcel.readInt(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString(),
    parcel.readFloat(),
    parcel.readString(),
    parcel.readInt(),
    parcel.readString(),
    parcel.readString(),
    parcel.readInt(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString(),
    parcel.readInt(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString(),
    parcel.readString(),
    parcel.readInt(),
    parcel.readInt(),
    parcel.readInt(),
    parcel.createTypedArrayList(OrderMember),
    parcel.readInt(),
    parcel.readInt(),
    parcel.readInt()
  ) {
  }

  fun getRealPicUrl(): String? {
    return if (kcPic!!.contains("http")) {
      kcPic
    } else {
      CommonApiConstants.BASE_JRZP_IMG_URL + kcPic
    }
  }

  fun paid(): Boolean {
    return isPay == 1
  }

  fun isSkill(): Boolean {
    return type == 1
  }

  /**
   * 订单状态
   */
  fun getOrderStateText(): String {
    return if (isPay == 0) {
      "待支付"
    } else if (paid()) {
      if (groupBuying()) {
        "拼单中"
      } else if (pintuanState == 3) {
        "拼团失败，退款中"
      } else {
        if (pingjia == 0) {
          "待评价"
        } else {
          "已评价"
        }
      }
    } else {
      if (pintuanState == 3) {
        "拼团失败，已退款"
      } else {
        "已退款"
      }
    }
  }

  /**
   * 获取格式化金额
   */
  fun getFormatPriceText(): Spanned {
    return HtmlUtils.fromHtml(String.format("<small>￥</small>%.2f", price))
  }

  fun getPaymentMethodText(): String {
    return when (payType) {
      1 -> {
        "支付宝"
      }
      2 -> {
        "微信"
      }
      else -> {
        "未知"
      }
    }
  }

  /**
   * 可评论
   */
  @Bindable
  fun getCanReview(): Boolean {
    return (paid() || isPay == -1) && //已支付
        pingjia != 2 && //未评价
        (pintuanState == 0 || pintuanState == 2)  //未拼团或拼团成功
  }

  @Bindable
  fun getGroupBuyResultText(): String {
    return when (pintuanState) {
      1 -> {
        "拼团中"
      }
      2 -> {
        "拼团成功"
      }
      3 -> {
        getOrderStateText()
      }
      else -> {
        "拼团中"
      }
    }
  }

  fun groupBuyFailed() {
    pintuanState = 3
    notifyPropertyChanged(BR.groupBuyResultText)
  }

  fun getGroupRemainingCount(): Int {
    return pintuanCount - (pintuanList?.size ?: 0)
  }

  /**
   * 是否是拼单
   */
  fun isGroupBuy(): Boolean {
    return pintuanState != 0
  }

  /**
   * 拼团中
   */
  fun groupBuying(): Boolean {
    return pintuanState == 1
  }

  /**
   * 有拼团成员
   */
  fun hasGroupBuyMember(): Boolean {
    return !pintuanList.isNullOrEmpty()
  }

  /**
   * 只有一个团员
   */
  fun onlyOneMember(): Boolean {
    return hasGroupBuyMember() && pintuanList?.size == 1
  }

  /**
   * 评论成功
   */
  fun addReviewSuccess() {
    pingjia += 1
    notifyPropertyChanged(BR.pingjia)
  }

  fun updateReviewState(state: Int) {
    pingjia = state
    notifyPropertyChanged(BR.pingjia)
    notifyPropertyChanged(BR.canReview)
  }

  override fun equals(other: Any?): Boolean {
    return if (other == null) {
      false
    } else {
      return if (other is CoursesOrderData) {
        other.id == this.id
      } else {
        false
      }
    }
  }

  data class OrderMember(var photo: String) : Parcelable {
    constructor(parcel: Parcel) : this(parcel.readString().getOrDefault()) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
      parcel.writeString(photo)
    }

    override fun describeContents(): Int {
      return 0
    }

    companion object CREATOR : Creator<OrderMember> {
      override fun createFromParcel(parcel: Parcel): OrderMember {
        return OrderMember(parcel)
      }

      override fun newArray(size: Int): Array<OrderMember?> {
        return arrayOfNulls(size)
      }
    }
  }

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeInt(id)
    parcel.writeInt(type)
    parcel.writeString(typeName)
    parcel.writeInt(kcid)
    parcel.writeString(kcName)
    parcel.writeString(kcPic)
    parcel.writeString(content)
    parcel.writeFloat(price)
    parcel.writeString(time)
    parcel.writeInt(isPay)
    parcel.writeString(num)
    parcel.writeString(payNum)
    parcel.writeInt(payType)
    parcel.writeString(payTime)
    parcel.writeString(pintuanEdate)
    parcel.writeString(comName)
    parcel.writeString(logo)
    parcel.writeInt(pingjia)
    parcel.writeString(uName)
    parcel.writeString(sexName)
    parcel.writeString(mobile)
    parcel.writeString(kefuPhone)
    parcel.writeString(pintuanTh)
    parcel.writeInt(pintuanTz)
    parcel.writeInt(pintuanCount)
    parcel.writeInt(pintuanState)
    parcel.writeTypedList(pintuanList)
    parcel.writeInt(canshu1)
    parcel.writeInt(canshu2)
    parcel.writeInt(canshu3)
  }

  override fun describeContents(): Int {
    return 0
  }

  override fun toString(): String {
    return "CoursesOrderData(id=$id, type=$type, typeName=$typeName, kcid=$kcid, kcName=$kcName, kcPic=$kcPic, content=$content, price='$price', time='$time', isPay=$isPay, num='$num', payNum='$payNum', payType=$payType, payTime='$payTime', pintuanEdate='$pintuanEdate', comName='$comName', logo='$logo', pingjia=$pingjia, uName=$uName, sexName=$sexName, mobile=$mobile, kefuPhone='$kefuPhone', pintuanTh='$pintuanTh', pintuanTz=$pintuanTz, pintuanCount=$pintuanCount, pintuanState=$pintuanState, pintuanList=$pintuanList, canshu1=$canshu1, canshu2=$canshu2, canshu3=$canshu3)"
  }

  companion object CREATOR : Creator<CoursesOrderData> {
    override fun createFromParcel(parcel: Parcel): CoursesOrderData {
      return CoursesOrderData(parcel)
    }

    override fun newArray(size: Int): Array<CoursesOrderData?> {
      return arrayOfNulls(size)
    }
  }

}