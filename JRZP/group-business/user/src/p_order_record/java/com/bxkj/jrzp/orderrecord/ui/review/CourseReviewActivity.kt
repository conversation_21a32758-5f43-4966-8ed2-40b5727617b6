package com.bxkj.jrzp.orderrecord.ui.review

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.recyclerutil.GridItemDecoration
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.jrzp.orderrecord.CourseOrderRxCode
import com.bxkj.jrzp.orderrecord.data.CourseRatingTagData
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityCourseReviewBinding
import com.bxkj.support.upload.data.FileItem
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.therouter.router.Route

/**
 * @Description: 课程评价
 * @author:45457
 * @date: 2020/8/12
 * @version: V1.0
 */
@Route(path = CourseReviewNavigation.PATH)
class CourseReviewActivity :
  BaseDBActivity<UserActivityCourseReviewBinding, CourseReviewViewModel>(), OnClickListener {

  private var mCourseReviewTagsAdapter: CourseReviewTagsAdapter? = null

  override fun getViewModelClass(): Class<CourseReviewViewModel> = CourseReviewViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_course_review

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    setupReviewPicListAdapter()

    setupRatingBarChangeListener()

    subscribeViewModelEvent()

    viewModel.setupCourseOrder(
      intent.getParcelableExtra(CourseReviewNavigation.EXTRA_COURSE_ORDER),
      intent.getParcelableExtra(CourseReviewNavigation.EXTRA_SERVICE_ORDER)
    )
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.tv_submit) {
        if (viewModel.getReviewType() == CourseReviewViewModel.REVIEW_COURSE) {
          mCourseReviewTagsAdapter?.let {
            viewModel.submit(it.selectedItems)
          }
        } else {
          viewModel.submitServiceComment()
        }
      }
    }
  }

  private fun setupRatingBarChangeListener() {
    viewBinding.ratingCourse.setOnRatingChangeListener { _, rating, _ ->
      viewModel.setupCourseRating(rating)
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.reviewTags.observe(this, Observer {
      setupTagListAdapter(it)
    })

    viewModel.reviewCourseSuccessEvent.observe(this, Observer {
      RxBus.get().post(RxBus.Message(CourseOrderRxCode.ADD_COURSE_ORDER_REVIEW_SUCCESS, it))
      finish()
    })

    viewModel.reviewServiceSuccessEvent.observe(this, Observer {
      RxBus.get().post(RxBus.Message(CourseOrderRxCode.ADD_SERVICE_ORDER_COMMENT_SUCCESS, it))
      finish()
    })
  }

  private fun setupTagListAdapter(tags: List<CourseRatingTagData>) {
    viewBinding.recyclerRatingTag.layoutManager =
      LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
    viewBinding.recyclerRatingTag.addItemDecoration(
      LineItemDecoration(
        ContextCompat.getDrawable(
          this,
          R.drawable.divider_8
        ), LinearLayoutManager.HORIZONTAL
      )
    )
    mCourseReviewTagsAdapter =
      CourseReviewTagsAdapter(this, R.layout.user_recycler_course_review_tag_item).apply {
        addAll(tags)
      }
    viewBinding.recyclerRatingTag.adapter = mCourseReviewTagsAdapter
  }

  private fun setupReviewPicListAdapter() {
    val photoListAdapter =
      SimpleDBListAdapter<FileItem>(this, R.layout.upload_recycler_photo_item)
        .apply {
          setOnItemClickListener(object :
            SuperItemClickListener {
            override fun onClick(v: View, position: Int) {
              if (v.id == R.id.iv_delete) {
                viewModel.deletePhoto(position)
              } else {
                data.let {
                  val photoItem = it[position]
                  if (photoItem.isAddItem) {
                    PermissionUtils.requestPermission(
                      this@CourseReviewActivity,
                      getString(R.string.permission_tips_title),
                      getString(R.string.permission_select_img_tips),
                      object : PermissionUtils.OnRequestResultListener {
                        override fun onRequestSuccess() {
                          PictureSelector.create(this@CourseReviewActivity)
                            .openGallery(SelectMimeType.ofImage())
                            .setSandboxFileEngine(SandboxFileEngine.getInstance())
                            .setCompressEngine(ImageCompressEngine.getInstance())
                            .setMaxSelectNum(viewModel.getAvailableImgNum())
                            .setImageEngine(GlideEngine.getInstance())
                            .setImageSpanCount(4)
                            .forResult(PictureConfig.CHOOSE_REQUEST)
                        }

                        override fun onRequestFailed(
                          permissions: MutableList<String>,
                          never: Boolean
                        ) {
                          showToast(getString(R.string.cancel_operation))
                        }
                      },
                      Permission.WRITE_EXTERNAL_STORAGE,
                      Permission.READ_EXTERNAL_STORAGE
                    )
                  } else {
                  }
                }
              }
            }
          }, R.id.iv_delete)
        }
    viewBinding.recyclerPic.layoutManager = GridLayoutManager(this, 3)
    viewBinding.recyclerPic.addItemDecoration(
      GridItemDecoration(
        ContextCompat.getDrawable(
          this,
          R.drawable.user_divider_course_review_pic
        )
      )
    )
    viewModel.setPhotoAdapter(photoListAdapter)
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == PictureConfig.CHOOSE_REQUEST) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        val pathList = PictureSelector.obtainSelectorList(data)
        viewModel.uploadFileList(pathList)
      }
    }
  }

}