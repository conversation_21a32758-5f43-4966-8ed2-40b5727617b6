package com.bxkj.jrzp.orderrecord.ui.courseorder

import android.os.Bundle
import android.view.View
import com.bxkj.common.adapter.indicator.MagicIndicatorAdapter
import com.bxkj.common.adapter.indicator.OnTabClickListener
import com.bxkj.common.adapter.viewpager.CommonPagerAdapter
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.jrzp.orderrecord.ui.list.CoursesOrderListFragment
import com.bxkj.jrzp.orderrecord.ui.list.OrderPaymentStatus
import com.bxkj.jrzp.orderrecord.ui.list.CoursesOrderReviewStatus
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityCoursesOrderBinding
import net.lucode.hackware.magicindicator.ViewPagerHelper
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/30
 * @version: V1.0
 */
class CoursesOrderFragment :
    BaseDBFragment<UserActivityCoursesOrderBinding, BaseViewModel>() {

    companion object {
        fun newInstance(): CoursesOrderFragment {
            return CoursesOrderFragment()
        }
    }

    override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

    override fun getLayoutId(): Int = R.layout.user_activity_courses_order

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        setupVpContent()
    }

    override fun initImmersionBar() {
        statusBarManager.statusBarDarkFont(true, 0.4f).init()
    }

    private fun setupVpContent() {
        val contentFragments = arrayListOf(
            CoursesOrderListFragment.newInstance(OrderPaymentStatus.ALL),
            CoursesOrderListFragment.newInstance(OrderPaymentStatus.UNPAID),
            CoursesOrderListFragment.newInstance(OrderPaymentStatus.PAID),
            CoursesOrderListFragment.newInstance(
                OrderPaymentStatus.ALL,
                CoursesOrderReviewStatus.REVIEWED
            )
        )
        viewBinding.vpContent.offscreenPageLimit = contentFragments.size
        viewBinding.vpContent.adapter = CommonPagerAdapter(childFragmentManager, contentFragments)

        setupCoursesOrderTypeIndicator()
    }

    private fun setupCoursesOrderTypeIndicator() {
        viewBinding.indicatorCoursesOrderType.navigator = CommonNavigator(parentActivity).apply {
            isAdjustMode = true
            adapter =
                MagicIndicatorAdapter(resources.getStringArray(R.array.user_order_status_type)).apply {
                    setOnTabClickListener(object : OnTabClickListener {
                        override fun onTabClicked(v: View, index: Int) {
                            viewBinding.vpContent.currentItem = index
                        }
                    })
                }
        }
        ViewPagerHelper.bind(viewBinding.indicatorCoursesOrderType, viewBinding.vpContent)
    }

}