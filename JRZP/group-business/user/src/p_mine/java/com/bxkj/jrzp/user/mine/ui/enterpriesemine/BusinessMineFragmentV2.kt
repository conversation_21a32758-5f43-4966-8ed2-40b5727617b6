package com.bxkj.jrzp.user.mine.ui.enterpriesemine

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.widget.ImageView
import androidx.recyclerview.widget.GridLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.applyDefaultConfig
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.iconselect.IconSelectDialog
import com.bxkj.enterprise.ui.activity.beanmall.BeanMallNavigation
import com.bxkj.enterprise.ui.activity.businesscontact.BusinessContactInfoNavigation
import com.bxkj.enterprise.ui.activity.collection.FavoritesResumeNavigation
import com.bxkj.enterprise.ui.activity.communicatedpeople.CommunicatedPeopleNavigation
import com.bxkj.enterprise.ui.activity.companyhomepage.CompanyHomePageNavigation
import com.bxkj.enterprise.ui.activity.hr.HRManagementNavigation
import com.bxkj.enterprise.ui.activity.interviewmanagement.InterviewManagementNavigation
import com.bxkj.enterprise.ui.activity.membercenter.MemberCenterNavigation
import com.bxkj.enterprise.ui.activity.myjoblist.MyJobListNavigation
import com.bxkj.enterprise.ui.activity.receivedresumelist.ReceivedResumeListNavigation
import com.bxkj.enterprise.ui.activity.recruitmentdata.RecruitmentDataNavigation
import com.bxkj.enterprise.ui.activity.schoolrecruitmanagement.SchoolRecruitManagementNavigation
import com.bxkj.jrzp.live.room.ui.createroom.CreateLiveRoomNavigation
import com.bxkj.jrzp.orderrecord.ui.ordergroup.OrderGroupNavigation
import com.bxkj.jrzp.support.feature.data.CheckInfoTag
import com.bxkj.jrzp.support.feature.data.CheckResultMsg
import com.bxkj.jrzp.support.feature.data.InfoCheckItem
import com.bxkj.jrzp.support.feature.data.InfoCheckItem.Builder
import com.bxkj.jrzp.support.feature.ui.infocheck.CheckInfoCallBackAdapter
import com.bxkj.jrzp.support.feature.ui.infocheck.InfoCompletedCheck
import com.bxkj.jrzp.support.feedback.ui.help.HelpNavigation
import com.bxkj.jrzp.support.scan.ui.qrcode.ScanQrCodeActivity
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.R.layout
import com.bxkj.jrzp.user.R.string
import com.bxkj.jrzp.user.databinding.UserFragmentBusinessMineV2Binding
import com.bxkj.jrzp.user.enterpriseinfo.ui.campustalk.CampusTalkManagementActivity
import com.bxkj.jrzp.user.enterpriseinfo.ui.jobfair.EnterpriseJobFairNavigation
import com.bxkj.jrzp.user.mine.UserFunctionItem
import com.bxkj.jrzp.user.switchidentity.ui.SwitchUserIdentityNavigation
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.ui.activity.fans.FansNavigation
import com.bxkj.personal.ui.activity.myfollowuser.MyFollowNavigation
import com.bxkj.personal.ui.activity.setting.SettingNavigation
import com.bxkj.personal.ui.activity.web.WebNavigation
import com.hjq.permissions.Permission
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.therouter.router.Route

/**
 * @Description: 企业个人主页
 * @author:45457
 * @date: 2020/9/16
 * @version: V1.0
 */
@Route(path = BusinessMineNavigation.PATH)
class BusinessMineFragmentV2 :
  BaseDBFragment<UserFragmentBusinessMineV2Binding, BusinessMineViewModel>(),
  OnClickListener {
  override fun getViewModelClass(): Class<BusinessMineViewModel> = BusinessMineViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_fragment_business_mine_v2

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    setupMoreFunctions()
  }

  private fun subscribeViewModelEvent() {
    viewModel.jumpToWechatCommand.observe(
      this,
      EventObserver {
        SystemUtil.copy(requireContext(), "微信号", it, "")
        ActionDialog
          .Builder()
          .setContent("已复制专属客服微信号，是否前往微信添加？")
          .setConfirmText("前往添加")
          .setOnConfirmClickListener {
            SystemUtil.jumpToWechat(requireContext())
          }.build()
          .show(childFragmentManager)
      },
    )

    viewModel.callServicesPhoneCommand.observe(
      this,
      EventObserver {
        SystemUtil.callPhone(requireContext(), it)
      },
    )
  }

  override fun onResume() {
    super.onResume()
    viewModel.refreshUserInfo()
    if (UserUtils.getUserAuthType() == AuthenticationType.PERSONAL_RECRUITER) {
      viewBinding.llCompanyHome.visibility = View.GONE
    } else {
      viewBinding.llCompanyHome.visibility = View.VISIBLE
    }
  }

  override fun initImmersionBar() {
    statusBarManager.titleBar(viewBinding.llTitleBar).statusBarDarkFont(true, 0.4f).init()
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.iv_scan -> { // 扫码
          startActivity(ScanQrCodeActivity.newIntent(parentActivity))
        }

        R.id.iv_switch_identity -> {
          switchIdentity()
        }

        R.id.iv_setting -> { // 设置
          SettingNavigation.navigate().start()
        }

        R.id.iv_user_header -> {
          toSelectCompanyLogo()
        }

        R.id.tv_user_info, R.id.iv_upgrade_member -> {
          checkAuthStateSuccess {
            MemberCenterNavigation.create().start()
          }
        }

        R.id.tv_company_name, R.id.tv_refresh_resume -> {
          UserHomeNavigation.navigate(localUserId).start()
        }

        R.id.ll_integral -> { // 积分
          BeanMallNavigation.create().start()
        }

        R.id.ll_follow -> { // 关注
          MyFollowNavigation.navigate(localUserId, false).start()
        }

        R.id.ll_fans -> { // 粉丝
          FansNavigation
            .navigate(localUserId, FansNavigation.QUERY_TYPE_PERSONAL)
            .start()
        }

        R.id.ll_job_management -> {
          MyJobListNavigation.navigate().start()
        }

        R.id.ll_communicated_people -> {
          CommunicatedPeopleNavigation.create().start()
        }

        R.id.ll_received_resume -> { // 收到的简历
          ReceivedResumeListNavigation.navigate().start()
        }

        R.id.ll_collection -> {
          FavoritesResumeNavigation.navigate().start()
        }

        R.id.ll_interview -> { // 我的面试
          InterviewManagementNavigation.create().start()
        }

        R.id.tv_my_benefits -> {
          checkAuthStateSuccess {
            WebNavigation
              .navigate("https://jrzpapi2.jdzj.com/page/Quanyi/Quanyi.aspx")
              .start()
          }
        }

        R.id.tv_company_home -> { // 公司主页
          CompanyHomePageNavigation.create().start()
        }

        R.id.tv_function_mall -> { // 功能商城
          checkAuthStateSuccess {
            BeanMallNavigation.create().start()
          }
        }

        R.id.tv_recruitment_data -> { // 招聘数据
          RecruitmentDataNavigation.create().start()
        }
      }
    }
  }

  private fun toSelectCompanyLogo() {
    PermissionUtils.requestPermission(
      requireActivity(),
      getString(R.string.permission_tips_title),
      getString(R.string.permission_select_img_tips),
      object : PermissionUtils.OnRequestResultListener {
        override fun onRequestSuccess() {
          PictureSelector
            .create(this@BusinessMineFragmentV2)
            .openGallery(SelectMimeType.ofImage())
            .applyDefaultConfig()
            .setSelectionMode(SelectModeConfig.SINGLE)
            .isDirectReturnSingle(true)
            .forResult(PictureConfig.CHOOSE_REQUEST)
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          if (never) {
            showToast(getString(R.string.permission_never_no_tips))
          } else {
            showToast(getString(R.string.cancel))
          }
        }
      },
      Permission.WRITE_EXTERNAL_STORAGE,
      Permission.READ_EXTERNAL_STORAGE,
    )
  }

  private fun switchIdentity() {
    SwitchUserIdentityNavigation.navigate().start()
  }

  private fun createLivePreCheck() {
    InfoCompletedCheck
      .with(requireContext())
      .checkItem(
        Builder()
          .checkInfoTag(CheckInfoTag.CHECK_COMPANY_CERTIFICATED)
          .onlyCheck(false)
          .checkTipsTitle(string.check_certificate)
          .checkTipsContent(string.check_certificate_default_tips)
          .build(),
        Builder()
          .checkInfoTag(CheckInfoTag.CHECK_PERSONAL_AUTH)
          .onlyCheck(false)
          .build(),
      ).setCheckInfoCallBack(
        object :
          CheckInfoCallBackAdapter() {
          override fun onAllCheckSuccess(result: CheckResultMsg) {
            CreateLiveRoomNavigation.navigate().start()
          }
        },
      ).start()
  }

  /**
   * 更多功能
   */
  private fun setupMoreFunctions() {
    val moreFunctionAdapter =
      getUserFunctionAdapter().apply {
        setOnItemClickListener(
          object : SuperItemClickListener {
            override fun onClick(
              v: View,
              position: Int,
            ) {
              when (data[position].opId) {
                UserFunctionItem.OP_CONTRACT_INFO -> {
                  checkAuthStateSuccess {
                    BusinessContactInfoNavigation.create().start()
                  }
                }

                UserFunctionItem.OP_HR_MANAGEMENT -> {
                  checkAuthStateSuccess {
                    HRManagementNavigation.navigate().start()
                  }
                }

                UserFunctionItem.OP_SCHOOL_RECRUIT_MANAGEMENT -> {
                  checkAuthStateSuccess {
                    IconSelectDialog
                      .Builder()
                      .addItem(
                        R.drawable.user_ic_enterprise_mine_seminar,
                        getString(R.string.user_enterprise_mine_seminar),
                      ).addItem(R.drawable.user_ic_school_recruit, "校招管理")
                      .addItem(R.drawable.user_ic_campus_talk, "宣讲会管理")
                      .setOnItemClickListener(
                        object : SuperItemClickListener {
                          override fun onClick(
                            v: View,
                            position: Int,
                          ) {
                            when (position) {
                              0 -> {
                                EnterpriseJobFairNavigation.create().start()
                              }

                              1 -> {
                                SchoolRecruitManagementNavigation.create().start()
                              }

                              2 -> {
                                startActivity(
                                  CampusTalkManagementActivity.newIntent(
                                    requireContext(),
                                  ),
                                )
                              }
                            }
                          }
                        },
                      ).build()
                      .show(childFragmentManager)
                  }
                }

                UserFunctionItem.OP_START_LIVE -> {
                  checkAuthStateSuccess {
                    createLivePreCheck()
                  }
                }

                UserFunctionItem.OP_MY_ORDER -> {
                  checkAuthStateSuccess {
                    OrderGroupNavigation.navigate().start()
                  }
                }

                UserFunctionItem.OP_SWITCH_IDENTITY -> {
                  switchIdentity()
                }

                UserFunctionItem.OP_HELP -> {
                  HelpNavigation.navigate().start()
                }
              }
            }
          },
        )
      }

    viewBinding.recyclerMoreFunctions.layoutManager = GridLayoutManager(parentActivity, 4)
    viewBinding.recyclerMoreFunctions.adapter = moreFunctionAdapter

    moreFunctionAdapter.reset(getMoreFunctions())
  }

  private fun checkAuthStateSuccess(success: () -> Unit) {
    InfoCompletedCheck
      .with(parentActivity)
      .checkItem(
        InfoCheckItem
          .Builder()
          .checkInfoTag(CheckInfoTag.CHECK_COMPANY_AUTH_NEW)
          .onlyCheck(false)
          .build(),
      ).setCheckInfoCallBack(
        object : CheckInfoCallBackAdapter() {
          override fun onAllCheckSuccess(result: CheckResultMsg) {
            success.invoke()
          }
        },
      ).start()
  }

  private fun getUserFunctionAdapter(): SimpleDBListAdapter<UserFunctionItem> =
    object : SimpleDBListAdapter<UserFunctionItem>(
      parentActivity,
      layout.user_recycler_mine_user_function_item,
    ) {
      override fun convert(
        holder: SuperViewHolder,
        viewType: Int,
        item: UserFunctionItem,
        position: Int,
      ) {
        super.convert(holder, viewType, item, position)
        holder.findViewById<ImageView>(R.id.iv_op_icon).setImageResource(item.opIcon)
        item.opFlagImg.getOrDefault(-1).let {
          if (it != -1) {
            holder.findViewById<ImageView>(R.id.iv_op_tag).setImageResource(it)
          }
        }
      }
    }

  private fun getMoreFunctions(): MutableList<UserFunctionItem> =
    arrayListOf(
      UserFunctionItem.get(
        UserFunctionItem.OP_CONTRACT_INFO,
        R.drawable.user_ic_enteprise_mine_contract_info,
        getString(R.string.user_enterprise_mine_contract_info),
      ),
      UserFunctionItem.get(
        UserFunctionItem.OP_HR_MANAGEMENT,
        R.drawable.user_ic_enteprise_mine_hr_management,
        getString(R.string.user_enterprise_mine_HR_management),
      ),
      UserFunctionItem.get(
        UserFunctionItem.OP_SCHOOL_RECRUIT_MANAGEMENT,
        R.drawable.user_ic_school_recruit,
        "校招管理",
      ),
      // UserFunctionItem.get(
      //   UserFunctionItem.OP_START_LIVE,
      //   R.drawable.user_ic_start_live,
      //   "直播",
      // ),
      UserFunctionItem.get(
        UserFunctionItem.OP_MY_ORDER,
        R.drawable.user_ic_mine_order,
        "订单/发票",
      ),
      UserFunctionItem.get(
        UserFunctionItem.OP_SWITCH_IDENTITY,
        R.drawable.user_ic_mine_switch_role,
        getString(R.string.user_business_i_want_job),
      ),
      UserFunctionItem.get(
        UserFunctionItem.OP_HELP,
        R.drawable.user_ic_personal_mine_support,
        getString(R.string.user_personal_mine_help),
      ),
    )

  override fun onActivityResult(
    requestCode: Int,
    resultCode: Int,
    data: Intent?,
  ) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == PictureConfig.CHOOSE_REQUEST && resultCode == Activity.RESULT_OK && data != null) {
      viewModel.uploadCompanyLogo(data.getSelectedFirstMediaPath())
    }
  }
}
