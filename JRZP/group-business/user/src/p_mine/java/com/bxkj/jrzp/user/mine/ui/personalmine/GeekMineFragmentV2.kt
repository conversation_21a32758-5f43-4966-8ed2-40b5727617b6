package com.bxkj.jrzp.user.mine.ui.personalmine

import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.recyclerview.widget.GridLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.enterprise.ui.activity.interviewmanagement.InterviewManagementNavigation
import com.bxkj.jrzp.commission.ui.list.CommissionListNavigation
import com.bxkj.jrzp.orderrecord.ui.ordergroup.OrderGroupNavigation
import com.bxkj.jrzp.support.feedback.ui.help.HelpNavigation
import com.bxkj.jrzp.support.scan.ui.qrcode.ScanQrCodeActivity
import com.bxkj.jrzp.user.BR
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserFragmentGeekMineV2Binding
import com.bxkj.jrzp.user.mine.UserFunctionItem
import com.bxkj.jrzp.user.mine.ui.group.MinePageContainerFragment
import com.bxkj.jrzp.user.ui.idcardvalidation.IDCardValidationNavigation
import com.bxkj.jrzp.user.videomanagement.VideoManagementNavigation
import com.bxkj.personal.ui.activity.applicationrecord.ResumeDeliveryRecordNavigation
import com.bxkj.personal.ui.activity.findjobbymap.FindJobByMapNavigation
import com.bxkj.personal.ui.activity.invitationstodelivery.InviteToDeliveryNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoNavigation
import com.bxkj.personal.ui.activity.microresumeinfo.ResumeRouteConstant
import com.bxkj.personal.ui.activity.minesearchjobs.SearchJobNavigation
import com.bxkj.personal.ui.activity.mycollectionjobs.MyCollectionJobsNavigation
import com.bxkj.personal.ui.activity.myfollowuser.MyFollowNavigation
import com.bxkj.personal.ui.activity.myhistory.MyHistoryNavigation
import com.bxkj.personal.ui.activity.myresume.MyResumeDetailsNavigation
import com.bxkj.personal.ui.activity.parttimeworkbench.PartTimeWorkbenchNavigation
import com.bxkj.personal.ui.activity.personalmember.PersonalMemberNavigation
import com.bxkj.personal.ui.activity.resumetop.ResumeTopNavigation
import com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord.SchoolRecruitDeliveryRecordNavigation
import com.bxkj.personal.ui.activity.seenmybusiness.ViewedMeCompanyNavigation
import com.bxkj.personal.ui.activity.setting.SettingNavigation
import com.bxkj.personal.ui.activity.uploadattechmentresume.AttachmentResumeNavigation
import com.therouter.router.Route

/**
 * @Description: 我的
 * <AUTHOR>
 * @date 2020/2/10
 * @version V1.0
 */
@Route(path = PersonalMineNavigation.PATH)
class GeekMineFragmentV2 :
  BaseDBFragment<UserFragmentGeekMineV2Binding, GeekMineViewModel>(),
  View.OnClickListener {

  private var userOptionsListAdapter: SimpleDBListAdapter<UserFunctionItem>? = null

  override fun getViewModelClass(): Class<GeekMineViewModel> =
    GeekMineViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_fragment_geek_mine_v2

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this
    subscribeViewModelEvent()
  }

  override fun onHiddenChanged(hidden: Boolean) {
    super.onHiddenChanged(hidden)
    if (!hidden) {
      viewModel.refresh()
    }
  }

  override fun onResume() {
    super.onResume()
    if (!isHidden) {
      viewModel.refresh()
    }
  }

  override fun initImmersionBar() {
    statusBarManager.titleBar(viewBinding.llTitleBar).statusBarDarkFont(true, 0.4f).init()
  }

  /**
   * 订阅viewModel事件
   */
  private fun subscribeViewModelEvent() {
    viewModel.userOptions.observe(this) {
      setupUserOptionsList(it)
    }

    viewModel.showNoResumeTipsCommand.observe(this, EventObserver {
      ActionDialog.Builder()
        .setContent("请先完善简历基本信息")
        .setConfirmText("去完善")
        .setOnConfirmClickListener {
          it.dismiss()
          MicroResumeInfoNavigation.create().start()
        }.build().show(childFragmentManager)
    })

    viewModel.toMyResumeDetailsCommand.observe(this, EventObserver {
      MyResumeDetailsNavigation.create().start()
    })

    viewModel.toCreateResumeCommand.observe(this, EventObserver {
      MicroResumeInfoNavigation.create(ResumeRouteConstant.ACTION_CREATE_RESUME)
        .start()
    })

    viewModel.jumpToWechatCommand.observe(this, EventObserver {
      // IDCardValidationNavigation.create().start()
      SystemUtil.copy(requireContext(), "微信号", it, "已复制，请前往微信添加")
      SystemUtil.jumpToWechat(requireContext())
    })

    viewModel.callServicesPhoneCommand.observe(this, EventObserver {
      SystemUtil.callPhone(requireContext(), it)
    })
  }

  private fun setupUserOptionsList(options: List<UserFunctionItem>) {
    if (viewBinding.recyclerUserOptions.adapter == null) {
      userOptionsListAdapter = object : SimpleDBListAdapter<UserFunctionItem>(
        parentActivity,
        R.layout.user_recycler_mine_user_function_item,
        BR.data
      ) {
        override fun convert(
          holder: SuperViewHolder,
          viewType: Int,
          item: UserFunctionItem,
          position: Int,
        ) {
          super.convert(holder, viewType, item, position)
          holder.findViewById<ImageView>(R.id.iv_op_icon).setImageResource(item.opIcon)
        }
      }.apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            when (data[position].opId) {
              UserFunctionItem.OP_WORKBENCH -> {
                PartTimeWorkbenchNavigation.create().start()
              }

              UserFunctionItem.OP_PERSONAL_VIP -> {
                PersonalMemberNavigation.create().start()
              }

              UserFunctionItem.OP_MY_ORDER -> {
                OrderGroupNavigation.navigate().start()
              }

              UserFunctionItem.OP_COMMISSION -> {
                CommissionListNavigation.navigate().start()
              }

              UserFunctionItem.OP_MY_RESUME -> {
                toMyResumeDetailsPage()
              }

              UserFunctionItem.OP_FIND_JOB_BY_MAP -> {
                FindJobByMapNavigation.navigate().start()
              }

              UserFunctionItem.OP_SCHOOL_RECRUIT_DELIVERY_RECORD -> {
                SchoolRecruitDeliveryRecordNavigation.create().start()
              }

              UserFunctionItem.OP_MY_FAVORITES -> {
                MyHistoryNavigation.navigate(0).start()
              }

              UserFunctionItem.OP_MY_COMMENT -> {
                MyHistoryNavigation.navigate(1).start()
              }

              UserFunctionItem.OP_MY_LIKE -> {
                MyHistoryNavigation.navigate(2).start()
              }

              UserFunctionItem.OP_MY_VIDEO -> {
                VideoManagementNavigation.navigate().start()
              }

              UserFunctionItem.OP_SEARCH_JOB -> {
                SearchJobNavigation.navigate().start()
              }

              UserFunctionItem.OP_SWITCH_IDENTITY -> {
                switchIdentity()
              }

              UserFunctionItem.OP_HELP -> {
                HelpNavigation.navigate().start()
              }
            }
          }
        })
      }
      viewBinding.recyclerUserOptions.layoutManager = GridLayoutManager(parentActivity, 4)
      viewBinding.recyclerUserOptions.adapter = userOptionsListAdapter
      viewBinding.recyclerUserOptions.isNestedScrollingEnabled = false
    }
    userOptionsListAdapter?.reset(options)
  }

  private fun toMyResumeDetailsPage() {
    viewModel.toMyResumeDetailsPreCheck()
  }

  override fun onClick(v: View?) {
    v?.let {
      when (v.id) {
        R.id.iv_scan -> { //扫码
          startActivity(ScanQrCodeActivity.newIntent(parentActivity))
        }

        R.id.iv_ad -> {
//          startActivity(
//            SearchJobResultActivity.newIntent(parentActivity, FilterParameters()
//              .apply {
//                jobFirstClassId = 19
//              })
//          )
        }

        R.id.iv_switch_identity -> {
          switchIdentity()
        }

        R.id.iv_setting -> {
          SettingNavigation.navigate().start()
        }

        R.id.cl_user_info -> {
          toMyResumeDetailsPage()
        }

        R.id.cl_resume_score -> {
          toMyResumeDetailsPage()
        }

        R.id.ll_wallet -> {
          CommissionListNavigation.navigate().start()
        }

        R.id.ll_follow -> {
          MyFollowNavigation.navigate(localUserId, false).start()
        }

        R.id.ll_collection_job -> {
          MyCollectionJobsNavigation.navigate().start()
        }

        R.id.ll_invitations_to_delivery -> {
          InviteToDeliveryNavigation.navigate().start()
        }

        R.id.ll_who_sew_me -> {
          ViewedMeCompanyNavigation.navigate().start()
        }

        R.id.ll_interview -> {
          InterviewManagementNavigation.create().start()
        }

        R.id.tv_online_resume -> {
          MyResumeDetailsNavigation.create().start()
        }

        R.id.tv_online_resume -> {
          MyResumeDetailsNavigation.create()
        }

        R.id.tv_relate_resume -> {
          AttachmentResumeNavigation.create().start()
        }

        R.id.tv_resume_top -> {
          PersonalMemberNavigation.create().start()
          // ResumeTopNavigation.create().start()
        }

        R.id.tv_delivery_success -> {
          ResumeDeliveryRecordNavigation.navigate(1).start()
        }

        else -> {}
      }
    }
  }

  private fun switchIdentity() {
    if (parentFragment is MinePageContainerFragment) {
      (parentFragment as MinePageContainerFragment).viewModel.switchIdentity()
    }
  }
}