package com.bxkj.jrzp.user.mine.ui.updatebindphonenumber

import android.content.Intent
import android.os.Bundle
import com.therouter.router.Route
import com.therouter.TheRouter
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.util.router.RouterNavigation.PersonalMainActivity
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityBindMobileBinding

/**
 * Description: 修改绑定手机号
 * Author:Sanjin
 * Date:2024/4/12
 **/
@Route(path = UpdateBindPhoneNumberNavigation.PATH)
class UpdateBindPhoneNumberActivity :
  BaseDBActivity<UserActivityBindMobileBinding, UpdateBindPhoneNumberViewModel>() {

  private val extraStep by lazy {
    intent.getIntExtra(
      UpdateBindPhoneNumberNavigation.EXTRA_REQ_STEP,
      UpdateBindPhoneNumberNavigation.STEP_VERIFY_OLD_PHONE
    )
  }

  override fun getViewModelClass(): Class<UpdateBindPhoneNumberViewModel> =
    UpdateBindPhoneNumberViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_bind_mobile

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupTitleText()

    subscribeViewModelEvent()

    viewModel.start(extraStep)
  }

  private fun subscribeViewModelEvent() {
    viewModel.verifySuccessEvent.observe(this, EventObserver {
      finish()
      UpdateBindPhoneNumberNavigation.create(UpdateBindPhoneNumberNavigation.STEP_VERIFY_NEW_PHONE)
        .start()
    })

    viewModel.bindSuccessEvent.observe(this, EventObserver {
      TipsDialog().setTitle("提示")
        .setContent("绑定手机修改成功\n请使用新手机号重新登录")
        .setOnConfirmClickListener {
          UserUtils.removeUserData()
          Router.getInstance()
            .to(PersonalMainActivity.URL)
            .withFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            .start()
          TheRouter.build(RouterNavigation.LoginActivity.URL)
            .navigation()
        }.show(supportFragmentManager)
    })
  }

  private fun setupTitleText() {
    if (extraStep == UpdateBindPhoneNumberNavigation.STEP_VERIFY_OLD_PHONE) {
      viewBinding.titleBar.setTitle(getString(R.string.user_update_bind_mobile_verify_old_phone_number))
      viewBinding.btnBind.setText(R.string.user_update_bind_mobile_verify)
      viewBinding.etPhoneNumber.isEnabled = false
    } else {
      viewBinding.titleBar.setTitle(getString(R.string.user_update_bind_mobile_number_title))
      viewBinding.btnBind.setText(R.string.user_update_bind_mobile_bind)
      viewBinding.etPhoneNumber.isEnabled = true
    }
  }
}