<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.user.mine.ui.schoolmine.SchoolMineViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <LinearLayout
      android:id="@+id/ll_title_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_44"
      android:background="@drawable/bg_ff7405"
      android:gravity="center_vertical">

      <ImageView
        android:id="@+id/iv_scan"
        style="@style/wrap_wrap"
        android:layout_marginStart="@dimen/dp_14"
        android:onClick="@{onClickListener}"
        android:src="@drawable/user_ic_scan" />

      <Space
        android:layout_width="@dimen/dp_0"
        android:layout_height="match_parent"
        android:layout_weight="1" />

      <ImageView
        android:id="@+id/iv_switch_identity"
        android:layout_width="@dimen/common_dp_32"
        android:layout_height="@dimen/common_dp_32"
        android:layout_marginEnd="@dimen/dp_18"
        android:onClick="@{onClickListener}"
        android:scaleType="centerInside"
        android:src="@drawable/user_ic_switch_identity_white" />

      <ImageView
        android:id="@+id/iv_setting"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_18"
        android:onClick="@{onClickListener}"
        android:src="@drawable/ic_setting" />
    </LinearLayout>

    <androidx.core.widget.NestedScrollView style="@style/match_wrap">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
          android:id="@+id/cl_top_layout"
          style="@style/match_wrap"
          android:background="@drawable/bg_ff7405">

          <Space
            android:id="@+id/v_bg_top"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

          <View
            android:id="@+id/v_bg_bottom"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_0"
            android:background="@drawable/bg_f4f4f4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/v_bg_top" />

          <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_user_info_group"
            style="@style/match_wrap"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
              android:id="@+id/cl_user_info"
              style="@style/wrap_wrap"
              android:layout_width="@dimen/dp_0"
              android:layout_marginStart="@dimen/dp_12"
              android:layout_marginEnd="@dimen/dp_16"
              android:onClick="@{onClickListener}"
              app:layout_constraintEnd_toStartOf="@id/tv_refresh_resume"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toTopOf="parent">

              <ImageView
                android:id="@+id/iv_user_header"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/bg_ffffff_round"
                android:padding="@dimen/dp_1"
                bind:imgErrorPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
                bind:imgIsCircle="@{true}"
                bind:imgPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
                bind:imgUrl="@{viewModel.userInfo.photo}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


              <TextView
                android:id="@+id/tv_user_name"
                style="@style/Text.18sp.FFFFFF"
                android:layout_marginStart="@dimen/dp_8"
                android:layout_marginTop="@dimen/dp_8"
                android:drawableEnd="@drawable/ic_white_next"
                android:drawablePadding="@dimen/dp_4"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:lines="1"
                android:text="@{viewModel.userInfo.name}"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/iv_user_header"
                app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
              android:id="@+id/tv_refresh_resume"
              style="@style/Text.14sp.FE6600"
              android:background="@drawable/bg_mine_certification"
              android:onClick="@{onClickListener}"
              android:paddingStart="@dimen/dp_10"
              android:paddingTop="@dimen/dp_2"
              android:paddingEnd="@dimen/dp_8"
              android:paddingBottom="@dimen/dp_2"
              android:text="@string/user_personal_mine_user_home"
              app:layout_constraintBottom_toBottomOf="@id/cl_user_info"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintTop_toTopOf="@id/cl_user_info" />

            <LinearLayout
              android:id="@+id/ll_wallet"
              style="@style/wrap_wrap"
              android:layout_marginTop="@dimen/dp_20"
              android:layout_marginBottom="@dimen/dp_16"
              android:gravity="center_horizontal"
              android:onClick="@{onClickListener}"
              android:orientation="vertical"
              app:layout_constraintEnd_toStartOf="@id/ll_integral"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/cl_user_info">

              <TextView
                android:id="@+id/tv_moment"
                style="@style/Text.16sp.FFFFFF"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:hint="@string/number_placeholder"
                android:text="@{String.valueOf(viewModel.userAccountBalance.realBalanceText)}"
                android:textColorHint="@color/common_white"
                android:textStyle="bold" />

              <TextView
                style="@style/Text.12sp.FFFFFF"
                android:layout_marginTop="@dimen/common_dp_5"
                android:text="@string/mine_wallet" />
            </LinearLayout>

            <LinearLayout
              android:id="@+id/ll_integral"
              style="@style/wrap_wrap"
              android:layout_marginTop="@dimen/dp_20"
              android:gravity="center_horizontal"
              android:onClick="@{onClickListener}"
              android:orientation="vertical"
              app:layout_constraintEnd_toStartOf="@id/ll_follow"
              app:layout_constraintStart_toEndOf="@id/ll_wallet"
              app:layout_constraintTop_toBottomOf="@id/cl_user_info">

              <TextView
                android:id="@+id/tv_integral"
                style="@style/Text.16sp.FFFFFF"
                android:gravity="center"
                android:hint="@string/number_placeholder"
                android:text="@{String.valueOf(viewModel.userVipBalanceInfo.integral)}"
                android:textColorHint="@color/common_white"
                android:textStyle="bold" />

              <TextView
                style="@style/Text.12sp.FFFFFF"
                android:layout_marginTop="@dimen/common_dp_5"
                android:text="@string/user_enterprise_mine_integral" />
            </LinearLayout>

            <LinearLayout
              android:id="@+id/ll_follow"
              style="@style/wrap_wrap"
              android:layout_marginTop="@dimen/dp_20"
              android:layout_marginBottom="@dimen/dp_16"
              android:gravity="center_horizontal"
              android:onClick="@{onClickListener}"
              android:orientation="vertical"
              app:layout_constraintEnd_toStartOf="@id/ll_fans"
              app:layout_constraintStart_toEndOf="@id/ll_integral"
              app:layout_constraintTop_toBottomOf="@id/cl_user_info">

              <TextView
                android:id="@+id/tv_fans"
                style="@style/Text.16sp.FFFFFF"
                android:gravity="center"
                android:hint="@string/number_placeholder"
                android:text="@{String.valueOf(viewModel.userInfo.followCount)}"
                android:textColorHint="@color/common_white"
                android:textStyle="bold" />

              <TextView
                style="@style/Text.12sp.FFFFFF"
                android:layout_marginTop="@dimen/common_dp_5"
                android:text="@string/mine_follow" />
            </LinearLayout>

            <LinearLayout
              android:id="@+id/ll_fans"
              style="@style/wrap_wrap"
              android:layout_marginTop="@dimen/dp_20"
              android:gravity="center_horizontal"
              android:onClick="@{onClickListener}"
              android:orientation="vertical"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toEndOf="@id/ll_follow"
              app:layout_constraintTop_toBottomOf="@id/cl_user_info">

              <TextView
                android:id="@+id/tv_collection"
                style="@style/Text.16sp.FFFFFF"
                android:gravity="center"
                android:hint="@string/number_placeholder"
                android:text="@{String.valueOf(viewModel.userInfo.fansCount)}"
                android:textColorHint="@color/common_white"
                android:textStyle="bold" />

              <TextView
                style="@style/Text.12sp.FFFFFF"
                android:layout_marginTop="@dimen/common_dp_5"
                android:text="@string/mine_fans" />
            </LinearLayout>

          </androidx.constraintlayout.widget.ConstraintLayout>

          <LinearLayout
            android:id="@+id/ll_user_options"
            android:layout_width="match_parent"
            android:layout_height="72dp"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_12"
            android:background="@drawable/bg_ffffff_radius_4"
            android:orientation="horizontal"
            app:layout_constraintBottom_toTopOf="@id/iv_ad"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_user_info_group"
            app:layout_goneMarginBottom="@dimen/dp_8">

            <LinearLayout
              android:id="@+id/ll_double_election"
              android:layout_width="@dimen/dp_0"
              android:layout_height="match_parent"
              android:layout_weight="1"
              android:gravity="center"
              android:onClick="@{onClickListener}"
              android:orientation="vertical">

              <TextView
                style="@style/Text.18sp.333333.Bold"
                android:gravity="center"
                android:hint="@string/user_enterprise_mine_count_hint"
                android:text="@{String.valueOf(viewModel.schoolInfoCount.sxhCount)}" />

              <TextView
                style="@style/Text.14sp.333333"
                android:text="@string/user_school_mine_double_election" />
            </LinearLayout>

            <LinearLayout
              android:id="@+id/ll_seminar"
              android:layout_width="@dimen/dp_0"
              android:layout_height="match_parent"
              android:layout_weight="1"
              android:gravity="center"
              android:onClick="@{onClickListener}"
              android:orientation="vertical">

              <TextView
                style="@style/Text.18sp.333333.Bold"
                android:gravity="center"
                android:hint="@string/user_enterprise_mine_count_hint"
                android:text="@{String.valueOf(viewModel.schoolInfoCount.xjhCount)}" />

              <TextView
                style="@style/Text.14sp.333333"
                android:text="@string/user_school_mine_seminar" />
            </LinearLayout>

            <LinearLayout
              android:id="@+id/ll_public_examination"
              android:layout_width="@dimen/dp_0"
              android:layout_height="match_parent"
              android:layout_weight="1"
              android:gravity="center"
              android:onClick="@{onClickListener}"
              android:orientation="vertical">

              <TextView
                style="@style/Text.18sp.333333.Bold"
                android:gravity="center"
                android:hint="@string/user_enterprise_mine_count_hint"
                android:text="@{String.valueOf(viewModel.schoolInfoCount.gzgkCount)}" />

              <TextView
                style="@style/Text.14sp.333333"
                android:text="@string/user_school_mine_public_examination" />
            </LinearLayout>

            <!--            <LinearLayout-->
            <!--              android:id="@+id/ll_admissions"-->
            <!--              android:layout_width="@dimen/dp_0"-->
            <!--              android:layout_height="match_parent"-->
            <!--              android:layout_weight="1"-->
            <!--              android:gravity="center"-->
            <!--              android:onClick="@{onClickListener}"-->
            <!--              android:orientation="vertical">-->

            <!--              <TextView-->
            <!--                style="@style/Text.18sp.333333.Bold"-->
            <!--                android:gravity="center"-->
            <!--                android:hint="@string/user_enterprise_mine_count_hint" />-->

            <!--              <TextView-->
            <!--                style="@style/Text.14sp.333333"-->
            <!--                android:text="@string/user_school_mine_admissions" />-->
            <!--            </LinearLayout>-->

          </LinearLayout>

          <ImageView
            android:id="@+id/iv_ad"
            style="@style/match_wrap"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_8"
            android:onClick="@{onClickListener}"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_user_options" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
          style="@style/Text.14sp.333333.Bold"
          android:layout_margin="@dimen/dp_14"
          android:text="@string/user_enterprise_mine_common_functions" />

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/recycler_common_functions"
          style="@style/match_wrap"
          android:overScrollMode="never" />

        <View
          style="@style/Line.Horizontal.Light"
          android:layout_height="@dimen/dp_8" />

        <TextView
          style="@style/Text.14sp.333333.Bold"
          android:layout_margin="@dimen/dp_14"
          android:text="@string/user_enterprise_mine_more_functions" />

        <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/recycler_more_functions"
          style="@style/match_wrap"
          android:overScrollMode="never" />

        <View
          style="@style/Line.Horizontal.Light"
          android:layout_height="@dimen/dp_8" />

        <TextView
          style="@style/Text.12sp.888888"
          android:layout_gravity="center_horizontal"
          android:layout_marginTop="@dimen/dp_30"
          android:layout_marginBottom="@dimen/dp_30"
          android:text="@{@string/mine_services_info_format(viewModel.serviceInfo.phone,viewModel.serviceInfo.time)}"
          android:visibility="@{viewModel.serviceInfo==null?View.GONE:View.VISIBLE }" />
      </LinearLayout>

    </androidx.core.widget.NestedScrollView>


  </LinearLayout>
</layout>