<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.jrzp.user.videorelate.data.VideoRelateResumeData.Resume" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_14"
    android:paddingEnd="@dimen/dp_14">

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.16sp.333333.Bold"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_10"
      android:layout_marginEnd="@dimen/dp_16"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.name}"
      app:layout_constraintEnd_toStartOf="@id/tv_relate"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_about"
      style="@style/Text.12sp.999999"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_4"
      android:layout_marginEnd="@dimen/dp_16"
      android:layout_marginBottom="@dimen/dp_16"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.edate1}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toStartOf="@id/tv_relate"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />


    <TextView
      android:id="@+id/tv_relate"
      style="@style/Text.14sp"
      android:background="@drawable/user_bg_btn_video_relate_resume"
      android:paddingStart="@dimen/dp_12"
      android:paddingTop="@dimen/dp_4"
      android:paddingEnd="@dimen/dp_12"
      android:paddingBottom="@dimen/dp_4"
      android:textColor="@color/cl_ff7647_to_ffffff_selector"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>