package com.bxkj.jrzp.commission.repository

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.commission.api.CommissionApi
import com.bxkj.jrzp.commission.data.CommissionItemData
import com.bxkj.jrzp.commission.data.CommissionNoticeItemData
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/29
 * @version: V1.0
 */
class CommissionRepository @Inject constructor(
  private val mCommissionApi: CommissionApi
) : BaseRepo() {

  /**
   * 返回佣金列表
   */
  suspend fun getBrokerageList(
    userID: Int,
    brokerageType: Int,
    pageIndex: Int,
    pageSize: Int
  ): ReqResponse<List<CommissionItemData>> {
    return httpRequest {
      mCommissionApi.getBrokerageList(
        mapOf(
          "puid" to userID,
          "type" to brokerageType,
          "pageIndex" to pageIndex,
          "pageSize" to pageSize
        ).paramsEncrypt()
      )
    }
  }

  /**
   * 获取佣金动态
   */
  suspend fun getCommissionNotice(): ReqResponse<List<CommissionNoticeItemData>> {
    return httpRequest {
      mCommissionApi.getCommissionNotice()
    }
  }
}