<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="data"
      type="com.bxkj.jrzp.commission.data.CommissionItemData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_16">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.14sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_16"
      android:layout_marginEnd="@dimen/dp_16"
      android:text="@{HtmlUtils.fromHtml(@string/user_commission_course_format(data.uName,data.content))}"
      app:layout_constraintEnd_toStartOf="@id/tv_income"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_income"
      style="@style/Text.18sp.FE6600.Bold"
      android:text="@{@string/user_commission_list_item_income_format(data.priceYongjin)}"
      app:layout_constraintBottom_toBottomOf="@id/tv_title"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_type"
      style="@style/Text.12sp.999999"
      android:layout_marginTop="@dimen/dp_4"
      android:text="@{@string/user_commission_list_item_type_format(data.typeName)}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_time"
      style="@style/Text.12sp.999999"
      android:layout_marginTop="@dimen/dp_2"
      android:text="@{data.payTime}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_type" />

    <View
      style="@style/Line.Horizontal"
      android:layout_marginTop="@dimen/dp_10"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_time" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>