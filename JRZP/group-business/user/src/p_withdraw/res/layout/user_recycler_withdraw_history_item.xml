<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.jrzp.user.withdraw.data.WithdrawHistoryItemData" />

  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_16">

    <TextView
      android:id="@+id/tv_user_name"
      style="@style/Text.14sp.333333.Bold"
      android:layout_marginTop="@dimen/dp_16"
      android:text="@{@string/user_withdraw_name_format(data.cardUName)}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_amount"
      style="@style/Text.14sp.333333"
      android:text="@{@string/user_withdraw_amount_format(data.showAmount)}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_user_name"
      app:layout_constraintEnd_toEndOf="parent" />

    <TextView
      android:id="@+id/tv_account"
      style="@style/Text.12sp.333333"
      android:layout_marginTop="@dimen/dp_4"
      android:text="@{data.cardName}"
      app:layout_constraintStart_toStartOf="@id/tv_user_name"
      app:layout_constraintTop_toBottomOf="@id/tv_user_name" />

    <TextView
      android:id="@+id/tv_date"
      style="@style/Text.12sp.999999"
      android:layout_marginStart="@dimen/dp_6"
      android:text="@{data.date}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_account"
      app:layout_constraintStart_toEndOf="@id/tv_account" />

    <TextView
      android:id="@+id/tv_state"
      style="@style/Text.12sp.FF7647"
      android:drawableEnd="@{data.failed()?@drawable/user_ic_withdraw_failed_more_info:null}"
      android:drawablePadding="@dimen/dp_4"
      android:gravity="center_vertical"
      android:text="@{data.withdrawStateText}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_account"
      app:layout_constraintEnd_toEndOf="parent" />

    <View
      style="@style/Line.Horizontal"
      android:layout_marginTop="@dimen/dp_16"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_account" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>