package com.bxkj.jrzp.user.withdraw.data

/**
 * @Description: 提现历史
 * @author:45457
 * @date: 2020/8/3
 * @version: V1.0
 */
data class WithdrawHistoryItemData(
  var id: Int,
  var type: Int,
  var typeName: String = "",
  var cardUName: String = "",
  var cardName: String = "",
  var cardID: String = "",
  var money: Int = 0,
  var date: String = "",
  var dealData: String = "",
  var state: Int,
  var stateMsg: String = ""
) {

  fun getWithdrawStateText(): String {
    return when (state) {
      0 -> {
        "处理中"
      }
      1 -> {
        "提现成功"
      }
      2 -> {
        "提现失败"
      }
      else -> {
        ""
      }
    }
  }

  fun failed(): Boolean {
    return state == 2
  }

  fun success(): Boolean {
    return state == 1
  }

  fun getShowAmount(): String {
    return String.format("%.2f", (money / 100.toFloat()))
  }
}