package com.bxkj.jrzp.user.withdraw.ui.history

import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.CommonDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.databinding.UserActivityWithdrawHistoryBinding
import com.bxkj.jrzp.user.withdraw.data.WithdrawHistoryItemData

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/2
 * @version: V1.0
 */
@Route(path = WithdrawHistoryNavigation.PATH)
class WithdrawHistoryActivity :
  BaseDBActivity<UserActivityWithdrawHistoryBinding, WithdrawHistoryViewModel>() {

  override fun getViewModelClass(): Class<WithdrawHistoryViewModel> =
    WithdrawHistoryViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_withdraw_history

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupWithdrawHistoryListAdapter()

    viewModel.start()
  }

  private fun setupWithdrawHistoryListAdapter() {
    val withdrawListAdapter = CommonDBListAdapter<WithdrawHistoryItemData>(
      this,
      R.layout.user_recycler_withdraw_history_item
    ).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          val clickItem = data[position]
          if (clickItem.failed()) {
            TipsDialog()
              .setTitle(getString(R.string.tips))
              .setContent(clickItem.stateMsg)
              .show(supportFragmentManager)
          }
        }
      })
    }
    val recyclerWithdrawList = viewBinding.includeWithdrawList.recyclerContent
    recyclerWithdrawList.layoutManager = LinearLayoutManager(this)
    recyclerWithdrawList.addItemDecoration(
      LineItemDecoration(
        ContextCompat.getDrawable(
          this,
          R.drawable.divider_f4f4f4
        ), LinearLayoutManager.VERTICAL
      )
    )
    viewModel.withdrawHistoryListViewModel.setAdapter(withdrawListAdapter)
  }

}