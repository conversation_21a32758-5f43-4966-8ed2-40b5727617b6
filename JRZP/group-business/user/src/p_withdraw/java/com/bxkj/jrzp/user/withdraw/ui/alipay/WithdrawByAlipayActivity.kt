package com.bxkj.jrzp.user.withdraw.ui.alipay

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.lifecycle.Observer
import com.therouter.router.Route
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.widget.dialog.BaseDialogFragment.OnDismissListener
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.ui.alipaylist.AlipayAccountListNavigation
import com.bxkj.jrzp.user.databinding.UserActivityWithdrawaByAlipayBinding
import com.bxkj.jrzp.user.withdraw.ui.history.WithdrawHistoryNavigation

/**
 * @Description: 支付宝提现
 * @author:45457
 * @date: 2020/8/1
 * @version: V1.0
 */
@Route(path = WithdrawByAlipayNavigation.PATH)
class WithdrawByAlipayActivity :
  BaseDBActivity<UserActivityWithdrawaByAlipayBinding, WithdrawByAlipayViewModel>(),
  OnClickListener {

  companion object {
    const val TO_ACCOUNT_LIST_CODE = 1
  }

  override fun getViewModelClass(): Class<WithdrawByAlipayViewModel> =
    WithdrawByAlipayViewModel::class.java

  override fun getLayoutId(): Int = R.layout.user_activity_withdrawa_by_alipay

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    viewBinding.titleBar.setRightOptionClickListener {
      WithdrawHistoryNavigation.navigate().start()
    }

    subscribeViewModelEvent()
  }

  private fun subscribeViewModelEvent() {
    viewModel.submitSuccessEvent.observe(this, Observer {
      TipsDialog().setTitle(getString(R.string.user_withdraw_success_tips_title))
        .setContent(getString(R.string.user_withdraw_success_tips_content))
        .setOnOverrideDismissListener (object : OnDismissListener {
          override fun onDismiss(dialogInterface: DialogInterface?) {
            WithdrawHistoryNavigation.navigate().start()
            finish()
          }
        }).show(supportFragmentManager)
    })
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.tv_alipay_account) {
        AlipayAccountListNavigation.navigate().startForResult(this, TO_ACCOUNT_LIST_CODE)
      }
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    viewModel.handleActivityResult(requestCode, resultCode, data)
  }

}