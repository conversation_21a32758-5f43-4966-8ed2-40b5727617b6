<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
  <!--============= FindFriendsActivity(发现好友) ============-->
  <string name="user_find_friends_page_title">发现好友</string>

  <!--============= RecommendFriendsFragment(推荐好友) ==============-->
  <string name="user_recommend_friends_view_contracts_friend">查看通讯录好友</string>
  <string name="user_recommend_friends_view">查看</string>
  <string name="user_recommend_friends_tips">你可能感兴趣的人</string>

  <!--============== ContractsFriendFragment(通讯录好友) ==============-->
  <string name="user_contracts_friend_count_format">你有%d位好友在今日招聘</string>
  <string name="user_contracts_friend_no_permission_tips_title">开启手机通讯录权限</string>
  <string name="user_contracts_friend_no_permission_tips_content">今日招聘没有权限访问你的手机通讯录，请在手机设置里开启，查看手机通讯录好友</string>
  <string name="user_contracts_friend_permission_tips_title">权限说明</string>
  <string name="user_contracts_friend_permission_tips_content">开启通讯录权限后可查看在通讯录中使用今日招聘的好友</string>
  <string name="user_contracts_friend_permission_cancel_tips">取消授权通讯录</string>
  <string name="user_contracts_friend_nick_name_format">昵称：%s</string>

  <string-array name="user_friend_type">
    <item>推荐好友</item>
    <item>通讯录</item>
  </string-array>


</resources>