package com.bxkj.jrzp.user.switchaccount.ui.switchaccount

import android.view.View
import android.widget.ImageView
import com.bxkj.common.adapter.SimpleDiffListAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.util.UserUtils
import com.bxkj.jrzp.support.db.entry.UserLoginHistory
import com.bxkj.jrzp.user.BR
import com.bxkj.jrzp.user.R

class AccountListAdapter : SimpleDiffListAdapter<UserLoginHistory>(
    R.layout.user_recycler_account_item,
    UserLoginHistory.DiffCallback(),
    BR.data
) {

    private var mShowDeleteTag = false

    override fun bind(holder: SuperViewHolder, item: UserLoginHistory, position: Int) {
        super.bind(holder, item, position)
        val ivDelete = holder.findViewById<ImageView>(R.id.iv_delete)
        ivDelete.visibility = if (mShowDeleteTag) View.VISIBLE else View.GONE

        val ivSelected = holder.findViewById<ImageView>(R.id.iv_selected)
        ivSelected.visibility =
            if (item.userID == UserUtils.getUserId()) View.VISIBLE else View.GONE
    }

    fun switchEditOpenStatus(status: Boolean) {
        mShowDeleteTag = status
        notifyDataSetChanged()
    }
}