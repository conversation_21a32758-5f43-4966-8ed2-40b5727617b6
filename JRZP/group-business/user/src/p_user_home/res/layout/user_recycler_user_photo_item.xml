<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="data"
      type="com.bxkj.jrzp.userhome.data.UserPhotoItemData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap">

    <ImageView
      android:id="@+id/iv_pic"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:scaleType="centerCrop"
      bind:imgUrl="@{data.pic}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintDimensionRatio="1:1"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      style="@style/Text.10sp.FFFFFF"
      android:layout_marginStart="@dimen/dp_2"
      android:layout_marginTop="@dimen/dp_2"
      android:background="@drawable/bg_ff7405"
      android:paddingStart="@dimen/dp_2"
      android:paddingEnd="@dimen/dp_2"
      android:text="@{data.typeName}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>