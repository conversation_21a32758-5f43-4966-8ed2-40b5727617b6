<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.jrzp.userhome.data.UserQuestionItemData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:padding="@dimen/dp_14">

    <com.google.android.material.imageview.ShapeableImageView
      android:id="@+id/iv_avatar"
      android:layout_width="@dimen/common_dp_32"
      android:layout_height="@dimen/common_dp_32"
      bind:imgErrorPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
      bind:imgPlaceholder="@{@drawable/ic_user_avatar_placeholder}"
      bind:imgUrl="@{data.userPhoto}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      app:shapeAppearance="@style/roundedCornerImageStyle" />

    <TextView
      android:id="@+id/tv_nick_name"
      style="@style/Text.14sp.333333.Bold"
      android:layout_marginStart="@dimen/dp_8"
      android:text="@{data.userName}"
      app:layout_constraintBottom_toTopOf="@id/tv_time"
      app:layout_constraintStart_toEndOf="@id/iv_avatar"
      app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <TextView
      android:id="@+id/tv_time"
      style="@style/Text.12sp.999999"
      android:text="@{data.createTime}"
      app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
      app:layout_constraintStart_toStartOf="@id/tv_nick_name"
      app:layout_constraintTop_toBottomOf="@id/tv_nick_name" />

    <ImageView
      android:id="@+id/iv_option"
      android:layout_width="@dimen/common_dp_32"
      android:layout_height="@dimen/common_dp_32"
      android:scaleType="centerInside"
      android:src="@drawable/user_ic_more_function"
      android:visibility="@{data.self?View.VISIBLE:View.GONE}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_answer_title"
      style="@style/Text.16sp.333333"
      android:layout_marginTop="@dimen/dp_16"
      android:lineSpacingExtra="@dimen/dp_3"
      android:text="@{@string/user_question_answer_format(data.toWendaTitle)}"
      android:textStyle="bold"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

    <TextView
      android:id="@+id/tv_content"
      style="@style/Text.16sp.333333"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_8"
      android:ellipsize="end"
      android:lineSpacingExtra="@dimen/dp_3"
      android:maxLines="5"
      android:text="@{data.content}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_answer_title" />

    <TextView
      android:id="@+id/tv_views"
      style="@style/Text.12sp.333333"
      android:layout_marginStart="@dimen/dp_50"
      android:layout_marginTop="@dimen/dp_16"
      android:drawableStart="@drawable/ic_user_notice_views"
      android:drawablePadding="@dimen/dp_4"
      android:text="@{String.valueOf(data.count)}"
      app:layout_constraintEnd_toStartOf="@id/tv_comment"
      app:layout_constraintHorizontal_chainStyle="spread_inside"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_content" />

    <TextView
      android:id="@+id/tv_comment"
      style="@style/Text.12sp.333333"
      android:drawableStart="@drawable/ic_user_notice_comment"
      android:drawablePadding="@dimen/dp_4"
      android:text="@{String.valueOf(data.commentsCount)}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_views"
      app:layout_constraintEnd_toStartOf="@id/tv_like"
      app:layout_constraintStart_toEndOf="@id/tv_views" />

    <TextView
      android:id="@+id/tv_like"
      style="@style/Text.12sp.333333"
      android:layout_marginEnd="@dimen/dp_50"
      android:drawableStart="@{data.like?@drawable/ic_user_notice_like_sel:@drawable/ic_user_notice_like_nor}"
      android:drawablePadding="@dimen/dp_4"
      android:text="@{String.valueOf(data.likesCount)}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_comment"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toEndOf="@id/tv_comment" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>