<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="data"
            type="com.bxkj.jrzp.userhome.data.UserMomentData.UserJobData" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_wrap"
        android:orientation="vertical"
        android:padding="@dimen/dp_14">

        <LinearLayout
            style="@style/match_wrap"
            android:orientation="horizontal">

            <TextView
                style="@style/Text.17sp.000000.Bold"
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_weight="1"
                android:ellipsize="end"
                android:lines="1"
                android:text="@{data.name}" />

            <TextView
                style="@style/Text.17sp.FE6600"
                android:text="@{data.money}" />

        </LinearLayout>

        <LinearLayout
            style="@style/match_wrap"
            android:layout_marginTop="@dimen/dp_8"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_city"
                style="@style/Text.12sp.888888"
                android:background="@drawable/bg_f4f4f4_radius_4"
                android:paddingStart="@dimen/dp_4"
                android:paddingTop="@dimen/dp_2"
                android:paddingEnd="@dimen/dp_4"
                android:paddingBottom="@dimen/dp_2"
                android:text="@{data.shiName}"
                app:layout_constraintEnd_toStartOf="@id/tv_edu"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_salary" />

            <TextView
                android:id="@+id/tv_edu"
                style="@style/Text.12sp.888888"
                android:layout_marginStart="@dimen/dp_8"
                android:background="@drawable/bg_f4f4f4_radius_4"
                android:paddingStart="@dimen/dp_4"
                android:paddingTop="@dimen/dp_2"
                android:paddingEnd="@dimen/dp_4"
                android:paddingBottom="@dimen/dp_2"
                android:text="@{data.quaName}"
                app:layout_constraintBottom_toBottomOf="@id/tv_city"
                app:layout_constraintEnd_toStartOf="@id/tv_exp"
                app:layout_constraintStart_toEndOf="@id/tv_city" />

            <TextView
                android:id="@+id/tv_exp"
                style="@style/Text.12sp.888888"
                android:layout_marginStart="@dimen/dp_8"
                android:layout_marginEnd="@dimen/dp_12"
                android:background="@drawable/bg_f4f4f4_radius_4"
                android:paddingStart="@dimen/dp_4"
                android:paddingTop="@dimen/dp_2"
                android:paddingEnd="@dimen/dp_4"
                android:paddingBottom="@dimen/dp_2"
                android:text="@{data.wtName}"
                app:layout_constraintBottom_toBottomOf="@id/tv_edu"
                app:layout_constraintEnd_toStartOf="@id/fl_video_cover"
                app:layout_constraintStart_toEndOf="@id/tv_edu" />

        </LinearLayout>

    </LinearLayout>
</layout>