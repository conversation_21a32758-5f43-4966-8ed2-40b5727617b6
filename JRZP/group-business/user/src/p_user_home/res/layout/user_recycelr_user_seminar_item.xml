<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="isSelf"
      type="Boolean" />

    <variable
      name="data"
      type="com.bxkj.jrzp.userhome.data.UserSeminarData" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:gravity="center_vertical"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_14"
    android:paddingTop="@dimen/dp_14"
    android:paddingEnd="@dimen/dp_14">

    <LinearLayout
      style="@style/match_wrap"
      android:orientation="horizontal">

      <LinearLayout
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
          style="@style/Text.17sp.000000"
          android:text="@{data.title}" />

        <TextView
          style="@style/Text.17sp.000000"
          android:text="@{data.gxName}" />

        <TextView
          style="@style/Text.12sp.888888"
          android:layout_marginTop="@dimen/dp_4"
          android:drawableStart="@drawable/ic_address"
          android:drawablePadding="@dimen/dp_4"
          android:text="@{data.address}" />

      </LinearLayout>

      <ImageView
        android:layout_width="@dimen/user_home_seminar_item_logo_size"
        android:layout_height="@dimen/user_home_seminar_item_logo_size"
        android:layout_marginStart="@dimen/dp_12"
        android:background="@drawable/frame_e8e8e8_radius_4"
        android:padding="@dimen/dp_1"
        android:visibility="@{data.hasLogo()?View.VISIBLE:View.GONE}"
        bind:loadRadiusImg="@{data.gxLogo}" />
    </LinearLayout>

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/common_dp_32"
      android:gravity="center_vertical">

      <TextView
        android:id="@+id/tv_seminar_tag"
        style="@style/Text.10sp.FFFFFF"
        android:background="#3c96d3"
        android:paddingStart="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_2"
        android:text="@string/user_home_page_seminar_tag" />

      <Space
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_weight="1" />

      <ImageView
        android:id="@+id/iv_more_function"
        android:layout_width="@dimen/common_dp_32"
        android:layout_height="@dimen/common_dp_32"
        android:scaleType="centerInside"
        android:src="@drawable/user_ic_more_function"
        android:visibility="@{isSelf?View.VISIBLE:View.GONE}" />
    </LinearLayout>

  </LinearLayout>
</layout>