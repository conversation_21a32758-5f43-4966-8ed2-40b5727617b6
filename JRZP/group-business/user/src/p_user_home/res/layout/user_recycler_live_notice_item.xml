<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="data"
      type="com.bxkj.jrzp.live.room.data.LiveRoomData" />
  </data>

  <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/user_home_page_live_notice_height">

    <ImageView
      style="@style/match_match"
      android:scaleType="centerCrop"
      bind:imgRadius="@{@dimen/dp_4}"
      bind:imgUrl="@{data.pic}" />

    <LinearLayout
      style="@style/wrap_wrap"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginTop="@dimen/dp_8"
      android:background="@drawable/bg_ff7405_radius_4"
      android:orientation="horizontal"
      android:gravity="center"
      android:paddingStart="@dimen/dp_4"
      android:paddingTop="@dimen/dp_2"
      android:paddingEnd="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_2">

      <pl.droidsonroids.gif.GifImageView
        style="@style/wrap_wrap"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:src="@drawable/user_gif_live_running" />

      <TextView
        style="@style/Text.12sp.FFFFFF"
        android:layout_marginStart="@dimen/dp_2"
        android:text="@string/user_home_page_live_running" />
    </LinearLayout>

    <TextView
      style="@style/common_Text.16sp.FFFFFF"
      android:layout_gravity="bottom"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_8"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.title}" />

  </FrameLayout>
</layout>