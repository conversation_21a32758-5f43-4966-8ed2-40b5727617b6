package com.bxkj.jrzp.userhome.ui.usernews.viewbinder

import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.jrzp.user.BR
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.userhome.data.UserNewsItemData

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/21
 * @version: V1.0
 */
class UserQuestionViewBinder :
  DefaultViewBinder<UserNewsItemData>(R.layout.user_recycler_user_question_item, BR.data, false) {

  override fun onBindViewHolder(holder: SuperViewHolder, item: UserNewsItemData, position: Int) {
    super.onBindViewHolder(holder, item, position)
    holder.bind(BR.data, item.wendaList)
  }
}