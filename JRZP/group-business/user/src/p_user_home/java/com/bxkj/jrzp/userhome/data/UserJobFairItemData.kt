package com.bxkj.jrzp.userhome.data

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.bxkj.jrzp.user.BR

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/23
 * @version: V1.0
 */
data class UserJobFairItemData(
  var id: Int,
  var type: Int,
  var title: String,
  var date: String,
  var time1: String,
  var time2: String,
  var weekName: String,
  var address: String,
  var gxId: Int,
  var gxName: String,
  var gxLogo: String?,
  @get:Bindable
  var bmState: Int,
  var count: Int
) : BaseObservable() {

  fun hasPic(): Boolean {
    return !gxLogo.isNullOrEmpty()
  }

  fun isNormalRecruit(): Boolean {
    return type == 2
  }

  fun isSchoolRecruit(): Boolean {
    return type == 14
  }

  @Bindable
  fun getUnRegistered(): Boolean {
    return bmState == 1
  }

  @Bindable
  fun getRegistered(): Boolean {
    return bmState >= 2
  }

  fun started(): Boolean {
    return bmState >= 3
  }

  @Bindable
  fun getStatusText(): String {
    return when (bmState) {
      1 -> "未报名"
      2 -> "已报名"
      3 -> "正在举办"
      4 -> "已举办"
      else -> ""
    }
  }

  fun updateRegisterStatus() {
    bmState = 2
    notifyPropertyChanged(BR.bmState)
    notifyPropertyChanged(BR.statusText)
    notifyPropertyChanged(BR.registered)
    notifyPropertyChanged(BR.unRegistered)
  }
}