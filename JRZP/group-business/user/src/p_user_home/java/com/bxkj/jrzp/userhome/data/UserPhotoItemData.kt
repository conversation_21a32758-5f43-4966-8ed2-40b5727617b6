package com.bxkj.jrzp.userhome.data

import com.bxkj.common.data.GalleryItem

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/21
 * @version: V1.0
 */
data class UserPhotoItemData(
  var id: Int,
  var type: Int,
  var glid: Int,
  var pic: String?,
  var toWendaID: Int,
  var toWendaTitle: String
) : GalleryItem {
  override fun getItemUrl(): String? {
    return pic
  }

  fun isCompanyProductPic(): Boolean {
    return type == -2
  }

  fun isCompanyStylePic(): Boolean {
    return type == -1
  }

  fun isNewsPic(): Boolean {
    return type == 1
  }

  fun isSchoolPic(): Boolean {
    return type == 2
  }

  fun isMomentPic(): Boolean {
    return type == 3
  }

  fun isQAPic(): Boolean {
    return type == 4
  }

  fun getTypeName(): String {
    return when {
      isCompanyStylePic() -> {
        "风采"
      }
      isCompanyProductPic() -> {
        "产品"
      }
      isNewsPic() -> {
        "资讯"
      }
      isSchoolPic() -> {
        "校园"
      }
      isMomentPic() -> {
        "动态"
      }
      isQAPic() -> {
        "问答"
      }
      else -> {
        "图片"
      }
    }
  }
}