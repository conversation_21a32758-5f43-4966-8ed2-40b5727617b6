package com.bxkj.jrzp.user.schoolinfo.data

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.bxkj.jrzp.user.BR
import com.google.gson.annotations.SerializedName

/**
 * @Description: 宣讲会
 * @author: YangXin
 * @date: 2020/11/24
 * @version: V1.0
 */
data class CampusTalkData(
    var id: Int,
    var date: String, //日期
    var time1: String, //开始时间
    var time2: String, //结束时间
    var weekName: String,  //星期
    var address: String,
    var cid: String,
    var comName: String,
    var comLogo: String,
    var status: Int, //0待审核、1审核失败、2审核通过,
) : BaseObservable() {

    fun getShowTitle(): String {
        return "${date}（${weekName}）${time1}-${time2}${comName}"
    }

    @Bindable
    fun isUnreviewed(): Boolean {
        return status == 0
    }

    fun pendingReview(): Boolean {
        return status == 0
    }

    @Bindable
    fun getStatusText(): String {
        return when (status) {
            UNREVIEWED -> "待审核"
            REVIEW_FAILED -> "审核失败"
            REVIEWED -> "审核通过"
            else -> ""
        }
    }

    fun updateStatus(status: Int) {
        this.status = status
        notifyPropertyChanged(BR.unreviewed)
        notifyPropertyChanged(BR.statusText)
    }

    companion object {
        const val UNREVIEWED = 0
        const val REVIEW_FAILED = 1
        const val REVIEWED = 2
    }
}