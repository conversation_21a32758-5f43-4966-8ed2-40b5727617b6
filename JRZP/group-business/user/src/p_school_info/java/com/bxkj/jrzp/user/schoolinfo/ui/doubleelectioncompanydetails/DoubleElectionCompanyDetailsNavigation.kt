package com.bxkj.jrzp.user.schoolinfo.ui.doubleelectioncompanydetails

import android.app.Activity
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/3
 * @version: V1.0
 */
class DoubleElectionCompanyDetailsNavigation {

    companion object {

        const val PATH = "${UserConstants.DIRECTORY}/doubleelectioncompanydetails"

        const val EXTRA_COMPANY_ID = "COMPANY_ID"
        const val EXTRA_COMPANY_USER_ID = "COMPANY_USER_ID"
        const val EXTRA_RESULT_STATUS = "RESULT_STATUS"

        const val RESULT_UPDATE_REVIEW_STATUS_SUCCESS = Activity.RESULT_OK + 1

        fun navigate(companyID: Int, companyUserID: Int): RouterNavigator {
            return Router.getInstance().to(PATH)
                .withInt(EXTRA_COMPANY_ID, companyID)
                .withInt(EXTRA_COMPANY_USER_ID, companyUserID)
        }
    }
}