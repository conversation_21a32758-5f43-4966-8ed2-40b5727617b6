package com.bxkj.jrzp.user.schoolinfo.ui.editdoubleelection

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.user.schoolinfo.api.SchoolInfoRepository
import com.bxkj.personal.ui.activity.editinfo.EditInfoNavigation
import com.bxkj.personal.ui.activity.editrichtext.EditRichTextNavigation
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/24
 * @version: V1.0
 */
class EditDoubleElectionViewModel @Inject constructor(
    private val mSchoolInfoRepository: SchoolInfoRepository
) : BaseViewModel() {

    val doubleElection = MutableLiveData<EditDoubleElectionParams>()

    val releaseSuccessEvent = LiveEvent<Void>()

    val updateSuccessEvent = LiveEvent<Void>()

    val toEditDescCommand = LiveEvent<String>()

    fun start(doubleElectionID: Int) {
        if (doubleElectionID == CommonApiConstants.NO_ID) {
            doubleElection.value = EditDoubleElectionParams.getDefault(getSelfUserID())
        } else {
            getDoubleElectionDetails(doubleElectionID)
        }
    }

    private fun getDoubleElectionDetails(doubleElectionID: Int) {
        showLoading()
        viewModelScope.launch {
            mSchoolInfoRepository.getDoubleElectionDetails(doubleElectionID)
                .handleResult({
                    it?.let {
                        it.id = doubleElectionID
                        it.uid = getSelfUserID()
                        doubleElection.value = it
                    }
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    fun setupStartTime(startTime: String) {
        doubleElection.value?.setupStartTime(startTime)
    }

    fun setupEndTime(endTime: String) {
        doubleElection.value?.setupEndTime(endTime)
    }

    fun getDoubleElectionStartTime(): String? {
        return doubleElection.value?.ksdate
    }

    fun getDoubleElectionEndTime(): String? {
        return doubleElection.value?.jsdate
    }

    fun toEditDesc() {
        doubleElection.value?.let {
            toEditDescCommand.value = it.content
        }
    }

    fun submit() {
        viewModelScope.launch {
            doubleElection.value?.let {
                if (it.hasEmpty()) {
                    showToast(it.getEmptyTips())
                    return@launch
                }
                if (it.id == CommonApiConstants.NO_ID) {
                    releaseDoubleElection(it)
                } else {
                    updateDoubleElection(it)
                }
            }
        }
    }

    private suspend fun releaseDoubleElection(it: EditDoubleElectionParams) {
        showLoading()
        mSchoolInfoRepository.releaseElection(it)
            .handleResult({
                releaseSuccessEvent.call()
            }, { err ->
                showToast(err.errMsg)
            }, {
                hideLoading()
            })
    }

    private suspend fun updateDoubleElection(it: EditDoubleElectionParams) {
        showLoading()
        mSchoolInfoRepository.updateDoubleElection(it)
            .handleResult({
                updateSuccessEvent.call()
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
    }

    fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            EditDoubleElectionActivity.TO_EDIT_ADDRESS_CODE -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    val result = data.getStringExtra(EditInfoNavigation.EXTRA_RESULT_TEXT).getOrDefault()
                    doubleElection.value?.setupAddress(result)
                }
            }
            EditDoubleElectionActivity.TO_EDIT_ROUTE_CODE -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    val result = data.getStringExtra(EditInfoNavigation.EXTRA_RESULT_TEXT).getOrDefault()
                    doubleElection.value?.setupRoute(result)
                }
            }
            EditDoubleElectionActivity.TO_EDIT_DESC_CODE -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    val result =
                        data.getStringExtra(EditRichTextNavigation.EXTRA_RESULT_CONTENT).getOrDefault()
                    doubleElection.value?.setupDesc(result)
                }
            }
        }
    }
}