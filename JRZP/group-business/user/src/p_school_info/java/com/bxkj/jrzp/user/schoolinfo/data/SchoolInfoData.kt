package com.bxkj.jrzp.user.schoolinfo.data

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.bxkj.common.util.kotlin.fixImgUrl
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.widget.adresspickerdialog.AddressData
import com.bxkj.jrzp.user.BR
import java.lang.StringBuilder

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/1
 * @version: V1.0
 */
class SchoolInfoData(
  var uid: Int,
  var name: String? = "",
  var xxlx: Int? = 0,
  var province: Int? = 0,
  var provinceName: String? = "",
  var city: Int? = 0,
  var cityName: String? = "",
  var county: Int? = 0,
  var countyName: String? = "",
  var dizhi: String? = "",
  var jyurl: String? = "",
  var tel: String? = "",
  var syl: Int? = 0,
  var jbw: Int? = 0,
  var ryy: Int? = 0,
  var bk: Int? = 0,
  var gz: Int? = 0,
  var yjsy: Int? = 0,
  var dlxy: Int? = 0,
  var mb: Int? = 0,
  var crjy: Int? = 0
) : BaseObservable() {

  var pic: String? = ""
    set(value) {
      field = value
      notifyPropertyChanged(BR.schoolLogo)
    }

  @get:Bindable
  var xxlxName: String? = ""
    set(value) {
      field = value
      notifyPropertyChanged(BR.xxlxName)
    }

  @get:Bindable
  var info: String? = ""
    set(value) {
      field = value
      notifyPropertyChanged(BR.info)
    }

  @Bindable
  fun getSchoolLogo(): String? {
    return pic?.fixImgUrl()
  }

  fun applySchoolNaturesChange(selectedSchoolNature: ArrayList<String>) {
    syl = if (selectedSchoolNature.contains("双一流")) 1 else 0
    jbw = if (selectedSchoolNature.contains("985")) 1 else 0
    ryy = if (selectedSchoolNature.contains("211")) 1 else 0
    bk = if (selectedSchoolNature.contains("本科")) 1 else 0
    gz = if (selectedSchoolNature.contains("高职")) 1 else 0
    yjsy = if (selectedSchoolNature.contains("研究生院")) 1 else 0
    dlxy = if (selectedSchoolNature.contains("独立学院")) 1 else 0
    mb = if (selectedSchoolNature.contains("民办")) 1 else 0
    crjy = if (selectedSchoolNature.contains("成人教育")) 1 else 0
    notifyPropertyChanged(BR.schoolNatureText)
  }

  @Bindable
  fun getSchoolNatureText(): String {
    val strNature = StringBuilder()
    if (syl == 1) {
      strNature.append("双一流").append("，")
    }
    if (jbw == 1) {
      strNature.append("985").append("，")
    }
    if (ryy == 1) {
      strNature.append("211").append("，")
    }
    if (bk == 1) {
      strNature.append("本科").append("，")
    }
    if (gz == 1) {
      strNature.append("高职").append("，")
    }
    if (yjsy == 1) {
      strNature.append("研究生院").append("，")
    }
    if (dlxy == 1) {
      strNature.append("独立学院").append("，")
    }
    if (mb == 1) {
      strNature.append("民办").append("，")
    }
    if (crjy == 1) {
      strNature.append("成人教育").append("，")
    }
    return if (strNature.isEmpty()) strNature.toString()
    else strNature.substring(0, strNature.length - 1)
  }

  fun getAddressData(): AddressData {
    return AddressData(
      province.getOrDefault(),
      provinceName,
      city.getOrDefault(),
      cityName,
      county.getOrDefault(),
      countyName
    )
  }

  fun updateAddress(address: AddressData, details: String) {
    this.province = address.provinceId
    this.provinceName = address.provinceName
    this.city = address.cityId
    this.cityName = address.cityName
    this.county = address.areaId
    this.countyName = address.areaName
    this.dizhi = details
    notifyPropertyChanged(BR.showAddressText)
  }

  @Bindable
  fun getShowAddressText(): String {
    return provinceName + cityName + countyName + dizhi
  }

  override fun toString(): String {
    return "SchoolInfoData(uid=$uid, name=$name, xxlx=$xxlx, province=$province, provinceName=$provinceName, city=$city, cityName=$cityName, county=$county, countyName=$countyName, dizhi=$dizhi, jyurl=$jyurl, tel=$tel, syl=$syl, jbw=$jbw, ryy=$ryy, bk=$bk, gz=$gz, yjsy=$yjsy, dlxy=$dlxy, mb=$mb, crjy=$crjy, pic=$pic)"
  }

}