package com.bxkj.jrzp.user.schoolinfo.ui.editemployment

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.bxkj.jrzp.user.BR

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/26
 * @version: V1.0
 */
class EditEmploymentParams private constructor(userID: Int, employmentID: Int = 0) :
  BaseObservable() {

  var uid: Int = userID
  var id: Int = employmentID
  var zyId: Int = 0

  @Bindable
  var azZhuanye: String = ""
    set(value) {
      field = value
      notifyPropertyChanged(BR.azZhuanye)
    }
  var azCount: String = ""

  @Bindable
  var azDate: String = ""
    set(value) {
      field = value
      notifyPropertyChanged(BR.azDate)
    }

  var yxId: Int = 0

  @get:Bindable
  var yxName: String = ""
    set(value) {
      field = value
      notifyPropertyChanged(BR.yxName)
    }

  companion object {

    fun getDefault(userID: Int): EditEmploymentParams {
      return EditEmploymentParams(userID)
    }

    fun getDefault(userID: Int, employmentID: Int): EditEmploymentParams {
      return EditEmploymentParams(userID, employmentID)
    }
  }
}