package com.bxkj.jrzp.user.schoolinfo.ui.contractinfo

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/7
 * @version: V1.0
 */
class SchoolContractInfoNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/schoolcontractinfo"

    fun create(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}