package com.bxkj.jrzp.user.schoolinfo.ui.editdoubleelection

import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/24
 * @version: V1.0
 */
class EditDoubleElectionNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/editdoubleelection"

    const val EXTRA_DOUBLE_ELECTION_ID = "DOUBLE_ELECTION_ID"

    fun navigate(doubleElectionID: Int = CommonApiConstants.NO_ID): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_DOUBLE_ELECTION_ID, doubleElectionID)
    }
  }
}