package com.bxkj.jrzp.user.schoolinfo.ui.civilserviceinfolist

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/4
 * @version: V1.0
 */
class CivilServiceInfoListNavigation {

  companion object {

    const val PATH = "${UserConstants.DIRECTORY}/civilserviceinfolist"

    fun create(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}