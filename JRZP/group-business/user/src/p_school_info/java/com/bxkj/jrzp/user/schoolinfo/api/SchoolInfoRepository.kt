package com.bxkj.jrzp.user.schoolinfo.api

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.network.ListDTO
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.objectEncrypt
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.user.data.CampusTalkEditorData
import com.bxkj.jrzp.user.schoolinfo.data.*
import com.bxkj.jrzp.user.schoolinfo.ui.editdoubleelection.EditDoubleElectionParams
import com.bxkj.jrzp.user.schoolinfo.ui.editemployment.EditEmploymentParams
import com.bxkj.personal.data.CompanyDetailsData
import com.bxkj.personal.data.UserNoticeItemData
import javax.inject.Inject

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/24
 * @version: V1.0
 */
class SchoolInfoRepository @Inject constructor(
    private val mSchoolInfoApi: SchoolInfoApi
) : BaseRepo() {

    /**
     * [status] -1全部，0待审核，1审核失败，2审核通过
     */
    suspend fun getSeminarList(
        type: Int,
        userID: Int,
        isSchool: Boolean,
        status: Int,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<List<CampusTalkData>> {
        return httpRequest {
            mSchoolInfoApi.getSeminarList(
                ZPRequestBody().apply {
                    put("type", type)
                    if (isSchool) {
                        put("uid2", userID)
                    } else {
                        put("uid", userID)
                    }
                    put("status", status)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }.paramsEncrypt()
            )
        }
    }

    suspend fun getCampusTalkListV2(
        type: Int,
        userID: Int,
        isSchool: Boolean,
        status: Int,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<ListDTO<CampusTalkDataV2>> {
        return httpRequest {
            mSchoolInfoApi.getCampusTalkListV2(
                ZPRequestBody().apply {
                    put("ntid", type)
                    if (isSchool) {
                        put("uid2", userID)
                    } else {
                        put("uid", userID)
                    }
                    put("status", status)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 获取学校相关信息数量
     */
    suspend fun getSchoolInfoCount(userID: Int): ReqResponse<SchoolInfoCountData> {
        return httpRequest {
            mSchoolInfoApi.getSchoolInfoCount(
                ZPRequestBody().apply {
                    put("uid", userID)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 获取学校联系信息
     */
    suspend fun getSchoolContractInfo(userID: Int): ReqResponse<SchoolContractData> {
        return httpRequest {
            mSchoolInfoApi.getSchoolContractInfo(
                ZPRequestBody().apply {
                    put("uid", userID)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 更新学校联系信息
     */
    suspend fun updateSchoolContractInfo(schoolContractInfo: SchoolContractData): ReqResponse<Nothing> {
        return httpRequest {
            mSchoolInfoApi.updateSchoolContractInfo(schoolContractInfo.objectEncrypt())
        }
    }

    /**
     * 获取就业安置详情
     */
    suspend fun getEmploymentInfo(
        userID: Int,
        employmentID: Int
    ): ReqResponse<EditEmploymentParams> {
        return httpRequest {
            mSchoolInfoApi.getEmploymentInfo(
                ZPRequestBody().apply {
                    put("uid", userID)
                    put("id", employmentID)
                }.paramsEncrypt()
            )
        }
    }

    //============================ 双选会（DoubleElection） ============================//

    /**
     * 双选会列表
     */
    suspend fun getDoubleElectionList(
        userID: Int,
        state: Int,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<List<DoubleElectionData>> {
        return httpRequest {
            mSchoolInfoApi.getDoubleElectionList(
                ZPRequestBody().apply {
                    put("uid", userID)
                    put("status", state)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 双选会详情
     */
    suspend fun getDoubleElectionDetails(doubleElectionID: Int): ReqResponse<EditDoubleElectionParams> {
        return httpRequest {
            mSchoolInfoApi.getDoubleElectionDetails(
                ZPRequestBody().apply {
                    put("id", doubleElectionID)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 修改双选会
     */
    suspend fun updateDoubleElection(doubleElectionParams: EditDoubleElectionParams): ReqResponse<Nothing> {
        return httpRequest {
            mSchoolInfoApi.updateDoubleElection(doubleElectionParams.objectEncrypt())
        }
    }

    /**
     * 发布双选会
     */
    suspend fun releaseElection(doubleElectionParams: EditDoubleElectionParams): ReqResponse<Nothing> {
        return httpRequest {
            mSchoolInfoApi.releaseDoubleElection(
                doubleElectionParams.objectEncrypt()
            )
        }
    }

    /**
     * 审核宣讲会
     */
    suspend fun reviewSeminar(userID: Int, seminarID: Int, status: Int): ReqResponse<Nothing> {
        return httpRequest {
            mSchoolInfoApi.reviewSeminar(
                ZPRequestBody().apply {
                    put("uid", userID)
                    put("id", seminarID)
                    put("status", status)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 获取院校列表
     */
    suspend fun getSchoolList(
        cityID: Int,
        name: String,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<List<SchoolAttachInfoData>> {
        return httpRequest {
            mSchoolInfoApi.getSchoolList(
                ZPRequestBody().apply {
                    put("city", cityID)
                    put("name", name)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }
            )
        }
    }

    /**
     * 返回院系列表
     */
    suspend fun getDepartmentList(
        parentID: Int,
        name: String
    ): ReqResponse<List<SchoolAttachInfoData>> {
        return httpRequest {
            mSchoolInfoApi.getDepartmentList(
                ZPRequestBody().apply {
                    put("gaoxiaoId", parentID)
                    put("name", name)
                }
            )
        }
    }

    /**
     * 返回专业列表
     */
    suspend fun getProfessionList(
        parentID: Int,
        name: String,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<List<SchoolAttachInfoData>> {
        return httpRequest {
            mSchoolInfoApi.getProfessionList(
                ZPRequestBody().apply {
                    put("proid", parentID)
                    put("name", name)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }
            )
        }
    }

    /**
     * 添加就业安置
     */
    suspend fun updateEmployment(employmentParams: EditEmploymentParams): ReqResponse<Nothing> {
        return httpRequest {
            mSchoolInfoApi.updateEmployment(
                employmentParams.objectEncrypt()
            )
        }
    }

    //============================ 学校信息（SchoolInfo） ============================//

    /**
     * 获取学校信息
     */
    suspend fun getSchoolInfo(userID: Int): ReqResponse<SchoolInfoData> {
        return httpRequest {
            mSchoolInfoApi.getSchoolInfo(
                ZPRequestBody().apply {
                    put("uid", userID)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 编辑学校信息
     */
    suspend fun updateSchoolInfo(schoolInfo: SchoolInfoData): ReqResponse<Nothing> {
        return httpRequest {
            mSchoolInfoApi.updateSchoolInfo(schoolInfo.objectEncrypt())
        }
    }

    /**
     * 获取学校分类
     */
    suspend fun getSchoolTypes(): ReqResponse<List<PickerOptionsData>> {
        return httpRequest {
            mSchoolInfoApi.getSchoolTypes()
        }
    }

    //============================ DoubleElectionCompany(双选会公司) ============================//
    /**
     * 获取双选会报名企业
     */
    suspend fun getDoubleElectionCompany(
        userID: Int,
        doubleElectionID: Int,
        doubleElectionType: Int,
        reviewStatus: Int,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<List<DoubleElectionCompanyData>> {
        return httpRequest {
            mSchoolInfoApi.getDoubleElectionCompany(
                ZPRequestBody().apply {
                    put("uid", userID)
                    put("nid", doubleElectionID)
                    put("type", doubleElectionType)
                    put("state", reviewStatus)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 审核双选会企业
     */
    suspend fun reviewDoubleElectionCompany(
        doubleElectionCompanyID: Int,
        state: Int
    ): ReqResponse<Nothing> {
        return httpRequest {
            mSchoolInfoApi.reviewDoubleElectionCompany(
                ZPRequestBody().apply {
                    put("id", doubleElectionCompanyID)
                    put("state", state)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 获取双选会企业详情
     */
    suspend fun getDoubleElectionCompanyDetails(companyID: Int): ReqResponse<CompanyDetailsData> {
        return httpRequest {
            mSchoolInfoApi.getDoubleElectionCompanyDetails(
                ZPRequestBody().apply {
                    put("id", companyID)
                }.paramsEncrypt()
            )
        }
    }

    //============================== 宣讲会 ==============================//

    /**
     * 编辑
     */
    suspend fun editSeminar(campusTalkData: CampusTalkEditorData): ReqResponse<Nothing> {
        return httpRequest {
            mSchoolInfoApi.editSeminar(campusTalkData.objectEncrypt())
        }
    }

    /**
     * 获取宣讲会详情
     */
    suspend fun getSeminarInfo(seminarID: Int): ReqResponse<CampusTalkEditorData> {
        return httpRequest {
            mSchoolInfoApi.getSeminarInfo(
                ZPRequestBody().apply {
                    put("id", seminarID)
                }.paramsEncrypt()
            )
        }
    }

    //============================== 公招公考 ==============================//
    /**
     * 公考
     */
    suspend fun getCivilServiceInfoList(
        userID: Int,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<List<UserNoticeItemData>> {
        return httpRequest {
            mSchoolInfoApi.getCivilServiceInfoList(
                ZPRequestBody().apply {
                    put("uid", userID)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }.paramsEncrypt()
            )
        }
    }
}