package com.bxkj.jrzp.user.schoolinfo.ui.schoolinfo

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.PickerOptionsData
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.common.widget.adresspickerdialog.AddressData
import com.bxkj.jrzp.user.repository.OpenAuthenticationRepository
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.user.schoolinfo.api.SchoolInfoRepository
import com.bxkj.jrzp.user.schoolinfo.data.SchoolInfoData
import com.bxkj.personal.ui.activity.editinfo.EditInfoNavigation
import com.bxkj.personal.ui.activity.selectaddressbymap.SelectAddressNavigation
import com.bxkj.support.upload.repository.UploadRepository
import com.luck.picture.lib.config.PictureConfig
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/11/27
 * @version: V1.0
 */
class SchoolInfoViewModel @Inject constructor(
  private val mSchoolInfoRepository: SchoolInfoRepository,
  private val mUploadRepository: UploadRepository,
  private val mOpenAuthenticationRepository: OpenAuthenticationRepository
) : BaseViewModel() {

  val schoolInfo = MutableLiveData<SchoolInfoData>()

  val showSchoolNaturesSelectDialogCommand =
    LiveEvent<List<String>>()
  val editSchoolDescCommand = LiveEvent<String>()
  val submitSuccessEvent = LiveEvent<SchoolInfoData>()
  val showSchoolTypePickerCommand =
    LiveEvent<List<PickerOptionsData>>()
  val toSelectDetailsAddressCommand =
    LiveEvent<SchoolInfoData>()
  val authed = MutableLiveData<Boolean>()

  private val mSelectedSchoolNature = ArrayList<String>()

  private var mSchoolTypes: List<PickerOptionsData>? = null

  init {
    getAuthStatus()
    getSchoolInfo()
  }

  private fun getAuthStatus() {
    viewModelScope.launch {
      mOpenAuthenticationRepository.getUserAuthStatus(getSelfUserID())
        .handleResult({
          it?.let {
            if (it[0].typeAuthSuccess(AuthenticationType.SCHOOL)) {
              authed.value = true
            }
          }
        })
    }
  }

  private fun getSchoolInfo() {
    viewModelScope.launch {
      mSchoolInfoRepository.getSchoolInfo(getSelfUserID())
        .handleResult({
          it?.let {
            it.uid = getSelfUserID()
            schoolInfo.value = it
            mSelectedSchoolNature.clear()
            mSelectedSchoolNature.addAll(it.getSchoolNatureText().split("，").toList())
          } ?: let {
            schoolInfo.value = SchoolInfoData(getSelfUserID())
          }
        }, {
          schoolInfo.value = SchoolInfoData(getSelfUserID())
        })
    }
  }

  fun toSelectDetailsAddress() {
    schoolInfo.value?.let {
      toSelectDetailsAddressCommand.value = it
    }
  }

  fun showSchoolNaturesSelectDialog() {
    showSchoolNaturesSelectDialogCommand.value = mSelectedSchoolNature
  }

  fun switchSchoolNatureSelectStatus(selectItem: String) {
    if (mSelectedSchoolNature.contains(selectItem)) {
      mSelectedSchoolNature.remove(selectItem)
    } else {
      mSelectedSchoolNature.add(selectItem)
    }
    applySchoolNaturesChange()
  }

  fun applySchoolNaturesChange() {
    schoolInfo.value?.applySchoolNaturesChange(mSelectedSchoolNature)
  }

  fun showSchoolTypesPicker() {
    mSchoolTypes?.let {
      showSchoolTypePickerCommand.value = it
    } ?: let {
      viewModelScope.launch {
        showLoading()
        mSchoolInfoRepository.getSchoolTypes()
          .handleResult({
            mSchoolTypes = it
            showSchoolTypePickerCommand.value = it
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    }
  }

  fun setupSelectSchoolType(pickerOptionsData: PickerOptionsData) {
    schoolInfo.value?.let {
      it.xxlx = pickerOptionsData.id
      it.xxlxName = pickerOptionsData.name
    }
  }

  fun editSchoolDesc() {
    schoolInfo.value?.let {
      editSchoolDescCommand.value = it.info
    }
  }

  fun submit() {
    schoolInfo.value?.let { schoolInfo ->
      viewModelScope.launch {
        showLoading()
        mSchoolInfoRepository.updateSchoolInfo(schoolInfo)
          .handleResult({
            submitSuccessEvent.value = schoolInfo
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    }
  }

  private fun uploadCompanyLogo(imgPath: String) {
    viewModelScope.launch {
      showLoading()
      mUploadRepository.uploadFileV3(
        imgPath,
        UploadFileRequestParams.getImageUploadParams(
          getSelfUserID(),
          UploadFileRequestParams.PATH_NAME_COMPANY_LOGO
        )
      ).handleResult({
        it?.let {
          schoolInfo.value?.pic = it.url
        }
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }

  fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    if (requestCode == SchoolInfoActivity.TO_EDIT_SCHOOL_DESC_CODE) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        val schoolDesc = data.getStringExtra(EditInfoNavigation.EXTRA_RESULT_TEXT)
        schoolInfo.value?.info = schoolDesc
      }
    } else if (requestCode == PictureConfig.CHOOSE_REQUEST) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        uploadCompanyLogo(data.getSelectedFirstMediaPath())
      }
    } else if (requestCode == SchoolInfoActivity.TO_SELECT_DETAILS_ADDRESS_CODE) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        val addressData =
          data.getParcelableExtra<AddressData>(SelectAddressNavigation.ADDRESS_DATA)
        val detailsAddress = data.getStringExtra(SelectAddressNavigation.DETAILS_ADDRESS)
        schoolInfo.value?.updateAddress(addressData!!,detailsAddress!!)
      }
    }
  }
}