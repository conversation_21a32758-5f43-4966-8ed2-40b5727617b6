<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.user.schoolinfo.ui.editdoubleelection.EditDoubleElectionViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:title="@string/user_edit_double_election_release_page_title" />

    <LinearLayout style="@style/Layout.InfoItem">

      <TextView
        style="@style/Text.15sp.333333"
        android:text="@string/user_edit_double_election_title" />

      <EditText
        style="@style/EditText.Basic.RightAlignment"
        android:hint="@string/user_edit_double_election_title_hint"
        android:text="@={viewModel.doubleElection.title}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_12" />

    <LinearLayout
      android:id="@+id/ll_start_time"
      style="@style/Layout.InfoItem"
      android:onClick="@{onClickListener}">

      <TextView
        style="@style/Text.15sp.333333"
        android:text="@string/user_edit_double_election_start_time" />

      <TextView
        style="@style/Text.InfoItem.Select"
        android:hint="@string/user_edit_double_election_start_time_hint"
        android:text="@{viewModel.doubleElection.ksdate}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_12" />

    <LinearLayout
      android:id="@+id/ll_end_time"
      style="@style/Layout.InfoItem"
      android:onClick="@{onClickListener}">

      <TextView
        style="@style/Text.15sp.333333"
        android:text="@string/user_edit_double_election_end_time" />

      <TextView
        style="@style/Text.InfoItem.Select"
        android:hint="@string/user_edit_double_election_end_time_hint"
        android:text="@{viewModel.doubleElection.jsdate}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_12" />

    <LinearLayout
      android:id="@+id/ll_address"
      style="@style/Layout.InfoItem"
      android:onClick="@{onClickListener}">

      <TextView
        style="@style/Text.15sp.333333"
        android:text="@string/user_edit_double_election_address" />

      <TextView
        android:id="@+id/tv_address"
        style="@style/Text.InfoItem.Select"
        android:hint="@string/user_edit_double_election_address_hint"
        android:text="@{viewModel.doubleElection.dizhi}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_12" />

    <LinearLayout style="@style/Layout.InfoItem">

      <TextView
        style="@style/Text.15sp.333333"
        android:text="@string/user_edit_double_election_venue" />

      <EditText
        style="@style/EditText.Basic.RightAlignment"
        android:hint="@string/user_edit_double_election_venue_hint"
        android:text="@={viewModel.doubleElection.cgName}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_12" />

    <LinearLayout
      android:id="@+id/ll_route"
      style="@style/Layout.InfoItem"
      android:onClick="@{onClickListener}">

      <TextView
        style="@style/Text.15sp.333333"
        android:text="@string/user_edit_double_election_route" />

      <TextView
        android:id="@+id/tv_route"
        style="@style/Text.InfoItem.Select"
        android:hint="@string/user_edit_double_election_route_hint"
        android:text="@{viewModel.doubleElection.traffic}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_12" />

    <LinearLayout
      android:id="@+id/ll_desc"
      style="@style/match_wrap"
      android:onClick="@{()->viewModel.toEditDesc()}"
      android:orientation="vertical"
      android:paddingStart="@dimen/dp_12"
      android:paddingEnd="@dimen/dp_12">

      <TextView
        style="@style/Text.15sp.333333"
        android:layout_marginTop="@dimen/dp_14"
        android:text="@string/user_edit_double_election_desc" />

      <TextView
        android:id="@+id/tv_desc"
        style="@style/Text.15sp.333333"
        android:ellipsize="end"
        android:hint="@string/user_edit_double_election_desc_hint"
        android:maxLines="3"
        android:paddingTop="@dimen/dp_14"
        android:paddingBottom="@dimen/dp_14"
        android:text="@{HtmlUtils.fromHtml(viewModel.doubleElection.content)}" />
    </LinearLayout>

    <View
      style="@style/Line.Horizontal"
      android:layout_marginStart="@dimen/dp_12"
      android:layout_marginEnd="@dimen/dp_12" />

    <TextView
      android:id="@+id/tv_btn_submit"
      style="@style/Button.Basic.Round"
      android:layout_margin="@dimen/dp_30"
      android:onClick="@{()->viewModel.submit()}"
      android:text="@string/user_edit_double_election_release" />

  </LinearLayout>
</layout>