package com.bxkj.jrzp.user.enterpriseinfo.ui.campustalk

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.TipsDialog
import com.bxkj.enterprise.ui.activity.schoolrecruitresume.SchoolRecruitResumeNavigation
import com.bxkj.jrzp.user.R
import com.bxkj.jrzp.user.R.layout
import com.bxkj.jrzp.user.databinding.UserFragmentEnterpriseSeminarBinding
import com.bxkj.jrzp.user.enterpriseinfo.ui.campustalk.edit.EditSeminarNavigation
import com.bxkj.jrzp.user.mine.ui.membercenter.MemberCenterWebNavigation
import com.bxkj.jrzp.user.schoolinfo.data.CampusTalkDataV2

/**
 * @Description: 宣讲会
 * @author: YangXin
 * @date: 2020/12/14
 * @version: V1.0
 */
class CampusTalkListFragment :
    BaseDBFragment<UserFragmentEnterpriseSeminarBinding, CampusTalkListViewModel>() {

    companion object {

        private const val EXTRA_TYPE = "TYPE"

        /**
         * [type] 0:宣讲会 1:云宣讲
         */
        fun newInstance(type: Int): Fragment {
            return CampusTalkListFragment().apply {
                arguments = bundleOf(EXTRA_TYPE to type)
            }
        }
    }

    override fun getViewModelClass(): Class<CampusTalkListViewModel> =
        CampusTalkListViewModel::class.java

    override fun getLayoutId(): Int = R.layout.user_fragment_enterprise_seminar

    override fun initPage(view: View, saveInstanceState: Bundle?) {
        viewBinding.viewModel = viewModel

        setupCampusTalkList()

        subscribeViewModelEvent()

        viewModel.setType(arguments?.getInt(EXTRA_TYPE, 0).getOrDefault())

    }

    private fun subscribeViewModelEvent() {
        viewModel.toReleaseCampusCommand.observe(this, Observer {
            EditSeminarNavigation.create(it).start()
        })

        viewModel.showUpgradeVipCommand.observe(this, Observer {
            ActionDialog.Builder()
                .setContent(it)
                .setConfirmText("升级会员")
                .setOnConfirmClickListener {
                    MemberCenterWebNavigation.create().start()
                }.build().show(childFragmentManager)
        })

        viewModel.showErrorTipsCommand.observe(this, Observer {
            TipsDialog()
                .setContent(it)
                .show(childFragmentManager)
        })
    }

    override fun onResume() {
        super.onResume()
        viewModel.refresh()
    }

    private fun setupCampusTalkList() {
        val enterpriseSeminarListAdapter =
            SimpleDBListAdapter<CampusTalkDataV2>(
                parentActivity,
                layout.user_recycler_enterprise_seminar_item
            ).apply {
                setOnItemClickListener(object : SuperItemClickListener {
                    override fun onClick(v: View, position: Int) {
                        data[position].let {
                            if (v.id == R.id.tv_delete) {
                                showConfirmDeleteSeminarDialog(it)
                            } else if (v.id == R.id.tv_delivery_count) {
                                if (it.isUnreviewed()) {
                                    TipsDialog()
                                        .setContent("该校招信息审核中，无法查看投递信息")
                                        .show(childFragmentManager)
                                } else {
                                    SchoolRecruitResumeNavigation.create(it.id, 2).start()
                                }
                            } else {
                                if (!it.pendingReview()) {
                                    EditSeminarNavigation.create(it.type, it.id).start()
                                }
                            }
                        }
                    }
                }, R.id.tv_delete, R.id.tv_delivery_count)
            }

        viewBinding.includeSeminars.recyclerContent.layoutManager =
            LinearLayoutManager(parentActivity)
        viewBinding.includeSeminars.recyclerContent.addItemDecoration(
            LineItemDecoration.Builder()
                .divider(getResDrawable(R.drawable.divider_f4f4f4))
                .margin(dip(14))
                .build()
        )

        viewModel.enterpriseSeminarListViewModel.setAdapter(enterpriseSeminarListAdapter)
    }

    private fun showConfirmDeleteSeminarDialog(campusTalkData: CampusTalkDataV2) {
        ActionDialog.Builder()
            .setTitle(getString(R.string.common_tips))
            .setContent(getString(R.string.user_enterprise_seminar_delete_tips))
            .setOnConfirmClickListener {
                viewModel.deleteSeminar(campusTalkData)
            }.build().show(childFragmentManager)
    }

}