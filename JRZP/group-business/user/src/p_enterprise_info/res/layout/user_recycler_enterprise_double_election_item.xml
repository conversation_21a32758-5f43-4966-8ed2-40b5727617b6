<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="data"
      type="com.bxkj.jrzp.user.schoolinfo.data.DoubleElectionData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingStart="@dimen/dp_14"
    android:paddingTop="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_14"
    android:paddingBottom="@dimen/dp_16">

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.NormalInfoTitle"
      android:layout_width="@dimen/dp_0"
      android:ellipsize="end"
      android:maxLines="2"
      android:text="@{data.title}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_date"
      style="@style/Text.14sp.999999"
      android:layout_marginTop="@dimen/dp_8"
      android:text="@{data.date}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_view_count"
      style="@style/Text.14sp.999999"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_12"
      android:text="@{@string/user_enterprise_double_election_view_count_format(data.count)}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_date"
      app:layout_constraintEnd_toStartOf="@id/tv_review_state"
      app:layout_constraintStart_toEndOf="@id/tv_date" />

    <TextView
      android:id="@+id/tv_review_state"
      style="@style/Text.14sp.FE6600"
      android:text="@{HtmlUtils.fromHtml(data.colorStateText)}"
      app:layout_constraintBaseline_toBaselineOf="@id/tv_date"
      app:layout_constraintEnd_toEndOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>