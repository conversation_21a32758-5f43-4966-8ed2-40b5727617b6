package com.bxkj.jrzp.user.ui.enterpriseauth

import android.app.Activity.RESULT_OK
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.jrzp.user.data.AuthenticationInfoData
import com.bxkj.jrzp.user.repository.AuthenticationRepository
import com.bxkj.support.upload.repository.UploadRepository
import com.luck.picture.lib.config.PictureConfig
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.certification
 * @Description:
 * <AUTHOR>
 * @date 2020/1/3
 * @version V1.0
 */
class EnterpriseAuthViewModel @Inject constructor(
  private val mAuthenticationRepository: AuthenticationRepository,
  private val mOpenUploadRepository: UploadRepository
) : BaseViewModel() {

  val companyName = MutableLiveData<String>()
  val certificationInfo = MutableLiveData<AuthenticationInfoData?>()

  val showImage = MutableLiveData<String?>()
  val showHrImg = MutableLiveData<String?>()

  val submitSuccessEvent = LiveEvent<Void>()

  private var mAuthType: Int = AuthenticationType.ENTERPRISE
  val isHrAuth = MutableLiveData<Boolean>()

  private val mPendingUploadPicIds = arrayOf(0, 0)
  private val mPendingUploadPics: Array<String?> = arrayOf("", "")

  init {
    getCertificationInfo()
  }

  fun setupParams(cName: String, authType: Int) {
    companyName.value = cName
    mAuthType = authType
    if (authType == AuthenticationType.HR) {
      isHrAuth.value = true
    }
  }

  private fun getCertificationInfo() {
    viewModelScope.launch {
      showLoading()
      mAuthenticationRepository.getUserAuthenticationInfo(getSelfUserID())
        .handleResult({ result ->
          if (!result.isNullOrEmpty()) {
            val currentAuth = result[0]
            certificationInfo.value = currentAuth
            if (currentAuth.type >= 2) {
              val authPics = currentAuth.picList
              if (!authPics.isNullOrEmpty()) {
                val firstAuthPic = authPics[0].pic
                if (!firstAuthPic.isNullOrBlank()) {
                  mPendingUploadPics[0] = firstAuthPic
                  showImage.value = currentAuth.picDomain + firstAuthPic
                }
                if (currentAuth.type == AuthenticationType.HR && authPics.size > 1) {
                  val secondAuthPic = authPics[1].pic
                  if (!secondAuthPic.isNullOrBlank()) {
                    mPendingUploadPics[1] = secondAuthPic
                    showHrImg.value = currentAuth.picDomain + secondAuthPic
                  }
                }
              }
            }
          }
        }, {
          certificationInfo.value = AuthenticationInfoData()
        }, {
          hideLoading()
        })

    }
  }

  fun clearSelectedImg() {
    showImage.value = null
  }

  fun clearHrImg() {
    showHrImg.value = null
  }

  fun submit() {

    if (mPendingUploadPics[0].isNullOrEmpty()) {
      if (mAuthType == AuthenticationType.SCHOOL || mAuthType == AuthenticationType.INSTITUTIONS) {
        showToast("未上传事业单位法人证书")
      } else {
        showToast("未上传营业执照")
      }
      return
    }
    if (mAuthType == AuthenticationType.HR && mPendingUploadPics[1].isNullOrEmpty()) {
      showToast("未上传人力资源许可证")
      return
    }

    certificationInfo.value?.let {
      viewModelScope.launch {
        showLoading()
        mAuthenticationRepository.submitAuthentication(
          getSelfUserID(),
          it.name,
          getPicsString(),
          getPicIdsString(),
          mAuthType
        ).handleResult({
          submitSuccessEvent.call()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  private fun getPicIdsString(): String {
    val picIdsStringBuilder = StringBuilder()
    picIdsStringBuilder.append(mPendingUploadPicIds[0])
    if (mAuthType == AuthenticationType.HR) {
      picIdsStringBuilder.append(",")
        .append(mPendingUploadPicIds[1]).toString()
    }
    return picIdsStringBuilder.toString()
  }

  private fun getPicsString(): String {
    val picStringBuilder = StringBuilder()
    picStringBuilder.append(mPendingUploadPics[0])
    if (mAuthType == AuthenticationType.HR) {
      picStringBuilder.append(",")
        .append(mPendingUploadPics[1]).toString()
    }
    return picStringBuilder.toString()
  }

  fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    if (requestCode == PictureConfig.CHOOSE_REQUEST) {
      if (resultCode == RESULT_OK && data != null) {
        val realPath = data.getSelectedFirstMediaPath()
        showImage.value = data.getSelectedFirstMediaPath()
        uploadImage(realPath, 0)
      }
    } else if (requestCode == EnterpriseAuthActivity.TO_SELECT_HR_CODE) {
      if (resultCode == RESULT_OK && data != null) {
        val realPath = data.getSelectedFirstMediaPath()
        showHrImg.value = realPath
        uploadImage(realPath, 1)
      }
    }
  }

  private fun uploadImage(realPath: String, picIndex: Int) {
    viewModelScope.launch {
      showLoading()
      mOpenUploadRepository.uploadFileV3(
        realPath,
        UploadFileRequestParams.getImageUploadParams(
          getSelfUserID(),
          UploadFileRequestParams.PATH_NAME_BUSINESS_LICENSE
        )
      ).handleResult({
        it?.let {
          if (picIndex == 0) {
            showImage.value = CommonApiConstants.BASE_JRZP_IMG_URL + it.url
          } else {
            showHrImg.value = CommonApiConstants.BASE_JRZP_IMG_URL + it.url
          }
          mPendingUploadPicIds[picIndex] = it.id
          mPendingUploadPics[picIndex] = it.url
        }
      }, {
        showToast(it.errMsg)
      }, {
        hideLoading()
      })
    }
  }
}