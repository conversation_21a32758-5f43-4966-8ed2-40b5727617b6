package com.bxkj.jrzp.user.data

import com.google.gson.annotations.SerializedName

/**
 * Description: 联系方式
 * Author:45457
 **/
data class UserContactWayBean(
    @SerializedName("name")
    val contactWay: String,
    @SerializedName("userPhone")
    val registerPhone: String
) {

    fun getShowPhoneNumber(): String {
        return if (contactWay.isNullOrBlank()) {
            registerPhone
        } else {
            contactWay
        }
    }
}