package com.bxkj.jrzp.user.ui.enterpriseauth

import com.bxkj.common.enums.AuthenticationType
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.user.UserConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/14
 * @version: V1.0
 */
object AuthenticationNavigation {

    const val PATH = "${UserConstants.DIRECTORY}/authentication"

    const val EXTRA_COMPANY_NAME = "COMPANY_NAME"
    const val EXTRA_AUTH_TYPE = "AUTH_TYPE"

    @JvmStatic
    fun navigate(
        companyName: String,
        authType: Int = AuthenticationType.ENTERPRISE
    ): RouterNavigator {
        return Router.getInstance().to(PATH)
            .withString(EXTRA_COMPANY_NAME, companyName)
            .withInt(EXTRA_AUTH_TYPE, authType)
    }
}