package com.bxkj.jrzp.user.di

import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.di.ViewModelKey
import com.bxkj.jrzp.commission.ui.list.CommissionListViewModel
import com.bxkj.jrzp.friend.ui.contractsfriend.ContractsFriendViewModel
import com.bxkj.jrzp.friend.ui.recommendfriends.RecommendFriendsViewModel
import com.bxkj.jrzp.orderrecord.ui.details.CourseOrderDetailsViewModel
import com.bxkj.jrzp.orderrecord.ui.industrialserviceorderlist.IndustrialServiceOrderListViewModel
import com.bxkj.jrzp.orderrecord.ui.list.CoursesOrderListViewModel
import com.bxkj.jrzp.orderrecord.ui.review.CourseReviewViewModel
import com.bxkj.jrzp.orderrecord.ui.servceorderdetails.ServiceOrderDetailsViewModel
import com.bxkj.jrzp.user.enterpriseinfo.ui.campustalk.CampusTalkListViewModel
import com.bxkj.jrzp.user.enterpriseinfo.ui.campustalk.edit.EditCampusTalkViewModel
import com.bxkj.jrzp.user.enterpriseinfo.ui.enterprisevideo.EnterpriseVideoViewModel
import com.bxkj.jrzp.user.enterpriseinfo.ui.jobfair.EnterpriseJobFairViewModel
import com.bxkj.jrzp.user.enterpriseinfo.ui.jobfair.doubleelection.EnterpriseDoubleElectionViewModel
import com.bxkj.jrzp.user.institutions.ui.institutionsinfo.InstitutionsInfoViewModel
import com.bxkj.jrzp.user.message.ui.MessageGroupViewModel
import com.bxkj.jrzp.user.mine.ui.enterpriesemine.BusinessMineViewModel
import com.bxkj.jrzp.user.mine.ui.group.MinePageContainerViewModel
import com.bxkj.jrzp.user.mine.ui.institutionsmine.InstitutionsMineViewModel
import com.bxkj.jrzp.user.mine.ui.personalmine.GeekMineViewModel
import com.bxkj.jrzp.user.mine.ui.schoolmine.SchoolMineViewModel
import com.bxkj.jrzp.user.mine.ui.updatebindphonenumber.UpdateBindPhoneNumberViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.bindschoolattachinfo.BindSchoolAttachInfoViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.civilserviceinfolist.CivilServiceInfoListViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.contractinfo.SchoolContractInfoViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.doubleelectioncompanydetails.DoubleElectionCompanyDetailsViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.doubleelectioncompanys.DoubleElectionCompanyViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.doubleelections.DoubleElectionsViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.editdoubleelection.EditDoubleElectionViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.editemployment.EditEmploymentViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.employments.EmploymentsViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.schoolinfo.SchoolInfoViewModel
import com.bxkj.jrzp.user.schoolinfo.ui.seminars.SeminarsViewModel
import com.bxkj.jrzp.user.switchaccount.ui.switchaccount.SwitchAccountViewModel
import com.bxkj.jrzp.user.switchidentity.ui.SwitchUserIdentityViewModel
import com.bxkj.jrzp.user.ui.addalipay.AddReceivingAccountViewModel
import com.bxkj.jrzp.user.ui.alipaylist.AlipayAccountListViewModel
import com.bxkj.jrzp.user.ui.enterpriseauth.EnterpriseAuthViewModel
import com.bxkj.jrzp.user.ui.idcardvalidation.IDCardValidationViewModel
import com.bxkj.jrzp.user.ui.jobproveauth.JobProveAuthViewModel
import com.bxkj.jrzp.user.ui.personalauthentication.PersonalAuthenticationViewModel
import com.bxkj.jrzp.user.ui.personalauthentication.stepone.PersonalAuthStepOneViewModel
import com.bxkj.jrzp.user.ui.personalauthentication.steptwo.PersonalAuthStepTwoViewModel
import com.bxkj.jrzp.user.ui.selectauthtype.SelectAuthTypeViewModel
import com.bxkj.jrzp.user.videomanagement.VideoManagementViewModel
import com.bxkj.jrzp.user.videorelate.ui.VideoRelateViewModel
import com.bxkj.jrzp.user.videorelate.ui.relatejob.RelateJobsViewModel
import com.bxkj.jrzp.user.withdraw.ui.alipay.WithdrawByAlipayViewModel
import com.bxkj.jrzp.user.withdraw.ui.history.WithdrawHistoryViewModel
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeViewModel
import com.bxkj.jrzp.userhome.ui.userjob.UserJobListViewModel
import com.bxkj.jrzp.userhome.ui.usernews.UserNewsListViewModel
import com.bxkj.jrzp.userhome.ui.userphoto.UserPhotoListViewModel
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/29
 * @version: V1.0
 */
@Module
abstract class UserViewModelModule {

  @Binds
  @IntoMap
  @ViewModelKey(IDCardValidationViewModel::class)
  abstract fun bindIDCardValidationViewModel(iDCardValidationViewModel: IDCardValidationViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(UpdateBindPhoneNumberViewModel::class)
  abstract fun updateBindPhoneNumberViewModel(updateBindPhoneNumberViewModel: UpdateBindPhoneNumberViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(SwitchUserIdentityViewModel::class)
  abstract fun switchUserIdentityViewModel(switchUserIdentityViewModel: SwitchUserIdentityViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(CommissionListViewModel::class)
  abstract fun brokerageListActivity(commissionListViewModel: CommissionListViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(CoursesOrderListViewModel::class)
  abstract fun coursesOrderListViewModel(coursesOrderListViewModel: CoursesOrderListViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(IndustrialServiceOrderListViewModel::class)
  abstract fun bindIndustrialServiceOrderListViewModel(industrialServiceOrderListViewModel: IndustrialServiceOrderListViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(ServiceOrderDetailsViewModel::class)
  abstract fun bindServiceOrderDetailsViewModel(serviceOrderDetailsViewModel: ServiceOrderDetailsViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(WithdrawByAlipayViewModel::class)
  abstract fun withdrawByAlipayViewModel(withdrawByAlipayViewModel: WithdrawByAlipayViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(WithdrawHistoryViewModel::class)
  abstract fun withdrawHistoryViewModel(withdrawHistoryViewModel: WithdrawHistoryViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(AlipayAccountListViewModel::class)
  abstract fun alipayAccountListViewModel(alipayAccountListViewModel: AlipayAccountListViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(AddReceivingAccountViewModel::class)
  abstract fun addReceivingAccountViewModel(addReceivingAccountViewModel: AddReceivingAccountViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(CourseReviewViewModel::class)
  abstract fun courseReviewViewModel(courseReviewViewModel: CourseReviewViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(CourseOrderDetailsViewModel::class)
  abstract fun courseOrderDetailsViewModel(courseOrderDetailsViewModel: CourseOrderDetailsViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(VideoRelateViewModel::class)
  abstract fun bindVideoRelateViewModel(videoRelateViewModel: VideoRelateViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(RelateJobsViewModel::class)
  abstract fun bindRelateJobsViewModel(relateJobsViewModel: RelateJobsViewModel): BaseViewModel

  //**************************************认证**************************************
  @Binds
  @IntoMap
  @ViewModelKey(SelectAuthTypeViewModel::class)
  abstract fun bindSelectAuthTypeViewModel(selectAuthTypeViewModel: SelectAuthTypeViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(EnterpriseAuthViewModel::class)
  abstract fun bindAuthenticationViewModel(authenticationViewModel: EnterpriseAuthViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(PersonalAuthenticationViewModel::class)
  abstract fun bindPersonalAuthenticationViewModel(personalAuthenticationViewModel: PersonalAuthenticationViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(PersonalAuthStepOneViewModel::class)
  abstract fun bindPersonalAuthStepOneViewModel(personalAuthStepOneViewModel: PersonalAuthStepOneViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(PersonalAuthStepTwoViewModel::class)
  abstract fun bindPersonalAuthStepTwoViewModel(personalAuthStepTwoViewModel: PersonalAuthStepTwoViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(JobProveAuthViewModel::class)
  abstract fun bindJobProveAuthViewModel(jobProveAuthViewModel: JobProveAuthViewModel): BaseViewModel

  //============================ 我的 ============================//
  @Binds
  @IntoMap
  @ViewModelKey(GeekMineViewModel::class)
  abstract fun bindPersonalMineViewModel(geekMineViewModel: GeekMineViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(BusinessMineViewModel::class)
  abstract fun bindEnterpriseMineViewModel(businessMineViewModel: BusinessMineViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(SchoolMineViewModel::class)
  abstract fun bindSchoolMineViewModel(schoolMineViewModel: SchoolMineViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(InstitutionsMineViewModel::class)
  abstract fun bindInstitutionsMineViewModel(institutionsMineViewModel: InstitutionsMineViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(MinePageContainerViewModel::class)
  abstract fun bindMineGroupViewModel(minePageContainerViewModel: MinePageContainerViewModel): BaseViewModel

  //============================ 个人中心 ============================//
  @Binds
  @IntoMap
  @ViewModelKey(UserHomeViewModel::class)
  abstract fun bindUserHomeViewModel(userHomeViewModel: UserHomeViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(UserNewsListViewModel::class)
  abstract fun bindUserNewsListViewModel(userNewsListViewModel: UserNewsListViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(UserPhotoListViewModel::class)
  abstract fun bindUserPhotoListViewModel(userPhotoListViewModel: UserPhotoListViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(UserJobListViewModel::class)
  abstract fun bindUserJobListViewModel(userJobListViewModel: UserJobListViewModel): BaseViewModel

  //============================ 消息 ============================//
  @Binds
  @IntoMap
  @ViewModelKey(MessageGroupViewModel::class)
  abstract fun bindMessageGroupViewModel(messageGroupViewModel: MessageGroupViewModel): BaseViewModel

  //============================ 视频管理 ============================//
  @Binds
  @IntoMap
  @ViewModelKey(VideoManagementViewModel::class)
  abstract fun bindVideoManagementViewModel(videoManagementViewModel: VideoManagementViewModel): BaseViewModel

  //============================ 发现好友 ============================//
  @Binds
  @IntoMap
  @ViewModelKey(RecommendFriendsViewModel::class)
  abstract fun bindRecommendFriendsViewModel(recommendFriendsViewModel: RecommendFriendsViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(ContractsFriendViewModel::class)
  abstract fun bindContractsFriendViewModel(contractsFriendViewModel: ContractsFriendViewModel): BaseViewModel

  //============================ 学校信息 ============================//
  @Binds
  @IntoMap
  @ViewModelKey(DoubleElectionsViewModel::class)
  abstract fun bindDoubleElectionsViewModel(doubleElectionsViewModel: DoubleElectionsViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(EditDoubleElectionViewModel::class)
  abstract fun editDoubleElectionViewModel(editDoubleElectionViewModel: EditDoubleElectionViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(SeminarsViewModel::class)
  abstract fun bindSeminarsViewModel(seminarsViewModel: SeminarsViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(EmploymentsViewModel::class)
  abstract fun bindEmploymentsViewModel(employmentsViewModel: EmploymentsViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(EditEmploymentViewModel::class)
  abstract fun bindEditEmploymentViewModel(editEmploymentViewModel: EditEmploymentViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(BindSchoolAttachInfoViewModel::class)
  abstract fun bindSchoolAttachInfoViewModel(bindSchoolAttachInfoViewModel: BindSchoolAttachInfoViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(SchoolInfoViewModel::class)
  abstract fun bindSchoolInfoViewModel(schoolInfoViewModel: SchoolInfoViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(CivilServiceInfoListViewModel::class)
  abstract fun bindCivilServiceInfoListViewModel(civilServiceInfoListViewModel: CivilServiceInfoListViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(SchoolContractInfoViewModel::class)
  abstract fun bindSchoolContractInfoViewModel(schoolContractInfoViewModel: SchoolContractInfoViewModel): BaseViewModel

  //============================ 双选会企业 ============================//
  @Binds
  @IntoMap
  @ViewModelKey(DoubleElectionCompanyViewModel::class)
  abstract fun bindDoubleElectionCompanyViewModel(doubleElectionCompanyViewModel: DoubleElectionCompanyViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(DoubleElectionCompanyDetailsViewModel::class)
  abstract fun bindDoubleElectionCompanyDetailsViewModel(doubleElectionCompanyDetailsViewModel: DoubleElectionCompanyDetailsViewModel): BaseViewModel

  //============================== 企业信息 ==============================//
  @Binds
  @IntoMap
  @ViewModelKey(EnterpriseJobFairViewModel::class)
  abstract fun bindEnterpriseSeminarsViewModel(enterpriseSeminarsViewModel: EnterpriseJobFairViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(EditCampusTalkViewModel::class)
  abstract fun bindEditSeminarViewModel(editSeminarViewModel: EditCampusTalkViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(CampusTalkListViewModel::class)
  abstract fun bindEnterpriseSeminarViewModel(campusTalkViewModel: CampusTalkListViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(EnterpriseDoubleElectionViewModel::class)
  abstract fun bindEnterpriseDoubleElectionViewModel(enterpriseDoubleElectionViewModel: EnterpriseDoubleElectionViewModel): BaseViewModel

  @Binds
  @IntoMap
  @ViewModelKey(EnterpriseVideoViewModel::class)
  abstract fun bindEnterpriseVideoViewModel(enterpriseVideoViewModel: EnterpriseVideoViewModel): BaseViewModel

  //============================== 事业单位 ==============================//
  @Binds
  @IntoMap
  @ViewModelKey(InstitutionsInfoViewModel::class)
  abstract fun bindInstitutionsInfoViewModel(institutionsInfoViewModel: InstitutionsInfoViewModel): BaseViewModel

  //============================ 切换账号 ============================//
  @Binds
  @IntoMap
  @ViewModelKey(SwitchAccountViewModel::class)
  abstract fun bindSwitchAccountViewModel(switchAccountViewModel: SwitchAccountViewModel): BaseViewModel
}