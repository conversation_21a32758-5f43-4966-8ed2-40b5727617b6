package com.bxkj.jrzp.user.api

import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.jrzp.user.data.*
import com.bxkj.jrzp.user.mine.data.ServiceItemData
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * @Description:
 * @author:45457
 * @date: 2020/7/31
 * @version: V1.0
 */
interface OpenUserApi {

  @POST("/user/GetMobileByUid/")
  suspend fun getUserBindPhoneNumber(@Body requestBody: ZPRequestBody): BaseResponse<String>

  @POST("/Company/UpdateCompanyWeixin/")
  suspend fun updateUserWechat(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

  @POST("Company/UpdateCompanyPhone1/")
  suspend fun updateUserContactPhone(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

  @POST("Company/GetCompanyWeixinShouji/")
  suspend fun getUserContactWay(@Body requestBody: ZPRequestBody): BaseResponse<UserContactWayBean>

  @POST("/Resume/GetResumeJzl")
  suspend fun getResumeScore(): BaseResponse<ResumeScoreData>

  @POST("/Comm/GetKefuInfoV2/")
  suspend fun getUserServicesInfo(): BaseResponse<List<ServiceItemData>>

  @POST("/Company/AddOrEditInfo/")
  suspend fun uploadCompanyLogo(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

  @POST(OpenUserApiConstants.I_GET_SERVICES_INFO)
  suspend fun getServicesInfo(): BaseResponse<ServiceItemData>

  @POST(OpenUserApiConstants.I_LIKE)
  suspend fun like(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>

  @POST(OpenUserApiConstants.I_FOLLOW)
  suspend fun follow(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>

  @POST(OpenUserApiConstants.I_CHECK_ENTERPRISE_AUTHENTICATION_HAS_COMPLETE)
  suspend fun checkEnterpriseAuthHasComplete(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>

  @POST(OpenUserApiConstants.I_DELETE_RELEASED_INFO)
  suspend fun deleteReleasedInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<String>

  @POST(OpenUserApiConstants.I_GET_TENCENT_IM_SIG_ID)
  suspend fun getTencentIMSigID(@Body encryptReqParams: EncryptReqParams): BaseResponse<String>

  @POST(OpenUserApiConstants.I_GET_ENTERPRISE_VIP_ACCOUNT_BALANCE)
  suspend fun getEnterpriseAccountBalance(@Body encryptReqParams: EncryptReqParams): BaseResponse<AccountVipData>

  @POST("/UserVip/GetVipLevel/")
  suspend fun getUserVipLevel(@Body encryptReqParams: EncryptReqParams): BaseResponse<UserVipLevelData>

  @POST("/ReleaseJob/GetListByPageOfShuangxuanhuiBaoming/")
  suspend fun getCompanySchoolJobList(@Body encryptReqParams: EncryptReqParams): BaseResponse<List<JobData>>

  @POST("/UserVip/GetYaoyueCount/")
  suspend fun getUserInviteBalance(@Body encryptReqParams: EncryptReqParams): BaseResponse<UserVipBalanceData>

  @POST("/UserVip/GetZhiliaoCount/")
  suspend fun getSayHelloBalance(@Body encryptReqParams: EncryptReqParams): BaseResponse<UserVipBalanceData>

  @POST("/Xuanjianghui/CanOrNotPublishMyXuanjianghui/")
  suspend fun releaseCampusTalkPreCheck(@Body encryptReqParams: EncryptReqParams): BaseResponse<CampusTalkCountData>
}