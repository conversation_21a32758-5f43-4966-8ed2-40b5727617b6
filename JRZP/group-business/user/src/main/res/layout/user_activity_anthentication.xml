<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <import type="com.bxkj.common.util.CheckUtils" />

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.bxkj.jrzp.user.ui.enterpriseauth.EnterpriseAuthViewModel" />
    </data>

    <LinearLayout style="@style/match_match"
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            style="@style/match_wrap"
            app:title="@string/certification_title" />

        <TextView
            android:id="@+id/tv_name_tips"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_30"
            android:background="@drawable/bg_f4f4f4"
            android:drawablePadding="@dimen/common_dp_5"
            android:drawableStart="@drawable/ic_drawable_start_tips"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_12"
            android:text="@string/certification_note"
            android:textColor="@color/common_ff4100" />

        <ScrollView style="@style/match_wrap">

            <LinearLayout
                style="@style/match_wrap"
                android:orientation="vertical">

                <LinearLayout style="@style/Layout.InfoItem">

                    <include layout="@layout/common_include_asterisk" />

                    <TextView
                        android:id="@+id/tv_name_text"
                        style="@style/Text.14sp.333333"
                        android:text="@string/certification_company_name" />

                    <TextView
                        android:id="@+id/tv_company_name"
                        style="@style/Text.InfoItem"
                        android:text="@{viewModel.companyName}" />
                </LinearLayout>

                <!--        <View style="@style/Line.Horizontal.Margin12OfStartAndEnd" />-->

                <!--        <LinearLayout style="@style/Layout.ItemInfo">-->

                <!--          <include layout="@layout/common_include_asterisk" />-->

                <!--          <TextView-->
                <!--            style="@style/Text.14sp.333333"-->
                <!--            android:text="@string/certification_corporate_name" />-->

                <!--          <com.bxkj.common.widget.MyEditText-->
                <!--            android:id="@+id/et_corporate"-->
                <!--            style="@style/EditText.Basic"-->
                <!--            android:drawablePadding="@dimen/common_dp_5"-->
                <!--            android:gravity="end|center_vertical"-->
                <!--            android:hint="@string/certification_corporate_hint"-->
                <!--            android:text="@={viewModel.certificationInfo.name}" />-->
                <!--        </LinearLayout>-->

                <View
                    style="@style/Line.Horizontal"
                    android:layout_height="@dimen/dp_8" />

                <LinearLayout style="@style/Layout.InfoItem">

                    <include layout="@layout/common_include_asterisk" />

                    <TextView
                        android:id="@+id/tv_license_text"
                        style="@style/Text.14sp.333333"
                        android:text="@string/certification_upload_title" />
                </LinearLayout>

                <FrameLayout
                    android:layout_width="@dimen/user_auth_pic_size"
                    android:layout_height="@dimen/user_auth_pic_size"
                    android:layout_marginStart="@dimen/dp_16">

                    <ImageView
                        android:id="@+id/iv_certification"
                        bind:imgUrl="@{viewModel.showImage}"
                        android:layout_width="86dp"
                        android:layout_height="86dp"
                        android:background="@drawable/ic_add_company_style_picture"
                        android:onClick="@{onClickListener}"
                        android:scaleType="center"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/iv_clear"
                        style="@style/wrap_wrap"
                        android:layout_gravity="end|top"
                        android:onClick="@{()->viewModel.clearSelectedImg()}"
                        android:src="@drawable/ic_delete_photo"
                        android:visibility="@{viewModel.showImage==null?View.GONE:View.VISIBLE}"
                        app:layout_constraintEnd_toEndOf="@id/iv_certification"
                        app:layout_constraintTop_toTopOf="parent" />

                </FrameLayout>

                <LinearLayout
                    style="@style/match_wrap"
                    android:orientation="vertical"
                    android:visibility="@{viewModel.isHrAuth()?View.VISIBLE:View.GONE}">

                    <View
                        style="@style/Line.Horizontal"
                        android:layout_marginEnd="@dimen/dp_14"
                        android:layout_marginStart="@dimen/dp_14"
                        android:layout_marginTop="@dimen/dp_16" />

                    <LinearLayout style="@style/Layout.InfoItem">

                        <include layout="@layout/common_include_asterisk" />

                        <TextView
                            style="@style/Text.14sp.333333"
                            android:text="@string/certification_hr_upload_title" />
                    </LinearLayout>

                    <FrameLayout
                        android:layout_width="@dimen/user_auth_pic_size"
                        android:layout_height="@dimen/user_auth_pic_size"
                        android:layout_marginStart="@dimen/dp_16">

                        <ImageView
                            android:id="@+id/iv_hr_pic"
                            bind:imgUrl="@{viewModel.showHrImg}"
                            android:layout_width="86dp"
                            android:layout_height="86dp"
                            android:background="@drawable/ic_add_company_style_picture"
                            android:onClick="@{onClickListener}"
                            android:scaleType="center"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:id="@+id/ic_hr_pic_remove"
                            style="@style/wrap_wrap"
                            android:layout_gravity="end|top"
                            android:onClick="@{()->viewModel.clearHrImg()}"
                            android:src="@drawable/ic_delete_photo"
                            android:visibility="@{viewModel.showHrImg==null?View.GONE:View.VISIBLE}"
                            app:layout_constraintEnd_toEndOf="@id/iv_certification"
                            app:layout_constraintTop_toTopOf="parent" />

                    </FrameLayout>

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_tips"
                    style="@style/Text.10sp"
                    android:layout_marginStart="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_16"
                    android:text="@string/certification_tips_prefix"
                    android:textColor="@color/common_b5b5b5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/iv_certification" />

                <TextView
                    style="@style/Text.10sp"
                    android:layout_marginStart="@dimen/dp_16"
                    android:text="@{viewModel.isHrAuth()?@string/certification_hr_upload_tips:@string/certification_upload_tips}"
                    android:textColor="@color/common_b5b5b5"
                    app:layout_constraintStart_toEndOf="@id/tv_tips"
                    app:layout_constraintTop_toTopOf="@id/tv_tips" />

                <TextView
                    android:id="@+id/tv_submit"
                    style="@style/Button.Basic"
                    android:layout_margin="@dimen/dp_30"
                    android:onClick="@{()->viewModel.submit()}"
                    android:text="@string/certification_submit_text" />
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</layout>