<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="com.bxkj.common.util.CheckUtils" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.user.ui.jobproveauth.JobProveAuthViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_match"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      style="@style/match_wrap"
      app:title="@string/user_job_prove_auth_page_title" />

    <androidx.core.widget.NestedScrollView
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1">

      <LinearLayout
        style="@style/match_wrap"
        android:orientation="vertical">

        <FrameLayout
          style="@style/match_wrap"
          android:background="@drawable/bg_f4f4f4">

          <ImageView
            android:id="@+id/iv_job_prove_pic"
            android:layout_width="match_parent"
            android:layout_height="@dimen/user_job_prove_pic_height"
            android:layout_margin="@dimen/dp_16"
            android:onClick="@{onClickListener}" />

        </FrameLayout>

        <TextView
          style="@style/Text.16sp.333333.Bold"
          android:layout_margin="@dimen/dp_16"
          android:text="@string/user_job_prove_auth_tips" />

        <ImageView
          android:layout_width="match_parent"
          android:layout_height="@dimen/user_job_prove_pic_height"
          android:layout_marginStart="@dimen/dp_16"
          android:layout_marginEnd="@dimen/dp_16"
          android:scaleType="fitXY"
          android:src="@drawable/user_img_job_prove_tips" />
      </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
      style="@style/Button.Basic.Round"
      android:layout_margin="@dimen/dp_30"
      android:enabled="@{!CheckUtils.isNullOrEmpty(viewModel.jobProvePic)}"
      android:onClick="@{()->viewModel.submit()}"
      android:text="@string/user_job_prove_auth_complete" />
  </LinearLayout>
</layout>