package com.bxkj.learning.data

import com.bxkj.common.util.kotlin.convertUrlToHttp
import com.google.gson.annotations.SerializedName

/**
 *
 * @author: YangXin
 * @date: 2021/4/23
 */
data class ServiceCommentData(
  val id: Int,
  val content: String,
  @SerializedName("logo")
  val userAvatar: String,
  @SerializedName("nicheng")
  val userNickName: String,
  val score: Int,
  val scoreName: String
) {
  fun getFixUserAvatar(): String {
    return userAvatar.convertUrlToHttp()
  }

  fun getScoreFloat(): Float {
    return score.toFloat()
  }
}