package com.bxkj.business.ui.businessintro

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.business.data.InfoTypeData
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.learning.repository.LearningRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: YangXin
 * @date: 2021/4/27
 */
class BusinessIntroViewModel @Inject constructor(
  private val learningRepository: LearningRepository
) : BaseViewModel() {

  val businessIntro = MutableLiveData<String>()

  private val _businessServiceTypeList = MutableLiveData<List<InfoTypeData>>()
  val businessServiceTypeList = _businessServiceTypeList

  fun start(businessId: Int, intro: String) {
    businessIntro.value = intro
    viewModelScope.launch {
      learningRepository.getBusinessServiceTypeList(businessId, 1000)
        .handleResult({
          _businessServiceTypeList.value = it
        }, {
        })
    }
  }
}