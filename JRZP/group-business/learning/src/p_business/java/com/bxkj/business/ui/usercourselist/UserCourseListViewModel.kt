package com.bxkj.business.ui.usercourselist

import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.network.handleResult
import com.bxkj.learning.repository.LearningRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

class UserCourseListViewModel @Inject constructor(
  private val learningRepository: LearningRepository
) : BaseViewModel() {

  val userCourseListViewModel = RefreshListViewModel()

  fun start(queryUserId: Int) {
    setupUserCourseListViewModel(queryUserId)
    userCourseListViewModel.refresh()
  }

  private fun setupUserCourseListViewModel(queryUserId: Int) {
    userCourseListViewModel.setOnLoadDataListener { currentPage ->
      viewModelScope.launch {
        learningRepository.getUserReleaseLearningInfoList(
          queryUserId,
          getSelfUserID(),
          1,
          currentPage,
          CommonApiConstants.DEFAULT_PAGE_SIZE
        ).handleResult({
          it?.map { item -> item.jinengList }?.let { convertCourseList ->
            userCourseListViewModel.autoAddAll(convertCourseList)
          }
        }, {
          if (it.isNoDataError) {
            userCourseListViewModel.noMoreData()
          } else {
            userCourseListViewModel.loadError()
          }
        })
      }
    }
  }

}