package com.bxkj.business.ui.skillcourse

import android.content.res.ColorStateList
import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.bxkj.business.ui.InfoSubTypeListAdapter
import com.bxkj.business.ui.CourseTypeListAdapter
import com.bxkj.business.ui.courseinfogroup.CourseInfoType
import com.bxkj.business.ui.courseinfogroup.LearningInfoChild
import com.bxkj.business.ui.industrialservicedetails.IndustrialServiceDetailsActivity
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBFragment
import com.bxkj.learning.data.CourseData
import com.bxkj.learning.data.IndustrialServicesData
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.kotlin.toPosition
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.learning.ui.web.LearningWebNavigation
import com.bxkj.learning.R
import com.bxkj.learning.databinding.LearningFragmentSkillCourseBinding
import com.google.android.material.shape.MaterialShapeDrawable

/**
 * 技能课程/专业用工/工业设计
 * @author: YangXin
 * @date: 2021/4/16
 */
class SkillCourseFragment :
  BaseDBFragment<LearningFragmentSkillCourseBinding, SkillCourseViewModel>(), LearningInfoChild {

  companion object {
    const val EXTRA_INFO_TYPE = "INFO_TYPE"

    fun newInstance(infoType: Int = CourseInfoType.TYPE_SKILL): Fragment {
      return SkillCourseFragment().apply {
        arguments = bundleOf(
          EXTRA_INFO_TYPE to infoType
        )
      }
    }
  }

  private var subTypeListAdapter: InfoSubTypeListAdapter? = null

  override fun getViewModelClass(): Class<SkillCourseViewModel> = SkillCourseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.learning_fragment_skill_course

  override fun initPage(view: View, saveInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    viewBinding.ivAllSubType.background = MaterialShapeDrawable().apply {
      fillColor = ColorStateList.valueOf(getResColor(R.color.common_white))
      elevation = dip(4).toFloat()
    }

    setupCourseTypeListAdapter()

    setupCourseSubTypeListAdapter()

    setupContentListAdapter()

    subscribeViewModelEvent()

    viewModel.start(getArgumentsInfoType())
  }

  private fun getArgumentsInfoType(): Int {
    return arguments?.getInt(EXTRA_INFO_TYPE).getOrDefault()
  }

  override fun startSearch(keyword: String) {
    viewModel.startSearch(keyword)
    viewBinding.includeCourseList.recyclerContent.toPosition(0)
  }

  private fun subscribeViewModelEvent() {
    viewModel.showAllSubTypeEvent.observe(this, {
      SkillCourseSubTypeDialog(it, subTypeListAdapter?.getSelectedIndex().getOrDefault()).apply {
        setOnOptionClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            dismiss()
            <EMAIL>(position)
            subTypeListAdapter?.setSelectedIndex(position)
            <EMAIL>(position)
          }
        })
      }.show(
        childFragmentManager
      )
    })

    viewModel.hideSoftKeyboardEvent.observe(this, {
      viewBinding.includeCourseList.recyclerContent.toPosition(0)
      SystemUtil.hideSoftKeyboard(parentActivity)
    })

    viewModel.subTypeList.observe(this, {
      subTypeListAdapter?.setSelectedIndex(0)
    })
  }

  private fun setupCourseTypeListAdapter() {
    viewBinding.recyclerCourseType.layoutManager = LinearLayoutManager(parentActivity)
    viewBinding.recyclerCourseType.adapter = CourseTypeListAdapter().apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          getData()?.let {
            viewModel.changeType(it[position].id)
          }
        }
      })
    }
  }

  private fun setupCourseSubTypeListAdapter() {
    viewBinding.recyclerCourseSubType.layoutManager =
      LinearLayoutManager(parentActivity, LinearLayoutManager.HORIZONTAL, false)
    subTypeListAdapter = InfoSubTypeListAdapter().apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          getData()?.let {
            viewModel.changeSubType(it[position].id)
          }
        }
      })
    }
    viewBinding.recyclerCourseSubType.adapter = subTypeListAdapter
    viewBinding.recyclerCourseSubType.addItemDecoration(
      LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_4))
        .orientation(LinearLayoutManager.HORIZONTAL).build()
    )
  }

  private fun setupContentListAdapter() {
    when (getArgumentsInfoType()) {
      CourseInfoType.TYPE_SKILL -> {
        setupSkillCourseListAdapter()
      }
      else -> {
        setupProEmploymentListAdapter()
      }
    }
  }

  private fun setupProEmploymentListAdapter() {
    val proEmploymentListAdapter = SimpleDBListAdapter<IndustrialServicesData>(
      parentActivity,
      R.layout.learning_recycler_pro_employment_item
    ).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          val item = data[position]
          startActivity(
            IndustrialServiceDetailsActivity.newIntent(
              parentActivity,
              item.productId,
              item.encryptBusinessId,
              item.encryptProductId
            )
          )
        }
      })
    }
    viewBinding.includeCourseList.recyclerContent.let {
      it.layoutManager = LinearLayoutManager(parentActivity)
      it.addItemDecoration(
        LineItemDecoration.Builder()
          .divider(getResDrawable(R.drawable.divider_f4f4f4))
          .marginStart(dip(12)).build()
      )
    }
    viewModel.courseListViewModel.setAdapter(proEmploymentListAdapter)
  }

  private fun setupSkillCourseListAdapter() {
    val skillCourseListAdapter = SimpleDBListAdapter<CourseData>(
      parentActivity,
      R.layout.learning_recycler_skill_course_item
    ).apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          afterLogin {
            LearningWebNavigation.navigate(
              LearningWebNavigation.PAGE_TYPE_COURSE,
              data[position].id,
              localUserId
            ).start()
          }
        }
      })
    }
    viewBinding.includeCourseList.recyclerContent.layoutManager =
      LinearLayoutManager(parentActivity)
    viewModel.courseListViewModel.setAdapter(skillCourseListAdapter)
  }

}