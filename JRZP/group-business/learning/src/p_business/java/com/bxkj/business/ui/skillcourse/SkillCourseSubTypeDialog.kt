package com.bxkj.business.ui.skillcourse

import android.graphics.Color
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.business.data.InfoTypeData
import com.bxkj.business.ui.InfoSubTypeListAdapter
import com.bxkj.learning.R
import com.bxkj.learning.databinding.LearningDialogCourseSubTypeBinding
import com.google.android.flexbox.FlexboxItemDecoration
import com.google.android.flexbox.FlexboxLayoutManager

/**
 *
 * @author: YangXin
 * @date: 2021/4/19
 */
class SkillCourseSubTypeDialog constructor(
  private val subTypes: List<InfoTypeData>,
  private val selectedIndex: Int = 0,
  private var title: String = ""
) : BaseDBDialogFragment<LearningDialogCourseSubTypeBinding, BaseViewModel>() {

  private var optionListAdapter: InfoSubTypeListAdapter? = null

  private var mSuperOptionItemClickListener: SuperItemClickListener? = null

  override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.learning_dialog_course_sub_type

  override fun initPage() {
    view?.findViewById<ImageView>(R.id.iv_close)?.setOnClickListener {
      dismiss()
    }
    setupSubTypeListAdapter()
    if (title.isNotBlank()) {
      viewBinding.tvTitle.text = title
    }
  }

  override fun enableBottomSheet(): Boolean {
    return true
  }

  override fun onStart() {
    super.onStart()
    dialog?.window?.let {
      it.setWindowAnimations(R.style.BottomPopupAnim)
      it.findViewById<ViewGroup>(R.id.design_bottom_sheet)?.let { layout ->
        layout.setBackgroundColor(Color.TRANSPARENT)
        val layoutParams = layout.layoutParams
        layoutParams.height = resources.displayMetrics.heightPixels
      }
    }
  }

  private fun setupSubTypeListAdapter() {
    optionListAdapter = InfoSubTypeListAdapter().apply {
      mSuperOptionItemClickListener?.let {
        setOnItemClickListener(it)
      }
    }
    view?.findViewById<RecyclerView>(R.id.recycler_course_sub_type)?.let {
      it.layoutManager = FlexboxLayoutManager(context)
      it.adapter = optionListAdapter?.apply {
        submitList(subTypes)
        setSelectedIndex(selectedIndex)
      }
      it.addItemDecoration(FlexboxItemDecoration(context).apply {
        setDrawable(getResDrawable(R.drawable.divider_8))
      })
    }
  }

  fun setOnOptionClickListener(superItemClickListener: SuperItemClickListener) {
    mSuperOptionItemClickListener = superItemClickListener
  }

}