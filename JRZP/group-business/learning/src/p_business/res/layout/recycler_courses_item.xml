<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.learning.data.CourseData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_wrap"
    android:paddingTop="@dimen/dp_16">

    <ImageView
      android:id="@+id/iv_logo"
      android:layout_width="90dp"
      android:layout_height="90dp"
      android:layout_marginStart="@dimen/dp_16"
      android:background="@drawable/frame_f4f4f4_radius_4"
      android:padding="@dimen/dp_1"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      bind:loadRadiusImg="@{data.photourl}" />

    <TextView
      android:id="@+id/tv_title"
      style="@style/Text.16sp.333333.Bold"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_8"
      android:ellipsize="end"
      android:maxLines="2"
      android:text="@{data.name}"
      app:layout_constraintEnd_toStartOf="@id/iv_offline_tag"
      app:layout_constraintStart_toEndOf="@id/iv_logo"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_goneMarginEnd="@dimen/dp_12" />

    <ImageView
      android:id="@+id/iv_offline_tag"
      style="@style/wrap_wrap"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginEnd="@dimen/dp_8"
      android:src="@drawable/learning_ic_course_offline_tag"
      android:visibility="@{data.offline()?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="@id/tv_title"
      app:layout_constraintEnd_toStartOf="@id/tv_salary"
      app:layout_constraintStart_toEndOf="@id/tv_title"
      app:layout_constraintTop_toTopOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_salary"
      style="@style/Text.14sp.FE6600.Bold"
      android:layout_marginEnd="@dimen/dp_16"
      android:text="@{data.formatPriceText()}"
      app:layout_constraintBottom_toBottomOf="@id/tv_title"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_comments"
      style="@style/Text.14sp.888888"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_4"
      android:layout_marginEnd="@dimen/dp_16"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{@string/home_recommend_course_about_format(data.buyCount,data.haopinglv)}"
      android:visibility="@{data.auditApproved()?View.VISIBLE:View.GONE}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_title"
      app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <TextView
      android:id="@+id/tv_audit_state"
      style="@style/Text.14sp.FE6600"
      android:layout_marginTop="@dimen/dp_4"
      android:layout_marginEnd="@dimen/dp_16"
      android:drawablePadding="@dimen/dp_2"
      android:text="@string/learning_course_auditing"
      android:visibility="@{data.auditApproved()?View.GONE:View.VISIBLE}"
      app:drawableStartCompat="@drawable/learning_ic_course_auditing_tag"
      app:layout_constraintStart_toStartOf="@id/tv_title"
      app:layout_constraintTop_toBottomOf="@id/tv_title" />


    <ImageView
      android:id="@+id/iv_self_product"
      android:layout_width="80dp"
      android:layout_height="@dimen/dp_16"
      android:layout_marginStart="@dimen/dp_8"
      android:layout_marginBottom="@dimen/dp_4"
      android:src="@drawable/learning_ic_course_self_tag"
      app:layout_constraintBottom_toBottomOf="@id/iv_logo"
      app:layout_constraintStart_toEndOf="@id/iv_logo" />

    <TextView
      android:id="@+id/tv_discount"
      style="@style/Text.10sp.FE6600"
      android:layout_marginStart="@dimen/dp_8"
      android:background="@drawable/frame_fe6600"
      android:paddingStart="@dimen/dp_4"
      android:paddingTop="@dimen/dp_1"
      android:paddingEnd="@dimen/dp_4"
      android:paddingBottom="@dimen/dp_1"
      android:text="@{@string/learning_course_list_discount_format(data.maxPintuanChajia)}"
      android:visibility="@{data.hasDiscount()?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="@id/iv_self_product"
      app:layout_constraintStart_toEndOf="@id/iv_self_product"
      app:layout_constraintTop_toTopOf="@id/iv_self_product"
      app:layout_goneMarginStart="@dimen/dp_8" />

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/cl_tag"
      android:layout_width="@dimen/dp_0"
      android:layout_height="wrap_content"
      android:layout_marginTop="@dimen/dp_14"
      android:layout_marginEnd="@dimen/dp_12"
      app:layout_constraintEnd_toStartOf="@id/tv_share"
      app:layout_constraintStart_toStartOf="@id/iv_logo"
      app:layout_constraintTop_toBottomOf="@id/iv_logo">

      <TextView
        android:id="@+id/tv_expect_one"
        style="@style/Text.12sp.333333"
        android:background="@drawable/bg_eaeaea_round"
        android:ellipsize="end"
        android:lines="1"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.infoTags.size()&gt;0?data.infoTags[0]:@string/home_recommend_courses}"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_expect_two"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

      <TextView
        android:id="@+id/tv_expect_two"
        style="@style/Text.12sp.333333"
        android:layout_marginStart="@dimen/dp_8"
        android:background="@drawable/bg_eaeaea_round"
        android:ellipsize="end"
        android:lines="1"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.infoTags[1]}"
        android:visibility="@{data.infoTags.size()&gt;1?View.VISIBLE:View.GONE}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_expect_three"
        app:layout_constraintStart_toEndOf="@id/tv_expect_one"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_min="@dimen/dp_30" />

      <TextView
        android:id="@+id/tv_expect_three"
        style="@style/Text.12sp.333333"
        android:layout_marginStart="@dimen/dp_8"
        android:background="@drawable/bg_eaeaea_round"
        android:ellipsize="end"
        android:lines="1"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_2"
        android:text="@{data.infoTags[2]}"
        android:visibility="@{data.infoTags.size()&gt;2?View.VISIBLE:View.GONE}"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_expect_two"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_min="@dimen/dp_30" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
      android:id="@+id/tv_share"
      style="@style/Text.12sp.FF7647"
      android:layout_marginEnd="@dimen/dp_6"
      android:background="@drawable/bg_ffd9c0_round"
      android:paddingStart="@dimen/dp_16"
      android:paddingTop="@dimen/dp_4"
      android:paddingEnd="@dimen/dp_16"
      android:paddingBottom="@dimen/dp_4"
      android:text="@string/home_recommend_share"
      android:visibility="@{data.hasCommission()?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="@id/cl_tag"
      app:layout_constraintEnd_toStartOf="@id/tv_sign_up"
      app:layout_constraintTop_toTopOf="@id/cl_tag" />

    <TextView
      android:id="@+id/tv_sign_up"
      style="@style/Text.12sp.FFFFFF"
      android:layout_marginEnd="@dimen/dp_16"
      android:background="@drawable/bg_fe6600_round"
      android:paddingStart="@dimen/dp_16"
      android:paddingTop="@dimen/dp_4"
      android:paddingEnd="@dimen/dp_16"
      android:paddingBottom="@dimen/dp_4"
      android:text="@string/home_recommend_sign_up"
      app:layout_constraintBottom_toBottomOf="@id/cl_tag"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/cl_tag" />

    <View
      style="@style/Line.Horizontal.Margin12OfStartAndEnd"
      android:layout_marginTop="@dimen/dp_14"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/tv_sign_up" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>