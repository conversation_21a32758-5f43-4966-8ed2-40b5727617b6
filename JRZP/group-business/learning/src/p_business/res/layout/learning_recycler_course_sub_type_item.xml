<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="data"
      type="com.bxkj.business.data.InfoTypeData" />
  </data>

  <TextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tv_sub_type"
    style="@style/Text.12sp"
    android:background="@drawable/learning_bg_course_sub_type_selector"
    android:gravity="center"
    android:paddingStart="@dimen/dp_8"
    android:paddingTop="@dimen/dp_6"
    android:paddingEnd="@dimen/dp_8"
    android:paddingBottom="@dimen/dp_6"
    android:text="@{data.name}"
    android:textColor="@color/common_333333_to_ff7647_selector" />
</layout>