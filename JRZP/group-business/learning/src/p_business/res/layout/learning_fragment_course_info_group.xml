<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_52"
      android:gravity="center_vertical"
      android:paddingStart="@dimen/dp_14"
      android:paddingEnd="@dimen/dp_14">

      <com.bxkj.common.widget.MyEditText
        android:id="@+id/et_search_content"
        style="@style/Text.14sp.999999"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_36"
        android:layout_marginEnd="@dimen/dp_18"
        android:layout_weight="1"
        android:background="@drawable/bg_f4f4f4_radius_4"
        android:drawableStart="@drawable/ic_search"
        android:drawablePadding="@dimen/dp_8"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:imeOptions="actionSearch"
        android:lines="1"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12" />

      <TextView
        android:id="@+id/tv_switch_show_type"
        style="@style/Text.10sp.FE6600"
        android:gravity="center"
        android:onClick="@{onClickListener}" />

    </LinearLayout>

    <FrameLayout
      android:id="@+id/fl_content"
      style="@style/match_match" />

  </LinearLayout>
</layout>