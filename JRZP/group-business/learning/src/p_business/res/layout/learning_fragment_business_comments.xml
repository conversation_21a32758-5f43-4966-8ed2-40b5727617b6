<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.business.ui.businesscomments.BusinessCommentsViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_match"
        android:orientation="vertical">

        <LinearLayout
            style="@style/wrap_wrap"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp_14">

            <TextView
                style="@style/Text.16sp.333333.Bold"
                android:text="@string/learning_service_provider_comment" />

            <TextView
                style="@style/Text.16sp.888888"
                android:layout_marginStart="@dimen/dp_4"
                android:text="@{@string/learning_service_provider_comment_count_format(viewModel.commentCount)}" />

        </LinearLayout>

        <include
            android:id="@+id/include_business_comments"
            layout="@layout/include_mvvm_refresh_layout"
            app:listViewModel="@{viewModel.commentListViewModel}" />

    </LinearLayout>
</layout>