package com.bxkj.jrzp.live.videocall.ui.videocall

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.live.LiveConstants
import org.jsoup.select.Evaluator.IsNthChild

/**
 *
 * @author: YangXin
 * @date: 2021/3/11
 */
class VideoCallNavigation {

  companion object {

    const val PATH = "${LiveConstants.DIRECTORY}/videocalling"

    const val TYPE_BEING_CALLED = 1
    const val TYPE_CALL = 2
    const val EXTRA_CALL_TYPE = "CALL_TYPE"
    const val EXTRA_CALL_USER_INFO = "CALL_USER_INFO"
    const val EXTRA_SELF_USER_ID = "SELF_USER_INFO"
    const val EXTRA_MAX_TIME = "MAX_TIME"

    @JvmOverloads
    fun create(
      callType: Int,
      selfUserId: Int,
      callUserInfo: VideoCallUserInfo,
      maxTime: Long = 0 //Second
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_CALL_TYPE, callType)
        .withInt(EXTRA_SELF_USER_ID, selfUserId)
        .withParcelable(EXTRA_CALL_USER_INFO, callUserInfo)
        .withLong(EXTRA_MAX_TIME, maxTime)
    }
  }
}