package com.bxkj.jrzp.live.audience.ui.audience

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.bxkj.common.adapter.BaseFragmentStateAdapter
import com.bxkj.jrzp.live.audience.ui.player.LivePlayerFragment
import com.bxkj.jrzp.live.room.data.LiveRoomData

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/16
 * @version: V1.0
 */
class AudiencePlayerListAdapter(activity: FragmentActivity, list: List<LiveRoomData>) :
    BaseFragmentStateAdapter<LiveRoomData>(activity, list) {

    override fun createFragment(position: Int): Fragment {
        return LivePlayerFragment.newInstance(getData()[position].id, getData()[position].roomName)
    }
}