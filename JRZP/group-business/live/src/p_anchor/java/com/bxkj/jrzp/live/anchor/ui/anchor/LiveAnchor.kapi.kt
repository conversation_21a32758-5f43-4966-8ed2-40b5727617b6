@file:JvmName("LiveAnchor")

package com.bxkj.jrzp.live.anchor.ui.anchor

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.jrzp.live.LiveConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/12
 * @version: V1.0
 */
const val LIVE_ANCHOR_PATH = "${LiveConstants.DIRECTORY}/anchor"

const val EXTRA_FRONT_CAMERA = "FRONT_CAMERA"
const val EXTRA_LIVE_ID = "LIVE_ID"
const val EXTRA_ROOM_ID = "ROOM_ID"

const val EXTRA_ONLY_CREATE_ROOM = "ONLY_CREATE_ROOM"

fun navigateToLiveAnchor(
    frontCamera: Boolean,
    liveID: Int,
    roomID: String? = "",
    onlyCreateRoom: Boolean = false
): RouterNavigator {
    return Router.getInstance().to(LIVE_ANCHOR_PATH)
        .withBoolean(EXTRA_FRONT_CAMERA, frontCamera)
        .withInt(EXTRA_LIVE_ID, liveID)
        .withString(EXTRA_ROOM_ID, roomID)
        .withBoolean(EXTRA_ONLY_CREATE_ROOM, onlyCreateRoom)
}
