<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="com.bxkj.jrzp.live">

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.RECORD_AUDIO" />
  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
  <uses-permission android:name="android.permission.BLUETOOTH" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />

  <uses-feature android:name="android.hardware.Camera" />
  <uses-feature android:name="android.hardware.camera.autofocus" />

  <application
    android:allowBackup="false"
    tools:replace="android:allowBackup">
    <activity
      android:name=".anchor.ui.anchor.LiveAnchorActivity"
      android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
      android:screenOrientation="portrait"
      android:theme="@style/Live_AnchorActivityTheme" />

    <activity
      android:name=".anchor.ui.livestatistics.LiveStatisticsActivity"
      android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout"
      android:screenOrientation="portrait" />

  </application>

</manifest>