package com.bxkj.jrzp.live.data

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.recyclerview.widget.DiffUtil
import com.bxkj.share.BR
import com.tencent.qcloud.ugckit.roomservice.commondef.AudienceInfo

/**
 * @Description:
 * @author:45457
 * @date: 2020/11/5
 * @version: V1.0
 */
class LiveAudienceInfo private constructor(
  val userID: Int,
  val userName: String,
  val userAvatar: String,
  @get:Bindable
  var followed: Boolean
) : BaseObservable() {

  fun setupFollowStatus(status: Boolean) {
    followed = status
    notifyPropertyChanged(BR.followed)
  }

  class DiffCallback : DiffUtil.ItemCallback<LiveAudienceInfo>() {
    override fun areItemsTheSame(oldItem: LiveAudienceInfo, newItem: LiveAudienceInfo): Boolean {
      return oldItem.userID == newItem.userID
    }

    override fun areContentsTheSame(oldItem: LiveAudienceInfo, newItem: LiveAudienceInfo): Boolean {
      return oldItem.userID == newItem.userID && oldItem.userName == newItem.userName && oldItem.userAvatar == newItem.userAvatar
    }

  }

  companion object {

    fun wrap(audience: AudienceInfo): LiveAudienceInfo {
      return LiveAudienceInfo(
        audience.userID.toInt(),
        audience.userName,
        audience.userAvatar,
        false
      )
    }
  }
}