package com.bxkj.jrzp.live.dialog.web

import android.content.Intent
import android.view.View
import android.view.ViewGroup
import android.webkit.WebChromeClient
import android.webkit.WebView
import androidx.core.os.bundleOf
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation.LoginActivity
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.util.web.BaseJSInterface.OnJSCallListener
import com.bxkj.common.util.web.CommonJSInterface
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.jrzp.live.R
import com.bxkj.jrzp.live.databinding.LiveDialogWebViewBinding
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.google.gson.JsonObject

/**
 * @Description: WebViewDialog
 * @author:45457
 * @date: 2020/11/11
 * @version: V1.0
 */
class LiveWebViewDialog : BaseDBDialogFragment<LiveDialogWebViewBinding, BaseViewModel>() {

  companion object {

    const val TO_USER_HOME_CODE = 1

    const val EXTRA_URL = "URL"

    fun newInstance(url: String): LiveWebViewDialog {
      return LiveWebViewDialog().apply {
        arguments = bundleOf(EXTRA_URL to url)
      }
    }
  }

  override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.live_dialog_web_view

  override fun enableBottomSheet(): Boolean {
    return true
  }

  override fun getTheme(): Int {
    return R.style.BaseDialogFragmentStyle_NoDim
  }

  override fun onStart() {
    super.onStart()
    dialog?.window?.let {
      it.setWindowAnimations(R.style.BottomPopupAnim)
      it.findViewById<ViewGroup>(R.id.design_bottom_sheet)?.let { layout ->
        val layoutParams = layout.layoutParams
        layoutParams.height = getLayoutHeight()
      }
    }
  }

  private fun getLayoutHeight(): Int {
    val peekHeight = resources.displayMetrics.heightPixels
    //设置弹窗高度为屏幕高度的3/4
    return peekHeight - peekHeight / 3
  }

  override fun initPage() {

    subscribeRxBusMsg()

    viewBinding.webView.webChromeClient = object : WebChromeClient() {
      override fun onProgressChanged(view: WebView, newProgress: Int) {
        if (newProgress == 100) {
          viewBinding.progress.visibility = View.GONE
        } else {
          viewBinding.progress.visibility = View.VISIBLE
          viewBinding.progress.progress = newProgress
        }
      }

      override fun onReceivedTitle(view: WebView?, title: String?) {
//        if (intent.getStringExtra(WebNavigation.EXTRA_PAGE_TITLE).isNullOrEmpty()) {
//          title?.let {
//            dataBinding.titleBar.setTitle(it)
//          }
//        }
      }
    }

    viewBinding.webView.addJavascriptInterface(CommonJSInterface(object : OnJSCallListener {
      override fun onJSCall(method: String?, params: JsonObject?) {
        handJSFunction(method, params)
      }
    }), AppConstants.JS_BRIDGE_NAME)

    arguments?.let {
      val url = it.getString(EXTRA_URL).getOrDefault()
      viewBinding.webView.loadUrl(url)
    } ?: showToast("加载失败，请重试")
  }

  private fun subscribeRxBusMsg() {
    addDisposable(
      RxBus.get().toObservable(RxBus.Message::class.java)
        .subscribe { it ->
          if (it.code == RxMsgCode.ACTION_LOGIN_SUCCESS) {
            viewBinding.webView.reload()
          }
        }
    )
  }

  private fun handJSFunction(method: String?, params: JsonObject?) {
    if (method != null) {
      when (method) {
        "login" -> {  //登录
          Router.getInstance().to(LoginActivity.URL)
            .startForResult(activity, 1)
        }
        "toUserHome" -> {
          params?.let {
            val userID = it.get("userID").asInt
            UserHomeNavigation.navigate(userID)
              .startForResult(this, TO_USER_HOME_CODE)
          }
        }
      }
    }
  }

  override fun onPause() {
    super.onPause()
    viewBinding.webView.onPause()
  }

  override fun onResume() {
    super.onResume()
    viewBinding.webView.onResume()
  }

  override fun onDestroyView() {
    super.onDestroyView()
    viewBinding.webView.destroy()
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == TO_USER_HOME_CODE) {
      if (resultCode == UserHomeNavigation.RESULT_FOLLOW_STATUS_CHANGE && data != null) {
        val userID = data.getIntExtra(UserHomeNavigation.EXTRA_QUERY_USER_ID, 0)
        val followStatus = data.getBooleanExtra(UserHomeNavigation.EXTRA_FOLLOW_STATUS, false)
        viewBinding.webView.execJS("javascript:Update(${userID},${(if (followStatus) 1 else 0)})", null)
      }
    }
  }
}