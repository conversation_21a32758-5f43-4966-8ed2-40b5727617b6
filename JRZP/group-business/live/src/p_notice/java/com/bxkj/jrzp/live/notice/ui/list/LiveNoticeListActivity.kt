package com.bxkj.jrzp.live.notice.ui.list

import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.therouter.router.Route
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.jrzp.live.BR
import com.bxkj.jrzp.live.LiveConstants
import com.bxkj.jrzp.live.R
import com.bxkj.jrzp.live.anchor.ui.anchor.navigateToLiveAnchor
import com.bxkj.jrzp.live.databinding.LiveActivityNoticeListBinding
import com.bxkj.jrzp.live.room.data.PendingLiveData
import com.bxkj.jrzp.live.room.ui.livenotice.LiveNoticeNavigation
import com.bxkj.jrzp.support.feature.ui.infocheck.CheckInfoCallBackAdapter
import com.bxkj.jrzp.support.feature.ui.infocheck.InfoCompletedCheck
import com.bxkj.jrzp.support.feature.data.CheckInfoTag
import com.bxkj.jrzp.support.feature.data.CheckResultMsg
import com.bxkj.jrzp.support.feature.data.InfoCheckItem

/**
 * @Description: 直播预告列表
 * @author:45457
 * @date: 2020/10/28
 * @version: V1.0
 */
@Route(path = LiveNoticeListNavigation.PATH)
class LiveNoticeListActivity :
  BaseDBActivity<LiveActivityNoticeListBinding, LiveNoticeListViewModel>(), OnClickListener {

  override fun getViewModelClass(): Class<LiveNoticeListViewModel> =
    LiveNoticeListViewModel::class.java

  override fun getLayoutId(): Int = R.layout.live_activity_notice_list

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    subscribeViewModelEvent()

    setupLiveNoticeList()

  }

  override fun onResume() {
    super.onResume()
    viewModel.start()
  }

  override fun onClick(v: View?) {
    if (v != null) {
      if (v.id == R.id.tv_add_live_notice) {
        InfoCompletedCheck
          .with(this)
          .checkItem(
            InfoCheckItem.Builder()
              .checkInfoTag(CheckInfoTag.CHECK_PERSONAL_AUTH)
              .onlyCheck(false)
              .build()
          ).setCheckInfoCallBack(object :
            CheckInfoCallBackAdapter() {
            override fun onAllCheckSuccess(result: CheckResultMsg) {
              LiveNoticeNavigation.navigate().start()
            }
          }).start()
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.toLiveAnchorPageCommand.observe(this, Observer {
      navigateToLiveAnchor(true, it, LiveConstants.getLiveID()).start()
    })
  }

  private fun setupLiveNoticeList() {
    val liveNoticeListAdapter =
      SimpleDBListAdapter<PendingLiveData>(
        this,
        R.layout.live_recycler_notice_item,
        BR.data
      ).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            val clickItem = data[position]
            when (v.id) {
              R.id.tv_start_now -> {
                viewModel.startLive(clickItem)
              }
              R.id.tv_edit -> {
                LiveNoticeNavigation.navigate(clickItem.id).start()
              }
              R.id.tv_delete -> {
                ActionDialog.Builder()
                  .setTitle(getString(R.string.common_tips))
                  .setContent(getString(R.string.live_notice_delete_tips))
                  .setOnConfirmClickListener {
                    viewModel.deleteLiveNotice(clickItem)
                  }.build().show(supportFragmentManager)
              }
            }
          }
        }, R.id.tv_start_now, R.id.tv_edit, R.id.tv_delete)
      }

    viewBinding.includeLiveNotice.recyclerContent.layoutManager = LinearLayoutManager(this)

    viewModel.liveNoticeListViewModel.setAdapter(liveNoticeListAdapter)
  }
}