package com.bxkj.jrzp.live.room.repository

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.jrzp.live.room.api.LiveRoomApi
import com.bxkj.jrzp.live.room.data.PendingLiveData
import com.bxkj.jrzp.live.room.data.CreateRoomParams
import com.bxkj.jrzp.live.room.data.LiveRoomDetailsData
import com.bxkj.jrzp.live.room.data.LiveRoomTypeData
import javax.inject.Inject

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/15
 * @version: V1.0
 */
class LiveRoomRepository @Inject constructor(
  private val mLiveRoomApi: LiveRoomApi
) : BaseRepo() {

  /**
   * 根据直播id开启直播
   */
  suspend fun startLiveByLiveID(
    userID: Int,
    liveID: Int,
    roomID: String
  ): ReqResponse<Nothing> {
    return httpRequest {
      mLiveRoomApi.startLiveByLiveID(
        ZPRequestBody().apply {
          put("uid", userID)
          put("id", liveID)
          put("roomName", roomID)
          put("streamName", roomID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 添加直播信息
   */
  suspend fun startLive(userID: Int, createRoomInfo: CreateRoomParams): ReqResponse<Int> {
    return httpRequest {
      mLiveRoomApi.startLive(
        ZPRequestBody().apply {
          put("uid", userID)
          put("type", createRoomInfo.liveType)
          put("title", createRoomInfo.title)
          put("pic", createRoomInfo.frontCover ?: "")
          put("picId", createRoomInfo.frontCoverID ?: "")
          put("roomName", createRoomInfo.roomID)
          put("streamName", createRoomInfo.roomID)
          put("state", 2) //0、预告  1、离线  2、直播中
          put("glid1", createRoomInfo.relateInfoIDs) //0、预告  1、离线  2、直播中
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 检查是否存在预约直播
   */
  suspend fun checkHasPendingLive(userID: Int): ReqResponse<List<PendingLiveData>> {
    return httpRequest {
      mLiveRoomApi.checkHasPendingLive(
        ZPRequestBody().apply {
          put("uid", userID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 停止直播
   */
  suspend fun stopLive(
    userID: Int,
    liveID: Int,
    liveCount: Int,
    audienceCount: Int
  ): ReqResponse<Nothing> {
    return httpRequest {
      mLiveRoomApi.stopLive(
        ZPRequestBody().apply {
          put("uid", userID)
          put("id", liveID)
          put("zanCount", liveCount)
          put("showCount", audienceCount)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取直播房间信息
   */
  suspend fun getLiveRoomInfo(userID: Int, liveID: Int): ReqResponse<LiveRoomDetailsData> {
    return httpRequest {
      mLiveRoomApi.getLiveRoomInfo(
        ZPRequestBody().apply {
          put("curUid", userID)
          put("id", liveID)
        }.paramsEncrypt()
      )
    }
  }

  /**
   * 获取开直播分类
   */
  suspend fun getStartLiveType(userID: Int): ReqResponse<List<LiveRoomTypeData>> {
    return httpRequest {
      mLiveRoomApi.getStartLiveType(
        ZPRequestBody().apply {
          put("uid", userID)
        }.paramsEncrypt()
      )
    }
  }

}