package com.bxkj.jrzp.support.comment.ui.replay

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.therouter.router.Route
import com.bxkj.common.adapter.OnItemLongClickListener
import com.bxkj.common.adapter.multitypeadapter.DefaultViewBinder
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.qmui.QMUIKeyboardHelper
import com.bxkj.common.util.qmui.QMUIKeyboardHelper.KeyboardVisibilityEventListener
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.InputDialog
import com.bxkj.common.widget.popup.menupopup.MenuPopup
import com.bxkj.jrzp.support.comment.R
import com.bxkj.jrzp.support.comment.data.CommentItemData
import com.bxkj.jrzp.support.comment.databinding.CommentActivityCommentReplyBinding
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.ui.activity.uploadavatar.UploadAvatarNavigation

/**
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.commentreply
 * @Description: 评论回复
 * <AUTHOR>
 * @date 2019/10/23
 * @version V1.0
 */
@Route(path = CommentReplayNavigation.PATH)
class CommentReplyActivity :
  BaseDBActivity<CommentActivityCommentReplyBinding, CommentReplyViewModel>() {
  companion object {

    fun newIntent(
      context: Context,
      commentType: Int,
      newsId: Int,
      parentPosition: Int,
      parentComment: CommentItemData
    ): Intent {
      val intent = Intent(context, CommentReplyActivity::class.java)
      intent.putExtra(CommentReplayNavigation.EXTRA_COMMENT_TYPE, commentType)
      intent.putExtra(CommentReplayNavigation.EXTRA_NEWS_ID, newsId)
      intent.putExtra(CommentReplayNavigation.EXTRA_PARENT_POSITION, parentPosition)
      intent.putExtra(CommentReplayNavigation.EXTRA_PARENT_COMMENT, parentComment)
      return intent
    }
  }

  private var mCommentDialog: InputDialog? = null
  private var mCommentIsDeleted = false

  override fun getViewModelClass(): Class<CommentReplyViewModel> =
    CommentReplyViewModel::class.java

  override fun getLayoutId(): Int = R.layout.comment_activity_comment_reply

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel

    setupListAdapter()
    subscribeViewModelEvent()
    listenerKeyboardVisibility()
    viewModel.start(intent)
  }

  private fun listenerKeyboardVisibility() {
    QMUIKeyboardHelper.setVisibilityEventListener(this, object : KeyboardVisibilityEventListener {
      override fun onVisibilityChanged(isOpen: Boolean, heightDiff: Int): Boolean {
        if (!isOpen) {
          hideReplyDialog()
        }
        return false
      }
    })
  }

  private fun hideReplyDialog() {
    mCommentDialog?.let {
      if (it.isShowing) {
        it.dismiss()
      }
    }
  }

  private fun subscribeViewModelEvent() {
    viewModel.replyParentCommentEvent.observe(this, Observer { parentComment ->
      if (checkToLogin()) {
        viewModel.commentPreCheck {
          showReplyDialog(parentComment)
        }
      }
    })

    viewModel.addReplySuccessEvent.observe(this, Observer {
      hideReplyDialog()
    })

    viewModel.toUploadAvatarCommand.observe(this, Observer {
      showToast(getString(R.string.please_upload_avatar))
      UploadAvatarNavigation.navigate().start()
    })

    viewModel.theCommentDeleteCommand.observe(this, Observer {
      mCommentIsDeleted = true
      finish()
    })
  }

  private fun showReplyDialog(commentItemData: CommentItemData) {
    mCommentDialog = InputDialog(this)
      .apply {
        hint = <EMAIL>(
          R.string.reply_format,
          commentItemData.nickName
        )
        onSendClickListener = object : InputDialog.OnSendClickListener {
          override fun onSendClicked(commentDialog: InputDialog, content: String) {
            viewModel.addReply(commentItemData, content)
          }
        }
      }
    mCommentDialog?.show()
  }

  private fun setupListAdapter() {
    val multiTypeAdapter = MultiTypeAdapter(this)
    multiTypeAdapter.register(CommentItemData::class.java)
      .to(ParentCommentViewBinder(viewModel)
        .apply {
          setOnItemLongClickListener(object : OnItemLongClickListener<CommentItemData> {
            override fun onLongClicked(
              v: View,
              position: Int,
              item: CommentItemData
            ): Boolean {
              showDeleteMenu(item, position)
              return true
            }
          })
          setOnItemClickListener(object :
            DefaultViewBinder.OnItemClickListener<CommentItemData> {
            override fun onItemClicked(
              v: View,
              position: Int,
              item: CommentItemData
            ) {
              if (v.id == R.id.iv_avatar || v.id == R.id.tv_name) {
//                                GzUserHomeNavigation.navigate(item.uid).start()
                UserHomeNavigation.navigate(item.uid).start()
              }
            }
          }, R.id.iv_avatar, R.id.tv_name)
        }, ReplyCommentViewBinder(viewModel)
        .apply {
          setOnItemLongClickListener(object :
            OnItemLongClickListener<CommentItemData> {
            override fun onLongClicked(
              v: View,
              position: Int,
              item: CommentItemData
            ): Boolean {
              showDeleteMenu(item, position)
              return true
            }
          })
          setOnItemClickListener(object :
            DefaultViewBinder.OnItemClickListener<CommentItemData> {
            override fun onItemClicked(
              v: View,
              position: Int,
              item: CommentItemData
            ) {
              if (v.id == R.id.iv_avatar || v.id == R.id.tv_name) {
                UserHomeNavigation.navigate(item.uid).start()
//                                    GzUserHomeNavigation.navigate(item.uid).start()
              }
            }
          }, R.id.iv_avatar, R.id.tv_name)
        }
      ).withClassLinker { position, _ ->
        if (position == 0) {
          return@withClassLinker ParentCommentViewBinder::class.java
        } else {
          return@withClassLinker ReplyCommentViewBinder::class.java
        }
      }
    val recyclerList = viewBinding.root.findViewById<RecyclerView>(R.id.recycler_content)
    recyclerList.layoutManager = LinearLayoutManager(this)
    viewModel.listViewModel.setAdapter(multiTypeAdapter)
  }

  private fun showDeleteMenu(item: CommentItemData, position: Int) {
    if (UserUtils.logged()) {
      if (item.uid == localUserId) {
        MenuPopup.Builder(this@CommentReplyActivity)
          .setData(resources.getStringArray(R.array.moment_details_delete_menu))
          .setOnItemClickListener { _, menuPosition ->
            if (menuPosition == 0) {
              showDeleteCommentConfirmDialog(position, item)
            }
          }.build().show()
      }
    }
  }

  private fun showDeleteCommentConfirmDialog(position: Int, item: CommentItemData) {
    ActionDialog.Builder()
      .setTitle(getString(R.string.tips))
      .setContent(getString(R.string.delete_comment_confirm_tips))
      .setOnConfirmClickListener {
        viewModel.deleteComment(position, item)
      }.build().show(supportFragmentManager)
  }

  override fun finish() {
    intent.putExtra(
      CommentReplayNavigation.EXTRA_NEW_COMMENT_COUNT,
      viewModel.getNewCommentCount()
    )
    intent.putExtra(CommentReplayNavigation.EXTRA_PARENT_COMMENT, viewModel.mParentComment)
    if (!mCommentIsDeleted) {
      setResult(CommentReplayNavigation.RESULT_COMMENT_CHANGED, intent)
    } else {
      setResult(CommentReplayNavigation.RESULT_COMMENT_DELETED, intent)
    }
    super.finish()
  }
}