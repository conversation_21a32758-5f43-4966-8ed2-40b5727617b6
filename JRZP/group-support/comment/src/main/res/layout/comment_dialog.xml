<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.jrzp.support.comment.ui.BottomSheetCommandViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        style="@style/match_match"
        android:background="@drawable/comment_bg_bottom_sheet"
        android:orientation="vertical">

        <FrameLayout
            style="@style/match_wrap"
            android:paddingTop="@dimen/dp_8"
            android:paddingBottom="@dimen/dp_8">

            <TextView
                style="@style/Text.12sp.333333"
                android:layout_gravity="center"
                android:text="@{@string/comment_count_format(viewModel.commentCount)}" />

            <ImageView
                android:id="@+id/iv_close"
                style="@style/wrap_wrap"
                android:layout_gravity="end|center_vertical"
                android:layout_marginEnd="@dimen/dp_14"
                android:src="@drawable/comment_ic_close_dialog" />
        </FrameLayout>

        <include
            android:id="@+id/include_comment_list"
            layout="@layout/include_mvvm_refresh_layout"
            app:listViewModel="@{viewModel.commentListViewModel}" />

        <View style="@style/Line.Horizontal" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_comment"
                style="@style/Text.12sp.888888"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/dp_14"
                android:layout_marginTop="@dimen/dp_6"
                android:layout_marginEnd="@dimen/dp_20"
                android:layout_marginBottom="@dimen/dp_6"
                android:layout_weight="1"
                android:background="@drawable/comment_bg_input"
                android:drawableStart="@drawable/comment_ic_edit"
                android:drawablePadding="@dimen/dp_8"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/dp_8"
                android:paddingEnd="@dimen/dp_8"
                android:text="@string/comment_hint" />

        </LinearLayout>

    </LinearLayout>
</layout>