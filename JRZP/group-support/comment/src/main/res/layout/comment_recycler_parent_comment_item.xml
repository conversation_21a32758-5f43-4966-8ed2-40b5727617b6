<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="android.view.View" />

    <import type="com.bxkj.common.util.CheckUtils" />

    <import type="com.bxkj.common.util.HtmlUtils" />

    <variable
      name="data"
      type="com.bxkj.jrzp.support.comment.data.CommentItemData" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:padding="@dimen/dp_12">

    <de.hdodenhof.circleimageview.CircleImageView
      android:id="@+id/iv_avatar"
      android:layout_width="@dimen/dp_36"
      android:layout_height="@dimen/dp_36"
      app:civ_border_color="@color/common_e8e8e8"
      app:civ_border_width="@dimen/dp_1"
      bind:imgUrl="@{data.photo}"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tv_name"
      style="@style/Text.13sp.4d8fcc"
      android:layout_width="@dimen/dp_0"
      android:layout_marginStart="@dimen/dp_6"
      android:layout_marginEnd="@dimen/dp_16"
      android:ellipsize="end"
      android:lines="1"
      android:text="@{data.nickName}"
      app:layout_constraintEnd_toStartOf="@id/tv_like"
      app:layout_constraintStart_toEndOf="@id/iv_avatar"
      app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <TextView
      android:id="@+id/tv_like"
      style="@style/Text.12sp.767676"
      android:drawableStart="@{data.isZan==0?@drawable/ic_like:@drawable/ic_liked}"
      android:drawablePadding="@dimen/dp_3"
      android:gravity="center_vertical"
      android:text="@{data.zanCount==0?@string/empty:String.valueOf(data.zanCount)}"
      app:layout_constraintBottom_toBottomOf="@id/tv_name"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_date"
      style="@style/Text.10sp.888888"
      android:text="@{data.date}"
      app:layout_constraintStart_toStartOf="@id/tv_name"
      app:layout_constraintTop_toBottomOf="@id/tv_name" />

    <TextView
      android:id="@+id/tv_content"
      style="@style/Text.14sp.black"
      android:layout_marginTop="@dimen/dp_6"
      android:text="@{HtmlUtils.fromHtml(CheckUtils.isNullOrEmpty(data.nickName2)?data.content:@string/comment_reply_format(data.nickName2,data.content))}"
      app:layout_constraintStart_toEndOf="@id/iv_avatar"
      app:layout_constraintTop_toBottomOf="@id/tv_date" />

    <TextView
      android:id="@+id/tv_all_reply"
      style="@style/Text.14sp.0B73DE"
      android:layout_width="@dimen/dp_0"
      android:layout_marginTop="@dimen/dp_12"
      android:background="@drawable/bg_f4f4f4"
      android:padding="@dimen/dp_10"
      android:text="@{@string/comment_reply_count_format(data.hfCount)}"
      android:visibility="@{data.hfCount==0||data.isParent?View.GONE:View.VISIBLE}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="@id/tv_content"
      app:layout_constraintTop_toBottomOf="@id/tv_content" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>