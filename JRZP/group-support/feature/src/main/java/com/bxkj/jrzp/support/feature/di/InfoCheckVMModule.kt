package com.bxkj.jrzp.support.feature.di

import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.di.ViewModelKey
import com.bxkj.jrzp.support.feature.ui.citypicker.CityPickerViewModel
import com.bxkj.jrzp.support.feature.ui.commonsearch.CommonSearchViewModel
import com.bxkj.jrzp.support.feature.ui.infocheck.CheckViewModel
import com.bxkj.jrzp.support.feature.ui.selectaddress.AddressMultiSelectViewModel
import com.bxkj.jrzp.support.feature.ui.seletjobtype.SelectJobTypeViewModel
import com.bxkj.jrzp.support.feature.ui.sendresume.SendResumeActionViewModel
import com.bxkj.jrzp.support.feature.ui.sharejobdialog.JobLinkViewModel
import com.bxkj.jrzp.support.feature.ui.sharejobdialog.JobPosterViewModel
import com.bxkj.jrzp.support.feature.ui.sharejobdialog.ShareJobDialogViewModel
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

/**
 * @Description:
 * @author:45457
 * @date: 2020/11/4
 * @version: V1.0
 */
@Module
abstract class InfoCheckVMModule {

    @Binds
    @IntoMap
    @ViewModelKey(SendResumeActionViewModel::class)
    abstract fun bindSendResumeActionViewModel(sendResumeActionViewModel: SendResumeActionViewModel): BaseViewModel

    @Binds
    @IntoMap
    @ViewModelKey(JobLinkViewModel::class)
    abstract fun bindJobLinkViewModel(jobLinkViewModel: JobLinkViewModel): BaseViewModel

    @Binds
    @IntoMap
    @ViewModelKey(JobPosterViewModel::class)
    abstract fun bindJobPosterViewModel(jobPosterViewModel: JobPosterViewModel): BaseViewModel

    @Binds
    @IntoMap
    @ViewModelKey(ShareJobDialogViewModel::class)
    abstract fun bindShareJobDialogViewModel(shareJobDialogViewModel: ShareJobDialogViewModel): BaseViewModel

    @Binds
    @IntoMap
    @ViewModelKey(CommonSearchViewModel::class)
    abstract fun bindCommonSearchViewModel(commonSearchViewModel: CommonSearchViewModel): BaseViewModel

    @Binds
    @IntoMap
    @ViewModelKey(AddressMultiSelectViewModel::class)
    abstract fun bindSelectAddressViewModel(addressMultiSelectViewModel: AddressMultiSelectViewModel): BaseViewModel

    @Binds
    @IntoMap
    @ViewModelKey(CityPickerViewModel::class)
    abstract fun bindCityPickerViewModel(cityPickerViewModel: CityPickerViewModel): BaseViewModel

    @Binds
    @IntoMap
    @ViewModelKey(CheckViewModel::class)
    abstract fun bindCheckViewModel(checkViewModel: CheckViewModel): BaseViewModel

    @Binds
    @IntoMap
    @ViewModelKey(SelectJobTypeViewModel::class)
    abstract fun bindSelectJobTypeViewModel(selectJobTypeViewModel: SelectJobTypeViewModel): BaseViewModel
}