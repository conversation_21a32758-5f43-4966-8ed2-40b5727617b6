package com.bxkj.jrzp.support.feature.ui.selectaddress

import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.data.AddressOptionData
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.support.feature.api.FeatureRepository
import com.bxkj.jrzp.support.feature.data.AddressItemData
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 *
 * @author: sanjin
 * @date: 2022/9/16
 */
class AddressMultiSelectViewModel @Inject constructor(
  private val repository: FeatureRepository
) : BaseViewModel() {

  val provinceListViewModel = RefreshListViewModel()
  val cityListViewModel = RefreshListViewModel()
  val areaListViewModel = RefreshListViewModel()

  private val _selectedAddress = ArrayList<AddressItemData>()
  val selectedAddress = MutableLiveData<List<AddressItemData>>()

  val saveSelectAddressCommand = MutableLiveData<VMEvent<List<AddressItemData>>>()

  val maxSelectNum = ObservableInt()

  private var _selectProvince: AddressOptionData? = null
  private var _selectCity: AddressOptionData? = null

  var selectCityPosition: Int = 0

  init {

    provinceListViewModel.refreshLayoutViewModel.enableRefresh(false)
    provinceListViewModel.refreshLayoutViewModel.enableLoadMore(false)
    cityListViewModel.refreshLayoutViewModel.enableRefresh(false)
    cityListViewModel.refreshLayoutViewModel.enableLoadMore(false)

    provinceListViewModel.setOnLoadDataListener {
      viewModelScope.launch {
        repository.getAreaList(CommonApiConstants.GET_PROVINCE_TYPE, 0)
          .handleResult({
            it?.let {
              provinceListViewModel.autoAddAll(it)
              syncSelectedProvince()
              _selectProvince = it[0]
              cityListViewModel.refresh()
            }
          }, {
            if (it.isNoDataError) {
              provinceListViewModel.noMoreData()
            } else {
              provinceListViewModel.loadError()
            }
          })
      }
    }

    cityListViewModel.setOnLoadDataListener {
      viewModelScope.launch {
        repository.getAreaList(
          CommonApiConstants.GET_CITY_TYPE,
          _selectProvince?.id.getOrDefault()
        ).handleResult({
          it?.let {
            _selectProvince?.let { province ->
              it.forEach { item ->
                item.pid = province.id
                item.pName = province.name
              }
            }
            cityListViewModel.autoAddAll(it)
            syncSelectedCity()
            _selectCity = it[0]
            areaListViewModel.refresh()
          }
        }, {
          if (it.isNoDataError) {
            cityListViewModel.noMoreData()
          } else {
            cityListViewModel.loadError()
          }
        })
      }
    }

    areaListViewModel.refreshLayoutViewModel.enableLoadMore(false)
    areaListViewModel.setOnLoadDataListener {
      viewModelScope.launch {
        repository.getAreaList(
          CommonApiConstants.GET_AREA_TYPE,
          _selectCity?.id.getOrDefault()
        ).handleResult({
          it?.let {
            val convertList = ArrayList(it)
            _selectCity?.let { city ->
              convertList.add(0, AddressOptionData(0, "全${city.name}"))
              convertList.forEach { item ->
                item.pid = city.id
                item.pName = city.name
              }
            }
            areaListViewModel.autoAddAll(convertList)
            syncSelectedArea()
          }
        }, {
          if (it.isNoDataError) {
            areaListViewModel.noMoreData()
          } else {
            areaListViewModel.loadError()
          }
        })
      }
    }
  }

  fun start(maxSelectNum: Int, address: ArrayList<AddressItemData>?) {
    this.maxSelectNum.set(maxSelectNum)
    address?.let {
      _selectedAddress.addAll(it)
      syncSelectedAddressChange()
    }
    provinceListViewModel.refresh()
  }

  fun switchProvince(areaOptionsData: AddressOptionData) {
    _selectProvince = areaOptionsData
    selectCityPosition = 0
    areaListViewModel.clear()
    cityListViewModel.refresh(true)
  }

  fun switchCity(city: AddressOptionData) {
    _selectCity = city
    areaListViewModel.refresh(true)
  }

  fun selectArea(area: AddressOptionData) {
    val selectedArea = _selectedAddress.find { it.cityid == area.pid && it.countyid == area.id }
    if (selectedArea == null) {
      if (_selectedAddress.size == maxSelectNum.get()) {
        showToast("最多选择${maxSelectNum.get()}个")
        return
      }
      area.isSelected = true
      _selectedAddress.add(
        AddressItemData(
          _selectProvince?.id,
          _selectProvince?.pName,
          _selectCity?.id,
          _selectCity?.name,
          area.id,
          area.name
        )
      )
      syncSelectedProvince()
      syncSelectedCity()
      syncSelectedAddressChange()
    } else {
      area.isSelected = false
      removeSelect(selectedArea)
    }
  }

  private fun syncSelectedAddressChange() {
    selectedAddress.value = ArrayList(_selectedAddress)
  }

  private fun syncSelectedProvince(selectedState: Boolean = true) {
    provinceListViewModel.data.map { it as AddressOptionData }.filter { province ->
      if (selectedState) {
        _selectedAddress.find { item -> (item.provinceid == province.id) } != null
      } else {
        _selectedAddress.find { item -> (item.provinceid == province.id) } == null
      }
    }.forEach { it.isSelected = selectedState }
  }

  private fun syncSelectedCity(selectedState: Boolean = true) {
    cityListViewModel.data.map { it as AddressOptionData }.filter { city ->
      if (selectedState) {
        _selectedAddress.find { item -> (item.cityid == city.id) } != null
      } else {
        _selectedAddress.find { item -> (item.cityid == city.id) } == null
      }
    }.forEach { it.isSelected = selectedState }
  }

  private fun syncSelectedArea(selectedState: Boolean = true) {
    areaListViewModel.data.map { it as AddressOptionData }.filter { area ->
      if (selectedState) {
        _selectedAddress.find { item -> (item.cityid == area.pid && item.countyid == area.id) } != null
      } else {
        _selectedAddress.find { item -> (item.cityid == area.pid && item.countyid == area.id) } == null
      }
    }.forEach { it.isSelected = selectedState }
  }

  fun removeSelect(address: AddressItemData) {
    _selectedAddress.remove(address)
    syncSelectedProvince(false)
    syncSelectedCity(false)
    syncSelectedArea(false)
    syncSelectedAddressChange()
  }

  fun save() {
    saveSelectAddressCommand.value = VMEvent(_selectedAddress)
  }
}