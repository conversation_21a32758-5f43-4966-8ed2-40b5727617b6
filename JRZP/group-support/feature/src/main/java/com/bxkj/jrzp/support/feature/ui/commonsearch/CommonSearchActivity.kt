package com.bxkj.jrzp.support.feature.ui.commonsearch

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.kotlin.closeDefaultAnim
import com.bxkj.common.util.kotlin.dip
import com.bxkj.common.util.kotlin.getResDrawable
import com.bxkj.common.util.recyclerutil.LineItemDecoration
import com.bxkj.jrzp.support.feature.R
import com.bxkj.jrzp.support.feature.databinding.FeatureActivitySearchBinding
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexboxItemDecoration
import com.google.android.flexbox.FlexboxLayoutManager
import com.skydoves.balloon.Balloon

/**
 * Description:搜索
 * Author:Sanjin
 **/
class CommonSearchActivity : BaseDBActivity<FeatureActivitySearchBinding, CommonSearchViewModel>(),
  OnClickListener {

  private val extraSearchFlags by lazy {
    intent.getParcelableArrayListExtra<SearchFlag>(
      EXTRA_SEARCH_FLAGS,
    )
  }

  private var searchHistoryListAdapter: SearchHistoryListAdapter? = null

  private var ballonSearchFlags: Balloon? = null

  override fun getViewModelClass(): Class<CommonSearchViewModel> = CommonSearchViewModel::class.java

  override fun getLayoutId(): Int = R.layout.feature_activity_search

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.viewModel = viewModel
    viewBinding.onClickListener = this

    statusBarManager.titleBar(viewBinding.flSearchBar).statusBarDarkFont(true, 0.4f).init()

    setupSearchHistoryListAdapter()
    setupEditSearchClickListener()
    subscribeViewModelEvent()

    extraSearchFlags?.let { searchFlags ->
      viewModel.switchSearchFlag(searchFlags.get(0))
      ballonSearchFlags = Balloon.Builder(this).apply {
        setLayout(R.layout.layout_search_flags)
        setBackgroundColor(getResColor(R.color.common_white))
      }.build().apply {
        getContentView().findViewById<RecyclerView>(R.id.recycler_search_flag).apply {
          layoutManager = LinearLayoutManager(this@CommonSearchActivity)
          addItemDecoration(
            LineItemDecoration.Builder().divider(getResDrawable(R.drawable.divider_f4f4f4))
              .margin(dip(4)).build()
          )
          adapter = object : SuperAdapter<SearchFlag>(
            this@CommonSearchActivity,
            R.layout.recycler_search_flag_item,
            searchFlags
          ) {
            override fun convert(
              holder: SuperViewHolder,
              viewType: Int,
              item: SearchFlag?,
              position: Int
            ) {
              (holder.itemView as TextView).setText(item?.name)
            }
          }.apply {
            setOnItemClickListener(object : SuperItemClickListener {
              override fun onClick(v: View, position: Int) {
                viewModel.switchSearchFlag(searchFlags[position])
                dismiss()
              }
            })
          }
        }
      }

      viewBinding.tvSearchFlag.setOnClickListener {
        ballonSearchFlags?.showAlignBottom(it)
      }
    }

    intent.getStringExtra(EXTRA_SEARCH_HINT)?.let {
      viewBinding.etSearchContent.hint = it
    }

    viewModel.start(intent.getIntExtra(EXTRA_SEARCH_TYPE, SearchType.NEWS.value))
  }

  private fun subscribeViewModelEvent() {
    viewModel.openEditHistory.observe(this) {
      searchHistoryListAdapter?.switchEditModel(it)
    }

    viewModel.startSearchCommand.observe(this) { keyword ->
      SystemUtil.hideSoftKeyboard(this)
      setResult(RESULT_OK, Intent().apply {
        putExtra(EXTRA_SEARCH_CONTENT, keyword)
        viewModel.searchFlag.value?.let {
          putExtra(EXTRA_RESULT_FLAG, it)
        }
      })
      finish()
    }
  }

  /**
   * 设置搜索按钮点击事件
   */
  private fun setupEditSearchClickListener() {
    viewBinding.etSearchContent.setOnEditorActionListener { v, actionId, _ ->
      if (actionId == EditorInfo.IME_ACTION_SEARCH) {
        viewModel.startSearch()
        return@setOnEditorActionListener true
      }
      return@setOnEditorActionListener false
    }
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.iv_back -> {
          finish()
        }

        R.id.tv_clear_history -> {
          viewModel.clearHistory()
        }
      }
    }
  }

  /**
   * 设置搜索历史适配器
   */
  private fun setupSearchHistoryListAdapter() {
    searchHistoryListAdapter = SearchHistoryListAdapter().apply {
      setOnItemClickListener(object : SuperItemClickListener {
        override fun onClick(v: View, position: Int) {
          getData()?.let {
            if (getEditMode()) {
              viewModel.deleteHistory(it[position])
            } else {
              viewModel.startSearchByKeyword(it[position].keyword)
            }
          }
        }
      })
    }

    viewBinding.recyclerHistory.apply {
      closeDefaultAnim()
      isNestedScrollingEnabled = false
      layoutManager = FlexboxLayoutManager(this@CommonSearchActivity, FlexDirection.ROW)
      addItemDecoration(
        FlexboxItemDecoration(this@CommonSearchActivity).apply {
          setDrawable(ContextCompat.getDrawable(this@CommonSearchActivity, R.drawable.divider_8))
          setOrientation(FlexboxItemDecoration.BOTH)
        }
      )
      adapter = searchHistoryListAdapter
    }
  }

  companion object {

    private const val EXTRA_SEARCH_TYPE = "SEARCH_TYPE"

    const val EXTRA_SEARCH_HINT = "SEARCH_HINT"

    const val EXTRA_SEARCH_CONTENT = "SEARCH_CONTENT"

    const val EXTRA_SEARCH_FLAGS = "SEARCH_FLAGS"

    const val EXTRA_RESULT_FLAG = "RESULT_FLAG"

    fun newIntent(
      context: Context,
      searchType: SearchType,
      hint: String = "",
      searchFlags: ArrayList<SearchFlag>? = null
    ): Intent {
      return Intent(context, CommonSearchActivity::class.java).apply {
        putExtra(EXTRA_SEARCH_TYPE, searchType.value)
        putExtra(EXTRA_SEARCH_HINT, hint)
        putExtra(EXTRA_SEARCH_FLAGS, searchFlags)
      }
    }
  }
}