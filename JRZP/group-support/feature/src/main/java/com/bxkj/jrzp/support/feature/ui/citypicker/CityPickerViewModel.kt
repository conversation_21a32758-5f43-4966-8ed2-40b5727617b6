package com.bxkj.jrzp.support.feature.ui.citypicker

import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.CheckUtils
import com.zaaach.citypicker.db.DBManager
import com.zaaach.citypicker.model.City
import com.zaaach.citypicker.model.LocatedCity
import javax.inject.Inject

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/5/18
 * @version: V1.0
 */
class CityPickerViewModel @Inject constructor() : BaseViewModel() {

    private val mCities: MutableLiveData<List<City>> = MutableLiveData()
    private val mSearchResultCities: MutableLiveData<List<City>> =
        MutableLiveData()
    private val mLocationCity: MutableLiveData<City> = MutableLiveData()

    private var mCityDBManager: DBManager? = null

    fun start(cityDBManager: DBManager) {
        mCityDBManager = cityDBManager
        getAllCites()
    }

    fun getAllCites() {
        mCities.value = mCityDBManager?.allCities
    }

    fun getCities(): MutableLiveData<List<City>> {
        return mCities
    }

    fun getSearchResultCities(): MutableLiveData<List<City>> {
        return mSearchResultCities
    }

    fun getLocationCity(): MutableLiveData<City> {
        return mLocationCity
    }

    fun getDBLocationInfoByLocate(location: String?) {
        searchCityByKeyword(location, true)
    }

    fun searchCityByKeyword(keyword: String) {
        searchCityByKeyword(keyword, false)
    }

    private fun searchCityByKeyword(
        location: String?,
        isLocate: Boolean
    ) {
        if (!location.isNullOrEmpty()) {
            val searchResult =
                mCityDBManager?.searchCity(
                    if (isLocate) location.subSequence(0, location.length - 1)
                        .toString() else location
                )
            if (!CheckUtils.isNullOrEmpty(searchResult)) {
                if (isLocate) {
                    mLocationCity.setValue(searchResult!![0])
                } else {
                    mSearchResultCities.setValue(searchResult)
                }
            } else {
                if (isLocate) {
                    mLocationCity.setValue(LocatedCity("杭州市", "浙江", "921"))
                } else {
                    mSearchResultCities.setValue(null)
                }
            }
        }
    }
}