package com.bxkj.jrzp.support.feature.data

import androidx.annotation.IntDef

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/6/21
 * @version: V1.0
 */
class CheckInfoTag {
  companion object {

    //检查个人信息
    const val CHECK_PERSONAL_INFO = 1

    //检查简历最大数量
    const val CHECK_RESUME_MAX_COUNT = 2

    //检查公司资料是否完善
    const val CHECK_COMPANY_INFO_COMPLETED = 3

    //检查公司是否认证
    const val CHECK_COMPANY_CERTIFICATED = 4

    //检查认证类型
    const val CHECK_COMPANY_AUTH_NEW = 5

    //检查是否是机构
    const val CHECK_COMPANY_IS_ORG = 6

    //检查是否个人认证
    const val CHECK_PERSONAL_AUTH = 7

    @IntDef(
      CHECK_PERSONAL_INFO,
      CHECK_RESUME_MAX_COUNT,
      CHECK_COMPANY_INFO_COMPLETED,
      CHECK_COMPANY_CERTIFICATED,
      CHECK_COMPANY_IS_ORG,
      CHECK_COMPANY_AUTH_NEW,
      CHECK_PERSONAL_AUTH
    )
    @Target(AnnotationTarget.VALUE_PARAMETER)
    @Retention(AnnotationRetention.SOURCE)
    annotation class Tag
  }
}