package com.bxkj.support.upload.data

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/28
 * @version: V1.0
 */
data class UploadVideoResult(
  var coverId: Int,
  var coverUrl: String = "",
  var videoId: Int = 0,
  var videoUrl: String = ""
) {

  companion object {
    fun fromCover(coverId: Int, coverUrl: String): UploadVideoResult {
      return UploadVideoResult(coverId, coverUrl)
    }
  }

  override fun toString(): String {
    return "UploadVideoResult(coverId=$coverId, coverUrl='$coverUrl', videoId=$videoId, videoUrl='$videoUrl')"
  }
}