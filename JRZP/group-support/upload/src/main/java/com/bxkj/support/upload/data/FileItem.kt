package com.bxkj.support.upload.data

import androidx.recyclerview.widget.DiffUtil
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.data.GalleryItem
import java.io.Serializable

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: biyeji-app
 * @Package com.bxkj.personal.ui.activity.releasenews
 * @Description:
 * @date 2019/11/22
 */
class FileItem private constructor(
    var id: Int = 0,
    var filePath: String = "",
    var fileUrl: String = "",
    var isVideo: Boolean = false,
    var coverId: Int = 0,
    var coverUrl: String = "",
    var isAddItem: Boolean = false,
    var width: Int = 0,
    var height: Int = 0
) : GalleryItem, Serializable {

    val realImgUrl: String?
        get() = if (fileUrl.startsWith("http")) {
            fileUrl
        } else {
            CommonApiConstants.BASE_JRZP_IMG_URL + fileUrl
        }

    override fun getItemUrl(): String {
        return fileUrl
    }

    class ItemDiffCallBack :
        DiffUtil.ItemCallback<FileItem>() {
        override fun areItemsTheSame(
            oldItem: FileItem,
            newItem: FileItem
        ): Boolean {
            return false
        }

        override fun areContentsTheSame(
            oldItem: FileItem,
            newItem: FileItem
        ): Boolean {
            return oldItem.fileUrl == newItem.fileUrl
        }
    }

    companion object {
        fun fromPathAndUrl(
            path: String,
            url: String,
            isVideo: Boolean
        ): FileItem {
            return FileItem(filePath = path, fileUrl = url, isVideo = isVideo)
        }

        fun fromUrl(url: String): FileItem {
            return FileItem(fileUrl = url)
        }

        val addItem: FileItem
            get() = FileItem(isAddItem = true)

        fun fromVideo(
            coverId: Int,
            coverUrl: String,
            videoId: Int,
            videoUrl: String,
            width: Int,
            height: Int
        ): FileItem {
            return FileItem(
                videoId,
                fileUrl = videoUrl,
                coverId = coverId,
                coverUrl = coverUrl,
                isVideo = true,
                width = width,
                height = height
            )
        }

        fun fromIdAndUrl(
            id: Int,
            url: String,
            isVideo: Boolean,
            width: Int = 0,
            height: Int = 0
        ): FileItem {
            return FileItem(
                id,
                fileUrl = url,
                isVideo = isVideo,
                width = width,
                height = height
            )
        }
    }
}