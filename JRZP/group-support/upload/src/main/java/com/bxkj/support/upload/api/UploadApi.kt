package com.bxkj.support.upload.api

import com.bxkj.common.network.BaseResponse
import com.bxkj.support.upload.data.UploadFileResult
import io.reactivex.Observable
import okhttp3.MultipartBody
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Url

/**
 * @Description:
 * @author:45457
 * @date: 2020/8/12
 * @version: V1.0
 */
interface UploadApi {

  @POST
  suspend fun uploadFile(@Url url: String, @Body requestBody: MultipartBody): BaseResponse<String>

  @POST
  suspend fun uploadFileV3(
    @Url url: String,
    @Body requestBody: MultipartBody
  ): BaseResponse<UploadFileResult>

  @POST
  fun updateFileV3Original(
    @Url url: String,
    @Body requestBody: MultipartBody
  ): Observable<BaseResponse<UploadFileResult>>
}