<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <import type="android.view.View" />

    <variable
      name="data"
      type="com.bxkj.support.upload.data.FileItem" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/match_wrap"
    android:background="@color/f2f2f2"
    android:orientation="vertical">

    <ImageView
      android:id="@+id/iv_photo"
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:scaleType="centerCrop"
      android:visibility="@{data.isAddItem?View.GONE:View.VISIBLE}"
      bind:imgUrl="@{data.realImgUrl}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintDimensionRatio="w,1:1"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <ImageView
      style="@style/wrap_wrap"
      android:src="@drawable/ic_play_video"
      android:visibility="@{data.video?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <ImageView
      android:id="@+id/iv_delete"
      style="@style/wrap_wrap"
      android:src="@drawable/ic_delete_photo"
      android:visibility="@{data.isAddItem?View.GONE:View.VISIBLE}"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <ImageView
      android:layout_width="@dimen/dp_0"
      android:layout_height="@dimen/dp_0"
      android:scaleType="center"
      android:src="@drawable/ic_news_photo_add"
      android:visibility="@{data.isAddItem?View.VISIBLE:View.GONE}"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintDimensionRatio="w,1:1"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>