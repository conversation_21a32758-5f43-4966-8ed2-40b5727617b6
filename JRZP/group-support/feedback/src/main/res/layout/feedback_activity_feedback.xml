<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.CheckUtils" />

    <variable
      name="feedbackViewModel"
      type="com.bxkj.jrzp.support.feedback.ui.feedback.FeedBackViewModel" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:background="@drawable/bg_f4f4f4"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      android:id="@+id/title_bar"
      style="@style/match_wrap"
      app:right_text="@string/feedback_history"
      app:show_right_text_bg="false"
      app:title="@string/personal_mine_feedback" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_48"
      android:gravity="center_vertical"
      android:orientation="horizontal">

      <TextView
        style="@style/Text.12sp.888888"
        android:layout_marginStart="@dimen/dp_12"
        android:text="@string/feedback_type" />

      <Space
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_weight="1" />

      <RadioGroup
        android:id="@+id/rg_feedback_type"
        style="@style/wrap_wrap"
        android:layout_marginEnd="@dimen/dp_12"
        android:orientation="horizontal">

        <RadioButton
          android:id="@+id/rb_features"
          style="@style/RadioButton.FeedBackType"
          android:text="@string/feedback_function" />

        <RadioButton
          style="@style/RadioButton.FeedBackType"
          android:text="@string/feedback_bug" />

        <RadioButton
          style="@style/RadioButton.FeedBackType"
          android:text="@string/feedback_suggest" />

        <RadioButton
          style="@style/RadioButton.FeedBackType"
          android:text="@string/feedback_other" />
      </RadioGroup>
    </LinearLayout>

    <LinearLayout
      style="@style/match_wrap"
      android:background="@drawable/bg_ffffff"
      android:orientation="vertical">

      <EditText
        android:id="@+id/et_content"
        style="@style/Text.14sp.333333"
        android:layout_width="match_parent"
        android:background="@null"
        android:gravity="top"
        android:hint="@string/feedback_input_hint"
        android:inputType="textMultiLine"
        android:lines="8"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_16"
        android:text="@={feedbackViewModel.feedbackContent}"
        android:textColor="@color/cl_333333"
        android:textColorHint="@color/common_b5b5b5" />

      <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_photo"
        style="@style/match_wrap"
        android:layout_marginStart="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_14"
        android:layout_marginBottom="@dimen/dp_14"
        android:overScrollMode="never"
        app:adapter="@{feedbackViewModel.photoAdapter}" />

    </LinearLayout>

    <androidx.legacy.widget.Space
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_0"
      android:layout_weight="1" />

    <TextView
      android:id="@+id/tv_submit"
      style="@style/Button.Basic"
      android:layout_marginStart="@dimen/dp_30"
      android:layout_marginEnd="@dimen/dp_30"
      android:layout_marginBottom="@dimen/dp_30"
      android:enabled="@{!CheckUtils.isNullOrEmpty(feedbackViewModel.feedbackContent)}"
      android:onClick="@{onClickListener}"
      android:text="@string/feedback_submit" />

  </LinearLayout>
</layout>
