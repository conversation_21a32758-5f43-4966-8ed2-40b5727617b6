<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.bxkj.common.util.CheckUtils" />

    <variable
      name="onClickListener"
      type="android.view.View.OnClickListener" />

    <variable
      name="viewModel"
      type="com.bxkj.jrzp.support.feedback.ui.conversation.FeedbackConversationViewModel" />
  </data>

  <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/match_match"
    android:background="@drawable/bg_f4f4f4"
    android:orientation="vertical">

    <com.bxkj.common.widget.CommonTitleBar
      style="@style/match_wrap"
      app:title="@string/feedback_conversation_page_title" />

    <include
      android:id="@+id/include_list"
      layout="@layout/include_mvvm_refresh_layout"
      app:listViewModel="@{viewModel.listViewModel}" />

    <View style="@style/Line.Horizontal" />

    <LinearLayout
      android:id="@+id/ll_edit_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/dp_52"
      android:background="@drawable/bg_ffffff"
      android:gravity="center_vertical"
      android:orientation="horizontal">

      <ImageView
        android:id="@+id/iv_select_img"
        style="@style/wrap_wrap"
        android:layout_marginStart="@dimen/dp_14"
        android:onClick="@{onClickListener}"
        android:src="@drawable/feedback_ic_conversation_select_img" />

      <EditText
        android:id="@+id/et_msg"
        style="@style/Text.16sp.333333"
        android:layout_width="@dimen/dp_0"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_weight="1"
        android:background="@null"
        android:hint="@string/feedback_conversation_hint"
        android:text="@={viewModel.feedbackMsgContent}" />

      <TextView
        style="@style/Text.16sp"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/common_bg_basic_btn_selector"
        android:enabled="@{!CheckUtils.isNullOrEmpty(viewModel.feedbackMsgContent)}"
        android:onClick="@{()->viewModel.sendNormalMsg()}"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_8"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_8"
        android:text="@string/chat_send"
        android:textColor="@color/common_button_text_selector" />

    </LinearLayout>

  </LinearLayout>
</layout>