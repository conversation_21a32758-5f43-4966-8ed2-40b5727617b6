package com.bxkj.jrzp.support.chat.widget.exchangephonenumdialog

import android.app.Dialog
import android.os.Bundle
import android.view.View
import android.view.View.OnClickListener
import androidx.appcompat.app.AppCompatDialog
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.UserUtils
import com.bxkj.common.widget.dialog.BaseDBDialogFragment
import com.bxkj.jrzp.support.chat.R
import com.bxkj.jrzp.support.chat.R.style
import com.bxkj.jrzp.support.chat.databinding.ChatDialogExchangePhoneNumberBinding

/**
 * Description: 交换手机号码
 * Author:45457
 **/
class ExchangePhoneNumberDialog(private val phoneNumber: String, private val onConfirm: (() -> Unit)? = null) :
    BaseDBDialogFragment<ChatDialogExchangePhoneNumberBinding, ExchangePhoneNumberViewModel>(), OnClickListener {

    override fun getViewModelClass(): Class<ExchangePhoneNumberViewModel> = ExchangePhoneNumberViewModel::class.java

    override fun getLayoutId(): Int = R.layout.chat_dialog_exchange_phone_number

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return AppCompatDialog(requireContext(), style.BaseDialogFragmentStyle)
    }

    override fun initPage() {
        viewBinding.onClickListener = this
        viewBinding.viewModel = viewModel
        isCancelable = false

        subscribeViewModelEvent()

        val isFirstRequest = UserUtils.isUserFirstRequestChangePhone()
        if (isFirstRequest) {
            viewBinding.llPhoneNumberInfo.visibility = View.VISIBLE
            viewBinding.llExchangeTips.visibility=View.GONE
        } else {
            viewBinding.llPhoneNumberInfo.visibility = View.GONE
            viewBinding.llExchangeTips.visibility=View.VISIBLE
        }

        viewModel.setPhoneNumber(phoneNumber)
    }

    private fun subscribeViewModelEvent() {
        viewModel.confirmEvent.observe(this, EventObserver<Unit> {
            UserUtils.setUserFirstRequestChangePhone(false)
            dismiss()
            onConfirm?.invoke()
        })
    }

    override fun onClick(v: View?) {
        v?.let {
            when (v.id) {
                R.id.tv_change -> {
                    viewModel.showChangePhoneNum()
                }

                R.id.tv_cancel -> {
                    dismiss()
                }
            }
        }
    }
}