package com.bxkj.jrzp.support.chat.ui.chatreplysetting

import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.network.handleResult
import com.bxkj.jrzp.support.chat.api.ChatRepo
import com.bxkj.jrzp.support.chat.data.ChatQuickMsgBean
import kotlinx.coroutines.launch
import javax.inject.Inject

class ChatReplySettingViewModel @Inject constructor(
    private val _chatRepo: ChatRepo
) : BaseViewModel() {

    val replyListViewModel = RefreshListViewModel()

    private var pendingUpdateMsgId = 0

    init {
        replyListViewModel.refreshLayoutViewModel.enableLoadMore(false)
        replyListViewModel.setOnLoadDataListener {
            viewModelScope.launch {
                _chatRepo.getQuickMsgList(getSelfUserID(), 3)
                    .handleResult({
                        replyListViewModel.autoAddAll(it?.dataList)
                    }, {
                        if (it.isNoDataError) {
                            replyListViewModel.noMoreData()
                        } else {
                            replyListViewModel.loadError()
                        }
                    })
            }
        }
    }

    fun start() {
        replyListViewModel.refresh()
    }

    fun addReply(content: String?) {
        content?.let {
            viewModelScope.launch {
                showLoading()
                _chatRepo.editChatQuickMsg(
                    getSelfUserID(),
                    3,
                    0,
                    ChatRole.getConvertLocalRole(),
                    it
                )
                    .handleResult({
                        showToast("添加成功")
                        replyListViewModel.refresh()
                    }, {
                        showToast(it.errMsg)
                    }, {
                        hideLoading()
                    })
            }
        }
    }

    fun setPendingUpdateMsgId(id: Int) {
        pendingUpdateMsgId = id
    }

    fun updateReply(content: String) {
        viewModelScope.launch {
            showLoading()
            _chatRepo.editChatQuickMsg(
                getSelfUserID(),
                3,
                0,
                ChatRole.getConvertLocalRole(),
                content,
                msgId = pendingUpdateMsgId
            ).handleResult({
                showToast("修改成功")
                replyListViewModel.refresh()
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
        }
    }

    fun deleteQuickMsg(item: ChatQuickMsgBean) {
        viewModelScope.launch {
            showLoading()
            _chatRepo.deleteChatQuickMsg(item.zid, getSelfUserID())
                .handleResult({
                    showToast("删除成功")
                    if (replyListViewModel.remove(item) == 0) {
                        replyListViewModel.refresh()
                    }
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }
}