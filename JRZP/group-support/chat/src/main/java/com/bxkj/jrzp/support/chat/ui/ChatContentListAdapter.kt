package com.bxkj.jrzp.support.chat.ui

import android.content.Context
import android.view.View
import android.widget.TextView
import com.bxkj.common.adapter.multitypeadapter.MultiTypeAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.bxkj.jrzp.support.chat.data.ChatMsgItemData
import com.bxkj.common.util.TimeUtils
import com.bxkj.jrzp.support.chat.R

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/2
 * @version: V1.0
 */
class ChatContentListAdapter constructor(
    context: Context,
    private val myName: String,
    private val myAvatar: String,
    private val friendName: String,
    private val friendAvatar: String
) : MultiTypeAdapter(context) {

    override fun convert(holder: SuperViewHolder, viewType: Int, item: Any?, position: Int) {
        item?.apply {
            if (item is ChatMsgItemData) {
                item.friendName = friendName
                item.friendAvatar = friendAvatar
                item.myName = myName
                item.myAvatar = myAvatar
                val timeTag = holder.findViewById<TextView>(R.id.tv_time_tag)
                checkNeedShowTimeTag(timeTag, position, item)
            }
        }
        super.convert(holder, viewType, item, position)
    }

    private fun checkNeedShowTimeTag(
        timeTag: TextView?,
        position: Int,
        item: ChatMsgItemData
    ) {
        if (timeTag != null) {
            if (position == 0) {
                timeTag.visibility = View.VISIBLE
                timeTag.text = item.date
            } else {
                val previousItem = data[position - 1]
                if (previousItem is ChatMsgItemData) {
                    previousItem.let {
                        if (TimeUtils.getSecondDifference(
                                TimeUtils.string2Date(previousItem.date, "yyyy/MM/dd HH:mm:ss"),
                                TimeUtils.string2Date(item.date, "yyyy/MM/dd HH:mm:ss")
                            ) > 60
                        ) {
                            timeTag.visibility = View.VISIBLE
                            timeTag.text = item.date
                        } else {
                            timeTag.visibility = View.GONE
                        }
                    }
                }
            }
        }
    }

    fun getItemAt(position: Int): ChatMsgItemData? {
        return if (data[position] is ChatMsgItemData) {
            (data[position] as ChatMsgItemData)
        } else {
            null
        }
    }
}