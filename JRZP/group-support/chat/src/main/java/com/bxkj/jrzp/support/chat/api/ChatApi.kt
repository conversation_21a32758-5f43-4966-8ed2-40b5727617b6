package com.bxkj.jrzp.support.chat.api

import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.data.ContactBodyBean
import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.ListDTO
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.jrzp.support.chat.data.ChatMsgItemData
import com.bxkj.jrzp.support.chat.data.ChatQuickMsgBean
import com.bxkj.jrzp.support.chat.data.InterviewInfoBean
import com.bxkj.jrzp.support.chat.data.MarkChatCountBean
import com.bxkj.jrzp.support.chat.data.NoReplyInfoBean
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Url

/**
 * Description:
 * Author:45457
 **/
interface ChatApi {

    @POST("/Conversation/SendMoreContent/")
    suspend fun sendContentToMultiUser(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/Notice/GetNoticeWeihuifuList/")
    suspend fun getNoReplyDetailsInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<NoReplyInfoBean>

    @POST("/Notice/GetNoticeWeihuifuCont/")
    suspend fun getNoReplyInfo(@Body encryptReqParams: EncryptReqParams): BaseResponse<NoReplyInfoBean>

    @POST("/Wenda/GetNoticeRead/")
    suspend fun setupMsgHasRead(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/Notify/HasUnLookedNotice/")
    suspend fun checkHasUnreadMsg(@Body requestBody: ZPRequestBody): BaseResponse<Boolean>

    @POST("/Notice/HasUnLookedNoticeV2/")
    suspend fun checkHasUnreadMsgV2(@Body requestBody: ZPRequestBody): BaseResponse<Boolean>

    @POST("/Conversation/UpdatetConversationRelid/")
    suspend fun switchChatJob(@Body requestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/ReleaseJob/GetJobListByPage2/")
    suspend fun getReleaseJobList(@Body requestBody: ZPRequestBody): BaseResponse<List<PositionItemBean>>

    @POST("/ReleaseJob/GetOnlineJob/")
    suspend fun getOnlineJobList(@Body requestBody: ZPRequestBody): BaseResponse<List<PositionItemBean>>

    @POST("/ZidingyiConversation/UpdateZidingyiConversationn/")
    suspend fun updateDefaultQuickMsg(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

    @POST
    suspend fun getQuickMsgListByUrl(
        @Url url: String
    ): BaseResponse<ListDTO<ChatQuickMsgBean>>

    @POST("/ZidingyiConversation/GetZidingyiName/")
    suspend fun getCustomQuickMsgList(@Body encryptReqParams: EncryptReqParams): BaseResponse<ListDTO<ChatQuickMsgBean>>

    @POST("/ZidingyiConversation/DeleteChangyongyu/")
    suspend fun deleteChatQuickMsg(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

    @POST("/ZidingyiConversation/AddZidingyiConversation/")
    suspend fun editChatQuickMsg(@Body encryptReqParams: EncryptReqParams): BaseResponse<Nothing>

    @POST("/ZidingyiConversation/GetJobZidingyiList/")
    suspend fun getChatQuickMsgList(@Body encryptReqParams: EncryptReqParams): BaseResponse<ListDTO<ChatQuickMsgBean>>

    @POST(CommonApiConstants.I_GET_CONVERSATION_CONTENT_LIST)
    suspend fun getConversationContentList(@Body requestBody: ZPRequestBody): BaseResponse<List<ChatMsgItemData>>

    @POST(CommonApiConstants.I_SEND_CONVERSATION_MSG)
    suspend fun sendConversationMsg(@Body pendingSendMsg: ChatMsgItemData): BaseResponse<Nothing>

    @POST("/Conversation/GetContentBuheshirencaiListByPage/")
    suspend fun getUnsuitableList(@Body mRequestBody: ZPRequestBody): BaseResponse<List<ContactBodyBean>>

    @POST("/Conversation/GetBuheshiCount/")
    suspend fun getBusinessMarkChatCount(): BaseResponse<MarkChatCountBean>

    @POST("/Conversation/GetDislikeCount/")
    suspend fun getGeekMarkChatCount(): BaseResponse<MarkChatCountBean>

    @POST("/JobInterview/GetJobInterview/")
    suspend fun getInterviewInfo(@Body requestBody: ZPRequestBody): BaseResponse<InterviewInfoBean>

    @POST("/Notice/AddJiaoHuanWeixinShouji/")
    suspend fun requestExchangeContactWay(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/Notice/AddWeixinShouji/")
    suspend fun agreeExchangeContactWay(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/Notice/AddRefuseWeixinShouji/")
    suspend fun refuseExchangeContactWay(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/Conversation/UpdateBuheshiState/")
    suspend fun markChat(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>

    @POST("/Conversation/UpdateDislikeState/")
    suspend fun geekMarkChat(@Body mRequestBody: ZPRequestBody): BaseResponse<Nothing>
}