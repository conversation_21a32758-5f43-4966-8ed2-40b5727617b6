package com.bxkj.jrzp.support.chat.ui.chatbase

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.LiveEvent
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.data.ChatInfoBean
import com.bxkj.common.data.UploadFileRequestParams
import com.bxkj.jrzp.support.chat.data.ChatMsgItemData
import com.bxkj.common.data.repository.ConversationRepository
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.enums.ChatRole.Companion.Role
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.RegularUtils
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.common.util.map.LngLat
import com.bxkj.jrzp.support.chat.ChatMsgType
import com.bxkj.jrzp.support.chat.api.ChatRepo
import com.bxkj.jrzp.user.api.OpenUserApiConstants.ContactType
import com.bxkj.jrzp.user.repository.OpenUserRepository
import com.bxkj.support.upload.repository.UploadRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
open class ChatBaseViewModel @Inject constructor(
  private val _chatRepo: ChatRepo,
  private val _conversationRepository: ConversationRepository,
  private val _openUserRepo: OpenUserRepository,
  private val _uploadRepo: UploadRepository
) : BaseViewModel() {

  //聊天对象信息
  val conversationInfo = MutableLiveData<ChatInfoBean>()

  val pageTitle = MutableLiveData<String>()

  val pageSubTitle = MutableLiveData<String>()

  //未读消息数
  val unreadMsgCount = MutableLiveData<Int>().apply { value = 0 }

  //滚动到最后一条消息
  val scrollToLastMsgPositionCommand = MutableLiveData<VMEvent<Unit>>()

  //消息列表加载完成
  val msgListLoadFinishEvent = MutableLiveData<VMEvent<Unit>>()

  val deleteConversationSuccessEvent =
    LiveEvent<Void>()

  //添加消息到列表
  val addNewMsgCommand = MutableLiveData<VMEvent<ChatMsgItemData>>()

  //重置消息列表
  val resetMsgListCommand = MutableLiveData<VMEvent<List<ChatMsgItemData>>>()

  //添加下一页消息列表
  val addNextPageMsgListCommand = MutableLiveData<VMEvent<List<ChatMsgItemData>>>()

  //消息内容
  val msgContent = MutableLiveData<String>()

  //显示交换微信弹窗
  val showExchangeWechatCommand = MutableLiveData<VMEvent<String>>()

  val showQuickMsg = MutableLiveData<Boolean>().apply { value = false }

  val quickMsgListViewModel = RefreshListViewModel()

  val showSetupWechatCommand = MutableLiveData<VMEvent<Int>>()

  //聊天身份
  @Role
  private var _chatRole: Int = ChatRole.PERSONAL

  //聊天对象userId
  protected var _friendUserId: Int = CommonApiConstants.NO_ID

  //会话id
  protected var _conversationId: Int = CommonApiConstants.NO_ID

  //绑定的职位id
  private var _attachJobId: Int = CommonApiConstants.NO_ID

  //绑定简历id
  protected var _attachResumeId: Int = CommonApiConstants.NO_ID

  private var mPageIndex: Int = 1

  init {
    quickMsgListViewModel.refreshLayoutViewModel.enableLoadMore(false)
    quickMsgListViewModel.setOnLoadDataListener {
      viewModelScope.launch {
        _chatRepo.getQuickMsgList(getSelfUserID(), 3)
          .handleResult({
            quickMsgListViewModel.autoAddAll(it?.dataList)
          }, {
            if (it.isNoDataError) {
              quickMsgListViewModel.noMoreData()
            } else {
              quickMsgListViewModel.loadError()
            }
          })
      }
    }
  }

  fun start(
    chatIdentity: Int,
    friendUserId: Int,
    attachJobId: Int,
    attachResumeId: Int,
    sayHello: Boolean
  ) {
    _chatRole = chatIdentity
    _friendUserId = friendUserId
    _attachJobId = attachJobId
    _attachResumeId = attachResumeId
    if (sayHello) {
      createMsgAndSend(getSayHelloText(), true)
    } else {
      refreshChatInfo()
    }
  }

  fun refreshQuickMsgList() {
    quickMsgListViewModel.refresh()
  }

  fun getConversationInfoValue(): ChatInfoBean? {
    return conversationInfo.value
  }

  /**
   * 请求交换微信
   */
  fun requestExchangeWechat() {
    conversationInfo.value?.let {
      if (it.wechatExchanged()) {
        val localShowWechatMsg =
          ChatMsgItemData.createTypeMsg(ChatRole.BUSINESS, ChatMsgType.AGREE_EXCHANGE_WECHAT)
        addLocalMsg(localShowWechatMsg)
      } else {
        viewModelScope.launch {
          showLoading()
          _openUserRepo.getUserContactInfo(ContactType.WECHAT)
            .handleResult({ result ->
              showExchangeWechatCommand.value = VMEvent(result?.contactWay.getOrDefault())
            }, { err ->
              showToast(err.errMsg)
            }, {
              hideLoading()
            })
        }
      }
    }
  }

  /**
   * 添加本地显示手机号信息Item
   */
  fun addShowContactPhoneItem() {
    val localShowPhoneMsg =
      ChatMsgItemData.createTypeMsg(
        if (UserUtils.isPersonalRole()) ChatRole.BUSINESS else ChatRole.PERSONAL,
        ChatMsgType.AGREE_EXCHANGE_PHONE
      )
    addLocalMsg(localShowPhoneMsg)
  }

  /**
   * 交换微信请求
   */
  fun sendExchangeWechatRequest() {
    conversationInfo.value?.let {
      showLoading()
      viewModelScope.launch {
        _chatRepo.requestExchangeContactWay(
          it.ncID,
          if (UserUtils.isPersonalRole()) ChatRole.BUSINESS else ChatRole.PERSONAL,
          getSelfUserID(),
          if (UserUtils.isPersonalRole()) it.cUserID else it.userID,
          it.relID,
          it.resID,
          ChatMsgType.REQ_EXCHANGE_WECHAT
        ).handleResult({
          refreshChatInfo()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  fun agreeExchangePreCheck(msgType: Int, msgId: Int) {
    if (msgType == ChatMsgType.REQ_EXCHANGE_WECHAT) {
      viewModelScope.launch {
        showLoading()
        _openUserRepo.getUserContactInfo(ContactType.WECHAT)
          .handleResult({ result ->
            result?.let {
              if (it.contactWay.isBlank()) {
                showSetupWechatCommand.value = VMEvent(msgId)
              } else {
                agreeExchangeContactWay(msgType, msgId)
              }
            } ?: let {
              showSetupWechatCommand.value = VMEvent(msgId)
            }
          }, { err ->
            showToast(err.errMsg)
          }, {
            hideLoading()
          })
      }
    } else {
      agreeExchangeContactWay(msgType, msgId)
    }
  }

  fun agreeExchangeWechat(msgId: Int) {
    agreeExchangeContactWay(ChatMsgType.REQ_EXCHANGE_WECHAT, msgId)
  }

  private fun agreeExchangeContactWay(msgType: Int, msgId: Int) {
    conversationInfo.value?.let {
      showLoading()
      viewModelScope.launch {
        _chatRepo.agreeExchangeContactWay(
          it.ncID,
          if (UserUtils.isPersonalRole()) ChatRole.BUSINESS else ChatRole.PERSONAL,
          getSelfUserID(),
          if (UserUtils.isPersonalRole()) it.cUserID else it.userID,
          it.relID,
          it.resID,
          msgType,
          msgId
        ).handleResult({
          refreshChatInfo()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  fun refuseExchangeContactWay(msgType: Int, msgId: Int) {
    conversationInfo.value?.let {
      showLoading()
      viewModelScope.launch {
        _chatRepo.refuseExchangeContactWay(
          it.ncID,
          if (UserUtils.isPersonalRole()) ChatRole.BUSINESS else ChatRole.PERSONAL,
          getSelfUserID(),
          if (UserUtils.isPersonalRole()) it.cUserID else it.userID,
          it.relID,
          it.resID,
          msgType,
          msgId
        ).handleResult({
          refreshChatInfo()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  /**
   * 删除会话
   */
  fun deleteThisConversation() {
    conversationInfo.value?.let {
      viewModelScope.launch {
        showLoading()
        _conversationRepository.deleteConversation(it.ncID, _chatRole)
          .handleResult({
            deleteConversationSuccessEvent.call()
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    }
  }

  fun scrollToLastMsgPosition() {
    scrollToLastMsgPositionCommand.value = VMEvent(Unit)
  }

  fun refreshMsgList(showLoadingDialog: Boolean = false) {
    viewModelScope.launch {
      if (showLoadingDialog) {
        showLoading()
      }
      mPageIndex = 1
      getConversationContentList(showLoadingDialog)
      unreadMsgCount.value = 0
      if (showLoadingDialog) {
        hideLoading()
      }
    }
  }

  fun loadNextPage() {
    mPageIndex += 1
    getConversationContentList()
  }

  /**
   * 发送消息
   */
  @JvmOverloads
  fun sendNormalMsg(msg: String = "") {
    val content = msg.ifEmpty { msgContent.value.getOrDefault() }
    if (!UserUtils.isPersonalRole()) {
      if (RegularUtils.containsContact(content)) {
        showToast("请勿发送联系方式等个人信息")
        return
      }
    } 
    conversationInfo.value?.let { info ->
      createMsgAndSend(content)
      msgContent.value = ""
    }
  }

  /**
   * 重新发送消息
   */
  fun resendMsg(item: ChatMsgItemData) {
    sendMsg(item)
  }

  /**
   * 处理接收到新消息
   */
  fun handleReceiveNewMsg(onListLast: Boolean) {
    conversationInfo.value?.refreshLastActiveTime()

    if (onListLast) {
      refreshMsgList()
    } else {
      unreadMsgCount.value?.let {
        unreadMsgCount.value = it + 1
      }
    }
  }

  /**
   * 判断滚动到底部是否需要刷新列表
   */
  fun checkNeedRefreshList(onListLast: Boolean) {
    if (onListLast) {
      unreadMsgCount.value?.let { msgCount ->
        if (msgCount > 0) {
          refreshMsgList()
        }
      }
    }
  }

  /**
   * 刷新会话信息
   */
  fun refreshChatInfo(onlyRefreshChatInfo: Boolean = false) {
    viewModelScope.launch {
      _conversationRepository.getConversationInfo(
        _chatRole,
        getSelfUserID(),
        _friendUserId
      ).handleResult({
        it?.let {
          if (_attachJobId == CommonApiConstants.NO_ID) {
            _attachJobId = it.relID
          }
          if (_attachResumeId == CommonApiConstants.NO_ID) {
            _attachResumeId = it.resID
          }
          conversationInfo.value = it
          if (UserUtils.isPersonalRole()) {
            pageTitle.value = it.cUserName.ifBlank { it.comName }
            pageSubTitle.value = if (it.cUserName.isBlank()) "" else it.comName
          } else {
            pageTitle.value = it.userName
            pageSubTitle.value = it.detailsName2
          }
          _conversationId = it.ncID
          if (!onlyRefreshChatInfo) {
            getConversationContentList(true)
          }
        }
      })
    }
  }

  fun addReply(content: String?) {
    content?.let {
      viewModelScope.launch {
        showLoading()
        _chatRepo.editChatQuickMsg(
          getSelfUserID(),
          3,
          0,
          ChatRole.getConvertLocalRole(),
          it
        ).handleResult({
          showToast("添加成功")
          quickMsgListViewModel.refresh()
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  /**
   * 发送位置信息
   */
  fun sendLocation(location: LngLat, address: String) {
    conversationInfo.value?.let {
      val pendingSendMsg = ChatMsgItemData.createLocationMsg(
        it.ncID,
        _chatRole,
        _friendUserId,
        _attachResumeId,
        _attachJobId,
        "${location.longitude},${location.lantitude}",
        address
      )

      sendMsg(pendingSendMsg)
    }
  }

  /**
   * 发送图片信息
   */
  fun sendImage(realMediaPath: String) {
    viewModelScope.launch {
      showLoading()
      _uploadRepo.uploadFileV3(
        realMediaPath,
        UploadFileRequestParams.getChatImageUploadParams(getSelfUserID())
      )
        .handleResult({ result ->
          conversationInfo.value?.let {
            val pendingSendMsg = ChatMsgItemData.createImageMsg(
              it.ncID,
              _chatRole,
              _friendUserId,
              _attachResumeId,
              _attachJobId,
              result?.url.getOrDefault()
            )

            sendMsg(pendingSendMsg)
          }
        }, {
          showToast("发送失败")
        }, {
          hideLoading()
        })
    }
  }

  /**
   * 发送消息
   */
  private fun createMsgAndSend(msg: String?, sayHello: Boolean = false) {
    if ((!sayHello) && conversationInfo.value == null) {
      showToast("会话信息加载中，请稍后再试")
      return
    }

    val chatId = if (sayHello) CommonApiConstants.NO_ID else _conversationId

    val pendingSendMsg = ChatMsgItemData.createStandardMsg(
      chatId, _chatRole, _friendUserId,
      sayHello, msg, _attachResumeId, _attachJobId
    )

    sendMsg(pendingSendMsg)
  }

  private fun addLocalMsg(msg: ChatMsgItemData) {
    addNewMsgCommand.value = VMEvent(msg)
    scrollToLastMsgPosition()
  }

  private fun sendMsg(pendingSendMsg: ChatMsgItemData) {
    if (!pendingSendMsg.isSayHelloMsg()) {
      addLocalMsg(pendingSendMsg)
      pendingSendMsg.sending()
    }

    viewModelScope.launch {
      _chatRepo.sendChatMsg(pendingSendMsg)
        .handleResult({
          if (pendingSendMsg.isSayHelloMsg()) {
            refreshChatInfo()
          } else {
            pendingSendMsg.sendSuccess()
          }
        }, {
          if (pendingSendMsg.isSayHelloMsg()) {
            refreshChatInfo()
          } else {
            showToast(it.errMsg)
            pendingSendMsg.sendFailed()
          }
        })
    }
  }

  protected fun getSayHelloText(): String {
    return if (_chatRole == ChatRole.PERSONAL) {
//            "您好"
      ""
    } else {
//            "您好，我们正在诚招${attachInfo}，有兴趣聊聊吗？"
      ""
    }
  }

  /**
   * 设置消息已读
   */
  private fun setupMsgHasRead(conversationID: Int) {
    viewModelScope.launch {
      _conversationRepository.setupMsgHasRead(getSelfUserID(), conversationID)
    }
  }

  /**
   * 获取聊天消息内容列表
   */
  private fun getConversationContentList(showLading: Boolean = false) {
    viewModelScope.launch {
      if (mPageIndex == 1 && showLading) {
        showLoading()
      }
      _chatRepo.getConversationContentList(_conversationId, mPageIndex, 1000)
        .handleResult({
          it?.let {
            it.last().let { lastMsg ->
              if (lastMsg.isAcceptVideoInterviewMsg()) {  //用户接受视频面试邀请，更新会话信息改变状态后可显示邀请视频面试按钮
                refreshChatInfo(true)
              }
              if (lastMsg.isSendResumeMsg()) { //用户投递一个新简历过来，要刷新会话信息来更新会话中的简历和职位id
                refreshChatInfo(true)
              }
            }
            if (mPageIndex == 1) {
              setupMsgHasRead(_conversationId)
              resetMsgListCommand.value = VMEvent(it)
            } else {
              addNextPageMsgListCommand.value = VMEvent(it)
            }
            if (mPageIndex == 1) {
              scrollToLastMsgPosition()
            }
          }
        }, {
          if (mPageIndex > 1) {
            mPageIndex -= 1
          }
          if (!it.isNoDataError) {
            showToast(it.errMsg)
          }
        }, {
          msgListLoadFinishEvent.value = VMEvent(Unit)
          if (mPageIndex == 1 && showLading) {
            hideLoading()
          }
        })
    }
  }
}