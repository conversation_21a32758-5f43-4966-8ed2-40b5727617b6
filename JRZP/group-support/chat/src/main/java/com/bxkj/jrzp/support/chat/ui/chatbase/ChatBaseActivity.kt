package com.bxkj.jrzp.support.chat.ui.chatbase

import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.ViewDataBinding
import com.bxkj.common.adapter.SimpleDBListAdapter
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.EventObserver
import com.bxkj.common.util.qmui.QMUIKeyboardHelper
import com.bxkj.common.util.qmui.QMUIKeyboardHelper.KeyboardVisibilityEventListener
import com.bxkj.jrzp.support.chat.R
import com.bxkj.jrzp.support.chat.data.ChatQuickMsgBean
import com.bxkj.jrzp.support.chat.ui.chatreplysetting.ChatReplySettingActivity
import com.bxkj.jrzp.support.chat.widget.exchangewechatdialog.ExchangeWechatDialog
import com.bxkj.personal.ui.activity.editinfo.EditInfoNavigation

/**
 * Description:
 * Author:45457
 **/
abstract class ChatBaseActivity<VB : ViewDataBinding, VM : ChatBaseViewModel> :
  BaseDBActivity<VB, VM>() {

  private val _addQuickReplyLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      if (it.resultCode == RESULT_OK) {
        viewModel.addReply(it.data?.getStringExtra(EditInfoNavigation.EXTRA_RESULT_TEXT))
      }
    }

  private val _editQuickReplyLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      viewModel.refreshQuickMsgList()
    }

  override fun initPage(savedInstanceState: Bundle?) {
    initQuickMsgAdapter()

    QMUIKeyboardHelper.setVisibilityEventListener(
      this,
      object : KeyboardVisibilityEventListener {
        override fun onVisibilityChanged(isOpen: Boolean, heightDiff: Int): Boolean {
          if (isOpen) {
            viewModel.showQuickMsg.value = false
          }
          return false
        }
      })

    subscribeViewModelEvent()
  }

  fun toAddQuickReply() {
    _addQuickReplyLauncher.launch(
      EditInfoNavigation.navigate(
        "新建常用语",
        "输入您的常用语回复，请不要填写QQ，微信,链接等联系方式和广告信息，否则系统将封禁您的账号",
        maxLength = 200
      ).createIntent(this)
    )
  }

  fun toQuickReplySetting() {
    _editQuickReplyLauncher.launch(ChatReplySettingActivity.newIntent(this))
  }

  private fun initQuickMsgAdapter() {
    val quickMsgAdapter =
      SimpleDBListAdapter<ChatQuickMsgBean>(this, R.layout.chat_recycler_quick_msg_item).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            data.get(position)?.let {
              viewModel.sendNormalMsg(it.Content)
            }
          }
        })
      }
    viewModel.quickMsgListViewModel.setAdapter(quickMsgAdapter)
  }

  private fun subscribeViewModelEvent() {
    viewModel.showExchangeWechatCommand.observe(this, EventObserver {
      showExchangeWXConfirmDialog(it)
    })

    viewModel.showSetupWechatCommand.observe(this, EventObserver { msgId ->
      ExchangeWechatDialog("") {
        viewModel.agreeExchangeWechat(msgId)
      }.show(supportFragmentManager)
    })
  }

  private fun showExchangeWXConfirmDialog(wechatNumber: String) {
    ExchangeWechatDialog(wechatNumber) {
      viewModel.sendExchangeWechatRequest()
    }.show(supportFragmentManager)
  }
}