package com.bxkj.jrzp.support.chat.api

import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.data.ContactBodyBean
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.network.ListDTO
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.objectEncrypt
import com.bxkj.common.util.kotlin.paramsEncrypt
import com.bxkj.common.util.kotlin.toIntOrDefault
import com.bxkj.enterprise.data.PositionItemBean
import com.bxkj.jrzp.support.chat.data.ChatMsgItemData
import com.bxkj.jrzp.support.chat.data.ChatQuickMsgBean
import com.bxkj.jrzp.support.chat.data.InterviewInfoBean
import com.bxkj.jrzp.support.chat.data.MarkChatCountBean
import com.bxkj.jrzp.support.chat.data.NoReplyInfoBean
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
class ChatRepo @Inject constructor(private val _chatApi: ChatApi) : BaseRepo() {

    /**
     * 发送多人消息
     */
    suspend fun sendContentToMultiUser(
        myUserId: Int,
        userIds: String,
        chatID: String,
        content: String
    ): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.sendContentToMultiUser(
                ZPRequestBody().apply {
                    put("uids", userIds)
                    put("cuid", myUserId)
                    put("lx", 0)
                    put("content", content)
                    put("messageType", 0)
                    put("ncID", chatID)
                }
            )
        }
    }

    /**
     * 获取未回复信息详情
     */
    suspend fun getNoReplyDetailsInfo(userId: Int): ReqResponse<NoReplyInfoBean> {
        return httpRequest {
            _chatApi.getNoReplyDetailsInfo(
                ZPRequestBody().apply {
                    put("uid", userId)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 获取未回复信息
     */
    suspend fun getNoReplyInfo(userId: Int): ReqResponse<NoReplyInfoBean> {
        return httpRequest {
            _chatApi.getNoReplyInfo(
                ZPRequestBody().apply {
                    put("uid", userId)
                }.paramsEncrypt()
            )
        }
    }

    /**
     * 设置消息已读
     */
    suspend fun setupMsgHasRead(msgId: Int): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.setupMsgHasRead(ZPRequestBody().apply {
                put("id", msgId)
            })
        }
    }

    /**
     * 检查是否存在未读消息
     */
    suspend fun checkHasUnreadMsg(role: Int, userID: Int, type: Int? = null): ReqResponse<Boolean> {
        return httpRequest {
            _chatApi.checkHasUnreadMsg(ZPRequestBody().apply {
                put("userID", userID)
                put("lx", role)
                type?.let {
                    put("type", type)
                }
            }).apply {
                if (status == 10001) {
                    data = (msg == "true")
                }
            }
        }
    }

    suspend fun checkHasUnreadMsgV2(role: Int, userID: Int, type: Int? = null): ReqResponse<Boolean> {
        return httpRequest {
            _chatApi.checkHasUnreadMsgV2(ZPRequestBody().apply {
                put("uid", userID)
                put("lx", role)
                put("look", 0)
                type?.let {
                    put("type", type)
                }
            }).apply {
                if (status == 10001) {
                    data = (msg.toIntOrDefault(0) > 0)
                }
            }
        }
    }

    /**
     * 切换聊天职位信息
     */
    suspend fun switchChatJob(
        userId: Int,
        chatId: Int,
        friendUserId: Int,
        originJobId: Int,
        targetJobId: Int,
        resumeId: Int
    ): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.switchChatJob(
                ZPRequestBody().apply {
                    put("cUserID", userId)
                    put("userID", friendUserId)
                    put("relid", originJobId)
                    put("relid2", targetJobId)
                    put("resid", resumeId)
                    put("id", chatId)
                }
            )
        }
    }

    suspend fun getReleasedJobList(
        userId: Int,
        jobStatus: Int,
        pageIndex: Int,
        pageSize: Int,
        jobName: String = ""
    ): ReqResponse<List<PositionItemBean>> {
        return httpRequest {
            _chatApi.getReleaseJobList(
                ZPRequestBody().apply {
                    put("uid", userId)
                    put("name", jobName)
                    put("state", jobStatus)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                }
            )
        }
    }

    suspend fun getOnlineJobList(userID: Int): ReqResponse<List<PositionItemBean>> {
        return httpRequest {
            _chatApi.getOnlineJobList(ZPRequestBody().apply {
                put("uid", userID)
            })
        }
    }

    /**
     * Lx:0全部，1自定义打招呼,2自动回复语,3常用语,4常规语,5幽默语,6礼貌语,7诚恳语
     */
    suspend fun updateDefaultQuickMsg(msgId: Int, userId: Int, type: Int): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.updateDefaultQuickMsg(ZPRequestBody().apply {
                if (UserUtils.isPersonalRole()) {
                    put("uid", userId)
                } else {
                    put("cuid", userId)
                }
                put("Lx", type)
                if (type == 1) {
                    put("zid", msgId)
                } else {
                    put("Content", msgId)
                }
            }.objectEncrypt())
        }
    }

    suspend fun getCustomQuickMsgList(userId: Int, role: Int): ReqResponse<ListDTO<ChatQuickMsgBean>> {
        return httpRequest {
            _chatApi.getCustomQuickMsgList(
                ZPRequestBody().apply {
                    put("type", 0)
                    put("faqi", role)
                    if (UserUtils.isPersonalRole()) {
                        put("uid", userId)
                    } else {
                        put("cuid", userId)
                    }
                    put("Lx", 1)
                }.objectEncrypt()
            )
        }
    }

    /**
     * 获取快捷消息列表
     */
    suspend fun getQuickMsgListByUrl(url: String): ReqResponse<ListDTO<ChatQuickMsgBean>> {
        return httpRequest {
            _chatApi.getQuickMsgListByUrl(url)
        }
    }

    /**
     * 删除自定义消息
     */
    suspend fun deleteChatQuickMsg(msgId: Int, userId: Int): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.deleteChatQuickMsg(ZPRequestBody().apply {
                put("zid1", msgId)
                if (UserUtils.isPersonalRole()) {
                    put("uid", userId)
                } else {
                    put("cuid", userId)
                }
            }.objectEncrypt())
        }
    }

    /**
     * 编辑自定义消息
     * Lx:0全部，1自定义打招呼,2自动回复语,3常用语,4常规语,5幽默语,6礼貌语,7诚恳语
     */
    suspend fun editChatQuickMsg(
        userId: Int,
        msgType: Int,
        type: Int,
        role: Int,
        content: String = "",
        px: Int = 0,
        msgId: Int = 0,
        jobId: Int = 0
    ): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.editChatQuickMsg(
                ZPRequestBody().apply {
                    if (UserUtils.isPersonalRole()) {
                        put("uid", userId)
                    } else {
                        put("cuid", userId)
                    }
                    put("Lx", msgType)
                    put("type", type)
                    put("faqi", role)
                    put("Content", content)
                    put("px", px)
                    put("zid", msgId)
                    if (jobId != 0) {
                        put("relid", jobId)
                    }
                }.objectEncrypt()
            )
        }
    }

    /**
     * 获取快速消息列表
     * lx:1自定义打招呼,2自动回复语,3常用语
     * type:0全部，1职位
     */
    suspend fun getQuickMsgList(
        userId: Int,
        msgType: Int,
        type: Int = 0
    ): ReqResponse<ListDTO<ChatQuickMsgBean>> {
        return httpRequest {
            _chatApi.getChatQuickMsgList(
                ZPRequestBody().apply {
                    if (UserUtils.isPersonalRole()) {
                        put("uid", userId)
                    } else {
                        put("cuid", userId)
                    }
                    put("lx", msgType)
                    put("type", type)
                }.objectEncrypt()
            )
        }
    }

    /**
     * 发送聊天消息
     */
    suspend fun sendChatMsg(pendingSendMsg: ChatMsgItemData): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.sendConversationMsg(pendingSendMsg)
        }
    }

    /**
     * 根据会话id[conversationId]获取会话内容列表
     */
    suspend fun getConversationContentList(
        conversationId: Int,
        pageIndex: Int,
        pageSize: Int
    ): ReqResponse<List<ChatMsgItemData>> {
        return httpRequest {
            _chatApi.getConversationContentList(ZPRequestBody().apply {
                put("id", conversationId)
                put("pageIndex", pageIndex)
                put("pageSize", pageSize)
            })
        }
    }

    /**
     * 获取不合适列表
     */
    suspend fun getBusinessMarkChatList(
        myUserId: Int,
        role: Int,
        pageIndex: Int,
        pageSize: Int,
    ): ReqResponse<List<ContactBodyBean>> {
        return httpRequest {
            _chatApi.getUnsuitableList(
                ZPRequestBody().apply {
                    if (role == ChatRole.PERSONAL) {
                        put("userID", myUserId)
                    } else {
                        put("cUserID", myUserId)
                    }
                    put("type", role)
                    put("pageIndex", pageIndex)
                    put("pageSize", pageSize)
                    put("source", -1)
                }
            )
        }
    }

    /**
     * 获取标记不合适的人数
     */
    suspend fun getBusinessMarkChatCount(): ReqResponse<MarkChatCountBean> {
        return httpRequest {
            _chatApi.getBusinessMarkChatCount()
        }
    }

    /**
     * 获取个人标记不感兴趣的企业数量
     */
    suspend fun getGeekMarkChatCount(): ReqResponse<MarkChatCountBean> {
        return httpRequest {
            _chatApi.getGeekMarkChatCount()
        }
    }

    /**
     * 标记会话
     * 0：合适 1：不合适
     */
    suspend fun markChat(chatID: Int, state: Int): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.markChat(
                ZPRequestBody().apply {
                    put("id", chatID)
                    put("buheshi", state)
                }
            )
        }
    }

    /**
     * 个人端标记不感兴趣
     */
    suspend fun geekMarkChat(chatID: Int, state: Int, reason: String = ""): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.geekMarkChat(
                ZPRequestBody().apply {
                    put("id", chatID)
                    put("dislike", state)
                    put("disContent", reason)
                }
            )
        }
    }

    /**
     * 获取面试详情
     */
    suspend fun getInterviewInfo(
        jobId: Int,
        resumeId: Int,
        role: Int
    ): ReqResponse<InterviewInfoBean> {
        return httpRequest {
            _chatApi.getInterviewInfo(
                ZPRequestBody().apply {
                    put("relid", jobId)
                    put("resid", resumeId)
                    put("lx", role)
                }
            )
        }
    }

    /**
     * 请求交换联系方式
     */
    suspend fun requestExchangeContactWay(
        chatID: Int,
        receiverRole: Int,
        myUserId: Int,
        friendUserId: Int,
        jobID: Int,
        resumeID: Int,
        sendType: Int
    ): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.requestExchangeContactWay(
                ZPRequestBody().apply {
                    put("ncID", chatID)
                    put("lx", receiverRole)
                    put("cuid", myUserId)
                    put("uid", friendUserId)
                    put("relId", jobID)
                    put("resid", resumeID)
                    put("type", sendType)
                }
            )
        }
    }

    /**
     * 同意交换联系方式
     */
    suspend fun agreeExchangeContactWay(
        chatID: Int,
        receiverRole: Int,
        myUserId: Int,
        friendUserId: Int,
        jobID: Int,
        resumeID: Int,
        sendType: Int,
        relateMsgId: Int,
    ): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.agreeExchangeContactWay(
                ZPRequestBody().apply {
                    put("ncID", chatID)
                    put("lx", receiverRole)
                    put("cuid", myUserId)
                    put("uid", friendUserId)
                    put("resid", resumeID)
                    put("relId", jobID)
                    put("type", sendType)
                    put("otherid", relateMsgId)
                }
            )
        }
    }

    /**
     * 拒绝交换联系方式
     */
    suspend fun refuseExchangeContactWay(
        chatID: Int,
        receiverRole: Int,
        myUserId: Int,
        friendUserId: Int,
        jobID: Int,
        resumeID: Int,
        sendType: Int,
        relateMsgId: Int
    ): ReqResponse<Nothing> {
        return httpRequest {
            _chatApi.refuseExchangeContactWay(
                ZPRequestBody().apply {
                    put("ncID", chatID)
                    put("cuid", myUserId)
                    put("uid", friendUserId)
                    put("lx", receiverRole)
                    put("relId", jobID)
                    put("resid", resumeID)
                    put("type", sendType)
                    put("otherid", relateMsgId)
                }
            )
        }
    }
}