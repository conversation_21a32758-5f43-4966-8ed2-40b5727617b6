package com.bxkj.jrzp.support.chat.ui.allcontact

import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.base.mvvm.viewmodel.RefreshListViewModel
import com.bxkj.common.data.ContactBodyBean
import com.bxkj.common.data.repository.ConversationRepository
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.enums.ChatRole.Companion.Role
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.getOrDefault
import com.bxkj.jrzp.support.chat.R
import com.bxkj.jrzp.support.chat.R.drawable
import com.bxkj.jrzp.support.chat.api.ChatRepo
import com.bxkj.jrzp.support.chat.data.ContactFeatureBean
import com.bxkj.jrzp.support.chat.data.ContactTopChatBean
import com.bxkj.jrzp.support.chat.data.NoReplyInfoBean
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
class AllContactViewModelV3 @Inject constructor(
    private val _commonConversationRepository: ConversationRepository,
    private val _chatRepo: ChatRepo,
) : BaseViewModel() {

    val contactListViewModel = RefreshListViewModel()

    private var _contractTopChatBean: ContactTopChatBean? = null

    private var _chatRole = ChatRole.ALL

    private var _filterJobId = 0

    private var _contactType: Int = AllContactFragment.NORMAL

    private val _topContactLayoutPosition = if (UserUtils.isPersonalRole()) 1 else 0

    init {
        if (_contactType == AllContactFragment.NORMAL) {
            contactListViewModel.refreshLayoutViewModel.enableLoadMore(false)
        }
        contactListViewModel.setOnLoadDataListener {
            viewModelScope.launch {
                if (_contactType == AllContactFragment.NORMAL) {
                    loadNormalContactList()
                } else {
                    loadUnsuitableContactList(it)
                }
            }
        }
    }

    fun setPageParams(contactType: Int?) {
        this._contactType = contactType.getOrDefault(AllContactFragment.NORMAL)
        if (contactType != AllContactFragment.NORMAL) {
            contactListViewModel.refresh()
        }
    }

    fun switchRole(@Role role: Int) {
        _chatRole = role
        contactListViewModel.refresh(true)
    }

    fun filterChatByJobId(jobId: Int) {
        _filterJobId = jobId
        contactListViewModel.refresh(true)
    }

    fun setAllMsgHasRead() {
        viewModelScope.launch {
            showLoading()
            _commonConversationRepository.setupMsgHasRead(getSelfUserID(), 0)
                .handleResult({
                    contactListViewModel.refresh(false)
                    showToast("设置成功")
                }, {
                    showToast(it.errMsg)
                }, {
                    hideLoading()
                })
        }
    }

    fun upOrCancelUpMsg(item: ContactBodyBean) {
        viewModelScope.launch {
            showLoading()
            _commonConversationRepository.upOrCancelUpMsg(
                item.id,
                ChatRole.getConvertLocalRole()
            ).handleResult({
                contactListViewModel.hiddenPageStatusLayout()
                if (!item.isOnTop()) { //未置顶
                    contactListViewModel.remove(item)
                    item.top()
                    //不存在置顶的item
                    if (contactListViewModel.data[_topContactLayoutPosition] !is ContactTopChatBean) {
                        if (_contractTopChatBean == null) {
                            _contractTopChatBean = ContactTopChatBean()
                        }
                        contactListViewModel.add(
                            if (hasNoReplyFriend && !UserUtils.isPersonalRole()) _topContactLayoutPosition + 1 else _topContactLayoutPosition,
                            _contractTopChatBean
                        )
                    }
                    _contractTopChatBean?.addChat(item)
                } else {
                    if (_contractTopChatBean?.removeChat(item) == 0) {
                        contactListViewModel.remove(_contractTopChatBean)
                    }
                    item.cancelTop()
                    contactListViewModel.add(_topContactLayoutPosition + 1, item)
                }
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
        }
    }

    fun deleteMsg(
        item: ContactBodyBean,
        isTopChat: Boolean = false,
    ) {
        viewModelScope.launch {
            showLoading()
            _commonConversationRepository.deleteConversation(
                item.id,
                ChatRole.getConvertLocalRole()
            ).handleResult({
                if (contactListViewModel.remove(item) == 0) {
                    contactListViewModel.refresh()
                }
                if (isTopChat) {
                    if (_contractTopChatBean?.removeChat(item) == 0) {
                        contactListViewModel.remove(_topContactLayoutPosition)
                    }
                }
            }, {
                showToast(it.errMsg)
            }, {
                hideLoading()
            })
        }
    }

    private fun refreshTopContactList() {
        viewModelScope.launch {
            if (_contractTopChatBean == null) {
                _commonConversationRepository.getTopContactList(
                    getSelfUserID(),
                    ChatRole.getConvertLocalRole(),
                    _chatRole
                ).handleResult({
                    _contractTopChatBean = ContactTopChatBean().apply {
                        chatList = ArrayList(it)
                    }
                }, {
                    if (!it.isNoDataError) {
                        contactListViewModel.remove(_topContactLayoutPosition)
                    }
                }, {
                    refreshNoReplyInfo()
                })
            }

            _contractTopChatBean?.let {
                if (contactListViewModel.data.size > 0 && !it.chatList.isNullOrEmpty() && contactListViewModel.data.size >= _topContactLayoutPosition) {
                    if (contactListViewModel.data[_topContactLayoutPosition] is ContactTopChatBean) {
                        contactListViewModel.replace(_topContactLayoutPosition, it)
                    } else {
                        contactListViewModel.add(_topContactLayoutPosition, it)
                    }
                }
                refreshNoReplyInfo()
            }

        }
    }

    private var hasNoReplyFriend = false

    private fun refreshNoReplyInfo() {
        if (!UserUtils.isPersonalRole()) {
            viewModelScope.launch {
                _chatRepo.getNoReplyInfo(getSelfUserID())
                    .handleResult({
                        if (contactListViewModel.data.isNotEmpty()) {
                            if (contactListViewModel.data[0] is NoReplyInfoBean) {
                                contactListViewModel.replace(0, it)
                            } else {
                                contactListViewModel.add(0, it)
                            }
                            hasNoReplyFriend = true
                        }
                    }, {
                        if (contactListViewModel.data.isNotEmpty()) {
                            if (contactListViewModel.data[0] is NoReplyInfoBean) {
                                contactListViewModel.removeAt(0)
                            }
                            hasNoReplyFriend = false
                        }
                    })
            }
        }
    }

    private fun refreshMarkChat() {
        viewModelScope.launch {
            if (UserUtils.isPersonalRole()) {
                _chatRepo.getGeekMarkChatCount().handleResult({
                    if (it?.count.getOrDefault() > 0) {
                        contactListViewModel.add(
                            contactListViewModel.data.size,
                            ContactFeatureBean(
                                R.drawable.chat_ic_mark_improper,
                                "不感兴趣的企业",
                                "${it?.count}个企业",
                                ContactFeatureBean.JUMP_TAG_UNSUITABLE
                            )
                        )
                    }
                })
            } else {
                _chatRepo.getBusinessMarkChatCount().handleResult({
                    if (it?.count.getOrDefault() > 0) {
                        contactListViewModel.add(
                            contactListViewModel.data.size,
                            ContactFeatureBean(
                                R.drawable.chat_ic_mark_improper,
                                "不合适的人才",
                                "${it?.count}个联系人",
                                ContactFeatureBean.JUMP_TAG_UNSUITABLE
                            )
                        )
                    }
                })
            }
        }
    }

    /**
     * 加载不合适的联系人列表
     */
    private fun loadUnsuitableContactList(currentPage: Int) {
        if (UserUtils.isPersonalRole()) {
            viewModelScope.launch {
                _commonConversationRepository.getConversationList(
                    getSelfUserID(),
                    ChatRole.getConvertLocalRole(),
                    _chatRole,
                    currentPage,
                    20,
                    _filterJobId,
                    true
                ).handleResult({
                    contactListViewModel.autoAddAll(it)
                }, {
                    if (it.isNetworkError) {
                        contactListViewModel.showLoadErrorPage()
                    } else {
                        contactListViewModel.noMoreData()
                    }
                })
            }
        } else {
            viewModelScope.launch {
                _chatRepo.getBusinessMarkChatList(
                    getSelfUserID(),
                    ChatRole.getConvertLocalRole(),
                    currentPage,
                    20
                ).handleResult({
                    contactListViewModel.autoAddAll(it)
                }, {
                    if (it.isNetworkError) {
                        contactListViewModel.showLoadErrorPage()
                    } else {
                        contactListViewModel.noMoreData()
                    }
                })
            }
        }
    }

    /**
     * 加载普通的联系人列表
     */
    private fun loadNormalContactList() {
        viewModelScope.launch {
            _commonConversationRepository.getConversationList(
                getSelfUserID(),
                ChatRole.getConvertLocalRole(),
                _chatRole,
                1,
                200,
                _filterJobId
            ).handleResult({
                contactListViewModel.reset(it)
                insertLatestJobInfo()
                refreshMarkChat()
            }, {
                if (it.isNetworkError) {
                    contactListViewModel.showLoadErrorPage()
                } else {
                    contactListViewModel.noMoreData()
                }
            }, {
                refreshTopContactList()
            })
        }
    }

    private fun insertLatestJobInfo() {
        if (UserUtils.isPersonalRole()) {
            contactListViewModel.add(
                0,
                ContactFeatureBean(
                    drawable.chat_ic_feature_latest_job,
                    "最新职位",
                    "为您推荐最新职位",
                    ContactFeatureBean.JUMP_TAG_0
                )
            )
        }
    }
}