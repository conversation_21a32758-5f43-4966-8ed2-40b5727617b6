package com.bxkj.jrzp.support.chat.widget.exchangephonenumdialog

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bxkj.common.base.mvvm.VMEvent
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.data.repository.CommonRepository
import com.bxkj.common.network.handleResult
import com.bxkj.common.util.kotlin.startCountDown
import com.bxkj.jrzp.user.repository.OpenUserRepository
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Description:
 * Author:45457
 **/
class ExchangePhoneNumberViewModel @Inject constructor(
  private val _commonRepo: CommonRepository,
  private val _openUserRepo: OpenUserRepository,
) : BaseViewModel() {

  val showSendSmsLayout = MutableLiveData<Boolean>()

  val showPhoneNumber = MutableLiveData<String>()
  val editPhoneNumber = MutableLiveData<String>()

  val smsCode = MutableLiveData<String>()

  val smdCodeCountdown = MutableLiveData<Int>().apply { value = 0 }

  val confirmEvent = MutableLiveData<VMEvent<Unit>>()

  private var _serviceCode: String? = null
  private var _receivedSmsCodePhoneNum: String = ""

  fun setPhoneNumber(phoneNumber: String) {
    this.showPhoneNumber.value = phoneNumber
  }

  fun showChangePhoneNum() {
    showSendSmsLayout.value = true
  }

  fun requestSmsCode() {
    val phoneNumber = editPhoneNumber.value
    if (phoneNumber.isNullOrBlank()) {
      showToast("请填写手机号后发送")
    } else {
      viewModelScope.launch {
        showLoading()
        _commonRepo.requestSmsCode(phoneNumber).handleResult({
          _serviceCode = it
          _receivedSmsCodePhoneNum = phoneNumber
          startCountDown(60, {
            smdCodeCountdown.value = it
          })
        }, {
          showToast(it.errMsg)
        }, {
          hideLoading()
        })
      }
    }
  }

  fun confirm() {
    if (showSendSmsLayout.value == true) {
      val phoneNumber = editPhoneNumber.value
      val smsCode = smsCode.value
      if (phoneNumber.isNullOrBlank()) {
        showToast("请填写手机号")
        return
      }
      if (phoneNumber != _receivedSmsCodePhoneNum) {
        showToast("手机号与接收验证码的手机号不一致")
        return
      }
      if (smsCode.isNullOrBlank()) {
        showToast("请填写验证码")
        return
      }
      if (smsCode != _serviceCode) {
        showToast("验证码错误")
        return
      }
      viewModelScope.launch {
        showLoading()
        _openUserRepo.updateUserContactPhone(phoneNumber, smsCode)
          .handleResult({
            confirmEvent.value = VMEvent(Unit)
          }, {
            showToast(it.errMsg)
          }, {
            hideLoading()
          })
      }
    } else {
      confirmEvent.value = VMEvent(Unit)
    }
  }
}