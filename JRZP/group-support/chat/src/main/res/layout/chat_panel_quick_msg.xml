<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:layout_width="match_parent"
    android:layout_height="300dp"
    android:orientation="vertical"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <include
        android:id="@+id/include_quick_msg"
        layout="@layout/include_mvvm_refresh_layout" />

    <View style="@style/Line.Horizontal.Light" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <FrameLayout
            android:id="@+id/fl_quick_msg_add"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:onClick="@{onClickListener}">

            <TextView
                style="@style/Text.16sp.333333"
                android:layout_gravity="center"
                android:drawablePadding="@dimen/dp_4"
                android:drawableStart="@drawable/chat_ic_quick_msg_add"
                android:gravity="center"
                android:paddingBottom="@dimen/dp_8"
                android:paddingTop="@dimen/dp_8"
                android:text="@string/chat_quick_msg_add" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_quick_msg_management"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <TextView
                style="@style/Text.16sp.333333"
                android:layout_gravity="center"
                android:drawablePadding="@dimen/dp_4"
                android:drawableStart="@drawable/chat_ic_quick_msg_management"
                android:gravity="center"
                android:paddingBottom="@dimen/dp_8"
                android:paddingTop="@dimen/dp_8"
                android:text="@string/chat_quick_msg_management" />
        </FrameLayout>

    </LinearLayout>
</LinearLayout>
