<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.bxkj.jrzp.support.chat.ui.chatsayhellosetting.CommonChatSayHelloSettingViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_msg_type"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:background="@drawable/bg_f4f4f4" />

        <include
            android:id="@+id/include_msg_list"
            layout="@layout/include_mvvm_refresh_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="7"
            android:visibility="@{viewModel.showCustomQuickMsgLayout?View.GONE:View.VISIBLE}"
            app:listViewModel="@{viewModel.quickMsgListViewModel}" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="7"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:visibility="@{viewModel.showCustomQuickMsgLayout?View.VISIBLE:View.GONE}">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_custom_quick_msg_list"
                style="@style/match_wrap"
                bind:items="@{viewModel.customQuickMsgList}" />

            <TextView
                style="@style/Text.14sp.FE6600"
                android:layout_marginTop="@dimen/common_dp_60"
                android:background="@drawable/frame_fe6600_round"
                android:onClick="@{()->viewModel.addOrEditCustomQuickMsg()}"
                android:paddingBottom="@dimen/dp_4"
                android:paddingEnd="@dimen/dp_24"
                android:paddingStart="@dimen/dp_24"
                android:paddingTop="@dimen/dp_4"
                android:text="@string/chat_quick_msg_edit_custom" />

        </LinearLayout>

    </LinearLayout>
</layout>