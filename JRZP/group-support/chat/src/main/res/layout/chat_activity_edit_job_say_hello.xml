<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="viewModel"
            type="com.bxkj.jrzp.support.chat.ui.editjobsayhello.EditJobSayHelloViewModel" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            android:id="@+id/title_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            app:right_text="@string/save" />

        <LinearLayout
            style="@style/match_wrap"
            android:paddingBottom="14dp"
            android:paddingEnd="16dp"
            android:paddingStart="@dimen/dp_16"
            android:paddingTop="14dp">

            <TextView
                style="@style/Text.14sp.333333"
                android:text="职位" />

            <TextView
                style="@style/Text.14sp.666666"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="end"
                android:lines="1"
                android:text="@{viewModel.jobName}" />

        </LinearLayout>

        <EditText
            android:id="@+id/et_info"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="top|start"
            android:inputType="textMultiLine"
            android:lineSpacingExtra="@dimen/dp_4"
            android:maxLength="100"
            android:padding="@dimen/dp_16"
            android:hint="@string/chat_say_hello_text_hint"
            android:text="@={viewModel.text}"
            android:textColor="@color/cl_333333"
            android:textColorHint="@color/common_b5b5b5"
            android:textSize="@dimen/sp_14" />


    </LinearLayout>
</layout>