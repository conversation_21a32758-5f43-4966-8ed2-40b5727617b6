<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.bxkj.common.widget.CommonTitleBar
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_dp_44"
            app:title="@string/chat_setting_hello_text" />

        <TextView
            android:id="@+id/tv_hello_tip"
            style="@style/Text.14sp.333333"
            android:layout_marginBottom="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_14"
            android:layout_marginStart="@dimen/dp_14"
            android:layout_marginTop="@dimen/dp_10"
            android:text="@string/chat_setting_hello_tip" />

        <net.lucode.hackware.magicindicator.MagicIndicator
            android:id="@+id/indicator_type"
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:layout_marginEnd="@dimen/dp_14"
            android:layout_marginStart="@dimen/dp_14" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>
</layout>