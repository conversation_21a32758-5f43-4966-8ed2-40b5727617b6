plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'org.jetbrains.kotlin.android'
}

android {

    defaultConfig {
        compileSdk libs.versions.compileSdkVersion.get().toInteger()
        minSdkVersion libs.versions.minSdkVersion.get()
        targetSdkVersion libs.versions.targetSdkVersion.get()

      testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    buildFeatures {
        dataBinding = true
    }
  namespace 'com.sanjindev.mui'
}

dependencies {

    implementation libs.android.ktx
    implementation libs.appcompat
    implementation libs.material
    implementation libs.constraintlayout
    implementation libs.photoView

    compileOnly libs.glide
}