package com.sanjindev.mui.weiget

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import androidx.annotation.FloatRange
import androidx.customview.widget.ViewDragHelper
import kotlin.math.absoluteValue
import kotlin.math.min

/**
 * 可拖动关闭ImageView
 * @author: sanjin
 * @date: 2021/7/16
 */

private const val TAG = "DragCloseLayout"

class DragCloseLayout @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {

  private var contentView: View? = null

  private var viewDragHelper: ViewDragHelper? = null

  private var onDragEventEventListener: OnDragEventListener? = null

  private var dragCloseThreshold = 0.1f

  private var vMaxDragDistance = 0f

  init {
    viewDragHelper = ViewDragHelper.create(this, object : ViewDragHelper.Callback() {
      override fun tryCaptureView(child: View, pointerId: Int): Boolean {
        requestDisallowInterceptTouchEvent(true)
        return true
      }

      override fun clampViewPositionVertical(child: View, top: Int, dy: Int): Int {
        return top
      }

      override fun getViewVerticalDragRange(child: View): Int {
        return 1
      }

      override fun onViewPositionChanged(changedView: View, left: Int, top: Int, dx: Int, dy: Int) {
        super.onViewPositionChanged(changedView, left, top, dx, dy)
        val progress = 1 - min(top.absoluteValue / height.toFloat(), 1f)
        background.mutate().alpha = (255 * progress).toInt()
      }

      override fun onViewReleased(releasedChild: View, xvel: Float, yvel: Float) {
        if (releasedChild.y.absoluteValue > vMaxDragDistance) {
          onDragEventEventListener?.onClose()
        } else {
          viewDragHelper?.settleCapturedViewAt(0, 0)
          invalidate()
        }
      }
    })
  }

  override fun computeScroll() {
    super.computeScroll()
    if (viewDragHelper?.continueSettling(true) == true) {
      invalidate()
    }
  }

  override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
    super.onSizeChanged(w, h, oldw, oldh)
    vMaxDragDistance = h * dragCloseThreshold
  }

  override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
    return if (ev.pointerCount > 1) {
      false
    } else {
      viewDragHelper?.shouldInterceptTouchEvent(ev) ?: super.onInterceptTouchEvent(ev)
    }
  }

  override fun onTouchEvent(event: MotionEvent): Boolean {
    viewDragHelper?.processTouchEvent(event)
    return true
  }

  override fun addView(child: View?) {
    if (childCount == 1) {
      throw IllegalStateException("DragCloseLayout can host only one direct child")
    }
    super.addView(child)
  }

  override fun onFinishInflate() {
    if (childCount > 1) {
      throw IllegalStateException("DragCloseLayout can host only one direct child")
    }
    super.onFinishInflate()
  }

  fun setDragCloseThreshold(@FloatRange(from = 0.0, to = 1.0) threshold: Float) {
    dragCloseThreshold = threshold
    vMaxDragDistance = height * dragCloseThreshold
  }

  interface OnDragEventListener {
    fun onClose()
  }

  fun setOnDragEventListener(onDragEventListener: OnDragEventListener) {
    this.onDragEventEventListener = onDragEventListener
  }

}