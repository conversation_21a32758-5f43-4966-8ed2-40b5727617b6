package com.bxkj.jrzp.support.scan.ui.qrcode

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.View.OnClickListener
import com.bxkj.common.base.mvvm.BaseDBActivity
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.bxkj.common.util.DeviceUtils
import com.bxkj.common.util.PermissionUtils
import com.bxkj.common.util.PermissionUtils.OnRequestResultListener
import com.bxkj.common.util.imageloader.GlideEngine
import com.bxkj.common.util.imageloader.ImageCompressEngine
import com.bxkj.common.util.imageloader.SandboxFileEngine
import com.bxkj.common.util.kotlin.getSelectedFirstMediaPath
import com.bxkj.jrzp.support.scan.R
import com.bxkj.jrzp.support.scan.R.string
import com.bxkj.jrzp.support.scan.databinding.ScanActivityQrcodeBinding
import com.bxkj.jrzp.support.scan.lib.zxing.util.MyCodeUtils
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.ui.activity.scanloginconfirm.ScanLoginConfirmNavigation
import com.bxkj.personal.ui.activity.web.WebNavigation
import com.hjq.permissions.Permission
import com.king.zxing.CaptureHelper
import com.king.zxing.OnCaptureCallback
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig

/**
 * @Description: 扫描二维码
 * @author:45457
 * @date: 2020/11/6
 * @version: V1.0
 */
class ScanQrCodeActivity : BaseDBActivity<ScanActivityQrcodeBinding, BaseViewModel>(),
  OnCaptureCallback, OnClickListener {

  companion object {

    private const val LOGIN_TAG = "scanLogin"
    private const val HTTP_TAG = "http"

    fun newIntent(context: Context): Intent {
      return Intent(context, ScanQrCodeActivity::class.java)
    }
  }

  private var mCaptureHelper: CaptureHelper? = null

  override fun getViewModelClass(): Class<BaseViewModel> = BaseViewModel::class.java

  override fun getLayoutId(): Int = R.layout.scan_activity_qrcode

  override fun initPage(savedInstanceState: Bundle?) {
    viewBinding.onClickListener = this

    setupCaptureHelper()
  }

  private fun setupCaptureHelper() {
    mCaptureHelper =
      CaptureHelper(
        this,
        viewBinding.surfaceView,
        viewBinding.viewfinderView,
        null
      ).apply {
        continuousScan(true)
        setOnCaptureCallback(this@ScanQrCodeActivity)
      }

    mCaptureHelper?.let {
      it.onCreate()
      it.cameraManager.setOnTorchListener { open ->
        viewBinding.ivTorch.isSelected = open
        viewBinding.tvTorchStatus.text =
          getString(if (open) R.string.scan_torch_on else R.string.scan_torch_off)
      }
    }
    checkCameraPermission()
  }

  private fun checkCameraPermission() {
    PermissionUtils.requestPermission(
      this,
      getString(string.scan_qr_code_permission_tips_title),
      getString(string.scan_qr_code_permission_tips_content),
      object : OnRequestResultListener {
        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          finish()
        }

        override fun onRequestSuccess() {
          mCaptureHelper?.let {
            it.onPause()
            it.onResume()
          }
        }
      },
      Permission.CAMERA
    )
  }

  override fun onClick(v: View?) {
    if (v != null) {
      when (v.id) {
        R.id.iv_back -> {
          finish()
        }

        R.id.ll_torch -> {
          if (DeviceUtils.hasCameraFlash(this)) {
            mCaptureHelper?.cameraManager?.setTorch(!viewBinding.ivTorch.isSelected)
          }
        }

        R.id.iv_select_pic -> {
          PermissionUtils.requestPermission(
            this,
            getString(R.string.permission_tips_title),
            getString(R.string.permission_only_select_img_tips),
            object : PermissionUtils.OnRequestResultListener {
              override fun onRequestSuccess() {
                PictureSelector.create(this@ScanQrCodeActivity)
                  .openGallery(SelectMimeType.ofImage())
                  .setSelectionMode(SelectModeConfig.SINGLE)
                  .setImageEngine(GlideEngine.getInstance())
                  .setCompressEngine(ImageCompressEngine.getInstance())
                  .setSandboxFileEngine(SandboxFileEngine.getInstance())
                  .forResult(PictureConfig.CHOOSE_REQUEST)
              }

              override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
                showToast(getString(R.string.cancel_operation))
              }
            },
            Permission.WRITE_EXTERNAL_STORAGE,
            Permission.READ_EXTERNAL_STORAGE
          )
        }
      }
    }
  }

  override fun onResume() {
    super.onResume()
    mCaptureHelper?.onResume()
  }

  override fun onPause() {
    super.onPause()
    mCaptureHelper?.onPause()
  }

  override fun onDestroy() {
    super.onDestroy()
    mCaptureHelper?.onDestroy()
  }

  override fun onTouchEvent(event: MotionEvent): Boolean {
    mCaptureHelper?.onTouchEvent(event)
    return super.onTouchEvent(event)
  }

  override fun onResultCallback(result: String?): Boolean {
    handleScanResult(result)
    return true
  }

  private fun handleScanResult(result: String?) {
    result?.let {
      when {
        result.contains(LOGIN_TAG) -> { //登录码
          afterLogin {
            val channel = result.substring(result.indexOf("-") + 1)
            ScanLoginConfirmNavigation.navigate(channel).start()
          }
        }

        result.contains(HTTP_TAG) -> {
          val uri = Uri.parse(result)
          val jumpAction = uri.getQueryParameter("action")
          jumpAction?.let {
            if (it == "1") {  //招聘号
              val uid = uri.getQueryParameter("uid")
              uid?.let {
                UserHomeNavigation.navigate(uid.toInt()).start()
                finish()
              }
            } else {
              WebNavigation.navigate(uri.toString()).start()
            }
          } ?: WebNavigation.navigate(uri.toString()).start()
        }

        else -> {
          showToast(getString(R.string.scan_qr_code_no_jrzp_content))
        }
      }
    } ?: showToast(getString(R.string.scan_qr_code_no_content))
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (requestCode == PictureConfig.CHOOSE_REQUEST) {
      if (resultCode == Activity.RESULT_OK && data != null) {
        val imgPath = data.getSelectedFirstMediaPath()
        handleScanResult(MyCodeUtils.parseQRCode(imgPath))
      }
    }
  }
}