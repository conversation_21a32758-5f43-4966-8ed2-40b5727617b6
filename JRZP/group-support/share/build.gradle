plugins {
  id 'kotlin-kapt'
  id 'com.android.library'
  id 'org.jetbrains.kotlin.android'
}

android {

  defaultConfig {
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()

    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    consumerProguardFiles "consumer-rules.pro"
  }

  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }

  buildFeatures {
    dataBinding = true
  }
  namespace 'com.bxkj.share'
}

dependencies {
  implementation fileTree(dir: "libs", include: ["*.jar"])

  implementation project(":lib-common")

  implementation project(":group-support:scan")
}