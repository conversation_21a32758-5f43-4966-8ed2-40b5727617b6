<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!--不可点击状态-->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/dp_50" />
            <solid android:color="@color/common_e8e8e8" />
        </shape>
    </item>

    <!--可点击状态-->
    <item android:state_enabled="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/dp_50" />
            <solid android:color="@color/cl_ff7405" />
        </shape>
    </item>

    <!--点击变色-->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/dp_50" />
            <solid android:color="@color/cl_ff7405" />
        </shape>
    </item>
</selector>