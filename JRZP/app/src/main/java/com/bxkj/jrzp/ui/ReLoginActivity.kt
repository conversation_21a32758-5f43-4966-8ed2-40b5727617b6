package com.bxkj.jrzp.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.NotificationManagerCompat
import com.therouter.TheRouter
import com.bxkj.common.base.BaseApplication
import com.bxkj.common.databinding.ActivityActionDialogBinding
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.jrzp.support.db.AppDatabase

/**
 *
 * @author: sanjin
 * @date: 2022/1/19
 */
class ReLoginActivity : AppCompatActivity() {

    companion object {

        private const val EXTRA_TITLE = "TITLE"
        private const val EXTRA_CONTENT = "CONTENT"
        private const val EXTRA_CONFIRM_TEXT = "CONFIRM_TEXT"

        @JvmStatic
        @JvmOverloads
        fun newIntent(
            context: Context,
            title: String?,
            content: String?,
            confirmText: String?
        ): Intent {
            return Intent(context, ReLoginActivity::class.java).apply {
                putExtra(EXTRA_TITLE, title)
                putExtra(EXTRA_CONTENT, content)
                putExtra(EXTRA_CONFIRM_TEXT, confirmText)
            }.apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
        }
    }

    private lateinit var activityActionDialogBinding: ActivityActionDialogBinding
    private val title by lazy { intent.getStringExtra(EXTRA_TITLE) }
    private val content by lazy { intent.getStringExtra(EXTRA_CONTENT) }
    private val confirmText by lazy { intent.getStringExtra(EXTRA_CONFIRM_TEXT) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityActionDialogBinding = ActivityActionDialogBinding.inflate(layoutInflater)
        setContentView(activityActionDialogBinding.root)

        setFinishOnTouchOutside(false)

        title?.let {
            activityActionDialogBinding.tvDialogTitle.text = title
        }

        content?.let {
            activityActionDialogBinding.tvDialogContent.text = content
        }

        confirmText?.let {
            activityActionDialogBinding.tvDialogConfirm.text = confirmText
        }

        activityActionDialogBinding.tvDialogConfirm.setOnClickListener {
            AppDatabase.getInstance(this).userLoginHistoryDao()
                .deleteHistory(BaseApplication.getZPUserId())
            //清除用户信息
            //清除用户信息
            NotificationManagerCompat.from(this).cancelAll()
            UserUtils.removeUserData()
            val mainIntent: Intent =
                com.bxkj.personal.ui.activity.main.MainActivity.Companion.newIntent(this)
            mainIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            mainIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(mainIntent)
            TheRouter.build(RouterNavigation.LoginActivity.URL)
                .navigation()
            finish()
        }
    }

    override fun onBackPressed() {
    }

}