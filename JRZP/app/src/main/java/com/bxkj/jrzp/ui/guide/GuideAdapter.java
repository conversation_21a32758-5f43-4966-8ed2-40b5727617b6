package com.bxkj.jrzp.ui.guide;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.jrzp.ui.lead
 * @Description: 引导页适配器
 * @date 2018/5/15
 */

public class GuideAdapter extends PagerAdapter {

    private List<ImageView> mImageList;

    public GuideAdapter(List<ImageView> imageViewList) {
        mImageList = imageViewList;
    }

    @Override
    public int getCount() {
        return mImageList.size();
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        ImageView ivGuide = mImageList.get(position);
        container.addView(ivGuide);
        return ivGuide;
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView((View) object);
    }
}
