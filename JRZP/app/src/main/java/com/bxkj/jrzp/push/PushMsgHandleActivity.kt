package com.bxkj.jrzp.push

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.constants.PushConstants
import com.bxkj.common.data.PushMsg
import com.bxkj.common.enums.ChatRole
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.kotlin.AppStatusManager
import com.bxkj.enterprise.api.parameters.SearchResumeParameters
import com.bxkj.enterprise.ui.activity.conversation.BusinessChatContentActivity
import com.bxkj.enterprise.ui.activity.resumedetails.ApplicantResumeDetailActivityV2
import com.bxkj.enterprise.ui.activity.searchresumeresult.SearchResultActivity
import com.bxkj.jrzp.AppApplication
import com.bxkj.jrzp.live.audience.ui.audience.LiveAudienceActivity
import com.bxkj.common.enums.AuthenticationType
import com.bxkj.jrzp.userhome.ui.homepage.UserHomeNavigation
import com.bxkj.personal.ui.activity.answerdetails.AnswerDetailsActivity
import com.bxkj.personal.ui.activity.conversation.GeekChatContentActivity
import com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsActivity
import com.elvishew.xlog.XLog
import java.net.URLDecoder

/**
 * 推送消息处理Activity
 * @author: sanjin
 * @date: 2022/1/11
 */
class PushMsgHandleActivity : AppCompatActivity() {

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    if (UserUtils.logged()) {
      handlePushUrlParams()
    } else {
      (application as AppApplication).showAuthFailedDialog("无账号信息，请登录")
    }
    finish()
  }

  private fun handlePushUrlParams() {
    var nextIntent: Intent? = null
    val pushMsg = parsePushMsg()
    pushMsg?.let {
      XLog.d("handlePushUrlParams: $pushMsg")
      XLog.d(
        "appIsAlive: ${
          SystemUtil.isAppAlive(
            this,
            this.packageName
          )
        }, 主页是否启动: ${AppConstants.MAIN_START},appIsForeground: ${AppStatusManager.isForeground()}"
      )
      if (SystemUtil.isAppAlive(this, this.packageName) && AppConstants.MAIN_START) {
        when (pushMsg.getMsgType()) {
          //收到投递邀请、面试邀请、收到简历、接受投递邀请、接受面试邀请、面试结果
          PushConstants.MSG_TYPE_RECEIVE_DELIVERY_INVITE,
          PushConstants.MSG_TYPE_RECEIVE_INTERVIEW_INVITATION,
          PushConstants.MSG_TYPE_CONVERSATION_MSG,
          PushConstants.MSG_TYPE_RECEIVE_RESUME,
          PushConstants.MSG_TYPE_ACCEPT_DELIVERY_INVITATION,
          PushConstants.MSG_TYPE_ACCEPT_INTERVIEW_INVITATION,
          PushConstants.MSG_INTERVIEW_RESULT_ACCEPT,
          PushConstants.MSG_INTERVIEW_RESULT_REJECT -> {
            nextIntent = getChatMsgIntent(pushMsg)
          }
          //邀请回答
          PushConstants.MSG_TYPE_INVITE_ANSWER -> {
            nextIntent = getQuestionIntent(pushMsg)
          }
          //新回答
          PushConstants.MSG_TYPE_NEW_ANSWER -> {
            nextIntent = getAnswerIntent(pushMsg)
          }
          //开启直播
          PushConstants.MSG_TYPE_START_LIVE -> {
            nextIntent = getLiveRoomIntent(pushMsg)
          }
          //企业查看个人
          PushConstants.MSG_COMPANY_VIEW_PERSONAL -> {
            nextIntent = getCompanyHomeIntent(pushMsg)
          }
          //个人查看企业
          PushConstants.MSG_PERSONAL_VIEW_COMPANY -> {
            nextIntent = getResumeDetailsIntent(pushMsg)
          }
          //推荐简历
          PushConstants.MSG_RECOMMEND_RESUME -> {
            nextIntent = getRecommendResumeIntent(pushMsg)
          }

          else -> {
          }
        }
      } else {
        nextIntent =
          this.packageManager.getLaunchIntentForPackage(AppConstants.APP_PACKAGE_NAME)
            ?.apply {
              putExtra(PushConstants.PUSH_PARAMS, pushMsg)
            }
      }
    }
    nextIntent?.let {
      startActivity(it)
    }
  }

  private fun getRecommendResumeIntent(pushMsg: PushMsg): Intent? {
    if (pushMsg.recommendResumeTypes.isNullOrBlank()) {
      return null
    } else {
      return SearchResultActivity.newIntent(
        this, SearchResumeParameters()
          .apply {
            filterResumeTypes = pushMsg.recommendResumeTypes
          })
    }
  }

  private fun getResumeDetailsIntent(pushMsg: PushMsg): Intent? {
    if (pushMsg.resumeId.isNullOrBlank() || pushMsg.userId.isNullOrBlank()) {
      return null
    } else {
      return ApplicantResumeDetailActivityV2.newIntent(
        this,
        pushMsg.resumeId!!.toInt(),
        pushMsg.userId!!.toInt()
      )
    }
  }

  private fun getCompanyHomeIntent(pushMsg: PushMsg): Intent? {
    if (pushMsg.userId.isNullOrBlank()) {
      return null
    } else {
      return UserHomeNavigation.navigate(pushMsg.userId!!.toInt())
        .createIntent(this)
    }
  }

  private fun getAnswerIntent(pushMsg: PushMsg): Intent? {
    if (pushMsg.questionTitle.isNullOrBlank() || pushMsg.answerID.isNullOrBlank()) {
      return null
    }
    return AnswerDetailsActivity.newIntent(
      this,
      pushMsg.questionTitle,
      pushMsg.answerID!!.toInt()
    )
  }

  private fun getQuestionIntent(pushMsg: PushMsg): Intent? {
    pushMsg.questionID?.let {
      return QuestionDetailsActivity.newIntent(this, it.toInt())
    } ?: let {
      return null
    }
  }

  private fun parsePushMsg(): PushMsg? {
    var pushMsg: PushMsg? = null
    try {
      intent?.let {
        intent.data?.getQueryParameter(PushConstants.PUSH_PARAMS)?.let { params ->
          pushMsg = PushMsg.fromJsonString(URLDecoder.decode(params, "UTF-8"))
        } ?: let {
          pushMsg = intent.getParcelableExtra(PushConstants.PUSH_PARAMS)
        }
      }
    } catch (e: Exception) {
      return pushMsg
    }
    return pushMsg
  }

  private fun getChatMsgIntent(pushMsg: PushMsg): Intent? {
    if (pushMsg.userType.isNullOrBlank() || pushMsg.senderUserId.isNullOrBlank()) {
      return null
    }

    val nextIntent: Intent?

    val appReceiveUserType =
      if (pushMsg.userType.equals("0")) ChatRole.PERSONAL else ChatRole.BUSINESS

    UserUtils.setupUserRole(if (appReceiveUserType == ChatRole.PERSONAL) AuthenticationType.PERSONAL else AuthenticationType.ENTERPRISE)

    nextIntent = if (appReceiveUserType == ChatRole.PERSONAL) {
      GeekChatContentActivity.newIntent(
        this,
        appReceiveUserType,
        pushMsg.senderUserId!!.toInt()
      )
    } else {
      BusinessChatContentActivity.newIntent(
        this,
        appReceiveUserType,
        pushMsg.senderUserId!!.toInt()
      )
    }
    return nextIntent.apply {
      flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
    }
  }

  private fun getLiveRoomIntent(pushMsg: PushMsg?): Intent? {
    if (pushMsg == null || pushMsg.liveID.isNullOrBlank() || pushMsg.roomID.isNullOrBlank()) {
      return null
    }
    return LiveAudienceActivity.newIntent(this, pushMsg.liveID!!.toInt(), pushMsg.roomID!!)
  }
}