package com.bxkj.jrzp

import android.content.Context
import com.bumptech.glide.Glide
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.integration.okhttp3.OkHttpUrlLoader
import com.bumptech.glide.load.model.GlideUrl
import com.bumptech.glide.module.AppGlideModule
import com.bxkj.common.util.imageloader.GlideOkHttpClientManager
import com.elvishew.xlog.XLog
import java.io.InputStream

/**
 * @date 2019/10/31
 */
@GlideModule
class OkHttpGlideModule : AppGlideModule() {
  override fun registerComponents(
    context: Context, glide: Glide,
    registry: Registry
  ) {
    registry.replace<GlideUrl?, InputStream?>(
      GlideUrl::class.java, InputStream::class.java,
      OkHttpUrlLoader.Factory(GlideOkHttpClientManager.getGlideOkHttpClient())
    )
  }
}
