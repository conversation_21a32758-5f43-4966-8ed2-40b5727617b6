plugins {
  id 'com.android.application'
  id 'therouter'
  id 'kotlin-kapt'
  id 'org.jetbrains.kotlin.android'
  id 'com.huawei.agconnect'
  id 'com.google.devtools.ksp'
}

//apply from: "${project.rootProject.file('projectDependencyGraph.gradle')}"

android {

  defaultConfig {
    applicationId "com.bxkj.jrzp"
    compileSdk libs.versions.compileSdkVersion.get().toInteger()
    minSdkVersion libs.versions.minSdkVersion.get()
    targetSdkVersion libs.versions.targetSdkVersion.get()
    versionCode libs.versions.versionCode.get().toInteger()
    versionName libs.versions.versionName.get()
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    resourceConfigurations += ['zh']
    multiDexEnabled true
    ndk {
      abiFilters 'armeabi-v7a', 'arm64-v8a'
    }
    //TPNS推送配置
    manifestPlaceholders = [
        XG_ACCESS_ID: "1580009181",
        XG_ACCESS_KEY: "AA99FKQQN3EQ",

        VIVO_APPID: "100213008",
        VIVO_APPKEY: "5f4aac3b61da22a0416a3b7812594cfd"
    ]
  }

  buildFeatures {
    dataBinding = true
  }

  dependenciesInfo {
  }

  signingConfigs {
    myConfig {
      storePassword "jdzjandjrzp"
      keyAlias "bxkj"
      keyPassword "jdzjandjrzp"
      storeFile file("../bxkj.store")
    }
  }

  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
      shrinkResources false
    }
    debug {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
      signingConfig signingConfigs.myConfig
      shrinkResources false
    }
  }

  externalNativeBuild {
    // Encapsulates your CMake build configurations.
    cmake {
      // Provides a relative path to your CMake build script.
      path "CMakeLists.txt"
    }
  }

  android.applicationVariants.all {
    variant ->
      variant.outputs.all {
        outputFileName = "com.bxkj.jrzp_${variant.versionName}_${variant.versionCode}.apk"
      }
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_17
    sourceCompatibility JavaVersion.VERSION_17
  }

  kotlinOptions {
    jvmTarget = "17"
  }
  
  namespace 'com.bxkj.jrzp'
  lint {
    abortOnError false
  }

  buildFeatures {
    dataBinding true
    buildConfig true
  }
  
}

dependencies {
  implementation fileTree(dir: 'libs', include: ['*.jar'])

  //    def privacyVersion = "1.2.7"
  //    implementation "com.github.allenymt.PrivacySentry:hook-sentry:$privacyVersion"
  //    implementation "com.github.allenymt.PrivacySentry:privacy-annotation:$privacyVersion"

  ksp libs.dagger.complier
  ksp libs.dagger.android.processor
  kapt libs.glide.compiler

  implementation project(":lib-common")

  implementation project(':accountmodule')
  implementation project(':personal')
  implementation project(':group-business:video')
  implementation project(':group-business:learning')
  implementation project(':group-business:user')
  implementation project(':group-business:live')

  implementation project(':group-support:upload')
  implementation project(':group-support:comment')
  implementation project(':group-support:feedback')
  implementation project(':group-support:yd_login')
  implementation project(':group-support:feature')
  implementation project(':group-support:scan')
  implementation project(':group-support:db')
  implementation project(':group-support:chat')

  implementation project(':group-enterprise:enterprise')
  implementation project(':group-enterprise:commonlib')

  implementation libs.tpns.vivo
  implementation libs.tpns.xiaomi
  implementation libs.tpns.huawei
  implementation 'com.huawei.hms:push:6.12.0.300'
  implementation libs.tpns.oppo
  implementation 'commons-codec:commons-codec:1.15'

  //  implementation libs.splashscreen

  //    lintChecks project(':lintlib')
}