<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  style="@style/match_match"
  android:orientation="vertical">

  <include layout="@layout/common_include_title_bar" />

  <!--手机注册-->
  <LinearLayout
    android:id="@+id/ll_register_of_mobile"
    style="@style/match_wrap"
    android:layout_marginStart="@dimen/dp_30"
    android:layout_marginEnd="@dimen/dp_30"
    android:divider="@drawable/divider_f4f4f4"
    android:orientation="vertical"
    android:showDividers="middle|end">

    <com.bxkj.common.widget.MyEditText
      android:id="@+id/et_mobile_number"
      style="@style/EditText.Basic"
      android:layout_marginTop="@dimen/dp_48"
      android:hint="@string/account_enter_mobile_number"
      android:imeOptions="actionNext"
      android:inputType="phone" />

    <LinearLayout
      style="@style/match_wrap"
      android:orientation="horizontal">

      <com.bxkj.common.widget.MyEditText
        android:id="@+id/et_verification_code"
        style="@style/EditText.Basic"
        android:layout_marginEnd="@dimen/dp_10"
        android:layout_weight="1"
        android:width="0dp"
        android:hint="@string/account_enter_verification_code"
        android:imeOptions="actionNext"
        android:inputType="number" />

      <TextView
        android:id="@+id/tv_get_verification_code"
        style="@style/wrap_wrap"
        android:text="@string/account_get_verification_code"
        android:textColor="@color/cl_ff7405" />
    </LinearLayout>

    <com.bxkj.common.widget.PasswordEditText
      android:id="@+id/et_password"
      style="@style/EditText.Basic"
      android:digits="@string/common_A_z_0_9_digits"
      android:hint="@string/account_enter_password"
      android:imeOptions="actionDone"
      android:inputType="textPassword" />
  </LinearLayout>

  <TextView
    android:id="@+id/btn_register"
    style="@style/Button.Basic.Round"
    android:layout_marginStart="@dimen/dp_30"
    android:layout_marginTop="@dimen/common_dp_60"
    android:layout_marginEnd="@dimen/dp_30"
    android:enabled="false"
    android:text="@string/agreement_and_register" />

  <FrameLayout
    android:id="@+id/fl_register_tips"
    style="@style/match_wrap"
    android:layout_gravity="center">

    <TextView
      android:id="@+id/tv_register_tips"
      style="@style/Text.13sp.888888"
      android:layout_gravity="center_horizontal"
      android:layout_marginTop="@dimen/dp_10"
      android:drawableStart="@drawable/ic_privacy_selector"
      android:drawablePadding="@dimen/dp_4"
      android:gravity="center_vertical" />
  </FrameLayout>

</LinearLayout>