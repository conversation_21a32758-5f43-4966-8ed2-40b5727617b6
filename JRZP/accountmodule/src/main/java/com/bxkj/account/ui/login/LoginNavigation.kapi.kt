package com.bxkj.account.ui.login

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation
import com.bxkj.common.util.router.RouterNavigator

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/27
 * @version: V1.0
 */
class LoginNavigation {

  companion object {

    const val PATH = RouterNavigation.LoginActivity.URL

    const val EXTRA_NEED_BACK = RouterNavigation.LoginActivity.EXTRA_BACK_AFTER_LOGIN
    const val EXTRA_LOGIN_FOR_CREATE_RESUME = RouterNavigation.LoginActivity.LOGIN_FROM_CREATE_RESUME
    const val EXTRA_LOGIN_TYPE = "LOGIN_TYPE"

    @JvmStatic
    fun navigate(
      loginType: Int,
      needBack: Boolean = true,
      loginForCreateResume: Boolean = false
    ): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withInt(EXTRA_LOGIN_TYPE, loginType)
        .withBoolean(EXTRA_NEED_BACK, needBack)
        .withBoolean(EXTRA_LOGIN_FOR_CREATE_RESUME, loginForCreateResume)
    }
  }
}