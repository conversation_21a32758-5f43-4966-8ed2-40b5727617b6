package com.bxkj.account.api

import com.bxkj.account.data.LoginResultData
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.base.mvvm.BaseRepo
import com.bxkj.common.data.EncryptReqParams
import com.bxkj.common.data.JobTypeData
import com.bxkj.common.network.BaseResponse
import com.bxkj.common.network.CommonServices
import com.bxkj.common.network.ZPRequestBody
import com.bxkj.common.network.ReqResponse
import com.bxkj.common.util.AESOperator
import com.bxkj.common.util.kotlin.toIntOrDefault
import com.bxkj.jrzp.user.data.UserAuthStatusData
import com.lzf.easyfloat.permission.rom.RomUtils.checkIsOppoRom
import io.reactivex.Observable
import retrofit2.Retrofit

/**
 * <AUTHOR>
 * @version V1.0
 * @Description: 账户相关api
 * @date 2018/3/27
 */
class AccountApi(retrofit: Retrofit) : BaseRepo() {
  private val mCommonServices = retrofit.create(CommonServices::class.java)
  private val mAccountServices = retrofit.create(AccountServices::class.java)

  suspend fun updatePwdWithOldPwdV2(
    userId: Int,
    oldPwd: String,
    newPwd: String
  ): ReqResponse<Nothing> {
    return httpRequest {
      mAccountServices.updatePwdWithOldPwdV2(
        ZPRequestBody().apply {
          put("id", userId)
          putAndMD5("upwd", newPwd)
          putAndMD5("upwd2", oldPwd)
        }
      )
    }
  }

  /**
   * 修改密码
   */
  suspend fun updatePwdWithMobileNumberV2(
    mobileNumber: String,
    newPwd: String
  ): ReqResponse<Nothing> {
    return httpRequest {
      mAccountServices.updatePwdWithMobileNumberV2(
        ZPRequestBody().apply {
          put("mobile", mobileNumber)
          putAndMD5("upwd", newPwd)
        }
      )
    }
  }

  /**
   * 注册
   */
  suspend fun registerWithMobile(mobile: String, password: String, type: Int): ReqResponse<Int> {
    return httpRequest {
      mAccountServices.registerWithMobile(
        ZPRequestBody().apply {
          put("mobile", mobile)
          putAndMD5("upwd", password)
          put("type", type)
        }
      ).apply {
        if (requestSuccess()) {
          data = msg.toIntOrDefault(0)
        }
      }
    }
  }

  /**
   * 检查手机号是否注册
   */
  suspend fun checkMobileNumberIsRegistered(mobileNumber: String): ReqResponse<Nothing> {
    return httpRequest {
      mAccountServices.checkMobileNumberIsRegistered(
        ZPRequestBody().apply {
          put("mobile", mobileNumber)
        }
      )
    }
  }

  /**
   * 登录
   *
   * @param userName 用户名
   * @param password 密码
   */
  fun login(
    userName: String, password: String?,
    loginType: Int
  ): Observable<BaseResponse<LoginResultData>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["uname"] = userName
    mRequestBody.putAndMD5("upwd", password)
    mRequestBody["type"] = loginType
    mRequestBody[CommonApiConstants.SOURCE_PARAMS_NAME] = CommonApiConstants.SOURCE_ANDROID
    return mAccountServices.login(mRequestBody)
  }

  suspend fun loginV2(
    userName: String,
    password: String,
    loginType: Int
  ): ReqResponse<LoginResultData> {
    return httpRequest {
      mAccountServices.loginV2(
        ZPRequestBody().apply {
          put("uname", userName)
          putAndMD5("upwd", password)
          put("type", loginType)
          put(CommonApiConstants.SOURCE_PARAMS_NAME, CommonApiConstants.SOURCE_ANDROID)
        }
      ).apply {
        if (this.requestSuccess()) {
          this.data?.loginUserName = userName
        }
      }
    }
  }

  /**
   * 验证码登陆
   */
  suspend fun loginWithSmsCode(
    phoneNumber: String,
    smsCode: String,
    loginType: Int
  ): ReqResponse<LoginResultData> {
    return httpRequest {
      mAccountServices.loginWithSmsCode(
        ZPRequestBody().apply {
          put("mobile", phoneNumber)
          put("smsCode", smsCode)
          put("type", loginType)
          put(CommonApiConstants.SOURCE_PARAMS_NAME, CommonApiConstants.SOURCE_ANDROID)
        }
      ).apply {
        if (requestSuccess()) {
          data?.loginUserName = phoneNumber
        }
      }
    }
  }

  suspend fun checkUserBindPhone(userID: Int): ReqResponse<Nothing> {
    return httpRequest {
      mAccountServices.checkUserBindPhone(
        ZPRequestBody().apply {
          putAndAes("id2en", userID)
        }
      )
    }
  }

  /**
   * 手机注册
   *
   * @param mobile   手机号码
   * @param password 密码
   * @param type     注册类型（0、个人，1、企业）
   */
  fun registerOfMobile(mobile: String, password: String?, type: Int): Observable<BaseResponse<*>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["mobile"] = mobile
    mRequestBody.putAndMD5("upwd", password)
    mRequestBody["type"] = type
    return mCommonServices.basePost(
      CommonApiConstants.BASE_URL + AccountApiConstants.I_REGISTER_OF_MOBILE, mRequestBody
    )
  }

  /**
   * 邮箱注册
   *
   * @param userName 用户名
   * @param email    邮箱
   * @param password 密码
   * @param type     注册类型
   */
  fun registerOfEmail(
    userName: String, email: String, password: String?,
    type: Int
  ): Observable<BaseResponse<*>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["uname"] = userName
    mRequestBody["email"] = email
    mRequestBody.putAndMD5("upwd", password)
    mRequestBody["type"] = type
    return mCommonServices.basePost(
      CommonApiConstants.BASE_URL + AccountApiConstants.I_REGISTER_OF_EMAIL, mRequestBody
    )
  }

  /**
   * 检查手机号是否存在
   *
   * @param mobileNumber 手机号码
   */
  fun checkTheMobileNumberExists(mobileNumber: String): Observable<BaseResponse<*>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["mobile"] = mobileNumber
    return mCommonServices.basePost(
      CommonApiConstants.BASE_URL + AccountApiConstants.I_CHECK_THE_MOBILE_NUMBER_EXISTS,
      mRequestBody
    )
  }

  /**
   * 发送短信验证码
   */
  fun sendSmsVerificationCode(mobileNumber: String, sendType: Int): Observable<BaseResponse<*>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["mobile"] = AESOperator.safeEncrypt("$mobileNumber|-20")
    mRequestBody["type"] = sendType
    return mCommonServices.basePost(
      CommonApiConstants.BASE_URL + AccountApiConstants.I_SEND_SMS_VERIFICATION_CODE,
      mRequestBody
    )
  }

  suspend fun requestSmsCode(phoneNumber: String, smsCodeType: Int): ReqResponse<String> {
    return httpRequest {
      mAccountServices.requestSmsCode(
        ZPRequestBody().apply {
          putAndAes("mobile", "${phoneNumber}|-20")
          put("type", smsCodeType)
        }
      ).apply {
        if (requestSuccess()) {
          data = msg
        }
      }
    }
  }

  /**
   * 获取职位分类列表
   *
   * @param type         1为查询大类，2为查询小类
   * @param firstClassId 大类id，查询大类时为0
   */
  fun getJobClassList(type: Int, firstClassId: Int): Observable<BaseResponse<List<JobTypeData>>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["type"] = type
    mRequestBody["pid"] = firstClassId
    return mAccountServices.getJobClassList(mRequestBody)
  }

  /**
   * 添加用户期望的职位
   *
   * @param userId 用户id
   * @param jobIds 选中的职位id
   */
  fun addUserExpectedJobs(userId: Int, jobIds: String): Observable<BaseResponse<*>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["uid"] = userId
    mRequestBody["jid"] = jobIds
    return mCommonServices.basePost(
      CommonApiConstants.BASE_URL + AccountApiConstants.I_ADD_EXPECTED_JOBS, mRequestBody
    )
  }

  /**
   * 根据名字搜索职位
   *
   * @param type 1、大类2、子类
   * @param name 职位名字
   * @param top  查询条数
   */
  fun searchJobClassByName(
    type: Int, name: String,
    top: Int
  ): Observable<BaseResponse<List<JobTypeData>>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["type"] = type
    mRequestBody["name"] = name
    mRequestBody["top"] = top
    return mAccountServices.searchJobClassByName(mRequestBody)
  }

  /**
   * 批量删除期望职位
   *
   * @param userId 用户id
   * @param ids    要删除的id
   */
  fun deleteUserCheckedJobById(userId: Int, ids: String): Observable<BaseResponse<*>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["uid"] = userId
    mRequestBody["jid"] = ids
    return mCommonServices.basePost(
      CommonApiConstants.BASE_URL + AccountApiConstants.I_DELETE_USER_CHECKED_JOB,
      mRequestBody
    )
  }

  /**
   * 发送修改密码邮件
   */
  fun sendUpdatePasswordEmail(userName: String, email: String): Observable<BaseResponse<Any?>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["uname"] = userName
    mRequestBody["email"] = email
    return mAccountServices.sendUpdatePasswordEmail(mRequestBody)
  }

  /**
   * 根据手机修改密码
   */
  fun updatePasswordByMobile(mobile: String, newPwd: String?): Observable<BaseResponse<Any?>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["mobile"] = mobile
    mRequestBody.putAndMD5("upwd", newPwd)
    return mAccountServices.updatePasswordByMobile(mRequestBody)
  }

  /**
   * 根据老密码修改密码
   *
   * @param userId 用户id
   * @param newPwd 新密码
   * @param oldPwd 旧密码
   */
  fun updatePasswordByOldPassword(
    userId: Int, newPwd: String?,
    oldPwd: String?
  ): Observable<BaseResponse<Any?>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["id"] = userId
    mRequestBody.putAndMD5("upwd", newPwd)
    mRequestBody.putAndMD5("upwd2", oldPwd)
    return mAccountServices.updatePasswordByOldPassword(mRequestBody)
  }

  /**
   * 获取简历数量
   */
  fun getResumeCount(userId: Int): Observable<BaseResponse<List<Any>>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["uid"] = userId
    return mAccountServices.getResumeCount(mRequestBody)
  }

  suspend fun getResumeCountV2(userID: Int): ReqResponse<List<Any>> {
    return httpRequest {
      mAccountServices.getResumeCountV2(
        ZPRequestBody().apply {
          put("uid", userID)
        }
      )
    }
  }

  /**
   * 绑定用户推送Token
   */
  fun bindUserPushToken(userId: Int, token: String): Observable<BaseResponse<*>> {
    val mRequestBody = ZPRequestBody()
    mRequestBody["uid"] = userId
    mRequestBody["type"] = if (checkIsOppoRom()) 3 else 1
    mRequestBody["userToken"] = token
    return mAccountServices.bindUserPushToken(EncryptReqParams.encryptMap(mRequestBody))
  }

  /**
   * 获取用户当前认证类型
   */
  fun getUserCurrentAuthStatus(userID: Int): Observable<BaseResponse<List<UserAuthStatusData?>?>?>? {
    val mRequestBody = ZPRequestBody()
    mRequestBody["uid"] = userID
    return mAccountServices.getUserCurrentAuthStatus(
      EncryptReqParams.encryptMap(
        mRequestBody
      )
    )
  }
}