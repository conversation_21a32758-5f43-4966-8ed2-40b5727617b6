package com.bxkj.account.mvp.contract;

import com.bxkj.common.mvp.mvp.BaseMvpPresenter;
import com.bxkj.common.mvp.mvp.BaseView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.mvp.contract
 * @Description: CheckMobileNumberExists
 * @TODO: TODO
 * @date 2018/3/27
 */

public interface SendSmsContract {
    interface View extends BaseView {

        void mobileNumberNoExists(String mobileNumber);

        void mobileNumberExists(String mobileNumber);

        void sendSMSVerificationCodeSuccess(String mobileNumber, String code);

        void sendSMSVerificationCodeError(String errMsg);

        void secondsToResend(long seconds);

        void resendCountDownOver();
    }

    abstract class Presenter extends BaseMvpPresenter<View> {

        public abstract void checkTheMobileNumberExists(String mobileNumber);

        public abstract void sendSMSVerificationCode(String mobileNumber,int sendType);

        public abstract void startSendSMSCountdown(int count);
    }
}
