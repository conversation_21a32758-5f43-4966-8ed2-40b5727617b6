package com.bxkj.account.ui.recoverpassword;


import com.bxkj.account.api.AccountApi;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.network.BaseResponse;
import com.bxkj.common.network.CustomObserver;
import com.bxkj.common.network.RxHelper;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.util.RegularUtils;

import javax.inject.Inject;

import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.account.ui.recoverpassword
 * @Description: RecoverPassword
 * @TODO: TODO
 * @date 2018/3/27
 */

public class RecoverPasswordPresenter extends RecoverPasswordContract.Presenter {

    private static final String TAG = RecoverPasswordPresenter.class.getSimpleName();
    private AccountApi mAccountApi;

    @Inject
    public RecoverPasswordPresenter(AccountApi accountApi) {
        mAccountApi = accountApi;
    }

    @Override
    public void sendUpdatePasswordEmail(String userName, String email) {
        if (CheckUtils.isNullOrEmpty(userName)) {
            mView.onError("用户名不可为空");
            return;
        } else if (CheckUtils.isNullOrEmpty(email)) {
            mView.onError("邮箱不可为空");
            return;
        } else if (!RegularUtils.isEmail(email)) {
            mView.onError("邮箱格式不正确");
            return;
        }
        mAccountApi.sendUpdatePasswordEmail(userName, email)
                .compose(RxHelper.applyThreadSwitch())
                .subscribe(new CustomObserver() {
                    @Override
                    protected void onSuccess(BaseResponse baseResponse) {
                        mView.sendUpdatePasswordEmailSuccess(baseResponse.getMsg());
                    }

                    @Override
                    protected void onError(RespondThrowable respondThrowable) {
                        mView.onError(respondThrowable.getErrMsg());
                    }

                    @Override
                    public void onSubscribe(Disposable d) {
                        mCompositeDisposable.add(d);
                    }
                });
    }

    @Override
    public void checkMobileAndVerificationCode(String mobile, String etMobile, String code, String etCode) {
        if (CheckUtils.isNullOrEmpty(mobile) || CheckUtils.isNullOrEmpty(code)) {
            mView.onError("验证码错误");
        } else if (!mobile.equals(etMobile)) {
            mView.onError("接受验证码手机号与当前手机号不一致");
        } else if (!code.equals(etCode)) {
            mView.onError("验证码错误");
        } else {
            mView.checkMobileAndCodeSuccess(mobile);
        }
    }
}
