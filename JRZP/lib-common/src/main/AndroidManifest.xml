<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools">

  <!--  请求安装APP-->
  <uses-permission android:name="android.permission.CALL_PHONE" />
  <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
  <uses-permission android:name="android.permission.ACCESS_ALL_DOWNLOADS" />

  <!-- 这个权限用于进行网络定位-->
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <!-- 这个权限用于访问GPS定位-->
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <!-- 用于访问wifi网络信息，wifi信息会用于进行网络定位-->
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

  <!-- 获取运营商信息，用于支持提供运营商信息相关的接口-->
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

  <!-- 访问网络，网络定位需要上网-->
  <uses-permission android:name="android.permission.INTERNET" />

  <!-- 用于读取手机当前的状态-->
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <!-- 写入扩展存储，向扩展卡写入数据，用于写入离线定位数据-->
  <uses-permission
    android:name="android.permission.WRITE_EXTERNAL_STORAGE"
    tools:node="replace" />

  <!--读取扩展卡-->
  <uses-permission
    android:name="android.permission.READ_EXTERNAL_STORAGE"
    tools:node="replace" />
  <!--  相机-->
  <uses-permission android:name="android.permission.CAMERA" />
  <!--  震动-->
  <uses-permission android:name="android.permission.VIBRATE" />
  <!--  录音-->
  <uses-permission android:name="android.permission.RECORD_AUDIO" />

  <!--  读取通讯录-->
  <uses-permission android:name="android.permission.READ_CONTACTS" />

  <!--  读取通话记录-->
  <uses-permission android:name="android.permission.READ_CALL_LOG" />

  <!-- Android 13 文件权限 -->
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
  <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
  <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

  <uses-sdk tools:overrideLibrary="tv.danmaku.ijk.media.player_arm64,tv.danmaku.ijk.media.player_x86_64,com.hw.videoprocessor,com.luck.picture.lib,com.yalantis.ucrop
    ,androidx.camera.camera2,androidx.camera.view,androidx.camera.lifecycle,androidx.camera.core" />

  <application
    android:allowBackup="false"
    android:maxAspectRatio="2.4"
    android:resizeableActivity="true"
    tools:replace="android:allowBackup"
    tools:targetApi="o">

    <provider
      android:name="androidx.core.content.FileProvider"
      android:authorities="${applicationId}.zpProvider"
      android:exported="false"
      android:grantUriPermissions="true">
      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/provider_file_paths" />
    </provider>

    <!--BUGLY-->
    <!--配置APP ID -->
<!--    <meta-data-->
<!--      android:name="BUGLY_APPID"-->
<!--      android:value="299b170156" />-->
    <meta-data
      android:name="BUGLY_APPID"
      android:value="d40c17a399" />

    <!-- 配置APP版本号 -->
    <meta-data
      android:name="BUGLY_APP_VERSION"
      android:value="3.8.1" />
    <!-- 配置Bugly调试模式（true或者false）-->
    <meta-data
      android:name="BUGLY_ENABLE_DEBUG"
      android:value="false" />

    <!--百度地图-->
    <meta-data
      android:name="com.baidu.lbsapi.API_KEY"
      android:value="73sRwhTE9DMiiiNaSM6GQ9H5QuGFG5tU" />

    <service
      android:name="com.baidu.location.f"
      android:enabled="true"
      android:exported="false"
      android:process=":remote" />

    <!--百度移动统计-->
    <meta-data
      android:name="BaiduMobAd_CHANNEL"
      android:value="qq" />

    <meta-data
      android:name="BaiduMobAd_STAT_ID"
      android:value="218f76dde3" />

    <!-- 是否获取基站位置信息 ,默认为true -->
    <meta-data
      android:name="BaiduMobAd_CELL_LOCATION"
      android:value="false" />

    <!-- 是否获取GPS位置信息，默认为true -->
    <meta-data
      android:name="BaiduMobAd_GPS_LOCATION"
      android:value="false" />

    <!-- 是否获取WIFI位置信息，默认为true -->
    <meta-data
      android:name="BaiduMobAd_WIFI_LOCATION"
      android:value="false" />

    <!--指定最大适配屏幕比例-->
    <meta-data
      android:name="android.max_aspect"
      android:value="2.4" />

    <!--适配华为（huawei）刘海屏-->
    <meta-data
      android:name="android.notch_support"
      android:value="true" />

    <!--适配小米（xiaomi）刘海屏-->
    <meta-data
      android:name="notch.config"
      android:value="portrait|landscape" />
  </application>

</manifest>
