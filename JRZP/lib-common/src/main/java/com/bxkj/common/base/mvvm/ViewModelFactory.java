package com.bxkj.common.base.mvvm;

import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel;
import java.util.Map;
import javax.inject.Inject;
import javax.inject.Provider;

/**
 * @date 2019/4/3
 */
public class ViewModelFactory extends ViewModelProvider.NewInstanceFactory {

  private final Map<Class<? extends BaseViewModel>, Provider<BaseViewModel>> mModelKeyValues;

  @Inject
  public ViewModelFactory(
      Map<Class<? extends BaseViewModel>, Provider<BaseViewModel>> modelKeyValues) {
    mModelKeyValues = modelKeyValues;
  }

  @NonNull
  @Override
  public <T extends ViewModel> T create(@NonNull Class<T> modelClass) {
    Provider<BaseViewModel> viewModel = mModelKeyValues.get(modelClass);
    if (viewModel == null) {
      for (Map.Entry<Class<? extends BaseViewModel>, Provider<BaseViewModel>> classProviderEntry : mModelKeyValues
          .entrySet()) {
        if (modelClass.isAssignableFrom(classProviderEntry.getKey())) {
          viewModel = classProviderEntry.getValue();
          break;
        }
      }
    }
    if (viewModel == null) {
      return null;
      //throw new IllegalArgumentException("Unknown ViewModel class: " + modelClass.getName());
    }
    return (T) viewModel.get();
  }
}
