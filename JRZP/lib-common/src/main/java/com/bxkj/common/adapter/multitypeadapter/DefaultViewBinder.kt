package com.bxkj.common.adapter.multitypeadapter

import android.view.View
import com.bxkj.common.BR
import com.bxkj.common.adapter.OnItemLongClickListener
import com.bxkj.common.adapter.superadapter.SuperViewHolder

/**
 * @Description:
 * <AUTHOR>
 */
open class DefaultViewBinder<T>(
  private val layoutId: Int,
  private val brId: Int=BR.data,
  private var defaultBind: Boolean = true
) : ItemViewBinder<T> {
  companion object {
    const val NO_BR_ID = 0
  }

  protected var mOnItemClickListener: OnItemClickListener<T>? = null
  private var mOnItemLongClickListener: OnItemLongClickListener<T>? = null
  private var ids: IntArray? = null
  private var longClickIds: IntArray? = null

  override fun onBindViewHolder(
    holder: SuperViewHolder,
    item: T,
    position: Int
  ) {
    if (defaultBind) {
      holder.bind(brId, item)
    }
    ids?.let {
      for (id in it) {
        holder.findViewById<View>(id).setOnClickListener { v: View? ->
          v?.let {
            mOnItemClickListener?.onItemClicked(v, position, item)
          }
        }
      }
    }
    holder.itemView.setOnClickListener {
      mOnItemClickListener?.onItemClicked(it, position, item)
    }
    mOnItemLongClickListener?.let {
      holder.itemView.setOnLongClickListener { v ->
        return@setOnLongClickListener it.onLongClicked(v, position, item)
      }
    }
  }

  override fun getLayoutId(): Int = layoutId

  fun setOnItemClickListener(onItemClickListener: OnItemClickListener<T>, vararg ids: Int) {
    this.mOnItemClickListener = onItemClickListener
    this.ids = ids
  }

  interface OnItemClickListener<T> {
    fun onItemClicked(v: View, position: Int, item: T)
  }

  fun setOnItemLongClickListener(
    onItemLongClickListener: OnItemLongClickListener<T>,
    vararg ids: Int
  ) {
    this.mOnItemLongClickListener = onItemLongClickListener
    this.longClickIds = ids
  }
}