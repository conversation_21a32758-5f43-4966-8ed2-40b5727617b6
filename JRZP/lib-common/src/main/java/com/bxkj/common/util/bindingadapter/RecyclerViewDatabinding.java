package com.bxkj.common.util.bindingadapter;

import androidx.databinding.BindingAdapter;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

/**
 * @version V1.0
 * @Description:
 * @date 2019/10/16
 */
public class RecyclerViewDatabinding {

  @BindingAdapter("bind:items")
  public static void setItems(RecyclerView recyclerView, List data) {
    if (recyclerView.getAdapter() == null) {
      return;
    }
    if (recyclerView.getAdapter() instanceof ListAdapter) {
      if (data == null) {
        data = new ArrayList();
      }
      ((ListAdapter) recyclerView.getAdapter()).submitList(data);
    }
  }
}