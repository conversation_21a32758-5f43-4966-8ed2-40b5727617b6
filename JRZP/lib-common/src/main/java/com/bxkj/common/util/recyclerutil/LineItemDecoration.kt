package com.bxkj.common.util.recyclerutil

import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.Orientation

/**
 * @Project: biyeji-app
 * @Package com.bxkj.commonlib.util.recyclerutil
 * @Description:
 * <AUTHOR>
 * @date 2019/11/27
 * @version V1.0
 */
class LineItemDecoration(
  private var mDivider: Drawable? = null,
  @Orientation private val mListOrientation: Int
) : RecyclerView.ItemDecoration() {

  private var mDrawHeader: Boolean = false
  private var mDrawFoot: Boolean = false
  private var mMarginStart: Int = 0
  private var mMarginEnd: Int = 0

  private constructor(builder: Builder) : this(builder.divider, builder.orientation) {
    mDrawHeader = builder.drawHeader
    mDrawFoot = builder.drawFoot
    mMarginStart = builder.marginStart
    mMarginEnd = builder.marginEnd
  }

  class Builder {
    internal var divider: Drawable? = null
    internal var orientation: Int = LinearLayoutManager.VERTICAL
    internal var drawHeader: Boolean = false  //第一条头部是否画分割线
    internal var drawFoot: Boolean = false  //最后一条尾部是否画分割线
    internal var marginStart: Int = 0
    internal var marginEnd: Int = 0

    fun divider(divider: Drawable?) = apply {
      this.divider = divider
    }

    fun marginStart(size: Int) = apply {
      marginStart = size
    }

    fun marginEnd(size: Int) = apply {
      marginEnd = size
    }

    fun margin(size: Int) = apply {
      marginStart = size
      marginEnd = size
    }

    fun orientation(@Orientation orientation: Int) = apply {
      this.orientation = orientation
    }

    fun drawHeader(draw: Boolean) = apply {
      this.drawHeader = draw
    }

    fun drawFoot(draw: Boolean) = apply {
      this.drawFoot = draw
    }

    fun build(): LineItemDecoration {
      return LineItemDecoration(this)
    }
  }

  override fun getItemOffsets(
    outRect: Rect,
    view: View,
    parent: RecyclerView,
    state: RecyclerView.State
  ) {
    mDivider?.let {
      val childPosition = parent.getChildAdapterPosition(view)
      outRect.set(
        if (childPosition == 0 && mListOrientation == LinearLayoutManager.HORIZONTAL) {
          if (mDrawHeader) it.intrinsicWidth else 0
        } else 0,
        if (childPosition == 0 && mListOrientation == LinearLayoutManager.VERTICAL) {
          if (mDrawHeader) it.intrinsicWidth else 0
        } else 0,
        if (mListOrientation == LinearLayoutManager.HORIZONTAL)
          getDividerSize(parent, view, it) else 0,
        if (mListOrientation == LinearLayoutManager.VERTICAL)
          getDividerSize(parent, view, it) else 0
      )
    }
  }

  private fun getDividerSize(
    parent: RecyclerView,
    view: View,
    divider: Drawable
  ): Int {
    return if (isLastRow(parent, view)) {
      if (mDrawFoot) {
        divider.intrinsicWidth
      } else {
        0
      }
    } else {
      divider.intrinsicWidth
    }
  }

  private fun isLastRow(parent: RecyclerView, view: View): Boolean {
    parent.adapter?.let {
      val viewPosition = parent.getChildAdapterPosition(view)
      if (viewPosition == it.itemCount-1) {
        return true
      }
    }
    return false
  }

  override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
    val childCount = parent.childCount
    for (i: Int in 0..childCount) {
      val child = parent.getChildAt(i)
      child?.let {
        val childLayoutParams = it.layoutParams as RecyclerView.LayoutParams
        if (mListOrientation == LinearLayoutManager.VERTICAL) {
          drawHorizontal(i, c, child, childLayoutParams)
        } else {
          drawVertical(i, c, child, childLayoutParams)
        }
      }
    }
  }

  /**
   * 画横线
   */
  private fun drawHorizontal(
    position: Int,
    c: Canvas,
    child: View,
    childLayoutParams: RecyclerView.LayoutParams
  ) {
    mDivider?.let {
      val left = child.left - childLayoutParams.leftMargin + mMarginStart
      val right = child.right + childLayoutParams.rightMargin - mMarginEnd

      if (mDrawHeader && position == 0) {
        val top = child.top - childLayoutParams.topMargin - it.intrinsicHeight
        val bottom = top + it.intrinsicHeight
        it.setBounds(left, top, right, bottom)
        it.draw(c)
      }

      val top = child.bottom + childLayoutParams.bottomMargin
      val bottom = top + it.intrinsicHeight
      it.setBounds(left, top, right, bottom)
      it.draw(c)
    }
  }

  /**
   * 画竖线
   */
  private fun drawVertical(
    position: Int,
    c: Canvas,
    child: View,
    childLayoutParams: RecyclerView.LayoutParams
  ) {
    mDivider?.let {
      val top = child.top - childLayoutParams.topMargin + mMarginStart
      val bottom = child.bottom + childLayoutParams.topMargin - mMarginEnd

      if (mDrawHeader && position == 0) {
        val left = child.left - childLayoutParams.leftMargin - it.intrinsicWidth
        val right = left + it.intrinsicWidth
        it.setBounds(left, top, right, bottom)
        it.draw(c)
      }

      val left = child.right + childLayoutParams.rightMargin
      val right = left + it.intrinsicWidth
      it.setBounds(left, top, right, bottom)
      it.draw(c)
    }
  }

}