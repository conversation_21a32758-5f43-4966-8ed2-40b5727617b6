package com.bxkj.common.data;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.bxkj.common.BR;
import com.bxkj.common.util.CheckUtils;

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/5/21
 * @version: V1.0
 */
public class AddressData extends BaseObservable implements Parcelable {

  private int provinceId;
  private String provinceName;
  private int cityId;
  private String cityName;
  private int districtId;
  private String districtName;
  private int streetId;
  private String streetName;
  private String details;
  private double longitude;   //经度
  private double latitude;    //纬度

  public AddressData() {
  }

  public AddressData(int provinceId, String provinceName, int cityId, String cityName,
      int districtId, String districtName, int streetId, String streetName, String details,
      double longitude, double latitude) {
    this.provinceId = provinceId;
    this.provinceName = provinceName;
    this.cityId = cityId;
    this.cityName = cityName;
    this.districtId = districtId;
    this.districtName = districtName;
    this.streetId = streetId;
    this.streetName = streetName;
    this.details = details;
    this.longitude = longitude;
    this.latitude = latitude;
  }

  protected AddressData(Parcel in) {
    provinceId = in.readInt();
    provinceName = in.readString();
    cityId = in.readInt();
    cityName = in.readString();
    districtId = in.readInt();
    districtName = in.readString();
    streetId = in.readInt();
    streetName = in.readString();
    details = in.readString();
    longitude = in.readDouble();
    latitude = in.readDouble();
  }

  @Bindable
  public String getAddressText() {
    StringBuilder addressBuilder = new StringBuilder();
    if (!CheckUtils.isNullOrEmpty(provinceName)) {
      addressBuilder.append(provinceName);
    }
    if (!CheckUtils.isNullOrEmpty(cityName)) {
      addressBuilder.append(cityName);
    }
    if (!CheckUtils.isNullOrEmpty(districtName)) {
      addressBuilder.append(districtName);
    }
    if (!CheckUtils.isNullOrEmpty(streetName)) {
      addressBuilder.append(streetName);
    }
    return addressBuilder.toString();
  }

  public void setAddressItem(AreaOptionsData province, AreaOptionsData city, AreaOptionsData county,
      AreaOptionsData street) {
    if (province != null) {
      setProvinceId(provinceId);
      setProvinceName(province.getName());
    }
    if (city != null) {
      setCityId(city.getId());
      setCityName(city.getName());
    }
    if (county != null) {
      setDistrictId(county.getId());
      setDistrictName(county.getName());
    }
    if (street != null) {
      setStreetId(street.getId());
      setStreetName(street.getName());
    }
    notifyPropertyChanged(BR.addressText);
  }

  public static final Creator<AddressData> CREATOR = new Creator<AddressData>() {
    @Override
    public AddressData createFromParcel(Parcel in) {
      return new AddressData(in);
    }

    @Override
    public AddressData[] newArray(int size) {
      return new AddressData[size];
    }
  };

  public int getProvinceId() {
    return provinceId;
  }

  public void setProvinceId(int provinceId) {
    this.provinceId = provinceId;
  }

  public String getProvinceName() {
    return provinceName;
  }

  public void setProvinceName(String provinceName) {
    this.provinceName = provinceName;
  }

  public int getCityId() {
    return cityId;
  }

  public void setCityId(int cityId) {
    this.cityId = cityId;
  }

  public String getCityName() {
    return cityName;
  }

  public void setCityName(String cityName) {
    this.cityName = cityName;
  }

  public int getDistrictId() {
    return districtId;
  }

  public void setDistrictId(int districtId) {
    this.districtId = districtId;
  }

  public String getDistrictName() {
    return districtName;
  }

  public void setDistrictName(String districtName) {
    this.districtName = districtName;
  }

  @Bindable
  public int getStreetId() {
    return streetId;
  }

  public void setStreetId(int streetId) {
    this.streetId = streetId;
    notifyPropertyChanged(BR.streetId);
  }

  public String getStreetName() {
    return streetName;
  }

  public void setStreetName(String streetName) {
    this.streetName = streetName;
  }

  public String getDetails() {
    return details;
  }

  public void setDetails(String details) {
    this.details = details;
  }

  public double getLongitude() {
    return longitude;
  }

  public void setLongitude(double longitude) {
    this.longitude = longitude;
  }

  public double getLatitude() {
    return latitude;
  }

  public void setLatitude(double latitude) {
    this.latitude = latitude;
  }

  @Override
  public int describeContents() {
    return 0;
  }

  @Override
  public void writeToParcel(Parcel dest, int flags) {
    dest.writeInt(provinceId);
    dest.writeString(provinceName);
    dest.writeInt(cityId);
    dest.writeString(cityName);
    dest.writeInt(districtId);
    dest.writeString(districtName);
    dest.writeInt(streetId);
    dest.writeString(streetName);
    dest.writeString(details);
    dest.writeDouble(longitude);
    dest.writeDouble(latitude);
  }

  //有经纬度
  public boolean hasLocation() {
    return longitude != 0 && latitude != 0;
  }

  //刷新地址
  public void refreshAddress() {
    notifyPropertyChanged(BR.addressText);
  }
}
