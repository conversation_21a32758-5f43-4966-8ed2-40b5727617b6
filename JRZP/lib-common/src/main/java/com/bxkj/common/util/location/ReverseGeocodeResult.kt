package com.bxkj.common.util.location

/**
 * author:Sanjin
 * date:2025/6/26
 **/
data class ReverseGeocodeResult(
    var returnCode: String?,
    var returnDesc: String?,
    var sites: List<Site?>?
){

  override fun toString(): String {
    return "ReverseGeocodeResult(returnCode=$returnCode, returnDesc=$returnDesc, sites=$sites)"
  }
}

data class Site(
    var address: Address?,
    var aoiFlag: Boolean?,
    var formatAddress: String?,
    var location: Location?,
    var name: String?,
    var poi: Poi?,
    var siteId: String?,
    var viewport: Viewport?
)

data class Address(
    var adminArea: String?,
    var city: String?,
    var country: String?,
    var countryCode: String?,
    var postalCode: String?,
    var subAdminArea: String?,
    var tertiaryAdminArea: String?
)

data class Location(
    var lat: Double?,
    var lng: Double?
)

data class Poi(
    var childrenNodes: List<Any?>?,
    var hwPoiTypes: List<String?>?,
    var internationalPhone: String?,
    var poiTypes: List<String?>?,
    var rating: Double?
)

data class Viewport(
    var northeast: Northeast?,
    var southwest: Southwest?
)

data class Northeast(
    var lat: Double?,
    var lng: Double?
)

data class Southwest(
    var lat: Double?,
    var lng: Double?
)