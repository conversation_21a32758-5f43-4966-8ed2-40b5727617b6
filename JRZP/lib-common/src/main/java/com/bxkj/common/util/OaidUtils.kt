package com.bxkj.common.util

import android.content.Context
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader

/**
 * Description:
 * Author:Sanjin
 * Date:2024/6/14
 **/
object OaidUtils {
  @JvmStatic
  fun loadPemFromAssetFile(context: Context, assetFileName: String?): String {
    return try {
      val `is` = context.assets.open(assetFileName!!)
      val `in` = BufferedReader(InputStreamReader(`is`))
      val builder = StringBuilder()
      var line: String?
      while (`in`.readLine().also { line = it } != null) {
        builder.append(line)
        builder.append('\n')
      }
      builder.toString()
    } catch (e: IOException) {
      ""
    }
  }
}