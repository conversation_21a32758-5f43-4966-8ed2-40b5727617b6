package com.bxkj.common.util

import android.app.Activity
import android.app.ActivityManager
import android.content.ClipData
import android.content.ClipboardManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager.NameNotFoundException
import android.net.Uri
import android.os.Build
import android.os.Build.VERSION
import android.os.Build.VERSION_CODES
import android.os.Process
import android.provider.Settings
import android.telecom.TelecomManager
import android.text.TextUtils
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.core.content.FileProvider
import com.bxkj.common.constants.AppConstants
import com.bxkj.common.util.location.CoordinateTransformUtil
import com.elvishew.xlog.XLog
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.hjq.toast.Toaster
import com.youth.banner.util.LogUtils
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.IOException
import java.util.zip.ZipFile

/**
 * @Description: 系统工具类
 * @date 2018/3/1
 */
object SystemUtil {
  const val QQ_PACKAGE_NAME: String = "com.tencent.mobileqq"

  fun getApkCRC(context: Context): Long {
    val zf: ZipFile
    try {
      zf = ZipFile(context.packageCodePath)
      // 获取apk安装后的路径
      val ze = zf.getEntry("classes.dex")
      return ze.crc
    } catch (e: Exception) {
      return 0
    }
  }

  /**
   * 打开本机地图展示一个点
   *
   * @param address @return
   */
  @JvmStatic fun openLocalMapShowLocation(
    context: Context,
    lng: String,
    lat: String,
    address: String,
  ): Boolean {
    try {
      val convert =
        CoordinateTransformUtil.bd09togcj02(lng.toDouble(), lat.toDouble())

      // 本机地图
      val localMapUri =
        Uri.parse(
          (
            "geo:" +
              (if (CheckUtils.isNullOrEmpty(lng)) "" else (convert[0])) +
              "," +
              (if (CheckUtils.isNullOrEmpty(lat)) "" else (convert[1])) +
              "?q=" +
              address
            ),
        )
      val localMapIntent = Intent(Intent.ACTION_VIEW, localMapUri)
      if (hasResolveActivity(context, localMapIntent)) {
        context.startActivity(localMapIntent)
        return true
      }

      // 百度地图
      val baiduMapUri = Uri.parse("baidumap://map/geocoder?src=openApiDemo&address=$address")
      val baiduMapIntent = Intent()
      baiduMapIntent.setData(baiduMapUri)
      if (hasResolveActivity(context, baiduMapIntent)) {
        context.startActivity(baiduMapIntent)
        return true
      }

      // 高德地图
      val aminiMapUri =
        Uri.parse(
          (
            "androidamap://viewMap?sourceApplication=jrzp&poiname=" +
              address + // + "&lon="
              // + convert[0]
              // + "&lat="
              // + convert[1]
              "&dev=0"
            ),
        )
      val aminiMapIntent = Intent(Intent.ACTION_VIEW, aminiMapUri)
      aminiMapIntent.setPackage("com.autonavi.minimap")
      if (hasResolveActivity(context, aminiMapIntent)) {
        context.startActivity(aminiMapIntent)
        return true
      }

      return false
    } catch (e: Exception) {
      return false
    }
  }

  /**
   * 检查是否有能处理此意图的活动
   */
  fun hasResolveActivity(
    context: Context,
    intent: Intent,
  ): Boolean = (intent.resolveActivity(context.packageManager) != null)

  /**
   * 获取APP版本名
   */
  fun getAppVersionName(context: Context): String {
    var versionName: String
    try {
      val packageInfo =
        context.applicationContext
          .packageManager
          .getPackageInfo(context.packageName, 0)
      versionName = packageInfo?.versionName.orEmpty()
    } catch (e: NameNotFoundException) {
      e.printStackTrace()
      versionName = AppConstants.APP_VERSION_NAME
    }
    return versionName
  }

  /**
   * 唤起拨号盘
   */
  @JvmStatic fun callPhone(
    context: Context,
    phoneNumber: String,
  ) {
    if (phoneNumber.isEmpty()) {
      Toaster.show("号码信息不正确")
      return
    }
    try {
      val intent = Intent()
      intent.setAction(Intent.ACTION_DIAL)
      intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      intent.setData(Uri.parse("tel:$phoneNumber"))
      if (hasResolveActivity(context, intent)) {
        context.startActivity(intent)
      }
    } catch (e: Exception) {
      Toaster.show("拨号失败，请手动拨打：$phoneNumber")
    }
  }

  fun Context.callPhoneNow(phoneNumber: String) {
    if (VERSION.SDK_INT >= VERSION_CODES.M) {
      (this.getSystemService(Context.TELECOM_SERVICE) as TelecomManager).placeCall(
        Uri.parse("tel:${phoneNumber}"),
        null
      )
    } else {
      startActivity(Intent(Intent.ACTION_CALL).apply {
        data = Uri.parse("tel:${phoneNumber}")
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
      })
    }
  }

  fun sendSms(
    context: Context,
    phoneNumber: String,
  ) {
    try {
      val sendIntent = Intent(Intent.ACTION_SENDTO)
      sendIntent.setData(Uri.parse("smsto:$phoneNumber"))
      if (hasResolveActivity(context, sendIntent)) {
        context.startActivity(sendIntent)
      }
    } catch (e: Exception) {
      Toaster.show("调起失败")
    }
  }

  /**
   * 前往QQ打开聊天页面
   */
  @JvmStatic fun toQqTalk(
    activity: Activity,
    qq: String,
  ) {
    if (isInstall(activity, QQ_PACKAGE_NAME)) {
      val intent =
        Intent(
          Intent.ACTION_VIEW,
          Uri.parse("mqqwpa://im/chat?chat_type=wpa&uin=$qq&version=1"),
        )
      intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      if (checkHasResolveActivity(activity, intent)) {
        activity.startActivity(intent)
      }
    } else {
      Toaster.show("未安装QQ应用")
    }
  }

  /**
   * 检查是否有能处理此意图的活动
   */
  fun checkHasResolveActivity(
    context: Context,
    intent: Intent,
  ): Boolean = (intent.resolveActivity(context.packageManager) != null)

  /**
   * 判断是否安装某宽应用
   */
  fun isInstall(
    context: Context,
    packageName: String,
  ): Boolean {
    try {
      context.packageManager.getPackageInfo(packageName, 0)
      return true
    } catch (e: NameNotFoundException) {
      e.printStackTrace()
      return false
    }
  }

  @JvmStatic fun hideSoftKeyboard(activity: Activity) {
    activity.currentFocus?.let { view ->
      val inputManager = activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
      inputManager.hideSoftInputFromWindow(view.windowToken, 0)
      try {
        view.clearFocus()
      } catch (e: Exception) {
        LogUtils.d("该View不支持clearFocus()")
      }
    }
  }

  /**
   * 隐藏软键盘(有输入框)
   */
  @JvmStatic fun hideSoftKeyboard(
    context: Context,
    editText: EditText,
  ) {
    val inputManager = context.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
    inputManager?.hideSoftInputFromWindow(editText.windowToken, 0)
  }

  /**
   * 显示软键盘
   */
  @JvmStatic fun showSoftKeyboardForView(view: View) {
    val context = view.context
    try {
      val imm =
        context.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
      if (imm != null) {
        view.requestFocus()
        imm.showSoftInputFromInputMethod(view.windowToken, InputMethodManager.RESULT_SHOWN)
        imm.toggleSoftInput(
          InputMethodManager.SHOW_FORCED,
          InputMethodManager.HIDE_IMPLICIT_ONLY,
        )
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  fun showKeyboard(view: View) {
    val imm =
      view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    if (imm != null) {
      view.requestFocus()
      imm.showSoftInput(view, 0)
    }
  }

  /**
   * 获取进程名
   */
  @JvmStatic fun getProgressName(pid: Int): String? {
    var reader: BufferedReader? = null
    try {
      reader = BufferedReader(FileReader("/proc/$pid/cmdline"))
      var progressName = reader.readLine()
      if (!TextUtils.isEmpty(progressName)) {
        progressName = progressName.trim { it <= ' ' }
      }
      return progressName
    } catch (e: IOException) {
      e.printStackTrace()
    } finally {
      if (reader != null) {
        try {
          reader.close()
        } catch (e: IOException) {
          e.printStackTrace()
        }
      }
    }
    return null
  }

  /**
   * 手机自带wevview打开url
   */
  fun openUrlInLocalWebView(
    context: Context,
    url: String,
  ) {
    val intent = Intent()
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    intent.setAction("android.intent.action.VIEW")
    val content_url = Uri.parse(if (url.contains("http")) url else "https://$url")
    intent.setData(content_url)
    context.startActivity(intent)
  }

  /**
   * 判断当前进程是否是主进程
   */
  fun isMainProcess(context: Context): Boolean {
    val am = (context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager)
    if (am?.runningAppProcesses == null) {
      return true
    }
    for (runningAppProcessInfo in am
      .runningAppProcesses) {
      if (runningAppProcessInfo.pid == Process.myPid() &&
        (
          context.packageName
            == runningAppProcessInfo.processName
          )
      ) {
        return true
      }
    }
    return false
  }

  /**
   * 根据包名启动app
   */
  fun startApp(
    context: Context,
    packageName: String,
  ) {
    val resolveIntent = context.packageManager.getLaunchIntentForPackage(packageName)
    if (resolveIntent != null) {
      resolveIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      context.startActivity(resolveIntent)
    }
  }

  /**
   * 根据包名启动应用市场
   */
  fun openMarketByPackageName(
    context: Context,
    packageName: String,
  ) {
    val uri = Uri.parse("market://details?id=$packageName")
    val intent = Intent(Intent.ACTION_VIEW, uri)
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    context.startActivity(intent)
  }
  
  /**
   * 判断app是否运行
   */
  fun isAppAlive(
    context: Context,
  ): Boolean {
    val activityManager =
      context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    val processInfos =
      activityManager.runningAppProcesses
    for (i in processInfos.indices) {
      if (processInfos[i].processName == context.packageName) {
        return true
      }
    }
    return false
  }

  /**
   * 判断某个界面是否在前台
   *
   * @param context Context
   * @param className 界面的类名
   * @return 是否在前台显示
   */
  fun isForeground(
    context: Context?,
    className: String,
  ): Boolean {
    if (context == null || TextUtils.isEmpty(className)) {
      return false
    }
    val am = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    val list = am.getRunningTasks(1)
    if (list != null && list.size > 0) {
      val cpn = list[0].topActivity
      if (className == cpn!!.className) {
        return true
      }
    }
    return false
  }

  /**
   * 跳转到设置页面
   */
  fun toSettingPage(activity: Activity) {
    try {
      val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
      intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      intent.setData(Uri.parse("package:" + activity.packageName))
      activity.startActivity(intent)
    } catch (e: Exception) {
      e.printStackTrace()
      Toaster.show("前往失败，请手动打开")
    }
  }

  @JvmOverloads @JvmStatic fun copy(
    context: Context,
    label: String?,
    content: String?,
    tips: String = "已复制到剪切板",
  ) {
    val clipboardManager =
      context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val clipData = ClipData.newPlainText(label, content)
    clipboardManager.setPrimaryClip(clipData)
    if (!tips.isEmpty()) {
      Toaster.show(tips)
    }
  }

  fun jumpToWechat(context: Context) {
    try {
      val intent = Intent()
      val cmp = ComponentName("com.tencent.mm", "com.tencent.mm.ui.LauncherUI")
      intent.setAction(Intent.ACTION_MAIN)
      intent.addCategory(Intent.CATEGORY_LAUNCHER)
      intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      intent.setComponent(cmp)
      context.startActivity(intent)
    } catch (e: Exception) {
    }
  }

  fun isServiceRunning(
    context: Context,
    className: String,
  ): Boolean {
    val am =
      context.applicationContext
        .getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    val info = am.getRunningServices(0x7FFFFFFF)
    if (info == null || info.size == 0) {
      return false
    }
    for (aInfo in info) {
      if (className == aInfo.service.className) {
        return true
      }
    }
    return false
  }

  fun openSchemeUrl(
    context: Activity,
    url: String,
    requestCode: Int,
  ) {
    if (url.contains("alipays") || url.contains("weixin")) {
      val paymentIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
      if (hasResolveActivity(context, paymentIntent)) {
        if (requestCode != -1) {
          context.startActivityForResult(paymentIntent, requestCode)
        } else {
          context.startActivity(paymentIntent)
        }
      } else {
        Toaster.show("请安装支付应用后重试")
      }
    }
  }

  @JvmStatic fun installApk(
    context: Context,
    apkFilePath: String,
  ) {
    XXPermissions
      .with(context)
      .permission(Permission.REQUEST_INSTALL_PACKAGES)
      .request(
        object : OnPermissionCallback {
          override fun onGranted(
            permissions: List<String>,
            all: Boolean,
          ) {
            try {
              val apkFile = File(apkFilePath)
              val intent = Intent(Intent.ACTION_VIEW)
              intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
              intent.addCategory(Intent.CATEGORY_DEFAULT)
              val uriData: Uri
              val type = "application/vnd.android.package-archive"
              if (VERSION.SDK_INT >= VERSION_CODES.N) {
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                uriData =
                  FileProvider
                    .getUriForFile(context, context.packageName + ".zpProvider", apkFile)
              } else {
                uriData = Uri.fromFile(apkFile)
              }
              intent.setDataAndType(uriData, type)
              context.startActivity(intent)
            } catch (e: Exception) {
              XLog.d("installApk: " + e.message)
              Toaster.show("安装失败，请前往应用市场更新")
            }
          }

          override fun onDenied(
            permissions: List<String>,
            never: Boolean,
          ) {
            Toaster.show("安装失败，请打开权限后重试")
          }
        },
      )
  }

  @JvmStatic fun killSelfProcess() {
    val myPid = Process.myPid()
    Process.killProcess(myPid)
    System.exit(1)
  }

  fun Context.getAppVersionCode(): Long {
    try {
      val pm = this.packageManager
      val packageInfo = pm.getPackageInfo(this.packageName, 0)
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
        return packageInfo.longVersionCode
      } else {
        return packageInfo.versionCode.toLong()
      }
    } catch (e: Exception) {
      return AppConstants.APP_VERSION_CODE
    }
  }
}
