package com.bxkj.common.util.imageloader;

import androidx.annotation.DrawableRes;

import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import com.bxkj.common.imageloder.base.BaseLoaderConfig;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.imageloder
 * @Description: Glide加载配置
 * @TODO: TODO
 * @date 2018/3/20
 */

public class GlideLoadConfig extends BaseLoaderConfig {

  private boolean isCircle;
  private boolean isGray;
  private int radius;
  private int overrideWidth;
  private int overrideHeight;

  private GlideLoadConfig(Builder builder) {
    this.url = builder.url;
    this.placeHolder = builder.placeHolder;
    this.placeholderDrawable = builder.placeholderDrawable;
    this.errorImg = builder.errorImg;
    this.errorImgDrawable = builder.errorImgDrawable;
    this.imageView = builder.imageView;
    this.isCircle = builder.isCircle;
    this.isGray = builder.isGray;
    this.radius = builder.radius;
    this.skipCache = builder.skipCache;
    this.scaleType = builder.scaleType;
    this.overrideWidth = builder.overrideWidth;
    this.overrideHeight = builder.overrideHeight;
  }

  public boolean isCircle() {
    return isCircle;
  }

  public boolean isGray() {
    return isGray;
  }

  public int radius() {
    return radius;
  }

  public int getOverrideWidth() {
    return overrideWidth;
  }

  public int getOverrideHeight() {
    return overrideHeight;
  }

  public static class Builder {

    private Object url;    //要加载的路径
    private int placeHolder;    //加载时的占位图
    private Drawable placeholderDrawable;
    private int errorImg;   //加载失败显示的图片
    private Drawable errorImgDrawable;
    private ImageView imageView;    //需要加载的目标imageview
    private boolean isCircle;
    private boolean isGray;
    private int radius;
    private boolean skipCache;
    private ImageView.ScaleType scaleType;
    private int overrideWidth;
    private int overrideHeight;

    public Builder url(Object url) {
      this.url = url;
      return this;
    }

    public Builder placeHolder(@DrawableRes int placeHolder) {
      this.placeHolder = placeHolder;
      return this;
    }

    public Builder placeHolder(Drawable drawable) {
      this.placeholderDrawable = drawable;
      return this;
    }

    public Builder error(Drawable error) {
      this.errorImgDrawable = error;
      return this;
    }

    public Builder error(@DrawableRes int errorImg) {
      this.errorImg = errorImg;
      return this;
    }

    /**
     * 设置目标ImageView
     *
     * @param imageView
     */
    public Builder into(ImageView imageView) {
      this.imageView = imageView;
      return this;
    }

    /**
     * 设置圆形图片
     */
    public Builder circle() {
      this.isCircle = true;
      return this;
    }

    /**
     * 设置圆角
     *
     * @param radius 单位：px
     */
    public Builder radius(int radius) {
      this.radius = radius;
      return this;
    }

    public Builder skipCache(boolean skip) {
      this.skipCache = skip;
      return this;
    }

    /**
     * 指定宽高加载图片
     * @param width 宽：px
     * @param height 高：px
     */
    public Builder override(int width, int height) {
      this.overrideWidth = width;
      this.overrideHeight = height;
      return this;
    }

    /**
     * 设置缩放类型
     * @param scaleType 缩放类型
     */
    public Builder setScaleType(ImageView.ScaleType scaleType) {
      this.scaleType = scaleType;
      return this;
    }

    public Builder gray() {
      this.isGray = true;
      return this;
    }

    public BaseLoaderConfig build() {
      return new GlideLoadConfig(this);
    }
  }
}
