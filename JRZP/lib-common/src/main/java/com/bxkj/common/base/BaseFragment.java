package com.bxkj.common.base;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.ColorRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.base.mvvm.BaseNActivity;
import com.bxkj.common.util.UserUtils;
import com.bxkj.common.util.router.Router;
import com.bxkj.common.util.router.RouterNavigation;
import com.gyf.immersionbar.ImmersionBar;
import com.hjq.toast.Toaster;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.baselib
 * @Description: Fragment基类
 * @TODO: TODO
 * @date 2018/3/27
 */

public abstract class BaseFragment extends Fragment {

  protected View rootView;

  private ImmersionBar mStatusBarManager;

  private FragmentActivity mActivity;

  protected static final int DEFAULT_REQUEST_CODE = 99;

  @Override
  public void onAttach(Context context) {
    super.onAttach(context);
    if (context instanceof Activity) {
      mActivity = (FragmentActivity) context;
    }
  }

  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
    @Nullable Bundle savedInstanceState) {
    rootView = inflater.inflate(getLayoutId(), container, false);
    initPresenter();
    return rootView;
  }

  protected void initImmersionBar() {
  }

  @LayoutRes
  public abstract int getLayoutId();

  public void initPresenter() {
  }

  public abstract void initPage();

  @Override
  public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
    super.onViewCreated(view, savedInstanceState);
    initStatusBarManager();
    initPage();
  }

  @Override
  public void onResume() {
    super.onResume();
    initImmersionBar();
  }

  @Override
  public void onHiddenChanged(final boolean hidden) {
    super.onHiddenChanged(hidden);
    if (!hidden) {
      initImmersionBar();
    }
  }

  /**
   * 初始化沉浸式状态栏
   */
  private void initStatusBarManager() {
    mStatusBarManager = ImmersionBar.with(this);
  }

  /**
   * 获取状态栏管理者
   */
  public ImmersionBar getStatusBarManager() {
    return mStatusBarManager;
  }

  /**
   * 获取非空的Activity
   */
  public FragmentActivity getParentActivity() {
    return mActivity;
  }

  /**
   * 获取颜色
   */
  public int getMColor(@ColorRes int color) {
    return ContextCompat.getColor(mActivity, color);
  }

  /**
   * 显示toast
   */
  public void showToast(String msg) {
    Toaster.show(msg);
  }

  /**
   * 获取用户id
   */
  public int getUserId() {
    return UserUtils.getUserId();
  }

  protected boolean checkLoginAndToLogin() {
    if (getUserId() == CommonApiConstants.NO_DATA) {
      Router.getInstance().to(RouterNavigation.LoginActivity.URL)
        .withBoolean(RouterNavigation.LoginActivity.EXTRA_BACK_AFTER_LOGIN, true)
        .startForResult(getActivity(), DEFAULT_REQUEST_CODE);
      return false;
    }
    return true;
  }

  public void showLoading() {
    showLoading("");
  }

  public void showLoading(String text) {
    if (mActivity instanceof BaseActivity) {
      ((BaseActivity) mActivity).showLoading(text);
    } else if (mActivity instanceof BaseNActivity) {
      ((BaseNActivity) mActivity).showLoading(text);
    }
  }

  public void hiddenLoading() {
    if (mActivity instanceof BaseActivity) {
      ((BaseActivity) mActivity).hiddenLoading();
    } else if (mActivity instanceof BaseNActivity) {
      ((BaseNActivity) mActivity).hiddenLoading();
    }
  }

  @Override
  public void onDestroy() {
    super.onDestroy();
  }
}
