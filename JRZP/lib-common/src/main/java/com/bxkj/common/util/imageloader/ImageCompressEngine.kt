package com.bxkj.common.util.imageloader

import android.content.Context
import android.net.Uri
import com.luck.picture.lib.engine.CompressFileEngine
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener
import top.zibin.luban.Luban
import top.zibin.luban.OnNewCompressListener
import java.io.File


/**
 * @date 2024/11/26
 * <AUTHOR>
 */
class ImageCompressEngine private constructor() : CompressFileEngine {

  override fun onStartCompress(
    context: Context?,
    source: ArrayList<Uri>?,
    call: OnKeyValueResultCallbackListener?
  ) {
    Luban.with(context).load(source).ignoreBy(100)
      .setCompressListener(object : OnNewCompressListener {
        override fun onStart() {
        }

        override fun onSuccess(source: String, compressFile: File) {
          call?.onCallback(source, compressFile.absolutePath)
        }

        override fun onError(source: String, e: Throwable) {
          call?.onCallback(source, null)
        }
      }).launch()
  }

  companion object {

    private var instance: ImageCompressEngine? = null

    @JvmStatic
    fun getInstance(): ImageCompressEngine {
      return instance ?: synchronized(this) {
        instance ?: ImageCompressEngine().also { instance = it }
      }
    }
  }
}