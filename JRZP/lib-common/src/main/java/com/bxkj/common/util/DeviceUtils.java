package com.bxkj.common.util;

import android.app.Service;
import android.content.Context;
import android.content.pm.PackageManager;
import android.content.res.AssetFileDescriptor;
import android.media.AudioAttributes;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.media.SoundPool;
import android.os.Build;
import android.os.Vibrator;
import android.util.Log;

import com.bxkj.common.R;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.util
 * @Description:
 * @TODO: TODO
 * @date 2019/7/16
 */
public class DeviceUtils {

  private static boolean shouldPlayBeep = true;

  /**
   * 标签TAG
   */
  public static final String TAG = DeviceUtils.class.getSimpleName();

  /**
   * @param context Context实例
   * @param milliseconds 震动时长 , 单位毫秒
   */
  public static void vibrate(Context context, long milliseconds) {
    Vibrator vib = (Vibrator) context.getSystemService(Service.VIBRATOR_SERVICE);
    if (vib != null) {
      vib.vibrate(milliseconds);
    }
  }

  /**
   * 播放声音
   */
  public static void playSound(Context context) {
    AudioManager audioService = (AudioManager) context
        .getSystemService(Context.AUDIO_SERVICE);
    if (audioService.getRingerMode() != AudioManager.RINGER_MODE_NORMAL) {
      shouldPlayBeep = false;//检查当前是否是静音模式
    }
    MediaPlayer mediaPlayer = new MediaPlayer();
    mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
    mediaPlayer.setOnCompletionListener(mp -> {
      mp.stop();
      mp.release();
    });
    // 绑定异常监听器
    mediaPlayer.setOnErrorListener((mp, what, extra) -> {
      Log.w(TAG, "__Failed to beep " + what + ", " + extra);
      // possibly media player error, so release and recreate
      mp.stop();
      mp.release();
      return true;
    });
    try (AssetFileDescriptor file = context.getResources()
        .openRawResourceFd(R.raw.scan_qr_code_success)) {
      mediaPlayer.setDataSource(file.getFileDescriptor(), file.getStartOffset(), file.getLength());
      mediaPlayer.setVolume(0.10f, 0.10f);
      mediaPlayer.prepare();
      if (shouldPlayBeep) {
        mediaPlayer.start();
      }
    } catch (IOException e) {
      e.printStackTrace();
      mediaPlayer.release();
    }
  }

  /**
   * 获取SoundPool对象
   */
  public static SoundPool getSoundPool() {
    SoundPool soundPool;
    if (Build.VERSION.SDK_INT >= 21) {
      SoundPool.Builder builder = new SoundPool.Builder();
      //传入音频的数量
      builder.setMaxStreams(1);
      //AudioAttributes是一个封装音频各种属性的类
      AudioAttributes.Builder attrBuilder = new AudioAttributes.Builder();
      //设置音频流的合适属性
      attrBuilder.setLegacyStreamType(AudioManager.STREAM_MUSIC);
      builder.setAudioAttributes(attrBuilder.build());
      soundPool = builder.build();
    } else {
      //第一个参数是可以支持的声音数量，第二个是声音类型，第三个是声音品质
      soundPool = new SoundPool(1, AudioManager.STREAM_SYSTEM, 5);
    }
    return soundPool;
  }

  /**
   * 设备是否有闪光灯
   */
  public static boolean hasCameraFlash(Context context) {
    try {
      return context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA_FLASH);
    } catch (Exception e) {
      return false;
    }
  }
}
