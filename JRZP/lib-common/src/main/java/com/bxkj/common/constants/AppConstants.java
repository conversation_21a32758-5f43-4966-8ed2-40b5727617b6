package com.bxkj.common.constants;

import android.os.Environment;

import androidx.annotation.IntDef;

import java.io.File;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description: 全局常量
 * @TODO: TODO
 * @date 2018/4/1
 */

public class AppConstants {

  //主页是否启动
  public static boolean MAIN_START = false;

  //个人用户
  public static final int USER_TYPE_PERSONAL = 1;

  //企业用户
  public static final int USER_TYPE_COMPANY = 2;

  @IntDef({ USER_TYPE_PERSONAL, USER_TYPE_COMPANY })
  @Retention(RetentionPolicy.SOURCE)
  @Target(ElementType.PARAMETER)
  public @interface UserType {

  }

  //版本
  public static final String APP_VERSION_NAME = "3.8.1";

  public static final long APP_VERSION_CODE = 191;

  //腾讯短视频UGC
  public static final String TENCENT_UGC_LICENCE_URL =
    "http://license.vod2.myqcloud.com/license/v1/dbe70f6e5475d76620337b3f86da5021/TXUgcSDK.licence";

  public static final String TENCENT_UGC_LICENSE_KEY = "b2618513e4fe2e24a80eb2b23bfb0759";

  //腾讯直播
  public static final String TENCENT_LIVE_LICENCE_URL =
    "http://license.vod2.myqcloud.com/license/v1/dbe70f6e5475d76620337b3f86da5021/TXLiveSDK.licence";

  public static final String TENCENT_LIVE_LICENCE_KEY = "b2618513e4fe2e24a80eb2b23bfb0759";

  //腾讯IM APPID
  public static final long TENCENT_LIVE_APP_ID = **********;

  //微信APPID
  public static final String WX_APP_ID = "wx869f3aee6ca30557";

  //华为APPKey
  public static final String HUAWEI_APP_KEY = "CgB6e3x9X20PToXESkge4O2hvY33qz1O8BOeMiCrz6Mh7pyazDkz30zvi2EzuxgILsSbELpHVR3Xb6/A3zjvwStC";

  //QQ APPID
  public static final String TENCENT_APP_ID = "**********";

  public static final String APP_PACKAGE_NAME = "com.bxkj.jrzp";

  //JaBridge名称
  public static final String JS_BRIDGE_NAME = "WebViewJavascriptBridge";

  //sp
  public static final String SP_IS_FIRST_START = "isFirstStart";

  public static final String SP_USER_UUID = "USER_UUID";

  public static final String SP_FIRST_REQ_CHANGE_PHONE = "firstReqChangeWechat";

  public static final String SP_USER_TOKEN = "USER_TOKEN";

  public static final String SP_USER_ID = "userId";

  public static final String SP_USER_NAME = "userName";

  public static final String SP_USER_TYPE = "userType";

  public static final String SP_USER_LOCATE_CITY = "userLocateCity";

  public static final String SP_USER_LOCATE_CITY_ID = "userLocateCityId";

  public static final String SP_USER_SELECTED_DISTRACT_NAME = "USER_SELECTED_DISTRACT_NAME";

  public static final String SP_USER_SELECTED_DISTRACT_ID = "USER_SELECTED_DISTRACT_ID";

  public static final String SP_USER_CITY = "USER_SELECTED_CITY_v2";

  public static final String SP_USER_CITY_ID = "USER_SELECTED_CITY_ID_v2";

  public static final String SP_USER_PROVINCE_ID = "userProvinceId";

  public static final String SP_USER_PROVINCE_NAME = "userProvinceName";

  public static final String SP_IS_AGREE_PRIVACY_POLICY = "is_agree_privacy_policy";

  public static final String SP_IS_VISITOR_MODE = "IS_VISITOR_MODE";

  public static final String SP_USER_LOCATION_LNG = "USER_NEARBY_LOCATION_LNG";

  public static final String SP_USER_LOCATION_LAT = "USER_NEARBY_LOCATION_LAT";

  public static final String SP_USER_OFFLINE_SEARCH_ACTION = "USER_OFF_LINE_SEARCH_ACTION";

  public static final String SP_USER_AGREE_PRIVACY_VERSION = "user_agree_privacy_version";

  public static final String SP_USER_NEARBY_JOB_ID_LIST = "USER_NEARBY_JOB_ID_LIST";

  public static final String SP_USER_PERSONALIZED_STATE = "USER_PERSONALIZED_STATE";

  public static final String SP_GEEK_UI_CONFIG_MAIN_TAB_TWO_STATE =
    "GEEK_UI_CONFIG_MAIN_TAB_TWO_STATE";

  public static final String SP_USER_REGISTER_PHONE = "userRegisterPhone";

  public static final String SP_PUSH_TOKEN = "push_token";
  public static final String SP_OAID = "oa_id";

  public static final String SP_IS_REGISTER_PUSH = "is_register_push";

  public static final String SP_FIRST_REQUEST_LOCATION_PERMISSION = "REJECT_LOCATION_PERMISSION";

  //用户类型
  public static final int PERSONAL_TYPE = 1;

  public static final int BUSINESS_TYPE = 2;

  //推送消息分组
  public static final String NOTIFICATION_CHANNEL_ID_PUSH = "push";

  //通知消息分组
  public static final String NOTIFICATION_CHANNEL_ID_NOTICE = "notice";

  //屏幕
  public static int SCREEN_WIDTH;

  public static int SCREEN_HEIGHT;

  public static float SCREEN_DENSITY;

  /* APP路径 */
  public static final String APP_PATH = Environment.getExternalStorageDirectory().getPath()
    + File.separator
    + "jrzp"
    + File.separator;

  //文件下载路径
  public static final String APP_DOWNLOAD_FILE_PATH = APP_PATH + "Download" + File.separator;

  //文件下载相对路径
  public static final String APP_DOWNLOAD_FILE_RELATIVE_PATH =
    File.separator + "Download" + File.separator;

  //缓存路径
  public static final String APP_CACHE_PATH = APP_PATH + "Cache" + File.separator;

  //图片缓存路径
  public static final String APP_IMG_CACHE_PATH = APP_CACHE_PATH + "Image" + File.separator;

  //app下载路径
  public static final String APK_FILE_NAME = "jrzp.apk";

  public static final String DOWNLOAD_PATH_V2 = File.separator + "download" + File.separator;

  public static final String DATA_CACHE_PATH = File.separator + "cache" + File.separator;
}
