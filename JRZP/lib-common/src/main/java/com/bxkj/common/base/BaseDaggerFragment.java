package com.bxkj.common.base;

import android.content.Context;

import com.bxkj.common.mvp.mvp.BasePresenter;
import com.bxkj.common.mvp.mvp.BaseView;
import com.bxkj.common.util.CheckUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import dagger.android.AndroidInjector;
import dagger.android.DispatchingAndroidInjector;
import dagger.android.HasAndroidInjector;
import dagger.android.support.AndroidSupportInjection;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib
 * @Description:
 * @TODO: TODO
 * @date 2018/3/30
 */

public abstract class BaseDaggerFragment extends BaseFragment
    implements HasAndroidInjector, BaseView {

  @Inject
  DispatchingAndroidInjector<Object> androidInjector; //注入器

  private List<BasePresenter> mPresenters;

  @Override
  public void onAttach(Context context) {
    AndroidSupportInjection.inject(this);
    super.onAttach(context);
    bindPresenter(initPresenter(new ArrayList<>()));
  }

  @Override
  public AndroidInjector<Object> androidInjector() {
    return androidInjector;
  }

  private void bindPresenter(List<BasePresenter> presenters) {
    mPresenters = presenters;
    if (!CheckUtils.isNullOrEmpty(mPresenters)) {
      for (BasePresenter mPresenter : mPresenters) {
        mPresenter.attachView(this);
      }
    }
  }

  protected List<BasePresenter> initPresenter(List<BasePresenter> presenters) {
    return presenters;
  }

  @Override public void showLoading(String text) {

  }

  @Override
  public void onError(String errMsg) {
    showToast(errMsg);
  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    if (!CheckUtils.isNullOrEmpty(mPresenters)) {
      for (BasePresenter mPresenter : mPresenters) {
        mPresenter.detachView();
      }
    }
  }
}
