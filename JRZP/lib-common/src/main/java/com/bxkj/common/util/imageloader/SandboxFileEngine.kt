package com.bxkj.common.util.imageloader

import android.content.Context
import com.luck.picture.lib.engine.UriToFileTransformEngine
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener
import com.luck.picture.lib.utils.SandboxTransformUtils

/**
 * @date 2024/11/26
 * <AUTHOR>
 */
class SandboxFileEngine private constructor() : UriToFileTransformEngine {
  override fun onUriToFileAsyncTransform(
    context: Context?,
    srcPath: String?,
    mineType: String?,
    call: OnKeyValueResultCallbackListener?
  ) {
    call?.let {
      val sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType)
      call.onCallback(srcPath, sandboxPath)
    }
  }

  companion object {

    private var instance: SandboxFileEngine? = null

    @JvmStatic
    fun getInstance(): SandboxFileEngine {
      return instance ?: synchronized(this) {
        instance ?: SandboxFileEngine().also { instance = it }
      }
    }
  }
}