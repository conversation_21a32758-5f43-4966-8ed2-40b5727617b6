package com.bxkj.common.widget.popup.menupopup;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.bxkj.common.R;
import com.bxkj.common.util.CheckUtils;
import com.bxkj.common.widget.popup.IWheelOptions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget.popup.bottommenupopup
 * @Description:
 * @TODO: TODO
 * @date 2018/7/13
 */
public class PopupMenuListAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

  private Context mContext;
  private List<String> mItems;
  private boolean mIsSelect;
  private int mSelectPosition = 0;
  private OnItemClickListener mOnItemClickListener;
  private boolean mOpenSelect;

  public PopupMenuListAdapter(Context context, boolean isSelect, boolean openSelect) {
    mContext = context;
    mIsSelect = isSelect;
    mOpenSelect = openSelect;
    mItems = new ArrayList<>();
  }

  public void setData(String[] items) {
    mItems = Arrays.asList(items);
    notifyDataSetChanged();
  }

  public void setData(List<? extends IWheelOptions> items) {
    mItems.clear();
    for (IWheelOptions item : items) {
      mItems.add(item.getItemOption());
    }
    notifyDataSetChanged();
  }

  public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
    mOnItemClickListener = onItemClickListener;
  }

  @NonNull
  @Override
  public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return mIsSelect ? new SelectViewHolder(LayoutInflater.from(mContext)
        .inflate(R.layout.common_recycler_menu_select_item, parent, false))
        : new NormalViewHolder(LayoutInflater.from(mContext)
            .inflate(R.layout.common_recycler_bottom_menu_item, parent, false));
  }

  @Override
  public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
    if (!mIsSelect) {
      NormalViewHolder normalViewHolder = (NormalViewHolder) holder;
      normalViewHolder.tvItem.setText(Html.fromHtml(mItems.get(position)));
      if (mOpenSelect) {
        normalViewHolder.tvItem.setTextColor(
            ContextCompat.getColorStateList(mContext, R.color.common_333333_to_ff7647_selector));
        normalViewHolder.tvItem.setSelected(mSelectPosition == position);
        normalViewHolder.ivSelected.setVisibility(
            mSelectPosition == position ? View.VISIBLE : View.GONE);
      }
    } else {
      SelectViewHolder selectViewHolder = (SelectViewHolder) holder;
      selectViewHolder.tvItem.setText(mItems.get(position));
      selectViewHolder.tvItem.setSelected(mSelectPosition == position);
      selectViewHolder.ivSelected.setVisibility(
          mSelectPosition == position ? View.VISIBLE : View.GONE);
    }

    holder.itemView.setOnClickListener(view -> {
      mSelectPosition = position;
      notifyDataSetChanged();
      if (mOnItemClickListener != null) {
        mOnItemClickListener.onItemClicked(view, position);
      }
    });
  }

  @Override
  public int getItemCount() {
    return mItems.size();
  }

  class SelectViewHolder extends RecyclerView.ViewHolder {

    private TextView tvItem;
    private ImageView ivSelected;

    public SelectViewHolder(View itemView) {
      super(itemView);
      tvItem = itemView.findViewById(R.id.tv_item);
      ivSelected = itemView.findViewById(R.id.iv_selected);
    }
  }

  class NormalViewHolder extends RecyclerView.ViewHolder {

    private TextView tvItem;
    private ImageView ivSelected;

    public NormalViewHolder(View itemView) {
      super(itemView);
      tvItem = itemView.findViewById(R.id.tv_item);
      ivSelected = itemView.findViewById(R.id.iv_selected);
    }
  }

  public List<String> getData() {
    return mItems;
  }

  public void setSelected(String selectedItem) {
    if (CheckUtils.isNullOrEmpty(selectedItem)) return;
    for (int i = 0; i < mItems.size(); i++) {
      if (mItems.get(i).equals(selectedItem)) {
        mSelectPosition = i;
        notifyDataSetChanged();
        break;
      }
    }
  }
}
