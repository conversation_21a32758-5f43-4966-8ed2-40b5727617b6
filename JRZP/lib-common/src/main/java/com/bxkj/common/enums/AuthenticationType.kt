package com.bxkj.common.enums

import androidx.annotation.IntDef
import kotlin.annotation.AnnotationRetention.SOURCE
import kotlin.annotation.AnnotationTarget.VALUE_PARAMETER

/**
 * @Description:
 * @author:45457
 * @date: 2020/9/14
 * @version: V1.0
 */
class AuthenticationType {

  companion object {

    const val QUERY_HIGHER_AUTH = -99

    const val PERSONAL = 1
    const val ENTERPRISE = 2
    const val HR = 3
    const val SCHOOL = 4
    const val ORG = 5
    const val INSTITUTIONS = 6
    const val PERSONAL_RECRUITER = 7
    const val JOB_PROVE = 101

    const val UN_AUTH_CODE = 30004

    fun getAuthTypeText(@Type authType: Int): String {
      return when (authType) {
        PERSONAL -> {
          "个人认证"
        }

        ENTERPRISE -> {
          "企业认证"
        }

        HR -> {
          "人力资源认证"
        }

        SCHOOL -> {
          "学校认证"
        }

        ORG -> {
          "机构认证"
        }

        INSTITUTIONS -> {
          "事业单位认证"
        }

        PERSONAL_RECRUITER -> {
          "个人招聘"
        }

        else -> {
          "未知"
        }
      }
    }

    /**
     * 认证等级比企业高
     */
    @JvmStatic
    fun higherEnterpriseAuth(@Type authType: Int): Boolean {
      return authType >= ENTERPRISE
    }

    @JvmStatic
    fun isHROrEnterprise(@Type authType: Int): Boolean {
      return authType == ENTERPRISE || authType == HR
    }

    @JvmStatic
    fun isSchool(@Type authType: Int): Boolean {
      return authType == SCHOOL
    }

    @JvmStatic
    fun isPersonal(@Type authType: Int): Boolean {
      return authType == PERSONAL
    }

    /**
     * 事业单位
     */
    @JvmStatic
    fun isInstitutions(@Type authType: Int): Boolean {
      return authType == INSTITUTIONS
    }

    @JvmStatic
    fun isQueryHigherAuth(@Type authType: Int): Boolean {
      return authType == QUERY_HIGHER_AUTH
    }
  }

  @IntDef(
    QUERY_HIGHER_AUTH,
    PERSONAL,
    ENTERPRISE,
    HR,
    SCHOOL,
    ORG,
    INSTITUTIONS,
    PERSONAL_RECRUITER,
    JOB_PROVE
  )
  @Target(VALUE_PARAMETER)
  @Retention(SOURCE)
  annotation class Type {}
}