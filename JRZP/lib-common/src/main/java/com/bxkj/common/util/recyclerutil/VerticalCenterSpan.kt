package com.bxkj.common.util.recyclerutil

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.text.TextPaint
import android.text.style.ReplacementSpan

/**
 * @Project: gzgk
 * @Package com.bxkj.commonlib.util
 * @Description: 垂直居中span
 * <AUTHOR>
 * @date 2020/3/2
 * @version V1.0
 */
class VerticalCenterSpan(private var mTextSize: Float) : ReplacementSpan() {

    override fun getSize(paint: Paint, text: CharSequence, start: Int, end: Int, fm: Paint.FontMetricsInt?): Int {
        val textPaint = getTextPaint(paint)
        return textPaint.measureText(text.subSequence(start, end).toString()).toInt()
    }

    override fun draw(canvas: Canvas, text: CharSequence, start: Int, end: Int, x: Float, top: Int, y: Int, bottom: Int, paint: Paint) {
        val subText = text.subSequence(start, end)
        val textPaint = getTextPaint(paint)
        val fm = textPaint.fontMetricsInt
        canvas.drawText(subText.toString(), x, (y - ((y + fm.descent + y + fm.ascent) / 2 - (bottom + top) / 2)).toFloat(), textPaint)
    }

    private fun getTextPaint(paint: Paint): TextPaint {
        val textPaint = TextPaint(paint)
        textPaint.textSize = mTextSize
        textPaint.color = Color.parseColor("#767676")
        return textPaint
    }
}