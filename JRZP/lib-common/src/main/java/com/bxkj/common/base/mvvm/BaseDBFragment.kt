package com.bxkj.common.base.mvvm

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import com.bxkj.common.base.mvvm.viewmodel.BaseViewModel
import com.gyf.immersionbar.ImmersionBar
import dagger.android.support.AndroidSupportInjection
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import javax.inject.Inject

/**
 * @Description: fragment基类
 * @date 2019/4/3
 */
abstract class BaseDBFragment<VB : ViewDataBinding, VM : BaseViewModel> : Fragment() {
  @Inject
  lateinit var mViewModelFactory: ViewModelFactory

  lateinit var viewBinding: VB
    private set
  lateinit var viewModel: VM
    private set

  private var isDataInitiated = false
  private var mCompositeDisposable: CompositeDisposable? = null
  protected lateinit var parentActivity: FragmentActivity
    private set

  override fun onResume() {
    super.onResume()
    initImmersionBar()
    prepareFetchData()
  }

  override fun onHiddenChanged(hidden: Boolean) {
    super.onHiddenChanged(hidden)
    if (!hidden) {
      initImmersionBar()
    }
  }

  private fun prepareFetchData(): Boolean {
    if (!isDataInitiated) {
      lazyLoadData()
      isDataInitiated = true
      return true
    }
    return false
  }

  protected open fun initImmersionBar() {}

  protected open fun lazyLoadData() {}

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    viewModel = ViewModelProvider(this, mViewModelFactory).get(getViewModelClass())
  }

  override fun onAttach(context: Context) {
    AndroidSupportInjection.inject(this)
    super.onAttach(context)
    if (context is FragmentActivity) {
      parentActivity = context
    }
  }

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?,
  ): View? {
    viewBinding = DataBindingUtil.inflate(inflater, getLayoutId(), container, false)
    viewBinding.lifecycleOwner = this
    initViewModel(viewModel)
    return viewBinding.root
  }

  override fun onViewCreated(
    view: View,
    savedInstanceState: Bundle?,
  ) {
    super.onViewCreated(view, savedInstanceState)
    initImmersionBar()
    initPage(view, savedInstanceState)
  }

  fun afterLogin(before: () -> Unit) {
    (parentActivity as BaseNActivity).afterLogin(before)
  }

  fun relaunchApp() {
    (parentActivity as BaseNActivity).relaunchApp()
  }

  fun checkVisitorMode(success: () -> Unit) {
    (parentActivity as BaseNActivity).checkVisitorMode(success = success)
  }

  open fun showErrorAndQuit(errTips: String) {
    (parentActivity as BaseNActivity).showErrorAndQuit(errTips)
  }

  private fun initViewModel(viewModel: VM) {
    (parentActivity as BaseDBActivity<*, *>).initViewModel(viewModel)
  }

  abstract fun getViewModelClass(): Class<VM>

  abstract fun getLayoutId(): Int

  protected abstract fun initPage(
    view: View,
    saveInstanceState: Bundle?,
  )

  protected val statusBarManager: ImmersionBar
    protected get() = (parentActivity as BaseDBActivity<*, *>).statusBarManager

  protected fun showToast(msg: String) {
    (parentActivity as BaseDBActivity<*, *>).showToast(msg)
  }

  protected val localUserId: Int
    protected get() = (parentActivity as BaseNActivity).localUserId

  protected fun getResColor(
    @ColorRes colorId: Int,
  ): Int = ContextCompat.getColor(parentActivity, colorId)

  protected fun checkLoginAndToLogin(): Boolean =
    (parentActivity as BaseDBActivity<*, *>?)!!.checkToLogin()

  protected fun addDisposable(disposable: Disposable) {
    if (mCompositeDisposable == null) {
      mCompositeDisposable = CompositeDisposable()
    }
    mCompositeDisposable?.add(disposable)
  }

  override fun onDestroyView() {
    mCompositeDisposable?.clear()
    viewBinding?.unbind()
    super.onDestroyView()
  }
}
