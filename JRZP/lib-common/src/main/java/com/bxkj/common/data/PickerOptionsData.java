package com.bxkj.common.data;

import android.os.Parcel;
import android.os.Parcelable;

import com.bxkj.common.widget.filterpopup.FilterOption;
import com.bxkj.common.widget.popup.IWheelOptions;
import com.contrarywind.interfaces.IPickerViewData;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.data
 * @Description:
 * @TODO: TODO
 * @date 2018/4/13
 */

public class PickerOptionsData implements IPickerViewData, FilterOption, IWheelOptions, Parcelable {

  private int id;
  private String name;
  private boolean selected;

  public PickerOptionsData(int id, String name) {
    this.id = id;
    this.name = name;
  }

  protected PickerOptionsData(Parcel in) {
    id = in.readInt();
    name = in.readString();
    selected = in.readByte() != 0;
  }

  @Override
  public void writeToParcel(Parcel dest, int flags) {
    dest.writeInt(id);
    dest.writeString(name);
    dest.writeByte((byte) (selected ? 1 : 0));
  }

  @Override
  public int describeContents() {
    return 0;
  }

  public static final Creator<PickerOptionsData> CREATOR = new Creator<PickerOptionsData>() {
    @Override
    public PickerOptionsData createFromParcel(Parcel in) {
      return new PickerOptionsData(in);
    }

    @Override
    public PickerOptionsData[] newArray(int size) {
      return new PickerOptionsData[size];
    }
  };

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  @Override
  public String getPickerViewText() {
    return name;
  }

  @Override
  public String getItemOption() {
    return name;
  }

  public boolean isSelected() {
    return selected;
  }

  public void setSelected(boolean selected) {
    this.selected = selected;
  }

  @Override public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    PickerOptionsData that = (PickerOptionsData) o;
    return Objects.equals(name, that.name);
  }
}
