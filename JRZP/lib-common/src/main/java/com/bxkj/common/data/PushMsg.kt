package com.bxkj.common.data

import android.os.Parcelable
import com.bxkj.common.constants.PushConstants
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 *
 * @author: sanjin
 * @date: 2022/1/14
 */
@Parcelize
data class PushMsg(
    var type: String?,
    var messageType: String?,
    @SerializedName("userType")
    var userType: String?,
    var comName: String?,
    @SerializedName("resID")
    var chatResumeId: String?,
    @SerializedName("relID")
    var chatJobId: String?,
    @SerializedName("cUserID")
    var senderUserId: String?,
    @SerializedName("resid")
    var resumeId: String?,
    @SerializedName("uid")
    var userId: String?,
    var liveID: String?,
    var roomID: String?,
    var questionID: String?,
    var questionTitle: String?,
    var answerID: String?,
    var ncID: String?,
    @SerializedName("jobType2s")
    var recommendResumeTypes:String?
) : Parcelable {
    companion object {
        fun fromJsonString(jsonString: String): PushMsg {
            return Gson().fromJson(jsonString, PushMsg::class.java)
        }
    }

    fun isChatMsg(): Boolean {
        return getMsgType() == PushConstants.MSG_TYPE_CONVERSATION_MSG ||
                getMsgType() == PushConstants.MSG_TYPE_RECEIVE_DELIVERY_INVITE ||
                getMsgType() == PushConstants.MSG_TYPE_RECEIVE_INTERVIEW_INVITATION ||
                getMsgType() == PushConstants.MSG_TYPE_RECEIVE_RESUME ||
                getMsgType() == PushConstants.MSG_TYPE_ACCEPT_DELIVERY_INVITATION ||
                getMsgType() == PushConstants.MSG_TYPE_ACCEPT_INTERVIEW_INVITATION ||
                getMsgType() == PushConstants.MSG_INTERVIEW_RESULT_ACCEPT ||
                getMsgType() == PushConstants.MSG_INTERVIEW_RESULT_REJECT
    }

    fun getMsgType(): String? {
        return if (type.isNullOrBlank()) messageType else type
    }

    fun getMsgId(): String? {
        return if (ncID.isNullOrBlank()) getMsgType() else ncID
    }
}