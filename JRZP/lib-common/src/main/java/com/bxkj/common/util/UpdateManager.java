package com.bxkj.common.util;

import android.app.Activity;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import com.bxkj.common.constants.AppConstants;
import com.bxkj.common.network.NetworkManager;
import com.hjq.toast.Toaster;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget.dialog
 * @Description: 更新管理类
 * @TODO: TODO
 * @date 2018/5/5
 */
public class UpdateManager {

    private Activity mActivity;

    /* 进度条与通知ui刷新的handler和msg常量 */
//  private ProgressDialog mProgressDialog;

    private static final int DOWN_UPDATE = 1;

    private static final int DOWN_OVER = 2;

    private static final int DOWN_FAILED = 3;

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case DOWN_UPDATE:
                    int progress = (int) msg.obj;
//          mProgressDialog.setProgress(progress);
                    break;
                case DOWN_OVER:
                    String path = (String) msg.obj;
                    SystemUtil.installApk(mActivity, path);
                    break;
                case DOWN_FAILED:
                    Toaster.show("下载失败");
                    break;
                default:
                    break;
            }
        }
    };

    public UpdateManager(Activity activity) {
        this.mActivity = activity;
    }

    public void startDownload(String url, String savePath) {
        if (!NetworkTool.isNetworkAvailable(mActivity)) {
            Toaster.show("当前无网络连接");
            return;
        }
        Toaster.show("开始后台下载");
        downloadApk(url, savePath);
    }

    /**
     * 下载apk
     */
    private void downloadApk(String url, String savePath) {
        download(url, savePath, new OnDownloadListener() {
            @Override
            public void onDownloadSuccess() {
                Message msg = mHandler.obtainMessage();
                msg.what = DOWN_OVER;
                msg.obj = savePath;
                mHandler.sendMessage(msg);
            }

            @Override
            public void onDownloading(int progress) {
                Message msg = mHandler.obtainMessage();
                msg.what = DOWN_UPDATE;
                msg.obj = progress;
                mHandler.sendMessage(msg);
            }

            @Override
            public void onDownloadFailed() {
                mHandler.sendEmptyMessage(DOWN_FAILED);
            }
        });
    }

    /**
     * 安装apk
     */
    public interface OnDownloadListener {

        /**
         * 下载成功
         */
        void onDownloadSuccess();

        /**
         * @param progress 下载进度
         */
        void onDownloading(int progress);

        /**
         * 下载失败
         */
        void onDownloadFailed();
    }

    private OkHttpClient okHttpClient = NetworkManager.getDownloadOkHttpClient();

    public void download(final String url, final String savePath, final OnDownloadListener listener) {
        Request request = new Request.Builder().url(url).build();
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                // 下载失败
                listener.onDownloadFailed();
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                File file = new File(savePath);
                if (file.exists()) {
                    file.delete();
                }
                file.getParentFile().mkdirs();
                file.createNewFile();
                try (
                        InputStream inputStream = Objects.requireNonNull(response.body()).byteStream();
                        FileOutputStream fos = new FileOutputStream(
                                file)
                ) {
                    long sum = 0;
                    int len;
                    byte[] buf = new byte[2048];
                    long totalLength = response.body().contentLength();
                    while ((len = inputStream.read(buf)) != -1) {
                        fos.write(buf, 0, len);
                        sum += len;
                        int progress = (int) (sum * 1.0f / totalLength * 100);
                        listener.onDownloading(progress);
                    }
                    fos.flush();
                    listener.onDownloadSuccess();
                } catch (Exception e) {
                    e.printStackTrace();
                    listener.onDownloadFailed();
                }
            }
        });
    }
}
