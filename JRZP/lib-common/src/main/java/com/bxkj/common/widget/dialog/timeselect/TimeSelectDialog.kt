package com.bxkj.common.widget.dialog.timeselect

import android.os.Bundle
import android.view.View
import androidx.fragment.app.DialogFragment
import com.bigkoo.pickerview.adapter.ArrayWheelAdapter
import com.bxkj.common.R
import com.bxkj.common.databinding.CommonDialogTimeSelectBinding
import com.bxkj.common.widget.dialog.ZPDialogFragment
import com.hjq.toast.Toaster

/**
 * Description:
 * Author:45457
 **/
class TimeSelectDialog constructor(
    private val title: String,
    private val onConfirmClickListener: OnConfirmClickListener?
) : ZPDialogFragment<CommonDialogTimeSelectBinding>() {

    companion object {

        private const val MODE_EXACT = 1
        private const val MODE_LOOSE = 2
    }

    private var _mode = MODE_EXACT

    private var _firstTimeSelectedPosition = 0
    private var _secondTimeSelectedPosition = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.BottomSheetDialog)
    }

    override fun enableBottomSheet(): Boolean {
        return true
    }

    override fun getLayoutId(): Int = R.layout.common_dialog_time_select

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewBinding.tvTitle.text = title
        enableDragClose(false)
        setupWheelContent()

        viewBinding.tvCancel.setOnClickListener {
            dismiss()
        }

        viewBinding.tvConfirm.setOnClickListener {
            val firstTimeValue = viewBinding.wheelTimeTimeFirst.adapter.getItem(_firstTimeSelectedPosition).toString()
            val secondTimeValue =
                viewBinding.wheelTimeTimeSecond.adapter.getItem(_secondTimeSelectedPosition).toString()
            if (_mode == MODE_LOOSE && _firstTimeSelectedPosition > _secondTimeSelectedPosition) {
                Toaster.show("开始时间不能大于结束时间")
                return@setOnClickListener
            }
            val time = if (_mode == MODE_EXACT) {
                "$firstTimeValue:$secondTimeValue"
            } else {
                "$firstTimeValue-$secondTimeValue"
            }
            dismiss()
            onConfirmClickListener?.onConfirm(time)
        }
    }

    private fun setupWheelContent() {
        viewBinding.wheelTimeMode.setCyclic(false)
        viewBinding.wheelTimeTimeFirst.setCyclic(false)
        viewBinding.wheelTimeTimeSecond.setCyclic(false)
        viewBinding.wheelTimeMode.apply {
            adapter = ArrayWheelAdapter(arrayListOf("精确时间", "宽松时间"))
            setOnItemSelectedListener {
                _mode = if (it == 0) {
                    MODE_EXACT
                } else {
                    MODE_LOOSE
                }
                refreshTimeWheelContent()
            }
        }
        viewBinding.wheelTimeTimeFirst.setOnItemSelectedListener {
            _firstTimeSelectedPosition = it
        }
        viewBinding.wheelTimeTimeSecond.setOnItemSelectedListener {
            _secondTimeSelectedPosition = it
        }
        refreshTimeWheelContent()
    }

    private fun refreshTimeWheelContent() {
        when (_mode) {
            MODE_EXACT -> {
                viewBinding.wheelTimeTimeFirst.adapter = ArrayWheelAdapter(
                    arrayListOf(
                        "08",
                        "09",
                        "10",
                        "11",
                        "12",
                        "13",
                        "14",
                        "15",
                        "16",
                        "17",
                        "18",
                        "19",
                        "20"
                    )
                )
                viewBinding.wheelTimeTimeSecond.adapter =
                    ArrayWheelAdapter(arrayListOf("00", "10", "20", "30", "40", "50"))
            }

            MODE_LOOSE -> {
                viewBinding.wheelTimeTimeFirst.adapter = ArrayWheelAdapter(
                    arrayListOf(
                        "08:00",
                        "09:00",
                        "10:00",
                        "11:00",
                        "12:00",
                        "13:00",
                        "14:00",
                        "15:00",
                        "16:00",
                        "17:00",
                        "18:00",
                        "19:00",
                        "20:00"
                    )
                )
                viewBinding.wheelTimeTimeSecond.adapter = ArrayWheelAdapter(
                    arrayListOf(
                        "08:00",
                        "09:00",
                        "10:00",
                        "11:00",
                        "12:00",
                        "13:00",
                        "14:00",
                        "15:00",
                        "16:00",
                        "17:00",
                        "18:00",
                        "19:00",
                        "20:00"
                    )
                )
            }
        }
    }

    interface OnConfirmClickListener {

        fun onConfirm(selected: String)
    }
}