package com.bxkj.common.util.location

import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

/**
 * 百度坐标（BD09）、国测局坐标（火星坐标，GCJ02）、和WGS84坐标系之间的转换的工具
 *
 * 参考 https://github.com/wandergis/coordtransform 实现的Kotlin版本
 * <AUTHOR>
 */
object CoordinateTransformUtil {
  private const val x_pi = Math.PI * 3000.0 / 180.0

  // π
  private const val pi = Math.PI

  // 长半轴
  private const val a = 6378245.0

  // 扁率
  private const val ee = 0.00669342162296594323

  /**
   * 百度坐标系(BD-09)转WGS坐标
   *
   * @param lng 百度坐标经度
   * @param lat 百度坐标纬度
   * @return WGS84坐标数组
   */
  @JvmStatic
  fun bd09towgs84(lng: Double, lat: Double): DoubleArray {
    val gcj = bd09togcj02(lng, lat)
    return gcj02towgs84(gcj[0], gcj[1])
  }

  /**
   * WGS坐标转百度坐标系(BD-09)
   *
   * @param lng WGS84坐标系的经度
   * @param lat WGS84坐标系的纬度
   * @return 百度坐标数组
   */
  @JvmStatic
  fun wgs84tobd09(lng: Double, lat: Double): DoubleArray {
    val gcj = wgs84togcj02(lng, lat)
    return gcj02tobd09(gcj[0], gcj[1])
  }

  /**
   * 火星坐标系(GCJ-02)转百度坐标系(BD-09)
   *
   * 谷歌、高德——>百度
   * @param lng 火星坐标经度
   * @param lat 火星坐标纬度
   * @return 百度坐标数组
   */
  @JvmStatic
  fun gcj02tobd09(lng: Double, lat: Double): DoubleArray {
    val z = sqrt(lng * lng + lat * lat) + 0.00002 * sin(lat * x_pi)
    val theta = atan2(lat, lng) + 0.000003 * cos(lng * x_pi)
    val bd_lng = z * cos(theta) + 0.0065
    val bd_lat = z * sin(theta) + 0.006
    return doubleArrayOf(bd_lng, bd_lat)
  }

  /**
   * 百度坐标系(BD-09)转火星坐标系(GCJ-02)
   *
   * 百度——>谷歌、高德
   * @param bd_lon 百度坐标纬度
   * @param bd_lat 百度坐标经度
   * @return 火星坐标数组
   */
  @JvmStatic
  fun bd09togcj02(bd_lon: Double, bd_lat: Double): DoubleArray {
    val x = bd_lon - 0.0065
    val y = bd_lat - 0.006
    val z = sqrt(x * x + y * y) - 0.00002 * sin(y * x_pi)
    val theta = Math.atan2(y, x) - 0.000003 * cos(x * x_pi)
    val gg_lng = z * cos(theta)
    val gg_lat = z * sin(theta)
    return doubleArrayOf(gg_lng, gg_lat)
  }

  /**
   * WGS84转GCJ02(火星坐标系)
   *
   * @param lng WGS84坐标系的经度
   * @param lat WGS84坐标系的纬度
   * @return 火星坐标数组
   */
  @JvmStatic
  fun wgs84togcj02(lng: Double, lat: Double): DoubleArray {
    if (outOfChina(lng, lat)) {
      return doubleArrayOf(lng, lat)
    }
    val dlat = transformlat(lng - 105.0, lat - 35.0)
    val dlng = transformlng(lng - 105.0, lat - 35.0)
    val radlat = lat / 180.0 * pi
    val magic = Math.sin(radlat)
    val magic2 = 1 - ee * magic * magic
    val sqrtmagic = sqrt(magic2)
    val dlat2 = (dlat * 180.0) / ((a * (1 - ee)) / (magic2 * sqrtmagic) * pi)
    val dlng2 = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * pi)
    val mglat = lat + dlat2
    val mglng = lng + dlng2
    return doubleArrayOf(mglng, mglat)
  }

  /**
   * GCJ02(火星坐标系)转GPS84
   *
   * @param lng 火星坐标系的经度
   * @param lat 火星坐标系纬度
   * @return WGS84坐标数组
   */
  @JvmStatic
  fun gcj02towgs84(lng: Double, lat: Double): DoubleArray {
    if (outOfChina(lng, lat)) {
      return doubleArrayOf(lng, lat)
    }
    val dlat = transformlat(lng - 105.0, lat - 35.0)
    val dlng = transformlng(lng - 105.0, lat - 35.0)
    val radlat = lat / 180.0 * pi
    val magic = Math.sin(radlat)
    val magic2 = 1 - ee * magic * magic
    val sqrtmagic = sqrt(magic2)
    val dlat2 = (dlat * 180.0) / ((a * (1 - ee)) / (magic2 * sqrtmagic) * pi)
    val dlng2 = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * pi)
    val mglat = lat + dlat2
    val mglng = lng + dlng2
    return doubleArrayOf(lng * 2 - mglng, lat * 2 - mglat)
  }

  /**
   * 纬度转换
   *
   * @param lng
   * @param lat
   * @return
   */
  @JvmStatic
  fun transformlat(lng: Double, lat: Double): Double {
    var ret =
      -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * sqrt(abs(lng))
    ret += (20.0 * sin(6.0 * lng * pi) + 20.0 * sin(2.0 * lng * pi)) * 2.0 / 3.0
    ret += (20.0 * sin(lat * pi) + 40.0 * sin(lat / 3.0 * pi)) * 2.0 / 3.0
    ret += (160.0 * sin(lat / 12.0 * pi) + 320 * sin(lat * pi / 30.0)) * 2.0 / 3.0
    return ret
  }

  /**
   * 经度转换
   *
   * @param lng
   * @param lat
   * @return
   */
  @JvmStatic
  fun transformlng(lng: Double, lat: Double): Double {
    var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * sqrt(abs(lng))
    ret += (20.0 * sin(6.0 * lng * pi) + 20.0 * sin(2.0 * lng * pi)) * 2.0 / 3.0
    ret += (20.0 * sin(lng * pi) + 40.0 * sin(lng / 3.0 * pi)) * 2.0 / 3.0
    ret += (150.0 * sin(lng / 12.0 * pi) + 300.0 * sin(lng / 30.0 * pi)) * 2.0 / 3.0
    return ret
  }

  /**
   * 判断是否在国内，不在国内不做偏移
   *
   * @param lng
   * @param lat
   * @return
   */
  @JvmStatic
  fun outOfChina(lng: Double, lat: Double): Boolean {
    return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271
  }
}
