package com.bxkj.common.util;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bxkj.common.R;


/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.shop.weight
 * @Description: TitleBar管理类
 * @TODO: TODO
 * @date 2018/2/28
 */

public class TitleBarManager {

    private View mTitleView;
    private ImageView imgLeft;
    private ImageView imgRight;
    private TextView tvTitle;
    private TextView tvRight;
    private View currentRightOption;

    public static TitleBarManager with(@NonNull View view) {
        return new TitleBarManager(view);
    }

    private TitleBarManager(View view) {
        mTitleView = view;
        imgLeft = view.findViewById(R.id.iv_left);
        imgRight = view.findViewById(R.id.iv_right_option_three);
        tvTitle = view.findViewById(R.id.tv_title);
        tvRight = view.findViewById(R.id.tv_right);
    }

    /**
     * 设置背景颜色
     *
     * @param color
     * @return
     */
    public TitleBarManager setBackgroundColor(@ColorInt int color) {
        mTitleView.setBackgroundColor(color);
        return this;
    }

    /**
     * 设置标题
     *
     * @param title
     * @return
     */
    public TitleBarManager setTitle(String title) {
        tvTitle.setText(title);
        return this;
    }

    /**
     * 设置标题颜色
     *
     * @param color
     * @return
     */
    public TitleBarManager setTitleTextColor(@ColorInt int color) {
        tvTitle.setTextColor(color);
        return this;
    }

    /**
     * 设置左侧图片
     *
     * @param leftImageResId
     * @return
     */
    public TitleBarManager setLeftImage(@DrawableRes int leftImageResId) {
        imgLeft.setImageResource(leftImageResId);
        return this;
    }

    /**
     * 设置右侧图片
     *
     * @param rightImage
     * @return
     */
    public TitleBarManager setRightImage(@DrawableRes int rightImage) {
        imgRight.setVisibility(View.VISIBLE);
        imgRight.setImageResource(rightImage);
        return this;
    }

    /**
     * 获取右侧图片
     *
     * @return
     */
    public ImageView getRightImgae() {
        return imgRight;
    }

    /**
     * 设置右侧文字
     *
     * @param text
     * @return
     */
    public TitleBarManager setRightText(String text) {
        tvRight.setVisibility(View.VISIBLE);
        tvRight.setText(text);
        return this;
    }

    /**
     * 获取右边文字选项
     * @return
     */
    public TextView getRightText() {
        return tvRight;
    }

    /**
     * 设置右侧文字颜色
     *
     * @param color
     * @return
     */
    public TitleBarManager setRightTextColor(@ColorInt int color) {
        tvRight.setTextColor(color);
        return this;
    }

    /**
     * 设置左边点击事件
     *
     * @param onClickListener
     * @return
     */
    public TitleBarManager setLeftOptionClickListener(View.OnClickListener onClickListener) {
        imgLeft.setOnClickListener(onClickListener);
        return this;
    }

    /**
     * 设置右边点击事件
     *
     * @param onClickListener
     * @return
     */
    public TitleBarManager setRightOptionClickListener(View.OnClickListener onClickListener) {
        if (imgRight.getVisibility() == View.VISIBLE) {
            imgRight.setOnClickListener(onClickListener);
        } else {
            tvRight.setOnClickListener(onClickListener);
        }
        return this;
    }

    public TitleBarManager setTitleVisibility(int visibility) {
        tvTitle.setVisibility(visibility);
        return this;
    }
}
