package com.bxkj.common.base;

import android.view.View;
import androidx.recyclerview.widget.RecyclerView;
import com.bxkj.common.R;
import com.bxkj.common.mvp.mvp.BaseHasListView;
import com.bxkj.common.network.exception.RespondThrowable;
import com.bxkj.common.util.refreshlayoutmanger.RefreshLayoutManager;
import com.bxkj.common.widget.pagestatuslayout.PageStatusConfigFactory;
import com.bxkj.common.widget.pagestatuslayout.PageStatusLayout;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib
 * @Description: 包含list的fragment
 * @TODO: TODO
 * @date 2018/10/24
 */
public abstract class BaseListFragment extends BaseLazyLoadFragment implements BaseHasListView {

    private View refreshContent;
    private RecyclerView recyclerContent;

    private RefreshLayoutManager mRefreshLayoutManager;
    private PageStatusLayout mPageStatusLayout;

    @Override
    public void initPage() {
        refreshContent = rootView.findViewById(R.id.refresh_content);
        recyclerContent = rootView.findViewById(R.id.recycler_content);

        mPageStatusLayout = PageStatusLayout.wrap(refreshContent);
        mPageStatusLayout.show(PageStatusConfigFactory.newLoadingConfig());
        mRefreshLayoutManager = new RefreshLayoutManager(refreshContent);
        mRefreshLayoutManager.setOnRefreshListener(this::fetchData)
                .setOnLoadMoreListener(this::fetchData)
                .setOnNotDataListener(this::onNoData)
                .setOnLoadErrorListener(this::onLoadError);
    }

    protected void onNoData() {
        mPageStatusLayout.show(PageStatusConfigFactory.newEmptyConfig());
    }

    protected void onLoadError() {
        mPageStatusLayout.show(PageStatusConfigFactory
                .newErrorConfig()
                .setOnButtonClickListener(() -> {
                            getPageStatusLayout().show(PageStatusConfigFactory.newLoadingConfig());
                            getRefreshLayoutManager().refreshPage();
                        }
                ));
    }

    protected RecyclerView getRecyclerView() {
        return recyclerContent;
    }

    protected PageStatusLayout getPageStatusLayout() {
        return mPageStatusLayout;
    }

    protected RefreshLayoutManager getRefreshLayoutManager() {
        return mRefreshLayoutManager;
    }

    @Override
    public void onResultNoData() {
        mRefreshLayoutManager.noMoreData();
    }

    @Override
    public void onRequestError(RespondThrowable respondThrowable) {
        mRefreshLayoutManager.loadError();
    }

}
