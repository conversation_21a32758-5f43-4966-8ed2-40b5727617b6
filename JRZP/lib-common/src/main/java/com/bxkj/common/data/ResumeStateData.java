package com.bxkj.common.data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.enterprise.data
 * @Description: 简历状态
 * @TODO: TODO
 * @date 2018/8/17
 */
public class ResumeStateData {

  public static final int RESUME_TYPE_USER = 1; //用户主动投递
  public static final int RESUME_TYPE_SYSTEM = 2; //系统推荐
  public static final int RESUME_TYPE_COMPANY_DOWNLOAD = 3; //企业主动下载(来源系统推荐)
  public static final int RESUME_TYPE_COMPANY_DOWNLOAD_2 = 4; //企业主动下载
  public static final int RESUME_TYPE_INVITE = 5; //邀请投递
  public static final int RESUME_TYPE_USER_RECOMMEND = 6; //用户推荐
  public static final int RESUME_TYPE_PART_TIME = 7; //兼职投递

  public static final int RESUME_STATE_UNTREATED = 0; //未处理
  public static final int RESUME_STATE_PRELIMINARY = 1; //初选合格（待沟通）
  public static final int RESUME_STATE_ELIMINATE = 2; //淘汰
  public static final int RESUME_STATE_INVITE_INTERVIEW = 3; //已发送面试
  public static final int RESUME_STATE_REJECT_INTERVIEW = 4; //面试被拒绝
  public static final int RESUME_STATE_ACCEPT_INTERVIEW = 5; //已接受面试
  public static final int RESUME_STATE_CANCEL_INTERVIEW = 12; //取消面试
  public static final int RESUME_STATE_PASS_INTERVIEW = 6; //面试通过


  private int id;

  private int islook;

  /**
   * 1用户主动投递；
   * 2系统推荐
   * 3企业主动下载(来源系统推荐)
   * 4企业主动下载
   * 5邀请投递
   * 6用户推荐
   * 7兼职投递
   */
  private int type;

  /**
   * 处理状态
   * 0未处理
   * 1初选合格（待沟通）
   * 2淘汰
   * 3已发送面试
   * 4面试被拒绝
   * 5已接受面试
   * 12取消面试
   * 6面试通过
   * 7已发offer
   * 8已拒offer
   * 9接受offer
   * 10已入职
   * 11已满有效期
   */
  private int state;

  private int relid;

  private int mianshiType;

  public int getId() {
    return id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public int getIslook() {
    return islook;
  }

  public void setIslook(int islook) {
    this.islook = islook;
  }

  public int getType() {
    return type;
  }

  public void setType(int type) {
    this.type = type;
  }

  public int getState() {
    return state;
  }

  public void setState(int state) {
    this.state = state;
  }

  public int getRelid() {
    return relid;
  }

  public void setRelid(int relid) {
    this.relid = relid;
  }

  //淘汰或拒绝面试
  public boolean isEliminateOrReject() {
    return state == 2 || state == 4;
  }

  public boolean acceptVideoCall() {
    return mianshiType == 1 && state == 5;
  }

  public String getStatusText() {
    String statusText = "";
    switch (state) {
      case 2: {
        statusText = "已淘汰";
        break;
      }
      case 4: {
        statusText = "面试被拒绝";
        break;
      }
    }
    return statusText;
  }

  /**
   * 不是系统推荐
   */
  public boolean notSystemRecommend() {
    return type != 2;
  }

  /**
   * 接受面试状态
   */
  public boolean onAcceptInterviewStatus() {
    return !isEliminateOrReject() && notSystemRecommend() && state == 5;
  }

  /**
   * 已邀请面试
   */
  public boolean onInvitedInterviewStatus() {
    return (!isEliminateOrReject()) && notSystemRecommend() && state == 3;
  }

  /**
   * 在面试过程中
   */
  public boolean inInterviewProcess() {
    return !isEliminateOrReject() && notSystemRecommend();
  }
}
