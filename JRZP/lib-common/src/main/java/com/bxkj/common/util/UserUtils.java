package com.bxkj.common.util;

import android.os.Build;
import android.webkit.CookieManager;
import android.webkit.CookieSyncManager;
import android.webkit.WebStorage;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.constants.AppConfig;
import com.bxkj.common.constants.AppConstants;
import com.bxkj.common.enums.ChatRole;
import com.bxkj.common.util.rxbus.RxBus;
import com.bxkj.common.util.rxbus.RxMsgCode;
import com.elvishew.xlog.XLog;
import com.tencent.android.tpush.XGIOperateCallback;
import com.tencent.android.tpush.XGPushManager;
import com.tencent.android.tpush.XGPushManager.AccountInfo;
import com.tencent.android.tpush.XGPushManager.AccountType;
import com.tencent.qcloud.ugckit.trtc.model.TRTCCalling;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * @version V1.0
 * @Description: 用户属性工具类
 */

public class UserUtils {

  public static final String[] TEST_LIVE_ANCHOR_USER_IDS = new String[] { "529873", "609290" };

  /**
   * 保存个人用户MainTabTwo状态
   *
   * @param state 0：兼职 1：校招
   */
  public static void saveGeekUIConfigMainTabTwoState(int state) {
    SPUtils.getInstance().put(AppConstants.SP_GEEK_UI_CONFIG_MAIN_TAB_TWO_STATE, state);
  }

  /**
   * 获取个人用户MainTabTwo状态
   *
   * @return 0：兼职 1：校招
   */
  public static int getGeekUIConfigMainTabTwoState() {
    return SPUtils.getInstance().getInt(AppConstants.SP_GEEK_UI_CONFIG_MAIN_TAB_TWO_STATE, 0);
  }

  /**
   * 保存用户是否开启个性化推送
   */
  public static void savePersonalizedPushState(boolean open) {
    SPUtils.getInstance().put(AppConstants.SP_USER_PERSONALIZED_STATE, open);
  }

  /**
   * 获取用户是否开启个性化推送
   */
  public static boolean getPersonalizedPushState() {
    return SPUtils.getInstance().getBoolean(AppConstants.SP_USER_PERSONALIZED_STATE);
  }

  /**
   * 返回UUID
   */
  public static String getUserUUID() {
    ensureUserUUID();
    return SPUtils.getInstance().getString(AppConstants.SP_USER_UUID);
  }

  public static boolean isUserFirstRequestChangePhone() {
    return SPUtils.getInstance().getBoolean(AppConstants.SP_FIRST_REQ_CHANGE_PHONE, true);
  }

  public static void setUserFirstRequestChangePhone(boolean isFirst) {
    SPUtils.getInstance().put(AppConstants.SP_FIRST_REQ_CHANGE_PHONE, isFirst);
  }

  /**
   * 确保UUID存在
   */
  private static void ensureUserUUID() {
    if (CheckUtils.isNullOrEmpty(SPUtils.getInstance().getString(AppConstants.SP_USER_UUID))) {
      SPUtils.getInstance().put(AppConstants.SP_USER_UUID, UUID.randomUUID().toString());
    }
  }

  /**
   * 保存用户token
   */
  public static void saveUserToken(String token) {
    SPUtils.getInstance().put(AppConstants.SP_USER_TOKEN, token);
  }

  /**
   * 返回UserToken
   */
  public static String getUserToken() {
    return SPUtils.getInstance().getString(AppConstants.SP_USER_TOKEN);
  }

  /**
   * 获取用户id
   */
  public static int getUserId() {
    return SPUtils.getInstance().getInt(AppConstants.SP_USER_ID, CommonApiConstants.NO_ID);
  }

  //是否同意隐私条款
  public static boolean isAgreePrivacyPolicy() {
    return SPUtils.getInstance().getBoolean(AppConstants.SP_IS_AGREE_PRIVACY_POLICY, false);
  }

  /**
   * 用户同意隐私条款
   */
  public static void agreePrivacyPolicy() {
    SPUtils.getInstance().put(AppConstants.SP_IS_AGREE_PRIVACY_POLICY, true);
  }

  /**
   * 用户已同意的隐私条款版本
   */
  public static String getUserAgreePrivacyVersion() {
    return SPUtils.getInstance().getString(AppConstants.SP_USER_AGREE_PRIVACY_VERSION);
  }

  /**
   * 保存用户已同意的隐私条款版本
   */
  public static void saveUserAgreePrivacyVersion(String version) {
    SPUtils.getInstance().put(AppConstants.SP_USER_AGREE_PRIVACY_VERSION, version);
  }

  /**
   * 用户类型
   */
  public static int getUserAuthType() {
    return SPUtils.getInstance().getInt(AppConstants.SP_USER_TYPE, AppConstants.USER_TYPE_PERSONAL);
  }

  public static int getUserChatRole() {
    return getUserAuthType() == AppConstants.USER_TYPE_PERSONAL ? ChatRole.PERSONAL : ChatRole.BUSINESS;
  }

  /**
   * 是否是个人用户
   */
  public static boolean isPersonalRole() {
    return getUserAuthType() == AppConstants.USER_TYPE_PERSONAL;
  }

  /**
   * 判断是否登录
   */
  public static boolean logged() {
    return getUserId() != CommonApiConstants.NO_ID;
  }

  /**
   * 判断登录用户是否自己
   */
  public static boolean userIsSelf(int userId) {
    return UserUtils.logged() && (userId == getUserId());
  }

  /**
   * 保存用户ID
   */
  public static void saveUserID(int userID) {
    bindPushAccount(String.valueOf(userID));
    SPUtils.getInstance().put(AppConstants.SP_USER_ID, userID);
  }

  /**
   * 保存用户信息
   */
  public static void saveUserData(int userId, int userType) {
    bindPushAccount(String.valueOf(userId));
    SPUtils.getInstance().put(AppConstants.SP_USER_ID, userId);
    SPUtils.getInstance().put(AppConstants.SP_USER_TYPE, userType);
    RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_USER_ROLE_CHANGE));
  }

  /**
   * 设置用户类型
   */
  public static void setupUserRole(int userType) {
    SPUtils.getInstance().put(AppConstants.SP_USER_TYPE, userType);
    RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_USER_ROLE_CHANGE));
  }

  /**
   * 保存用户名（或手机号）
   */
  public static void saveUserName(String userName) {
    SPUtils.getInstance().put(AppConstants.SP_USER_NAME, userName);
  }

  /**
   * 清除用户信息
   */
  public static void removeUserData() {
    delPushAccount();
    TRTCCalling.destroySharedInstance();
    SPUtils.getInstance().remove(AppConstants.SP_USER_ID);
    SPUtils.getInstance().remove(AppConstants.SP_USER_TYPE);
    SPUtils.getInstance().remove(AppConstants.SP_USER_NAME);
    SPUtils.getInstance().remove(AppConstants.SP_PUSH_TOKEN);
    SPUtils.getInstance().remove(AppConstants.SP_USER_TOKEN);
    setUserFirstRequestChangePhone(true);
    clearUserWebCookies();
  }

  /**
   * 清除用户cookies
   */
  private static void clearUserWebCookies() {
    CookieManager cookieManager = CookieManager.getInstance();
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      cookieManager.removeSessionCookies(null);
      cookieManager.removeAllCookies(null);
      cookieManager.flush();
    } else {
      cookieManager.removeAllCookie();
      CookieSyncManager.getInstance().sync();
    }
    WebStorage.getInstance().deleteAllData();
  }

  /**
   * 保存用户选择区县
   */
  public static void saveUserSelectedDistrictInfo(int cityId, String cityName) {
    SPUtils.getInstance().put(AppConstants.SP_USER_SELECTED_DISTRACT_ID, cityId);
    SPUtils.getInstance().put(AppConstants.SP_USER_SELECTED_DISTRACT_NAME, cityName);
  }

  /**
   * 获取用户选择区县id
   */
  public static int getUserSelectedDistrictId() {
    return SPUtils.getInstance().getInt(AppConstants.SP_USER_SELECTED_DISTRACT_ID);
  }

  /**
   * 获取用户选择区县
   */
  public static String getUserSelectedDistrictName() {
    return SPUtils.getInstance().getString(AppConstants.SP_USER_SELECTED_DISTRACT_NAME);
  }

  /**
   * 清除用户区县信息
   */
  public static void clearUserDistrictInfo() {
    SPUtils.getInstance().remove(AppConstants.SP_USER_SELECTED_DISTRACT_NAME);
    SPUtils.getInstance().remove(AppConstants.SP_USER_SELECTED_DISTRACT_ID);
  }

  /**
   * 保存用户手动选择的城市
   */
  public static void saveUserSelectedCityInfo(int cityId, String cityName) {
    final int oldCityId = getUserSelectedCityId();
    final String oldCityName = getUserSelectedCityName();
    SPUtils.getInstance().put(AppConstants.SP_USER_CITY_ID, cityId);
    SPUtils.getInstance().put(AppConstants.SP_USER_CITY, cityName);
    clearUserDistrictInfo();
    if (oldCityId != cityId && !oldCityName.equals(cityName)) {
      RxBus.get().post(RxBus.Message.fromCode(RxMsgCode.ACTION_SELECT_CITY_CHANGE));
    }
  }

  /**
   * 保存用户定位城市
   */
  public static void saveUserLocateCityData(int cityId, String cityName) {
    SPUtils.getInstance().put(AppConstants.SP_USER_LOCATE_CITY_ID, cityId);
    SPUtils.getInstance().put(AppConstants.SP_USER_LOCATE_CITY, cityName);
  }

  /**
   * 保存用户省份
   */
  public static void saveUserProvinceInfo(int provinceId, String provinceName) {
    SPUtils.getInstance().put(AppConstants.SP_USER_PROVINCE_ID, provinceId);
    SPUtils.getInstance().put(AppConstants.SP_USER_PROVINCE_NAME, provinceName);
  }

  /**
   * 保存用户定位信息
   */
  public static void saveUserLocation(double lng, double lat) {
    SPUtils.getInstance().put(AppConstants.SP_USER_LOCATION_LNG, lng);
    SPUtils.getInstance().put(AppConstants.SP_USER_LOCATION_LAT, lat);
  }

  /**
   * 获取用户经度
   */
  public static double getUserLocationLng() {
    return SPUtils.getInstance()
      .getDouble(AppConstants.SP_USER_LOCATION_LNG, CommonApiConstants.NO_DATA);
  }

  /**
   * 获取用户纬度
   */
  public static double getUserLocationLat() {
    return SPUtils.getInstance()
      .getDouble(AppConstants.SP_USER_LOCATION_LAT, CommonApiConstants.NO_DATA);
  }

  /**
   * 获取用户城市
   */
  public static String getUserSelectedCityName() {
    return CheckUtils.isNullOrEmpty(SPUtils.getInstance().getString(AppConstants.SP_USER_CITY))
      ? "全国"
      : SPUtils.getInstance().getString(AppConstants.SP_USER_CITY);
  }

  /**
   * 获取用户省份
   */
  public static String getUserProvinceName() {
    return SPUtils.getInstance().getString(AppConstants.SP_USER_PROVINCE_NAME);
  }

  /**
   * 获取用户省份id
   */
  public static int getUserProvinceId() {
    return SPUtils.getInstance().getInt(AppConstants.SP_USER_PROVINCE_ID, CommonApiConstants.NO_ID);
  }

  /**
   * 用户选择城市ID
   */
  public static int getUserSelectedCityId() {
    return SPUtils.getInstance().getInt(AppConstants.SP_USER_CITY_ID, CommonApiConstants.NO_ID);
  }

  /**
   * 用户定位城市名字
   */
  public static String getUserLocateCityName() {
    return SPUtils.getInstance().getString(AppConstants.SP_USER_LOCATE_CITY);
  }

  /**
   * 用户定位城市ID
   */
  public static int getUserLocateCityId() {
    return SPUtils.getInstance().getInt(AppConstants.SP_USER_LOCATE_CITY_ID, 28590);
  }

  /**
   * 获取用户名（或手机号）
   */
  public static String getUserName() {
    return SPUtils.getInstance().getString(AppConstants.SP_USER_NAME);
  }

  /**
   * 记录用户离线搜索行为
   */
  public static void recordUserOfflineSearchAction(String keyword) {
    SPUtils.getInstance()
      .put(AppConstants.SP_USER_OFFLINE_SEARCH_ACTION,
        getUserOfflineSearchAction() + keyword + ",");
  }

  /**
   * 获取用户离线搜索行为
   */
  public static String getUserOfflineSearchAction() {
    return SPUtils.getInstance().getString(AppConstants.SP_USER_OFFLINE_SEARCH_ACTION);
  }

  /**
   * 清除用户离线搜索行为
   */
  public static void clearUserOfflineSearchAction() {
    SPUtils.getInstance().remove(AppConstants.SP_USER_OFFLINE_SEARCH_ACTION);
  }

  /**
   * 保存附近id列表
   */
  public static void saveUserNearbyJobIdList(String jobIdList) {
    SPUtils.getInstance().put(AppConstants.SP_USER_NEARBY_JOB_ID_LIST, jobIdList);
  }

  /**
   * 获取附近id列表
   */
  public static String getUserNearbyJobIdList() {
    return SPUtils.getInstance().getString(AppConstants.SP_USER_NEARBY_JOB_ID_LIST);
  }

  /**
   * web登录凭证
   */
  public static String getUserWebParams() {
    return "?ly=android" + "&para=" + getUserWebToken();
  }

  public static String getUserWebToken() {
    return UserUtils.getUserId() == CommonApiConstants.NO_ID ? "logout" : AESOperator.safeEncrypt(
      getUserId() + "|" + TimeUtils.getCurrentTime(TimeUtils.SECOND, "-")
    );
  }

  /**
   * 保存用户注册手机号
   */
  public static void saveUserRegisterPhone(String phoneNumber) {
    SPUtils.getInstance().put(AppConstants.SP_USER_REGISTER_PHONE, phoneNumber);
  }

  /**
   * 获取用户注册手机号
   */
  public static String getUserRegisterPhone() {
    return SPUtils.getInstance().getString(AppConstants.SP_USER_REGISTER_PHONE);
  }

  public static void saveOAID(String oaid) {
    SPUtils.getInstance().put(AppConstants.SP_OAID, oaid);
  }

  public static String getOAID() {
    return SPUtils.getInstance().getString(AppConstants.SP_OAID);
  }

  //============================ 推送 ============================//

  //获取推送token
  public static String getPushToken() {
    return SPUtils.getInstance().getString(AppConstants.SP_PUSH_TOKEN);
  }

  //保存推送是否注册
  public static void setRegisterPush(boolean registered) {
    SPUtils.getInstance().put(AppConstants.SP_IS_REGISTER_PUSH, registered);
  }

  //绑定推送账户
  public static void bindPushAccount(String newUserId) {
    final List<AccountInfo> accountInfo = new ArrayList<>();
    accountInfo
      .add(new XGPushManager.AccountInfo(AccountType.UNKNOWN.getValue(),
        String.valueOf(newUserId)));
    XGPushManager
      .upsertAccounts(AppConfig.getAppContext(), accountInfo, new XGIOperateCallback() {
        @Override
        public void onSuccess(Object o, int i) {
          XLog.d("onSuccess: code=" + i + "----msg=" + o);
        }

        @Override
        public void onFail(Object o, int i, String s) {
          XLog.d("onFail: code=" + i + "----msg=" + s);
        }
      });
  }

  /**
   * 刪除推送账户
   */
  public static void delPushAccount() {
    final Set<Integer> delAccounts = new HashSet<>();
    delAccounts.add(AccountType.UNKNOWN.getValue());
    XGPushManager.delAccounts(AppConfig.getAppContext(), delAccounts, new XGIOperateCallback() {
      @Override
      public void onSuccess(Object o, int i) {
        XLog.d("onSuccess: code=" + i + "----msg=" + o);
      }

      @Override
      public void onFail(Object o, int i, String s) {
        XLog.d("onFail: code=" + i + "----msg=" + s);
      }
    });
  }
}
