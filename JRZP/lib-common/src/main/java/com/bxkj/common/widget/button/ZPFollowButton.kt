package com.bxkj.common.widget.button

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.widget.FrameLayout
import android.widget.ProgressBar
import android.widget.TextView
import com.bxkj.common.R
import com.bxkj.common.util.kotlin.dip

/**
 *
 * @author: sanjin
 * @date: 2021/7/13
 */
class ZPFollowButton @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyleAttr: Int = R.attr.zp_followButtonStyle,
) : FrameLayout(context, attrs, defStyleAttr) {

  private var loadingViewDrawable: Drawable? = null
  private var loadingViewSize: Int = android.view.ViewGroup.LayoutParams.MATCH_PARENT

  private var progressBar: ProgressBar? = null

  private var followText: TextView? = null

  private var isLoading: Boolean = false

  init {
    setupAttrs(context, attrs, defStyleAttr)
    initProgressBar()
  }

  private fun setupAttrs(context: Context, attrs: AttributeSet?, defStyleAttr: Int) {
    val typedArray =
      context.obtainStyledAttributes(attrs, R.styleable.ZPFollowButton, defStyleAttr, 0)

    loadingViewDrawable = typedArray.getDrawable(R.styleable.ZPFollowButton_loadingViewDrawable)
    loadingViewSize =
      typedArray.getDimensionPixelOffset(R.styleable.ZPFollowButton_loadingViewSize, 0)

    val textSize =
      typedArray.getDimension(R.styleable.ZPFollowButton_android_textSize, 12f)
    val textColor = typedArray.getColorStateList(R.styleable.ZPFollowButton_android_textColor)

    initFollowText(textSize, textColor)

    typedArray.recycle()
  }

  private fun initFollowText(textSize: Float, textColor: ColorStateList?) {
    followText = TextView(context).apply {
      layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
        gravity = Gravity.CENTER
      }
      setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
      textColor?.let {
        setTextColor(textColor)
      }
    }
    addView(followText)
  }

  private fun initProgressBar() {
    progressBar = ProgressBar(context).apply {
      indeterminateDrawable = loadingViewDrawable
      layoutParams = LayoutParams(loadingViewSize, loadingViewSize).apply {
        topMargin = dip(2)
        bottomMargin = dip(2)
        gravity = Gravity.CENTER
      }
    }
    addView(progressBar)
    progressBar?.visibility = GONE
  }

  fun setText(text: String) {
    followText?.text = text
  }

  fun showLoading() {
    progressBar?.visibility = VISIBLE
    followText?.visibility = GONE
  }

  fun hiddenLoading() {
    progressBar?.visibility = GONE
    followText?.visibility = VISIBLE
  }

  fun setFollow(followed: Boolean) {
    isSelected = followed
    hiddenLoading()
  }

  override fun setOnClickListener(l: OnClickListener?) {
    super.setOnClickListener {
      if (!isLoading) {
        showLoading()
        l?.onClick(it)
      }
    }
  }
}