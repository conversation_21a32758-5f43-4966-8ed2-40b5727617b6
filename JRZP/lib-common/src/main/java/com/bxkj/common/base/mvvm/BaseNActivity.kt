package com.bxkj.common.base.mvvm

import android.content.Intent
import android.os.Bundle
import android.os.Process
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.bxkj.common.R
import com.bxkj.common.api.CommonApiConstants
import com.bxkj.common.util.ActivityManager
import com.bxkj.common.util.CustomDensityManager
import com.bxkj.common.util.SystemUtil
import com.bxkj.common.util.UserUtils
import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigation.LoginActivity
import com.bxkj.common.util.rxbus.RxBus
import com.bxkj.common.util.rxbus.RxMsgCode
import com.bxkj.common.widget.dialog.ActionDialog
import com.bxkj.common.widget.dialog.LoadingDialog
import com.bxkj.common.widget.dialog.LoadingDialog.Builder
import com.bxkj.common.widget.dialog.TipsDialog
import com.gyf.immersionbar.ImmersionBar
import com.hjq.toast.Toaster
import dagger.android.AndroidInjection
import dagger.android.AndroidInjector
import dagger.android.DispatchingAndroidInjector
import dagger.android.HasAndroidInjector
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import javax.inject.Inject
import kotlin.system.exitProcess

/**
 * @version V1.0
 * @date 2019/4/3
 */
abstract class BaseNActivity : FragmentActivity(),
  HasAndroidInjector {

  @Inject
  lateinit var androidInjector: DispatchingAndroidInjector<Any>
  private var mLoadingDialog: LoadingDialog? = null
  lateinit var statusBarManager: ImmersionBar
    private set

  private var mAfterLoginAction: (() -> Unit)? = null

  private var mCompositeDisposable: CompositeDisposable? = null

  override fun onCreate(savedInstanceState: Bundle?) {
    AndroidInjection.inject(this)
    super.onCreate(savedInstanceState)
    initGlobalConfig()
    mCompositeDisposable = CompositeDisposable()
    subscribeLoginSuccessEvent()
  }

  private fun subscribeLoginSuccessEvent() {
    addDisposable(RxBus.get().toObservable(RxBus.Message::class.java).subscribe {
      if (it.code == RxMsgCode.ACTION_LOGIN_SUCCESS || it.code == RxMsgCode.ENTERPRISE_LOGIN_SUCCESS) {
        mAfterLoginAction?.invoke()
        mAfterLoginAction = null
      }
    })
  }

  private fun initGlobalConfig() {
    if (enableScreenAdaptation()) {
      //屏幕适配换算
      CustomDensityManager.setCustomDensity(this, application)
    }
    //状态栏管理
    statusBarManager = ImmersionBar.with(this).navigationBarColor(R.color.common_white)
    //将activity压入栈中
    ActivityManager.getInstance().pushActivity(this)
  }

  /**
   * 是否开启屏幕适配
   */
  protected open fun enableScreenAdaptation(): Boolean {
    return true
  }

  protected open fun addDisposable(disposable: Disposable) {
    mCompositeDisposable?.add(disposable)
  }

  @JvmOverloads
  fun showLoading(@StringRes textId: Int = CommonApiConstants.NO_ID) {
    showLoading(if (textId == CommonApiConstants.NO_ID) "" else getString(textId))
  }

  fun showLoading(text: String) {
    if (mLoadingDialog == null) {
      mLoadingDialog = Builder(this).build()
    }
    if (text.isNotBlank()) {
      mLoadingDialog?.setText(text)
    } else {
      mLoadingDialog?.clearLoadingText()
    }
    mLoadingDialog?.show()
  }

  fun hiddenLoading() {
    mLoadingDialog?.let {
      if (it.isShowing) {
        it.dismiss()
      }
    }
  }

  fun showToast(text: String?) {
    Toaster.show(text)
  }

  fun showToast(@StringRes textId: Int) {
    Toaster.show(textId)
  }

  fun getResColor(@ColorRes colorResId: Int): Int {
    return ContextCompat.getColor(this, colorResId)
  }

  val localUserId: Int
    get() = UserUtils.getUserId()

  fun checkToLogin(): Boolean {
    if (UserUtils.isAgreePrivacyPolicy()) {
      if (!UserUtils.logged()) {
        navigateToLogin()
        return false
      }
      return true
    } else {
      showVisitorModeTipsDialog()
      return false
    }
  }

  @JvmOverloads
  fun showVisitorModeTipsDialog(tips: String? = getString(R.string.visitor_mode_tips)) {
    ActionDialog.Builder()
      .setTitle(getString(R.string.visitor_mode_tips_title))
      .setContent(tips)
      .setConfirmText("去授权")
      .setOnConfirmClickListener {
        relaunchApp()
      }
      .build()
      .show(supportFragmentManager)
  }

  fun relaunchApp() {
    startActivity(packageManager.getLaunchIntentForPackage(packageName)?.apply {
      flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
    })
    Process.killProcess(Process.myPid())
    exitProcess(0)
  }

  /**
   * 检查登录后执行
   */
  fun afterLogin(afterAction: () -> Unit) {
    if (checkToLogin()) {
      afterAction.invoke()
    } else {
      mAfterLoginAction = afterAction
    }
  }

  @JvmOverloads
  fun checkVisitorMode(
    tips: String? = getString(R.string.visitor_mode_tips),
    success: () -> Unit
  ) {
    if (UserUtils.isAgreePrivacyPolicy()) {
      success.invoke()
    } else {
      showVisitorModeTipsDialog(tips)
    }
  }

  /**
   * 展示错误消息并退出
   */
  fun showErrorAndQuit(errorMsg: String) {
    TipsDialog()
      .setContent(errorMsg)
      .setOnConfirmClickListener {
        finish()
      }.show(supportFragmentManager)
  }

  fun hideSoftKeyboard() {
    SystemUtil.hideSoftKeyboard(this)
  }

  protected fun navigateToLogin() {
    Router.getInstance().to(LoginActivity.URL)
      .withBoolean(LoginActivity.EXTRA_BACK_AFTER_LOGIN, true)
      .startForResult(this, LOGIN_REQUEST_CODE)
  }

  override fun androidInjector(): AndroidInjector<Any> {
    return androidInjector
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    //取消登录将登录后要执行的函数置空
    if (requestCode == LOGIN_REQUEST_CODE && resultCode != RESULT_OK) {
      mAfterLoginAction = null
    }
  }

  override fun onStop() {
    super.onStop()
    hiddenLoading()
    mLoadingDialog = null
  }

  override fun onDestroy() {
    mCompositeDisposable?.clear()
    ActivityManager.getInstance().popActivity(this)
    super.onDestroy()
  }

  override fun finish() {
    SystemUtil.hideSoftKeyboard(this)
    super.finish()
  }

  companion object {

    const val LOGIN_REQUEST_CODE = 99
  }
}