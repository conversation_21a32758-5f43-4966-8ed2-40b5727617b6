package com.bxkj.common.base.mvvm.viewmodel

import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.bxkj.common.base.mvvm.LiveEvent

/**
 * @Project: gzgk
 * @Package com.bxkj.personal.ui.activity.myhistory
 * @Description:
 * <AUTHOR>
 * @date 2020/2/17
 * @version V1.0
 */
abstract class HasDeleteBarViewModel : BaseViewModel() {

    val openDeleteBar = ObservableBoolean(false)
    private var _checkedItem: List<Int>? = null
    val checkedItem = MutableLiveData<List<Int>>()
    val deleteSuccessEvent = LiveEvent<Void>()
    val showClearConfirmEvent = LiveEvent<Void>()
    val deleteToEmptyEvent = LiveEvent<Void>()

    fun openOrCloseDeleteBar(open: Boolean) {
        openDeleteBar.set(open)
    }

    fun setCheckedItem(items: List<Int>) {
        _checkedItem = items
        checkedItem.value = _checkedItem
    }

    fun clickedDelete() {
        confirmDelete(_checkedItem)
    }

    fun clearChecked() {
        _checkedItem = null
        checkedItem.value = _checkedItem
        deleteSuccessEvent.call()
    }

    fun clear() {
        showClearConfirmEvent.call()
    }

    abstract fun confirmDelete(item: List<Int>?)

}