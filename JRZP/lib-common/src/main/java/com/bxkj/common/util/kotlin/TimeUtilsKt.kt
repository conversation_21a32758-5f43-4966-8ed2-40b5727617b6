package com.bxkj.common.util.kotlin

import com.bxkj.common.util.TimeUtils
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/26
 * @version: V1.0
 */
fun Date.format(pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
  val simpleDateFormat =
    SimpleDateFormat(pattern, Locale.CHINESE)
  simpleDateFormat.timeZone = TimeZone.getTimeZone("GMT+8:00")
  return simpleDateFormat.format(this)
}

fun String.toDate(pattern: String = "yyyy-MM-dd HH:mm:ss"): Date {
  return TimeUtils.stringToDate(pattern, this)
}

fun String.toCalender(pattern: String = "yyyy-MM-dd HH:mm:ss"): Calendar {
  val calendar = Calendar.getInstance()
  try {
    calendar.time = TimeUtils.stringToDate(pattern, this)
  } catch (e: Exception) {
    return calendar
  }
  return calendar
}

fun Long.millis2Day(): Int {
  return TimeUtils.millis2Day(this)
}

fun LocalDate.getMonday(): LocalDate {
  return this.minusDays(this.dayOfWeek.value.toLong() - 1)
}

fun LocalDate.getSunday(): LocalDate {
  return this.plusDays(7 - this.dayOfWeek.value.toLong())
}

fun LocalDate.toFormatString(pattern: String = "yyyy-MM-dd"): String {
  return this.format(DateTimeFormatter.ofPattern(pattern))
}

fun String.toLocalDate(pattern: String = "yyyy-MM-dd"): LocalDate {
  return LocalDate.parse(this, DateTimeFormatter.ofPattern(pattern))
}

fun String.toLocalDateTime(pattern: String = "yyyy-MM-dd HH:mm:ss"): LocalDateTime {
  return LocalDateTime.parse(this, DateTimeFormatter.ofPattern(pattern))
}