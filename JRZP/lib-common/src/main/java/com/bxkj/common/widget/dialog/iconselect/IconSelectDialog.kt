package com.bxkj.common.widget.dialog.iconselect

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bxkj.common.R
import com.bxkj.common.adapter.superadapter.SuperItemClickListener
import com.bxkj.common.util.DensityUtils
import com.bxkj.common.widget.dialog.BaseDialogFragment

/**
 * @Description: 带图标选择Dialog
 * @author: YangXin
 * @date: 2020/12/22
 * @version: V1.0
 */
class IconSelectDialog(private val builder: Builder) : BaseDialogFragment() {

  class Builder {
    internal val items = ArrayList<IconSelectItem>()
    internal var mSuperItemClickListener: SuperItemClickListener? = null

    fun addItem(@DrawableRes icon: Int, desc: String): Builder = apply {
      items.add(IconSelectItem(icon, desc))
    }

    fun setOnItemClickListener(superItemClickListener: SuperItemClickListener): Builder = apply {
      this.mSuperItemClickListener = superItemClickListener
    }

    fun build(): IconSelectDialog {
      return IconSelectDialog(this)
    }
  }

  override fun getRootViewId(): Int = R.layout.common_dialog_icon_select

  override fun initView() {
    rootView?.findViewById<ImageView>(R.id.iv_close)?.setOnClickListener {
      dismiss()
    }

    val recyclerItemList = rootView?.findViewById<RecyclerView>(R.id.recycler_items)
    recyclerItemList?.layoutManager = GridLayoutManager(context, builder.items.size)

    val iconItemListAdapter =
      IconSelectListAdapter(requireContext(), R.layout.common_recycler_icon_select_item).apply {
        setOnItemClickListener(object : SuperItemClickListener {
          override fun onClick(v: View, position: Int) {
            dismiss()
            builder.mSuperItemClickListener?.onClick(v, position)
          }
        })
      }

    recyclerItemList?.adapter = iconItemListAdapter

    iconItemListAdapter.reset(builder.items)
  }

  override fun onStart() {
    super.onStart()
    setupWidthAndHeight(DensityUtils.dp2px(context, 320f), ViewGroup.LayoutParams.WRAP_CONTENT)
  }

  private fun setupWidthAndHeight(width: Int, height: Int) {
    val dialog = dialog
    if (dialog != null && dialog.window != null) {
      dialog.window!!.setLayout(width, height)
    }
  }
}