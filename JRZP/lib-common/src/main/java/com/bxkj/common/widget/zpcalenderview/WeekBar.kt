package com.bxkj.common.widget.zpcalenderview

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.bxkj.common.util.kotlin.sp

/**
 * Description:
 * Author:45457
 **/
class WeekBar @JvmOverloads constructor(context: Context, attributeSet: AttributeSet? = null, defStyleRes: Int = 0) :
    AppCompatTextView(context, attributeSet, defStyleRes) {

    companion object {

        private val WEEK_DAYS = arrayOf("日", "一", "二", "三", "四", "五", "六")
    }

    private val textPaint: Paint = Paint().apply {
        isAntiAlias = true
        color = Color.parseColor("#333333")
        textSize = sp(14f).toFloat()
        textAlign = Paint.Align.CENTER
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val availableWidth = measuredWidth - paddingLeft - paddingRight
        val cellWidth = availableWidth / WEEK_DAYS.size
        for (i in WEEK_DAYS.indices) {
            val text = WEEK_DAYS[i]
            val textHeight = textPaint.fontMetrics.bottom - textPaint.fontMetrics.top
            val startX = paddingLeft + cellWidth * i + cellWidth / 2.toFloat()
            val startY = (measuredHeight - textHeight) / 2 + textHeight - textPaint.fontMetrics.bottom
            canvas.drawText(text, startX, startY, textPaint)
        }
    }
}