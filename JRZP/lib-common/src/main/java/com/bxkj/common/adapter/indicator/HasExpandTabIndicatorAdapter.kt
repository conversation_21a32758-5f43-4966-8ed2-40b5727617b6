package com.bxkj.common.adapter.indicator

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.bxkj.common.R
import com.bxkj.common.util.DensityUtils
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView.OnPagerTitleChangeListener

/**
 * @Project: VideoRecruitment
 * @Description:
 * @author:45457
 * @date: 2020/4/30
 * @version: V1.0
 */
const val TAG_EXPAND_TAB = "EXPAND_TAB"

class HasExpandTabIndicatorAdapter(
    titles: Array<String?>,
    vararg hasExpandPosition: Int?
) : MagicIndicatorAdapter(titles) {

    private var mHasExpandPosition = hasExpandPosition

    override fun getTitleView(context: Context, index: Int): IPagerTitleView {
        val hasExpandTabView = CommonPagerTitleView(context)
        val margin = DensityUtils.dp2px(context, 0f)
        val customView: View =
            LayoutInflater.from(context).inflate(R.layout.layout_has_expand_indicator_tab, null)
        val tvTabItem = customView.findViewById<TextView>(R.id.tv_tab_item)
        tvTabItem.text = getTitles()[index]
        val ivExpand = customView.findViewById<ImageView>(R.id.iv_expand)
        if (mHasExpandPosition.contains(index)) {
            hasExpandTabView.tag = TAG_EXPAND_TAB
            ivExpand.visibility = View.VISIBLE
        } else {
            ivExpand.visibility = View.GONE
        }
        hasExpandTabView.setContentView(customView, FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        ).apply {
            marginStart = margin
            marginEnd = margin
        })
        hasExpandTabView.onPagerTitleChangeListener = object : OnPagerTitleChangeListener {
            override fun onSelected(i: Int, i1: Int) {
                setCurrent(index)
                tvTabItem.isSelected = true
                ivExpand.isSelected = true
            }

            override fun onDeselected(i: Int, i1: Int) {
                tvTabItem.isSelected = false
                ivExpand.isSelected = false
            }

            override fun onLeave(i: Int, i1: Int, v: Float, b: Boolean) {}
            override fun onEnter(i: Int, i1: Int, v: Float, b: Boolean) {}
        }
        hasExpandTabView.setOnClickListener { view: View? ->
            getOnTabClickListener()?.onTabClicked(hasExpandTabView, index)
        }
        return hasExpandTabView
    }
}