package com.bxkj.common.util.rxbus;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * @Description: Rx消息码
 * @TODO: TODO
 * @date 2018/9/27
 */
public class RxMsgCode {

  private static final int INIT_MSG_CODE = 1;

  public static final int ACTION_MAIN_TAB_ONE_TO_TOP = INIT_MSG_CODE + 1;

  public static final int ACTION_MAIN_TAB_TWO_TO_TOP = ACTION_MAIN_TAB_ONE_TO_TOP + 1;

  public static final int ACTION_MAIN_TAB_THREE_TO_TOP = ACTION_MAIN_TAB_TWO_TO_TOP + 1;

  public static final int ACTION_MAIN_TAB_FOUR_TO_TOP = ACTION_MAIN_TAB_THREE_TO_TOP + 1;

  public static final int ACTION_MAIN_TAB_FIVE_TO_TOP = ACTION_MAIN_TAB_FOUR_TO_TOP + 1;

  public static final int ACTION_HIDE_NO_PERMISSION_TIPS = ACTION_MAIN_TAB_FIVE_TO_TOP + 1;

  public static final int ACTION_LOGIN_SUCCESS = ACTION_HIDE_NO_PERMISSION_TIPS + 1;

  public static final int REFRESH_INVITATIONS_TO_DELIVERY_LIST = ACTION_LOGIN_SUCCESS + 1;

  public static final int ACTION_FROM_REGISTER_USER = REFRESH_INVITATIONS_TO_DELIVERY_LIST + 1;

  public static final int ACTION_WECHAT_LOGIN_SUCCESS = ACTION_FROM_REGISTER_USER + 1;

  public static final int ACTION_SHARE_BACK = ACTION_WECHAT_LOGIN_SUCCESS + 1;

  public static final int ACTION_CERTIFICATION_SUBMIT_SUCCESS = ACTION_SHARE_BACK + 1;

  //接收到新聊天消息
  public static final int ACTION_RECEIVE_NEW_MSG = ACTION_CERTIFICATION_SUBMIT_SUCCESS + 1;

  //视频发布成功
  public static final int ACTION_PUBLISH_VIDEO_SUCCESS = ACTION_RECEIVE_NEW_MSG + 1;

  //视频加载更多
  public static final int VIDEO_GALLERY_LOAD_MORE = ACTION_PUBLISH_VIDEO_SUCCESS + 1;

  //视频浏览position改变
  public static final int ACTION_VIDEO_GALLERY_POSITION_CHANGE = VIDEO_GALLERY_LOAD_MORE + 1;

  //切换身份
  public static final int ACTION_USER_ROLE_CHANGE = ACTION_VIDEO_GALLERY_POSITION_CHANGE + 1;

  //切换手动选择城市
  public static final int ACTION_SELECT_CITY_CHANGE = ACTION_USER_ROLE_CHANGE + 1;

  //发布资讯
  public static final int COMMAND_POST_NEWS = ACTION_SELECT_CITY_CHANGE + 1;

  //企业版
  public static final int REFRESH_POSITION_LIST_CODE = COMMAND_POST_NEWS + 1;

  public static final int REFRESH_RESUME_LISE_CODE = REFRESH_POSITION_LIST_CODE + 1;

  public static final int ENTERPRISE_LOGIN_SUCCESS = REFRESH_RESUME_LISE_CODE + 1;

  public static final int ACTION_CHAT_INVITE_SUCCESS = ENTERPRISE_LOGIN_SUCCESS + 1;

  public static final int ACTION_SWITCH_MY_JOB_LIST_TYPE = ACTION_CHAT_INVITE_SUCCESS + 1;

  //校招信息发生改变
  public static final int ACTION_SCHOOL_RECRUIT_INFO_CHANGE_SUCCESS =
    ACTION_SWITCH_MY_JOB_LIST_TYPE + 1;

  //切换个性化推送开关
  public static final int ACTION_SWITCH_SYS_RECOMMEND =
    ACTION_SCHOOL_RECRUIT_INFO_CHANGE_SUCCESS + 1;

  public static final int ERROR_AUTH_FAILED = ACTION_SWITCH_SYS_RECOMMEND + 1;

  //刷新联系人列表
  public static final int REFRESH_CONTACT_LIST = ERROR_AUTH_FAILED + 1;

  public static final int BUY_BEAN_PACKAGE_SUCCESS = REFRESH_CONTACT_LIST + 1;

  //切换到校招
  public static final int ACTION_SWITCH_TO_SCHOOL_RECRUIT = BUY_BEAN_PACKAGE_SUCCESS + 1;

  public static final int EVENT_CALL_END = ACTION_SWITCH_TO_SCHOOL_RECRUIT + 1;

  @IntDef({
    ACTION_LOGIN_SUCCESS, REFRESH_INVITATIONS_TO_DELIVERY_LIST, ACTION_FROM_REGISTER_USER,
    ACTION_WECHAT_LOGIN_SUCCESS, ACTION_SHARE_BACK, ACTION_CERTIFICATION_SUBMIT_SUCCESS,
    ACTION_RECEIVE_NEW_MSG, ACTION_PUBLISH_VIDEO_SUCCESS, VIDEO_GALLERY_LOAD_MORE,
    ACTION_VIDEO_GALLERY_POSITION_CHANGE, ACTION_USER_ROLE_CHANGE, ACTION_SWITCH_TO_SCHOOL_RECRUIT,
    //企业版
    REFRESH_POSITION_LIST_CODE, REFRESH_RESUME_LISE_CODE, ENTERPRISE_LOGIN_SUCCESS,
    ACTION_CHAT_INVITE_SUCCESS, ACTION_SWITCH_MY_JOB_LIST_TYPE,
    ACTION_SWITCH_SYS_RECOMMEND, BUY_BEAN_PACKAGE_SUCCESS,
    //公用
    ERROR_AUTH_FAILED, REFRESH_CONTACT_LIST, EVENT_CALL_END
  })
  @Retention(RetentionPolicy.SOURCE)
  public @interface Code {

  }
}
