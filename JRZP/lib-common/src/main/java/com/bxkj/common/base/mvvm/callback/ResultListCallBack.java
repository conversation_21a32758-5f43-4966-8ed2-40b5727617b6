package com.bxkj.common.base.mvvm.callback;

import androidx.annotation.NonNull;

import com.bxkj.common.network.exception.RespondThrowable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.base.mvvm.callback
 * @Description:
 * @TODO: TODO
 * @date 2019/7/25
 */
public interface ResultListCallBack<T> {
    void onSuccess(T data);

    void onError(@NonNull RespondThrowable respondThrowable);

    void onNoMoreData();
}
