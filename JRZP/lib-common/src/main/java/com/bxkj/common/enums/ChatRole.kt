package com.bxkj.common.enums

import androidx.annotation.IntDef
import com.bxkj.common.util.UserUtils
import kotlin.annotation.AnnotationRetention.SOURCE

/**
 * @Project: gzgk
 * @Description:
 * @author:45457
 * @date: 2020/7/1
 * @version: V1.0
 */
class ChatRole {

  companion object {

    const val ALL = -1
    const val PERSONAL = 0
    const val BUSINESS = 1

    @IntDef(
      ALL,
      PERSONAL,
      BUSINESS
    )
    @Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
    @Retention(SOURCE)
    annotation class Role

    fun getConvertLocalRole(): Int {
      return if (AuthenticationType.higherEnterpriseAuth(UserUtils.getUserAuthType())) {
        BUSINESS
      } else {
        PERSONAL
      }
    }
  }
}