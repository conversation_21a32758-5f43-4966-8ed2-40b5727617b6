package com.bxkj.common.widget

import android.R.attr.startY
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.abs

/**
 * @Description:
 * @author:45457
 * @date: 2020/10/29
 * @version: V1.0
 */
class MyRecyclerView @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyle: Int = 0
) : RecyclerView(context, attrs, defStyle) {

  private var startX = 0
  private var startY = 0

  override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
    when (ev?.action) {
      MotionEvent.ACTION_DOWN -> {
        startX = ev.x.toInt()
        startY = ev.y.toInt()
        parent.requestDisallowInterceptTouchEvent(true)
      }
      MotionEvent.ACTION_MOVE -> {
        val endX = ev.x.toInt()
        val endY = ev.y.toInt()
        val disX = abs(endX - startX)
        val disY = abs(endY - startY)
        if (disX > disY) {
          parent.requestDisallowInterceptTouchEvent(canScrollHorizontally(startX - endX))
        } else {
          parent.requestDisallowInterceptTouchEvent(canScrollVertically(startY - endY))
        }
      }
      MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL ->
        parent.requestDisallowInterceptTouchEvent(false)
    }
    return super.dispatchTouchEvent(ev)
  }
}