package com.bxkj.common.widget.filterpopup

import android.content.Context
import android.view.View
import android.widget.TextView
import com.bxkj.common.R.id
import com.bxkj.common.adapter.superadapter.SuperAdapter
import com.bxkj.common.adapter.superadapter.SuperViewHolder

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.personal.ui.searchjobresult
 * @Description: 筛选recycler item适配器
 * @TODO: TODO
 * @date 2018/4/3
 */
class FilterRecyclerItemAdapter<T : FilterOption?> @JvmOverloads constructor(
    context: Context?,
    layoutResId: Int,
    list: List<T>? = null,
    private var openMultiSelect: Boolean = false
) : SuperAdapter<T>(context, layoutResId, list) {

    private var selectPosition = 0
    fun resetPosition() {
        clearSelected()
        selectPosition = 0
    }

    override fun openMultiSelect(): Bo<PERSON>an {
        return openMultiSelect
    }

    override fun convert(holder: SuperViewHolder, viewType: Int, item: T, position: Int) {
        val tvFilterOption = holder.findViewById<TextView>(id.tv_filter_options)
        tvFilterOption.text = item!!.name
        if (openMultiSelect()) {
            tvFilterOption.isSelected = selectedItems.contains(item)
        } else {
            tvFilterOption.isSelected = selectPosition == position
            holder.itemView.setOnClickListener { view: View? ->
                if (selectPosition == position) return@setOnClickListener
                selectPosition = position
                if (SuperItemClickListener != null) {
                    SuperItemClickListener.onClick(holder.itemView, selectPosition)
                }
                notifyDataSetChanged()
            }
        }
    }
}