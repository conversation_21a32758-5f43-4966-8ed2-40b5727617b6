package com.bxkj.common.widget.pagestatuslayout;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.widget.newemptylayout
 * @Description:
 * @TODO: TODO
 * @date 2018/10/24
 */
public class PageStatusConfigFactory {

    public static EmptyConfig newEmptyConfig() {
        return new EmptyConfig();
    }

    public static LoadingConfig newLoadingConfig() {
        return new LoadingConfig();
    }

    public static ErrorConfig newErrorConfig() {
        return new ErrorConfig();
    }
}
