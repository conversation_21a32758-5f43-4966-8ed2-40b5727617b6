package com.bxkj.common.util.location

import android.content.Context
import android.os.Looper
import androidx.fragment.app.FragmentActivity
import com.baidu.location.LocationClientOption
import com.baidu.location.LocationClientOption.LocationMode
import com.baidu.location.LocationClientOption.LocationMode.Hight_Accuracy
import com.bxkj.common.util.PermissionUtils
import com.elvishew.xlog.XLog
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.huawei.hms.location.LocationCallback
import com.huawei.hms.location.LocationRequest
import com.huawei.hms.location.LocationResult
import com.huawei.hms.location.LocationServices
import com.huawei.hms.location.LocationSettingsRequest

/**
 * @Description: 定位管理类
 * @date 2019/11/29
 * @version V1.0
 */
object LocationManager {

  fun getLocationInfo(
    context: Context,
    onLocationListener: OnLocationListener
  ) {
    XXPermissions.with(context)
      .permission(Permission.ACCESS_FINE_LOCATION)
      .request(object : OnPermissionCallback {
        override fun onGranted(permissions: MutableList<String>, all: Boolean) {
          requestLocation(context, onLocationListener)
        }

        override fun onDenied(permissions: MutableList<String>, never: Boolean) {
          onLocationListener.onFailed()
        }
      })
  }

  @JvmOverloads
  fun requestLocation(
    context: Context,
    onLocationListener: OnLocationListener,
    config: LocationClientOption.() -> LocationClientOption = { this }
  ) {
    val locationRequest = LocationRequest().apply {
      priority = LocationRequest.PRIORITY_HIGH_ACCURACY
      numUpdates = 1
    }

    LocationServices.getSettingsClient(context)
      .checkLocationSettings(
        LocationSettingsRequest.Builder().addLocationRequest(locationRequest).build()
      )
      .addOnSuccessListener { locationSettingsResponse ->
        if (locationSettingsResponse.locationSettingsStates.isLocationUsable) {
          LocationServices.getFusedLocationProviderClient(context)
            .requestLocationUpdates(locationRequest, object : LocationCallback() {
              override fun onLocationResult(p0: LocationResult?) {
                p0?.let {
                  it.lastLocation.let { location ->
                    XLog.d("定位成功：${location.latitude}，${location.longitude}")
                    onLocationListener.onSuccess(ZPLocation(location.latitude, location.longitude))
                  }
                }
              }
            }, Looper.getMainLooper())
        } else {
          onLocationListener.onFailed()
        }
      }.addOnFailureListener {
        onLocationListener.onFailed()
      }
  }

  @JvmOverloads
  fun getLocationInfo(
    activity: FragmentActivity,
    tipsTitle: String,
    tipsContent: String,
    onLocationListener: OnLocationListener,
    vararg permission: String = arrayOf(
      Permission.ACCESS_FINE_LOCATION,
      Permission.ACCESS_COARSE_LOCATION
    ),
    config: LocationClientOption.() -> LocationClientOption = { this }
  ) {
    PermissionUtils.requestPermission(
      activity,
      tipsTitle,
      tipsContent,
      object : PermissionUtils.OnRequestResultListener {
        override fun onRequestSuccess() {
          requestLocation(activity, onLocationListener, config)
        }

        override fun onRequestFailed(permissions: MutableList<String>, never: Boolean) {
          onLocationListener.onFailed()
        }
      },
      *permission
    )
  }

  interface OnLocationListener {
    fun onSuccess(location: ZPLocation)
    fun onFailed()
  }
}