package com.bxkj.common.di.module;

import android.content.Context;

import com.bxkj.common.api.CommonApi;
import com.bxkj.common.api.CommonApiConstants;
import com.bxkj.common.network.NetworkManager;

import com.bxkj.common.network.ZPCommonApi;
import dagger.Module;
import dagger.Provides;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.di.module
 * @Description: 网络请求module
 * @TODO: TODO
 * @date 2018/3/20
 */
@Module
public class NetWorkModule {

  @Provides
  OkHttpClient provideOkHttpClient(Context context) {
    return NetworkManager.getNoSSLOkHttpInstance();
  }

  @Provides
  Retrofit provideRetrofit(OkHttpClient okHttpClient) {
    Retrofit retrofit = new Retrofit.Builder()
        .client(okHttpClient)
        .baseUrl(CommonApiConstants.BASE_URL)
        .addCallAdapterFactory(RxJava2CallAdapterFactory.createAsync())
        .addConverterFactory(GsonConverterFactory.create())
        .build();
    return retrofit;
  }

  @Provides
  ZPCommonApi provideCommonApi(Retrofit retrofit) {
    return new ZPCommonApi(retrofit);
  }

  @Provides
  CommonApi provideCommonCoroutinesApi(Retrofit retrofit) {
    return retrofit.create(CommonApi.class);
  }
}
