package com.bxkj.common.widget.zpcalenderview

import com.bxkj.common.widget.zpcalenderview.ZPCalenderView.OnDateClickListener
import java.time.LocalDate

/**
 * Description:
 * Author:45457
 **/
class CalenderHelper {

    var startDate: LocalDate = LocalDate.now()
    var endDate: LocalDate = LocalDate.now().plusYears(1)

    var selectedDate: LocalDate = LocalDate.now()
        private set

    var onDateClickListener: OnDateClickListener? = null

    fun updateSelectedDate(date: LocalDate): Boolean {
        if (selectedDate != date) {
            selectedDate = date
            return true
        }
        return false
    }
}