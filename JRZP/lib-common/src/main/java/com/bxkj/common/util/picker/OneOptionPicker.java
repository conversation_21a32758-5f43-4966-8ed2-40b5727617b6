package com.bxkj.common.util.picker;

import android.content.Context;
import android.graphics.Color;

import android.view.View;
import android.view.ViewGroup;
import com.bigkoo.pickerview.builder.OptionsPickerBuilder;
import com.bigkoo.pickerview.view.OptionsPickerView;

/**
 * <AUTHOR>
 * @version V1.0
 * @Project: jdzj
 * @Package com.bxkj.commonlib.util
 * @Description:
 * @TODO: TODO
 * @date 2018/4/14
 */

public class OneOptionPicker extends Picker {

  private OnConfirmListener mOnConfirmListener;

  public OneOptionPicker(Context context) {
    super(context);
  }

  @Override
  public Picker setOnConfirmListener(OnConfirmListener onConfirmListener) {
    mOnConfirmListener = onConfirmListener;
    return this;
  }

  public OptionsPickerView create() {
    OptionsPickerView optionsPickerView =
        new OptionsPickerBuilder(mContext, (options1, options2, options3, v) -> {
          if (mOnConfirmListener != null) {
            mOnConfirmListener.onConfirm(options1);
          }
        }).setSubCalSize(16)
            .setTitleBgColor(Color.parseColor("#FFFFFF"))
            .setCancelColor(Color.parseColor("#888888"))
            .setSubmitColor(Color.parseColor("#FF7647"))
            .setSubmitText("完成")
            .build();
    optionsPickerView.setPicker(getOptionsDataList());
    return optionsPickerView;
  }

  public OptionsPickerView create(ViewGroup decorView) {
    OptionsPickerView optionsPickerView =
        new OptionsPickerBuilder(mContext, (options1, options2, options3, v) -> {
          if (mOnConfirmListener != null) {
            mOnConfirmListener.onConfirm(options1);
          }
        }).setSubCalSize(16)
            .setTitleBgColor(Color.parseColor("#FFFFFF"))
            .setCancelColor(Color.parseColor("#888888"))
            .setSubmitColor(Color.parseColor("#FF7647"))
            .setSubmitText("完成")
            .setDecorView(decorView)
            .build();
    optionsPickerView.setPicker(getOptionsDataList());
    return optionsPickerView;
  }

}
