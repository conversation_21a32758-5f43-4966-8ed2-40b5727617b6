package com.bxkj.common.widget.dialog

import android.content.Context
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.view.Gravity
import android.view.KeyEvent
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatDialog
import com.bxkj.common.R
import com.bxkj.common.api.CommonApiConstants

/**
 * @Project: biyeji-app
 * @Package com.bxkj.commonlib.widget.dialog
 * @Description: 评论Dialog
 * <AUTHOR>
 * @date 2019/10/23
 * @version V1.0
 */
class EditInfoDialog constructor(context: Context) : AppCompatDialog(context, R.style.BaseDialogTheme) {

    companion object {
        const val TAG = "CommentDialog"
    }

    var hint: String = "请输入内容"
    var initText: String = CommonApiConstants.NO_TEXT
    var inputMinLength: Int = 0
    var inputMaxLength: Int = 0
    var onConfirmClickListener: OnSendClickListener? = null

    init {
        setContentView(R.layout.dialog_edit_info)
    }

    private fun initView() {
        val tvConfirm = findViewById<TextView>(R.id.tv_confirm)
        tvConfirm?.isEnabled = (inputMinLength == 0) || (initText.length >= inputMinLength)
        val tvAmount = findViewById<TextView>(R.id.tv_length)

        val editContent = findViewById<EditText>(R.id.et_content)
        editContent?.let {
            it.hint = hint
            it.setText(initText)
            it.filters = arrayOf(InputFilter.LengthFilter(inputMaxLength))
            it.addTextChangedListener(object : TextWatcher {
                override fun afterTextChanged(edit: Editable?) {
                    edit?.let { info ->
                        tvAmount?.text = (inputMaxLength - info.length).toString()
                        tvConfirm?.isEnabled = info.length >= inputMinLength
                    }
                }

                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                }
            })
        }

        tvConfirm?.setOnClickListener {
            onConfirmClickListener?.onSendClicked(this, editContent?.text.toString())
        }

        findViewById<TextView>(R.id.tv_length_tips)?.text = context.getString(R.string.edit_dialog_length_format, inputMinLength, inputMaxLength)

        setOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.repeatCount == 0) {
                dismiss()
            }
            return@setOnKeyListener false
        }
    }

    override fun onStart() {
        super.onStart()
        window?.let { window ->
            val layoutParams = window.attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            window.attributes = layoutParams
            window.setGravity(Gravity.BOTTOM)
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE)
        }
        initView()
    }

    interface OnSendClickListener {
        fun onSendClicked(commentDialog: EditInfoDialog, content: String)
    }

    override fun dismiss() {
        if (currentFocus is TextView) {
            val mInputMethodManager = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            mInputMethodManager.hideSoftInputFromWindow(currentFocus?.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        }
        super.dismiss()
    }

}