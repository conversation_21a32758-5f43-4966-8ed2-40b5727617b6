package com.bxkj.common.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.bxkj.common.BR
import com.bxkj.common.adapter.superadapter.SuperViewHolder
import com.youth.banner.adapter.BannerAdapter

/**
 *
 * @author: sanjin
 * @date: 2022/11/18
 */
open class SimpleDBBannerAdapter<T> constructor(private val layoutId: Int, list: List<T>) :
    BannerAdapter<T, SuperViewHolder>(list) {
    override fun onCreateHolder(parent: ViewGroup, viewType: Int): SuperViewHolder {
        return SuperViewHolder(
            DataBindingUtil.inflate<ViewDataBinding>(
                LayoutInflater.from(parent.context),
                layoutId,
                parent,
                false
            )
        )
    }

    override fun onBindView(holder: SuperViewHolder, data: T, position: Int, size: Int) {
        holder.bind(BR.data, data)
    }
}