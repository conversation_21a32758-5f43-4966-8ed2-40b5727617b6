package com.bxkj.common.widget.zpcalenderview

import java.time.LocalDate
import java.time.Period
import java.time.temporal.TemporalAdjusters

/**
 * Description:
 * Author:45457
 **/
object ZPCalenderUtils {

    fun isBeforeToday(date:LocalDate):Boolean{
        return date.isBefore(LocalDate.now())
    }

    fun getMonthBetween(startDate: LocalDate, endDate: LocalDate): Int {
        return Period.between(startDate, endDate).toTotalMonths().toInt()
    }

    fun getMonthDays(initDate: LocalDate, sundayIsFirstColum: Boolean): List<LocalDate> {
        val dayList = mutableListOf<LocalDate>()
        val monthDays = initDate.lengthOfMonth()

        val monthFirstDay = initDate.with(TemporalAdjusters.firstDayOfMonth())
        val firstDayOfWeek = monthFirstDay.dayOfWeek.value

        val monthLastDay = initDate.with(TemporalAdjusters.lastDayOfMonth())
        val lastDayOfWeek = monthLastDay.dayOfWeek.value

        val lastMonthDayCount = if (sundayIsFirstColum) firstDayOfWeek else firstDayOfWeek-1

        if (firstDayOfWeek<7){
            //上月
            for (i in lastMonthDayCount downTo 1) {
                dayList.add(monthFirstDay.minusDays(i.toLong()))
            }
        }

        //本月
        for (i in 1..monthDays) {
            dayList.add(LocalDate.of(initDate.year,initDate.month,i))
        }

        //下月
        val nextMonthDayCount = if (sundayIsFirstColum) 6 - lastDayOfWeek else 7 - lastDayOfWeek

        for (i in 1..nextMonthDayCount) {
            dayList.add(monthLastDay.plusDays(i.toLong()))
        }

        return dayList
    }
}