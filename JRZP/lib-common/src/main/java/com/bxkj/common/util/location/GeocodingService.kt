package com.bxkj.common.util.location

import android.util.Log
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import java.net.URLEncoder

object GeocodingService {

  const val ROOT_URL = "https://siteapi.cloud.huawei.com/mapApi/v1/siteService/reverseGeocode"
  const val connection = "?key="
  val JSON = "application/json; charset=utf-8".toMediaType()
  private val gson = Gson()

  suspend fun reverseGeocoding(apiKey: String): ReverseGeocodeResult? =
    withContext(Dispatchers.IO) {
      val json = JSONObject()

      try {
        val location = JSONObject().apply {
          put("lng", 77.2155)
          put("lat", 18.0527)
        }
        json.put("location", location)
        json.put("radius", 10)
      } catch (e: JSONException) {
        Log.e("error", e.message.toString())
        return@withContext null
      }

      val body = json.toString().toRequestBody(JSON)

      val client = OkHttpClient()
      val request = Request.Builder()
        .url("$ROOT_URL$connection${URLEncoder.encode(apiKey, "UTF-8")}")
        .post(body)
        .build()

      try {
        val response = client.newCall(request).execute()
        val responseBody = response.body?.string()

        if (responseBody != null) {
          Log.d("ReverseGeocoding", responseBody)
          gson.fromJson(responseBody, ReverseGeocodeResult::class.java)
        } else {
          Log.e("ReverseGeocoding", "Empty response")
          null
        }
      } catch (e: IOException) {
        Log.e("ReverseGeocoding", e.toString())
        null
      } catch (e: Exception) {
        Log.e("ReverseGeocoding", "Parse error: ${e.message}")
        null
      }
    }
}