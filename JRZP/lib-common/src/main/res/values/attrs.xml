<?xml version="1.0" encoding="utf-8"?>
<resources>


  <!--============================== 自定义View Start ==============================-->

  <declare-styleable name="LabelLayout">
    <attr name="itemTextSize" format="dimension|reference" />
    <attr name="itemTextColor" format="color|reference" />
    <attr name="itemBackground" format="reference" />
    <attr name="itemMargin" format="dimension|reference" />
    <attr name="itemPaddingTB" format="dimension|reference" />
    <attr name="itemPaddingLR" format="dimension|reference" />
    <attr name="maxShowTab" format="integer" />
  </declare-styleable>

  <declare-styleable name="AvatarGroupView">
    <attr name="itemSize" format="dimension|reference" />
    <attr name="itemOffset" format="dimension|reference" />
  </declare-styleable>

  <declare-styleable name="ZPTextView"></declare-styleable>

  <declare-styleable name="ZPSideIndexBar">
    <attr name="indexTextSize" format="reference|dimension" />
    <attr name="indexTextNormalColor" format="reference" />
    <attr name="indexTextSelectColor" format="reference" />
  </declare-styleable>

  <!-- GifView -->
  <declare-styleable name="common_GIFVIEW">
    <!--gif文件引用-->
    <attr name="common_gifSrc" format="reference" />
    <!--是否加载完自动播放-->
    <attr name="common_autoPlay" format="boolean" />
    <!--播放次放，默认永远播放-->
    <attr name="common_playCount" format="integer" />
  </declare-styleable>

  <declare-styleable name="MyEditText">
    <attr name="show_clear_icon" format="boolean" />
  </declare-styleable>

  <!-- TitleBar -->
  <declare-styleable name="CommonTitleBar">
    <attr name="title" format="string" />
    <attr name="left_img" format="reference" />
    <attr name="left_text" format="string" />
    <attr name="showBackIcon" format="boolean" />
    <attr name="right_img" format="reference" />
    <attr name="right_img_two" format="reference" />
    <attr name="show_right_text_bg" format="boolean" />
    <attr name="right_text" format="string" />
    <attr name="show_right" format="boolean" />
  </declare-styleable>

  <declare-styleable name="VideoPlayLayout">
    <attr name="showTitle" format="boolean" />
    <attr name="showOption" format="boolean" />
    <attr name="showTag" format="boolean" />
    <attr name="playCount" format="integer" />
    <attr name="showPlayCount" format="boolean" />
    <attr name="reviewStatus" format="integer" />
  </declare-styleable>

  <!-- 飘星自定义属性 -->
  <declare-styleable name="HeartLayout">
    <attr name="initX" format="dimension" />
    <attr name="initY" format="dimension" />
    <attr name="xRand" format="dimension" />
    <attr name="animLengthRand" format="dimension" />
    <attr name="xPointFactor" format="dimension" />
    <attr name="animLength" format="dimension" />
    <attr name="heart_width" format="dimension" />
    <attr name="heart_height" format="dimension" />
    <attr name="bezierFactor" format="integer" />
    <attr name="anim_duration" format="integer" />
  </declare-styleable>

  <!--++++++++++ 展开/收起TextView ++++++++++-->
  <declare-styleable name="ExpandTextView">
    <attr name="contentText" format="reference|string" />
    <attr name="foldHint" format="reference|string" />
    <attr name="expandHint" format="reference|string" />
    <attr name="textContentColor" format="reference|color" />
    <attr name="textHintColor" format="reference|color" />
    <attr name="indicateImage" format="reference" />
    <attr name="minVisibleLines" format="integer" />
    <attr name="contentTextSize" format="dimension" />
    <attr name="hintTextSize" format="dimension" />
    <attr name="animationDuration" format="integer" />
  </declare-styleable>


  <declare-styleable name="ZPFollowButton">
    <attr name="loadingViewDrawable" format="reference" />
    <attr name="loadingViewSize" format="dimension" />
    <attr name="android:textSize" />
    <attr name="android:textColor" />
  </declare-styleable>

  <!--============================== 自定义View End ==============================-->

  <attr name="zp_followButtonStyle" format="reference" />
  <attr name="zp_followTextColor" format="reference" />

  <!--LoadingView-->
  <declare-styleable name="LoadingView">
    <attr name="loading_view_size" format="dimension" />
    <attr name="loading_view_color" format="color" />
  </declare-styleable>

</resources>