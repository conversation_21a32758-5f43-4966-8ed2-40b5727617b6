<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  style="@style/wrap_wrap"
  android:background="@drawable/bg_664f4b4b_radius_4"
  android:gravity="center"
  android:orientation="vertical">

  <com.bxkj.common.widget.LoadingView
    android:id="@+id/loading"
    style="@style/wrap_wrap"
    android:layout_marginStart="@dimen/dp_24"
    android:layout_marginTop="@dimen/dp_24"
    android:layout_marginEnd="@dimen/dp_24"
    app:layout_constraintBottom_toTopOf="@id/tv_loading"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_goneMarginBottom="@dimen/dp_24" />

  <TextView
    android:id="@+id/tv_loading"
    style="@style/Text.12sp.FFFFFF"
    android:layout_marginStart="@dimen/dp_12"
    android:layout_marginTop="@dimen/dp_12"
    android:layout_marginEnd="@dimen/dp_12"
    android:layout_marginBottom="@dimen/dp_12"
    android:gravity="center"
    android:lineSpacingExtra="@dimen/dp_4"
    android:visibility="gone"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/loading" />
</androidx.constraintlayout.widget.ConstraintLayout>