<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_filter_content"
    style="@style/match_match"
    android:background="@drawable/bg_ffffff"
    android:orientation="vertical">


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_filter_options"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:paddingBottom="@dimen/dp_16" />

    <View
        android:id="@+id/v_line"
        style="@style/Line.Horizontal" />

    <LinearLayout
        android:id="@+id/ll_filter_bottom_bar"
        style="@style/match_wrap"
        android:background="@drawable/bg_ffffff"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_filter_reset"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_48"
            android:layout_weight="1"
            android:background="@drawable/bg_ffffff"
            android:gravity="center"
            android:text="@string/common_reset"
            android:textColor="@color/common_888888"
            android:textSize="@dimen/dp_16" />

        <TextView
            android:id="@+id/tv_filter_confirm"
            android:layout_width="@dimen/dp_0"
            android:layout_height="@dimen/dp_48"
            android:layout_weight="1"
            android:background="@color/cl_ff7405"
            android:gravity="center"
            android:text="@string/common_save"
            android:textColor="@color/common_white"
            android:textSize="@dimen/dp_16" />

    </LinearLayout>
</LinearLayout>