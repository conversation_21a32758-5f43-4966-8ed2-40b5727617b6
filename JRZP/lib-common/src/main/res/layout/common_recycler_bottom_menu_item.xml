<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
  style="@style/match_wrap">

  <TextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tv_item"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_52"
    android:layout_gravity="center"
    android:gravity="center"
    android:textColor="@color/cl_333333"
    android:textSize="@dimen/common_sp_17" />

  <ImageView
    android:id="@+id/iv_selected"
    style="@style/wrap_wrap"
    android:layout_gravity="end|center_vertical"
    android:layout_marginEnd="@dimen/dp_16"
    android:src="@drawable/ic_address_selected"
    android:visibility="gone" />
</FrameLayout>