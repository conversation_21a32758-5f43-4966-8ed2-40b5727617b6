<merge xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content">

  <TextView
    android:id="@+id/tv_content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:ellipsize="end"
    android:lineSpacingExtra="4dp"
    android:textColor="?android:textColorPrimary"
    android:textSize="14sp" />

  <RelativeLayout
    android:id="@+id/rl_show_more"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="6dp"
    android:paddingLeft="10dp"
    android:paddingRight="20dp">

    <LinearLayout
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_centerInParent="true"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:padding="5dp">

      <ImageView
        android:id="@+id/iv_arrow_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center" />

      <TextView
        android:id="@+id/tv_more_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:textColor="?android:textColorPrimary"
        android:textSize="12sp" />
    </LinearLayout>
  </RelativeLayout>
</merge>