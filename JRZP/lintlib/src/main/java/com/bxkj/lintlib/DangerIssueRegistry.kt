package com.bxkj.lintlib

import com.android.tools.lint.client.api.IssueRegistry
import com.android.tools.lint.detector.api.CURRENT_API
import com.android.tools.lint.detector.api.Issue
import com.bxkj.lintlib.detector.*
import com.bxkj.lintlib.detΩector.StealCameraDetector

/**
 * @date on 2019-12-23  14:07
 * <AUTHOR>
 * @mail  <EMAIL>
 */
class DangerIssueRegistry : IssueRegistry() {

    override val issues: List<Issue>
        get() {
            return listOf(
                StealImeiDetector.ISSUE,
                StealImsiDetector.ISSUE,
                StealPhoneNumberDetector.ISSUE,
                ListenPhoneDetector.ISSUE,
                StealGpsDetector.ISSUE,
                StealCameraDetector.ISSUE,
                StorageDetector.ISSUE
            )
        }

    override val api: Int
        get() = CURRENT_API
}