package com.bxkj.personal.ui.activity.editrichtext

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author: YangXin
 * @date: 2020/12/7
 * @version: V1.0
 */
class EditRichTextNavigation {

  companion object {

    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/editrichtext"

    const val EXTRA_PAGE_TITLE = "PAGE_TITLE"
    const val EXTRA_HINT = "HINT"
    const val EXTRA_CONTENT = "CONTENT"

    const val EXTRA_RESULT_CONTENT = "RESULT_TEXT"

    fun create(pageTitle: String, hint: String, content: String? = null): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withString(EXTRA_PAGE_TITLE, pageTitle)
        .withString(EXTRA_HINT, hint)
        .withString(EXTRA_CONTENT, content)
    }
  }
}