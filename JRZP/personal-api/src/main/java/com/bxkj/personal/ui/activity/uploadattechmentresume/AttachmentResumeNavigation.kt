package com.bxkj.personal.ui.activity.uploadattechmentresume

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * Description:
 * Author:Sanjin
 * Date:2024/4/18
 **/
class AttachmentResumeNavigation {

  companion object {
    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/upload_attachment_resume"

    fun create(): RouterNavigator {
      return Router.getInstance().to(PATH)
    }
  }
}