package com.bxkj.personal.ui.activity.scanloginconfirm

import com.bxkj.common.util.router.Router
import com.bxkj.common.util.router.RouterNavigator
import com.bxkj.personal.PersonalConstants

/**
 * @Description:
 * @author:45457
 * @date: 2020/11/6
 * @version: V1.0
 */
class ScanLoginConfirmNavigation {

  companion object {
    const val PATH = "${PersonalConstants.PERSONAL_DIRECTORY}/scanloginconfirm"

    const val EXTRA_LOGIN_CHANNEL = "LOGIN_CHANNEL"

    fun navigate(channel: String): RouterNavigator {
      return Router.getInstance().to(PATH)
        .withString(EXTRA_LOGIN_CHANNEL, channel)
    }
  }
}