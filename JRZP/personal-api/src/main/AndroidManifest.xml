<manifest package="com.bxkj.personal.api">
  <uses-permission android:name="android.permission.CALL_PHONE" xmlns:android="http://schemas.android.com/apk/res/android"/>
  <application android:supportsRtl="true" xmlns:android="http://schemas.android.com/apk/res/android" android:theme="@style/JrzpAppTheme">
    <activity android:name="com.bxkj.personal.ui.activity.main.MainActivity" android:configChanges="orientation|keyboardHidden|screenSize" android:exported="true" android:screenOrientation="portrait" android:theme="@style/DefaultCityPickerTheme" android:windowSoftInputMode="adjustNothing">
      <intent-filter>
        <data android:host="m.jrzp.com" android:path="/app.aspx" android:scheme="jrzpdeeplinking"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.BROWSABLE"/>
      </intent-filter>
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:host="jrzpapi.jdzj.com" android:pathPattern="/jinrizhaopin/app.aspx" android:scheme="https"/>
      </intent-filter>
    </activity>
    <activity android:name=".ui.activity.parttimeworkbench.customersearchresult.CustomerSearchResultActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.parttimeworkbench.PartTimeWorkbenchActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.jobdetails.JobDetailsActivityV2" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.myresume.workexp.EditWorkExpActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.findjobbymap.FindJobOnMapActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.personalmember.PersonalMemberActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.resumetop.ResumeTopActivityV2" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.applicationrecord.ResumeDeliveryRecordActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.uploadattechmentresume.AttachmentResumeActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.resumeopenstatesetting.ResumeOpenStateSettingActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.videolist.VideoListActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.onlinecampustalkdetails.OnlineCampusTalkDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.campustalkdetails.CampusTalkDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.campusrecruitdetails.CampusRecruitDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.parttimejob.PartTimeJobContainerActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.lastjob.LatestJobActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.interviewdetails.GeekInterviewDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.parttimejobtypelist.PartTimeJobSearchResultActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.parttimejobtypelist.PartTimeJobTypeListActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name=".ui.activity.searchjobresult.SearchJobResultActivityV2" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.hotdiscuss.HotDiscussActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.myresume.SelfEvaluationActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.myresume.MyResumeDetailsActivityV2" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.jobintention.JobIntentionActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.microresumeinfo.MicroResumeInfoActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.hotdiscuss.HotDiscussRankActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.govnews.GovRecruitmentActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait" android:theme="@style/DefaultCityPickerTheme"/>
    <activity android:name="com.bxkj.personal.ui.activity.finearticle.FineArticleDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.study.StudyActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.finearticle.FineArticleActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.fragment.qa.QAActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.recommendjob.RecommendJobActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.recommendcompany.RecommendCompanyActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.schoolrecruitdeliveryrecord.DeliveryRecordManagementActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.aboutus.AboutUsV2Activity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.sysrecomendsetting.SysRecommendSettingActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.searchnews.SearchVideoActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.searchjobresult.SearchJobResultActivity" android:screenOrientation="portrait" android:theme="@style/JRZPCityPickerTheme"/>
    <activity android:name="com.bxkj.personal.ui.activity.searchjobs.SearchJobsActivity" android:screenOrientation="portrait" android:theme="@style/DefaultCityPickerTheme"/>
    <activity android:name="com.bxkj.personal.ui.activity.selectarea.SelectAreaActivity" android:screenOrientation="portrait" android:theme="@style/DefaultCityPickerTheme"/>
    <activity android:name="com.bxkj.personal.ui.activity.selectresume.SelectResumeActivity" android:launchMode="singleTop" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.selectaddress.SelectAddressActivity" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
    <activity android:name="com.bxkj.personal.ui.activity.mycollectionjobs.MyCollectionJobsActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.myresume.careerobjective.CareerObjectiveActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.myresume.workexp.WorkExpActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.editinfo.EditInfoActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateHidden|adjustPan"/>
    <activity android:name="com.bxkj.personal.ui.activity.myresume.edubackground.EduBackgroundActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateHidden|adjustPan"/>
    <activity android:name="com.bxkj.personal.ui.activity.myresume.professionalskill.ProfessionalSkillActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateHidden|adjustPan"/>
    <activity android:name="com.bxkj.personal.ui.activity.myresume.languageskills.LanguageSkillsActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.myresume.schoolsituation.SchoolSituationActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.myresume.certificate.CertificateActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.setting.SettingActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.minesearchjobs.MineSearchJobsActivity" android:screenOrientation="portrait" android:windowSoftInputMode="stateHidden|adjustPan"/>
    <activity android:name="com.bxkj.personal.ui.activity.feedbackmsgdetails.FeedbackMsgDetailsActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.resumesetting.ResumeSettingActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.applicationrecord.ApplicationRecordActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.invitationstodelivery.InvitationsToDeliveryActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.feedbackmsgcontent.FeedbackMsgContentActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.seenmybusiness.ViewedMeCompanyAcitivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.msgnotification.MsgNotificationActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.msgnotificationcontent.MsgNotificationContentActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.systemmsg.SystemMsgActivity" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.createresumesteptwo.CreateResumeStepTwoActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
    <activity android:name="com.bxkj.personal.ui.activity.createresumestepthree.CreateResumeStepThreeActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
    <activity android:name="com.bxkj.personal.ui.activity.createresumestepfour.CreateResumeStepFourActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan|stateHidden"/>
    <activity android:name="com.bxkj.personal.ui.activity.mycollectioncompany.MyCollectionCompanyActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.web.WebActivity" android:configChanges="screenSize|orientation|keyboardHidden|smallestScreenSize|screenLayout" android:launchMode="singleTop" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.agreement.AgreementActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.service.ServiceActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.scanloginconfirm.ScanLoginConfirmActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.companydetails.CompanyDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.jobdetails.JobDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.schoolrecruitdetails.SchoolRecruitDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.selectdepartment.SelectDepartmentActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.postnews.PostNewsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.integralrecharge.IntegralRechargeActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.paymentorder.PaymentOrderActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.paymentresult.PaymentResultActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.userbasicinfo.UserBasicInfoActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize|stateHidden"/>
    <activity android:name="com.bxkj.personal.ui.activity.gallery.GalleryActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait" android:theme="@style/ScaleActivityTheme"/>
    <activity android:name="com.bxkj.personal.ui.activity.myfollowuser.MyFollowActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.fans.FansActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.followjobfairlist.JobFairListActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.paiduser.PaidUserActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.uploadavatar.UploadAvatarActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.selectdefaultavatar.SelectDefaultAvatarActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.bindmobile.BindMobileActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.gznews.GzNewsDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.videodetails.VideoDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.postvideo.PostVideoActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.takecover.TakeCoverActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.postnotice.PostNoticeActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan"/>
    <activity android:name="com.bxkj.personal.ui.activity.selectaddressbymap.SelectAddressByMapActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.gzuserhome.GzUserHomeActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.questiondetails.QuestionDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.addanswer.AddAnswerActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.addquestion.AddQuestionActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait" android:windowSoftInputMode="adjustPan"/>
    <activity android:name="com.bxkj.personal.ui.activity.answerdetails.AnswerDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.myhistory.MyHistoryActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.findjobbymap.FindJobByMapActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.myresumelist.MyResumeListActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.resumetop.ResumeTopActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.orderhistory.OrderHistoryActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.searchnews.SearchNewsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.inviteuser.InviteUserActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.searchanswer.SearchAnswerActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.msgnotification.NewMsgNotificationActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.msgnotificationcontent.NewMsgNotificationContentActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.questioninvite.QuestionInviteActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.searchquestion.SearchQuestionActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.typenews.TypeNewsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.signupuser.SignUpUserActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.videosignupmsg.VideoSignUpMsgActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.qaranklist.QARankListActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.studynewsdetails.StudyNewsDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.quickanswer.QuickAnswerActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.arealist.AreaListActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.shieldcompany.ShieldCompanyActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.addshieldcompany.AddShieldCompanyActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait" android:windowSoftInputMode="stateVisible"/>
    <activity android:name="com.bxkj.personal.ui.activity.shieldcompany.ShieldCompanyTipsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.usersetting.UserSettingActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.conversation.GeekChatContentActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait" android:windowSoftInputMode="adjustResize"/>
    <activity android:name="com.bxkj.personal.ui.activity.resumedetails.ResumePreviewActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.conversationreport.CommonReportActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.reportreason.ReportReasonActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.privacysetting.PrivacySettingActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.permissionmanagement.PermissionManagementActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.momentdetails.MomentDetailsActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.selectrelateschool.SelectRelateSchoolActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
    <activity android:name="com.bxkj.personal.ui.activity.editrichtext.EditRichTextActivity" android:configChanges="keyboardHidden|orientation|screenSize" android:screenOrientation="portrait"/>
  </application>
</manifest>
